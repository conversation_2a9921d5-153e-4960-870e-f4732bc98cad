<template>
  <div>
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <div>
        <CetDateSelect
          class="dateSelect mb-J3"
          v-bind="CetDateSelect_time"
          v-on="CetDateSelect_time.event"
        ></CetDateSelect>
        <CetTable
          style="height: 450px"
          :data.sync="CetTable_Message.data"
          :dynamicInput.sync="CetTable_Message.dynamicInput"
          v-bind="CetTable_Message"
          v-on="CetTable_Message.event"
        >
          <el-table-column
            v-for="(item, index) in Column_Message"
            :key="index"
            v-bind="item"
          ></el-table-column>
        </CetTable>
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
        ></el-pagination>
      </div>
      <template v-slot:footer>
        <span>
          <el-button
            @click="CetDialog_pagedialog.closeTrigger_in = Date.now()"
            plain
          >
            {{ $T("关闭") }}
          </el-button>
        </span>
      </template>
    </CetDialog>
  </div>
</template>
<script>
import common from "@/utils/common";
import customApi from "@/api/custom";
import omegaAuth from "@omega/auth";
import { EVENT_LEVEL } from "@/config/const.js";
export default {
  name: "MessageCenter",
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    }
  },
  data() {
    return {
      CetDateSelect_time: {
        layout: "button",
        value: { dateType: "1" },
        typeList: ["day", "week", "month"],
        event: {
          date_out: this.CetDateSelect_time_date_out
        }
      },
      filterTime: [
        this.$moment().startOf("day").valueOf(),
        this.$moment().endOf("day").valueOf()
      ],
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        top: "10vh",
        width: "60%",
        height: "600px",
        title: $T("信息中心"),
        showClose: true
      },
      // Message表格组件
      CetTable_Message: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        border: false,
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        showPaginationUI: true,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      },
      Column_Message: [
        {
          type: "index",
          prop: "",
          label: $T("序号"),
          headerAlign: "left",
          align: "left",
          width: 60,
          showOverflowTooltip: true
        },
        {
          prop: "recordTime",
          label: $T("时间"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true,
          width: 230,
          formatter: common.formatDateCol("YYYY-MM-DD HH:mm:ss.SSS")
        },
        {
          prop: "logTypeName",
          label: $T("类型"),
          headerAlign: "left",
          align: "left",
          width: 130,
          showOverflowTooltip: true
        },
        {
          prop: "",
          label: $T("事件等级"),
          headerAlign: "left",
          align: "left",
          width: 100,
          showOverflowTooltip: true,
          formatter: this.formatterClass
        },
        {
          prop: "description",
          label: $T("描述"),
          headerAlign: "left",
          align: "left",
          showOverflowTooltip: true
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 30,
        total: 0
      }
    };
  },
  watch: {
    openTrigger_in(val) {
      this.openDialog(val);
    }
  },
  methods: {
    /**
     * @description: 弹窗打开
     */
    openDialog(val) {
      this.queryAllMessage();
      this.CetDialog_pagedialog.openTrigger_in = val;
    },

    /**
     * @description: 查询表格数据
     */
    queryAllMessage() {
      const param = {
        startTime: this.filterTime[0],
        endTime: this.filterTime[1],
        noticeMode: 4,
        receiver: omegaAuth.user.getUserId().toString(),
        page: {
          index: (this.pagination.currentPage - 1) * this.pagination.pageSize,
          limit: this.pagination.pageSize
        }
      };
      this.CetTable_Message.data = [];
      customApi.queryWebMessagesList(param).then(res => {
        if (res.code === 0 && res.data) {
          this.CetTable_Message.data = res.data;
          this.pagination.total = res.total;
        }
      });
    },
    /**
     * @description: 格式化等级
     * */
    formatterClass(row, column, cellValue, index) {
      const content = row.content ? JSON.parse(row.content) : {};
      const eventClass = content.eventClass;
      const obj = EVENT_LEVEL.find(item => item.id === eventClass) || {};
      return obj.text || "--";
    },

    /**
     * @description: 日期返回
     */
    CetDateSelect_time_date_out(val) {
      this.filterTime = _.cloneDeep(val);
      this.currentPage = 1;
      this.queryAllMessage();
    },

    /**
     * @description: 修改每页条数
     */
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.queryAllMessage();
    },

    /**
     * @description: 修改页数
     */
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.queryAllMessage();
    }
  },
  created() {}
};
</script>
<style lang="scss" scoped>
::v-deep .el-pagination {
  margin-top: var(--J3);
  display: flex;
  justify-content: end;
}
</style>
