<template>
  <div class="block cet-notice-panel" v-show="alarmTip">
    <el-popover placement="top" trigger="hover" width="420" v-model="visible">
      <el-badge
        :hidden="hideBadge"
        :value="items.length"
        :max="99"
        slot="reference"
      >
        <omega-icon class="icon-hover-normal" symbolId="message-lin" />
      </el-badge>
      <el-header
        height="22px"
        style="padding: 0; line-height: 22px"
        class="bgCard"
      >
        <p class="fl leftHeader fs14 fwb">{{ $T("消息") }}</p>
        <p class="fr rightHeader fs14">
          {{ $T("{0}条", items.length) }}
          <span class="ZS pl10" @click="handleClearAllMessage">
            {{ $T("全部已读") }}
          </span>
        </p>
      </el-header>
      <el-main style="padding: 0; height: 330px; overflow-y: scroll">
        <template v-for="(item, index) in items">
          <el-collapse-transition :key="index">
            <NoticeItem
              ref="NoticeItem"
              :item="item"
              @handleRemoveItem="handleRemoveItem"
              @handleGoToPage="handleGoToPage"
            ></NoticeItem>
          </el-collapse-transition>
        </template>
        <div v-if="!items.length" class="notice-empty">
          <el-empty
            :image="require(`./assets/noticeEmpty.png`)"
            :image-size="144"
          ></el-empty>
        </div>
      </el-main>
      <p class="fl mt16 ZS fs14" @click="handleViewAllMessage">
        {{ $T("查看全部消息") }}
      </p>
    </el-popover>

    <!--右下角事件弹窗 -->
    <noticeDrawe @handleGoToPageByItem="handleGoToPageByItem" />
    <!--告警闪屏 -->
    <AlarmTwinkle />
    <!--消息中心弹窗页面 -->
    <MessageCenter
      v-bind="MessageCenter"
      :openTrigger_in="MessageCenter.openTrigger_in"
      v-on="MessageCenter.event"
    ></MessageCenter>
    <audio id="myaudio" muted>
      <source src="/static/alarm/alarm.mp3" />
    </audio>
    <a allow="autoplay" ref="empty_iframe" style="display: none"></a>
  </div>
</template>
<script>
import NoticeItem from "./NoticeItem.vue";
import MessageCenter from "./MessageCenter.vue";
import noticeDrawe from "./noticeDrawe.vue";
import AlarmTwinkle from "./AlarmTwinkle.vue";

import omegaAuth from "@omega/auth";
import { mapState, mapMutations } from "vuex";
import { http } from "@omega/http";
import { EVENT_Reaction, RELOAD_KEY, TENANT_KEY } from "@/config/const";
export default {
  name: "LayoutNotice",
  components: {
    NoticeItem,
    MessageCenter,
    noticeDrawe,
    AlarmTwinkle
  },
  data() {
    return {
      speechObj: SpeechSynthesisUtterance
        ? new SpeechSynthesisUtterance("")
        : {},
      audioSrc: "/static/alarm/alarm.mp3",
      websocket: null,
      timer: null,
      visible: false,
      //请填写组件含义弹窗页面的data
      MessageCenter: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        //查询数据的id入参
        queryId_in: 0,
        inputData_in: null
      },
      manualClose: false,
      audio: null
    };
  },
  computed: {
    hideBadge() {
      return this.items.length === 0;
    },
    ...mapState("settings", ["soundMode", "isMuted", "alarmTip"]),
    ...mapState("notice", ["items"]),
    volume: {
      get() {
        const volume = this.$store.state.settings.volume;
        return +(volume / 100).toFixed(2);
      }
    }
  },

  watch: {
    soundMode(val) {
      this.pause();
      window.speechSynthesis && window.speechSynthesis.cancel();
      this.changeConfig();
      if (this.soundMode === "continuous" && this.items.length > 0)
        this.playLoop();
    },
    volume(val) {
      let audio = document.getElementById("myaudio");
      audio.volume = val;
    },
    isMuted(val) {
      let audio = document.getElementById("myaudio");
      audio.muted = val;
    }
  },
  methods: {
    ...mapMutations("notice", {
      setItems: "SET_ITEMS",
      addItem: "ADD_ITEM",
      removeItem: "REMOVE_ITEM"
    }),

    /**
     * @description:创建websocket连接
     */
    createWS() {
      const userId = omegaAuth.user.getUserId();
      const protocol = window.location.protocol === "https:" ? "wss" : "ws";

      //创建websocket连接
      this.websocket = new WebSocket(
        // `${protocol}://************:8300/messageServer/websocket/118`

        `${protocol}://${window.location.host}/messageServer/websocket/${userId}`,
        [
          window.sessionStorage.getItem("omega_token") ??
            window.localStorage.getItem("omega_token")
        ]
      );

      // 连接发生错误的回调方法
      this.websocket.onclose = () => {
        if (this.manualClose) {
          return;
        }
        this.manualClose = false;
        clearInterval(this.timer);
        setTimeout(() => {
          this.createWS();
        }, 30000);
      };

      // 连接成功建立的回调方法，注册客户端
      this.websocket.onopen = () => {
        this.sendHeartbeatPacket(); //心跳包发送开启
        // 注册自己
        http({
          url: `/notice/v1/service/web/client`,
          method: "POST",
          data: {
            // 用户登录ID
            userId: userId,
            // 用户标签，用户给一组用户发送信息时用
            // 一个用户对应多个标签用“,”隔开，如巡检员,预试员,业主,项目1
            tags: [`${userId}`]
          }
        });
      };

      // 接收到消息的回调方法
      this.websocket.onmessage = event => {
        this.sendHeartbeatPacket(); //心跳包重置
        if (event.data) {
          this.newMessage(event);
        }
      };
    },

    /**
     * @description:循环发送心跳包
     */
    sendHeartbeatPacket() {
      if (this.timer) clearInterval(this.timer);
      this.timer = setInterval(() => {
        this.websocket.send("");
      }, 40000);
    },

    /**
     * @description:新产生一条消息
     */
    newMessage(event) {
      const message = event.data && JSON.parse(event.data);

      setTimeout(() => {
        this.changeConfig();
        this.startPalyAudio(message);
      }, 1000);
      if (this.independentEvent(message)) return;
      this.addItem(message);
    },

    /**
     * @description:处理事件的不同字段
     */
    //独立弹窗消息不进入队列
    independentEvent(alarmItem) {
      const content = alarmItem.content ? JSON.parse(alarmItem.content) : {};
      const independentCard = content?.independentCard;
      if (independentCard === EVENT_Reaction.ACTION) {
        this.$notify({
          title:
            this.$moment(alarmItem.time).format("YYYY-MM-DD HH:mm:ss.SSS") + "",
          dangerouslyUseHTMLString: true,
          message:
            `<div>${alarmItem.logTypeName}</div>` + " " + alarmItem.description,
          duration: 0
        });
        return true;
      }
      return false;
    },

    /**
     * @description: iframe加载后播放一段空的音频。用于解决浏览器禁用播放功能
     */
    ifmLoad() {
      this.$refs.empty_iframe.src = "../../../static/assets/notice/empty.mp3";
    },

    /**
     * @description: 开始播放
     */
    startPalyAudio(alarmItem) {
      const content = alarmItem.content ? JSON.parse(alarmItem.content) : {};
      const warningSound = content?.warningSound;
      if (warningSound === EVENT_Reaction.NOT) return;
      if (this.soundMode === "single") {
        this.playSingle();
      } else if (this.soundMode === "level") {
        this.playLevel(alarmItem);
      } else if (this.soundMode === "continuous") {
        this.playLoop();
      } else if (this.soundMode === "speech") {
        this.playSpeech(alarmItem);
      } else {
        this.pause();
      }
    },

    /**
     * @description: 单次播放
     */
    playSingle() {
      let audio = document.getElementById("myaudio");
      if (!audio) return;
      audio.muted = this.isMuted;
      audio.volume = this.volume;

      this.$nextTick(() => {
        audio.play();
      });
    },

    /**
     * @description: 循环播放
     */
    playLoop() {
      let audio = document.getElementById("myaudio");
      if (!audio) return;
      audio.muted = this.isMuted;
      audio.volume = this.volume;

      this.$nextTick(() => {
        audio.play();
      });
    },

    /**
     * @description: 语音播报
     */
    playSpeech(alarmItem) {
      window.speechSynthesis && window.speechSynthesis.cancel();
      const content = `${alarmItem.logTypeName}${alarmItem.description}`;
      this.speechObj = new SpeechSynthesisUtterance(content);
      this.speechObj.volume = this.isMuted ? 0 : this.volume;
      // 速度
      this.speechObj.rate = 1;
      // 音调-语音播报无法在播放过程中调整音调
      // this.speechObj.pitch = 1.5;
      window.speechSynthesis.speak(this.speechObj);
    },

    /**
     * @description: 事件等级播放
     */
    playLevel(alarmItem) {
      let audio = document.getElementById("myaudio");
      if (!audio) return;
      audio.muted = this.isMuted;
      const content = alarmItem.content ? JSON.parse(alarmItem.content) : {};
      const eventClass = content.eventClass;
      const warningSoundSelect = content?.warningSoundSelect;
      if (!_.isNumber(eventClass)) return;
      audio.src = `/static/alarm/level_${eventClass}.mp3`;
      if (warningSoundSelect)
        audio.src = `/static/alarm/${warningSoundSelect}.mp3`;
      audio.loop = false;
      audio.volume = this.volume;

      this.$nextTick(() => {
        audio.play();
      });
    },

    /**
     * @description: 播放类型变更
     */
    changeConfig() {
      const audio = document.getElementById("myaudio");
      const soundMode = this.soundMode;
      switch (soundMode) {
        case "single":
          audio.src = "/static/alarm/level_0.mp3";
          audio.loop = false;
          break;
        case "speech":
          audio.loop = false;
          break;
        case "level":
          audio.loop = false;
          break;
        case "continuous":
          audio.src = "/static/alarm/level_0.mp3";
          audio.loop = "loop";
          break;
        default:
          break;
      }
    },

    /**
     * @description: 暂停
     */
    pause() {
      const audio = document.getElementById("myaudio");
      audio.pause();
    },
    /**
     * @description: 全部设置为已读
     */
    handleClearAllMessage() {
      this.setItems([]);
      this.pause();
    },

    /**
     * @description: 查看全部
     */
    handleViewAllMessage() {
      this.MessageCenter.openTrigger_in = new Date().getTime();
    },

    /**
     * @description: 设置消息为已读
     */
    handleRemoveItem(val) {
      this.removeItem(val);

      if (this.items.length === 0) {
        this.pause();
      }
    },
    /**
     * @description: 右下角弹窗跳转逻辑
     */
    handleGoToPageByItem(val) {
      let canGoPage = this.$refs?.NoticeItem[0]?.checkIsShowGoto(val);
      if (!canGoPage) return;
      this.handleGoToPage(val);
    },
    /**
     * @description: 信息详情模板点击转到、已读对应的事件
     */
    handleGoToPage(val) {
      const content = val.content ? JSON.parse(val.content) : {};
      const tenantId = content.tenantId;
      const redirectPageUrl = content.redirectPageUrl;
      if (tenantId) {
        const urlParams = new URLSearchParams(val);
        sessionStorage.setItem(TENANT_KEY, tenantId);
        sessionStorage.setItem(
          RELOAD_KEY,
          redirectPageUrl + "?" + urlParams.toString()
        );
        window.location.reload();
        return;
      }
      this.$router.push({
        path: redirectPageUrl,
        query: val
      });
    }
  },
  created() {
    // 判断当前浏览器是否支持WebSocket
    if ("WebSocket" in window) {
      this.createWS();
    } else {
      this.$message({
        message: $T("浏览器不支持websocket，无法进行获取通知消息"),
        type: "error"
      });
    }

    // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    window.onbeforeunload = () => {
      if (this.websocket) {
        this.websocket.close();
      }
    };
  },
  mounted() {
    this.audio = document.getElementById("myaudio");
    this.$nextTick(() => {
      this.ifmLoad();
    });
  },
  beforeDestroy() {
    if (this.timer) clearInterval(this.timer);
    this.manualClose = true;
    this.websocket.close();
  }
};
</script>
<style lang="scss">
.cet-notice-panel {
  .el-badge__content {
    border: 0px;
  }
}
</style>
<style lang="scss" scoped>
.block {
  float: right;
  width: 36px;
  height: 36px;
  cursor: pointer;
  margin-right: 6px;
}

.notice-icon {
  background-color: transparent;
  //background-image: url("./assets/Message_icon.png");
  border-width: 0px;
  width: 36px;
  height: 36px;
}

p {
  margin: 0;
}
u {
  // color: #0066cc;
  cursor: pointer;
}
.left {
  float: left;
  font-weight: 400;
  font-size: 20px;
  // color: #0053a6;
}
.right {
  float: right;
  font-weight: 400;
  font-size: 14px;
  color: #999999;
}
.fwb {
  font-weight: 600;
  color: #13171f;
}
.ZS {
  cursor: pointer;
  color: var(--ZS);
}
.leftHeader {
  @include font_color(T1);
}
.rightHeader {
  @include font_color(T2);
}
.notice-empty {
  display: flex;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  height: 100%; /* 设定父容器的高度 */
}
::v-deep .el-empty__description p {
  @include font_color(T4);
}
</style>
