import util from "@/utils/util";

class Event {
  _events = [];
  emit(type, payload) {
    this._events.forEach(event => {
      if (type === event.type) {
        event.handler(payload);
      }
    });
  }
  on(type, handler) {
    this._events.push({
      type,
      handler
    });
  }
}

class HeartBeat {
  constructor({ interval = 4e4 } = {}) {
    this._task = null;
    this._interval = interval;
    this._cancelTimer = null;
  }

  init({ task }) {
    this._task = task;
  }

  _start() {
    this._cancelTimer = util.timer(this._task, this._heartInterval, {
      immediate: false
    });
  }

  restart() {
    this.stop();
    this._start();
  }

  stop() {
    if (this._cancelTimer) {
      this._cancelTimer();
      this._cancelTimer = null;
    }
  }
}

export class WebSocketConnectClient extends Event {
  constructor() {
    super();

    this._ws = null;
    this._manualClose = false;

    this._heartBeat = new HeartBeat();
  }

  async connect({ url }) {
    const ws = (this._ws = await this._create(url));

    this._heartBeat.init({
      task() {
        ws.send("");
      }
    });

    ws.onclose = event => {
      if (this._manualClose) {
        return;
      }
      this._manualClose = false;

      this._heartBeat.stop();

      this.connect({ url });
    };

    ws.onopen = event => {
      //心跳包发送开启
      this._heartBeat.restart();
      this.emit("open", event);
      // 接收到消息的回调方法
      ws.onmessage = msg => {
        this._heartBeat.restart();
        let data = null;
        try {
          data = msg.data ? JSON.parse(msg.data) : null;
        } catch (e) {
          util.warn(e);
        }

        this.emit("message", data);
      };
    };
  }

  _create(url) {
    return new Promise((resolve, reject) => {
      let ws = null;
      util.timer(cancel => {
        try {
          ws = new WebSocket(url);
          cancel();

          resolve(ws);
        } catch (e) {
          util.error(e);
        }
      }, 5e3);
    });
  }

  close() {
    this._manualClose = true;
    this._ws?.close();
    this._heartBeat.stop();
  }
}
