<template>
  <div class="text-center">
    <el-switch v-model="twinkle" v-bind="switchConfig" />
  </div>
</template>

<script>
export default {
  name: "AlarmTwinkleMode",
  components: {},
  computed: {
    twinkle: {
      get() {
        return this.$store.state.settings.twinkle;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "twinkle",
          value: val
        });
      }
    }
  },
  data() {
    return {
      switchConfig: {
        activeColor: "#13ce66",
        inactiveColor: "#ff4949"
      }
    };
  },
  watch: {},
  methods: {},
  mounted() {}
};
</script>

<style lang="scss" scoped></style>
