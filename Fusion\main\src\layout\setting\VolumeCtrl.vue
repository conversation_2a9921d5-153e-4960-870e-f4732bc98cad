<template>
  <div class="volume-ctrl">
    <OmegaIcon
      slot="reference"
      class="volume-ctrl-horn-icon"
      :symbolId="isMuted ? 'layout-volume-no-lin' : 'layout-volume-lin'"
      @click="evHornClick"
    />
    <div class="volume-ctrl-slider-container">
      <el-slider
        class="volume-ctrl-slider"
        :class="{
          ismuted: isMuted
        }"
        v-model="volume"
        :show-tooltip="false"
      ></el-slider>
    </div>
  </div>
</template>

<script>
import OmegaIcon from "@omega/icon";
export default {
  name: "VolumeCtrl",
  components: { OmegaIcon },
  data() {
    return {};
  },
  computed: {
    isMuted: {
      get() {
        return this.$store.state.settings.isMuted;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "isMuted",
          value: val
        });
      }
    },
    volume: {
      get() {
        return this.$store.state.settings.volume;
      },
      set(val) {
        this.$store.dispatch("settings/changeSetting", {
          key: "volume",
          value: val
        });
      }
    }
  },
  watch: {
    volume(val) {
      if (val === 0) {
        this.isMuted = true;
      } else {
        this.isMuted = false;
      }
    }
  },
  mounted() {},
  methods: {
    evHornClick() {
      this.isMuted = !this.isMuted;
    }
  }
};
</script>

<style lang="scss" scoped>
.volume-ctrl {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  // &:hover {
  //   .volume-ctrl-slider-container {
  //     z-index: 999;
  //     opacity: 1;
  //   }
  // }
  &-horn-icon {
    font-size: 24px;
    cursor: pointer;
    @include font_color("color-text-primary");
    &:hover {
      @include font_color("hover");
    }
  }
}
.volume-ctrl-slider-container {
  // position: absolute;
  // left: 32px;
  // top: 50%;
  // transform: translateY(-50%);
  // z-index: -999;
  // opacity: 0;
  @include margin_left(J2);
  transition-duration: 0.3s;
  transition-delay: 0.5s;
  // padding: 10px;
}
.volume-ctrl-slider {
  width: 100px;
  &.ismuted ::v-deep {
    opacity: 0.5;
    .el-slider__button {
      @include font_color("color-text-primary");
    }
    .el-slider__bar {
      @include background_color("color-primary");
    }
  }
}
</style>
