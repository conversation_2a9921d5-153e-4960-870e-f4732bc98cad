<template>
  <span
    class="to-blade"
    :title="$T('系统设置')"
    v-if="user.isRoot() && isFusionSystemConfig"
  >
    <omega-icon @click="goBlade" symbolId="workbench-sg" />
  </span>
  <!-- <el-button
    v-if="user.isRoot() && isFusionSystemConfig"
    @click="goBlade"
    size="mini"
    plain
    class="to-blade"
    icon="el-icon-receiving"
  >
    {{ $T("系统设置") }}
  </el-button> -->
  <!-- <div style="width: 100px; height: 100px"></div> -->
</template>

<script>
import { user } from "@omega/auth";
import { isFusionSystemConfig } from "@altair/lord";
export default {
  name: "ToBlade",
  data() {
    return {
      isFusionSystemConfig,
      user
    };
  },
  methods: {
    goBlade() {
      this.$router.push("/bladeView");
    }
  },
  computed: {}
};
</script>

<style lang="scss" scoped>
.to-blade {
  order: -1;
  cursor: pointer;
  display: inline-block;
  width: 32px;
  height: 32px;
  margin-right: var(--J1);
  border-radius: var(--Ra);
  &:hover {
    background-color: var(--BG2);
  }
  .omega-icon {
    width: 20px;
    height: 20px;
    margin-top: 5px;
    margin-left: 6px;
  }
  // color: var(--ZS);
}
</style>
