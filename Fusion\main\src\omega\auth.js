import omegaApp from "@omega/app";
import { OmegaAuthPlugin } from "@omega/auth";

omegaApp.plugin.register(OmegaAuthPlugin, {
  // defaultHomepage: "/homepage",
  // isTokenPersist: true,
  // enableReplayProtection: true,
  // openPermissionCheck: true,
  whiteRouteList: ["/bladeView"],
  whiteRouteExpression(path) {
    const pathCheck = path.includes("iframe-") && path.startsWith("/fusion/");
    const isInframe =
      self.frameElement && self.frameElement.tagName === "IFRAME";
    return pathCheck && isInframe;
  }
  // checkUserIsROOT() {
  //   return true;
  // }
  // openHomepage: true,
  // openPagePermission: true,
  // isUseSuperAdminRole: false,
  // 自定义权限检查
  // checkPermission: function (rule) {
  //   if (rule === 'p.usercenter') {
  //     return true;
  //   }
  // }
  // apiProxy: {}
  // apiPrefix: {
  //   "auth-service": "/auth"
  // },
  // apiRequestInterceptor: function(config) {return config}
});
