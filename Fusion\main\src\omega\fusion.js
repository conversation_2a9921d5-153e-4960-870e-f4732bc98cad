import omegaApp from "@omega/app";
import { FusionManager } from "@altair/lord";
//开发环境的配置
const develop = {
  appList: [
    {
      name: "cloud-auth"
      // url: "//localhost:9529"
      // alive: false
      // preload: true,
      // exec: true
    }
    // {
    //   name: "baseconfig"
    //   // url: "/fusion/fusion-list/dist/",
    //   // url: "//localhost:9540"
    //   // alive: false
    //   // preload: true,
    //   // exec: true
    // }
  ],
  options: {
    // systemConfig: "bff-service",
    isMultiProject: true
  }
};
//部署环境的配置
const config = {
  appList: [],
  options: {
    isMultiProject: true
  }
};

omegaApp.plugin.register(
  FusionManager,
  process.env.NODE_ENV === "development" ? develop : config
);
