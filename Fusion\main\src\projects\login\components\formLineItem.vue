<template>
  <div class="form-line-item">
    <div class="form-line-item-label">
      <omega-icon
        class="form-line-item-label-icon icon-size-I1 icon-p3"
        :symbolId="symbolId"
      ></omega-icon>
      <span class="form-line-item-label-text">{{ label }}</span>
    </div>
    <div class="form-line-item-content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "FormLineItem",
  props: {
    label: {
      type: String
    },
    symbolId: {
      type: String
    }
  }
};
</script>

<style lang="scss" scoped>
.form-line-item {
  display: flex;
  @include margin_top(J3);
}
.form-line-item-label {
  width: 33%;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid;
  border-right: none;
  height: 32px;
  border-radius: 4px 0 0 4px;
  box-sizing: border-box;
  @include font_color(T3);
  @include background_color(BG1);
  @include font_size(Aa);
  @include border_color(B1);
}

.form-line-item-content {
  width: 66%;
}

.form-line-item-label-icon {
  @include margin_right(J1);
  height: 32px;
}

.form-line-item-content ::v-deep {
  .el-input {
    .el-input__inner {
      border-radius: 0 4px 4px 0;
      @include background_color(BG);
    }
  }
  .el-form-item {
    .el-form-item__content {
      display: flex;
      line-height: unset;
    }
  }
}
</style>
