<template>
  <div class="login-mobile">
    <el-form :model="login" :rules="rules" ref="form">
      <FormLineItem icon-class="proejct_login_phone" :label="$T('手机号')">
        <el-form-item prop="phoneNumber">
          <el-input
            :placeholder="$T('请输入手机号')"
            v-model="login.phoneNumber"
          />
        </el-form-item>
      </FormLineItem>
      <FormLineItem icon-class="project_login_password" :label="$T('验证码')">
        <el-form-item class="login-mobile-verification" prop="verificationCode">
          <el-input
            :placeholder="$T('请输入验证码')"
            v-model="login.verificationCode"
          />
          <el-link
            v-if="!timing"
            class="login-mobile-verification-action"
            type="primary"
            @click="evVerificationClick"
          >
            {{ $T("获取验证码") }}
          </el-link>
          <el-link
            class="login-mobile-verification-action"
            v-if="!!timing"
            disabled
            :underline="false"
          >
            {{ $T("重发验证码") }}
            <span>({{ timing }}s)</span>
          </el-link>
        </el-form-item>
      </FormLineItem>
      <el-button
        class="login-btn"
        type="primary"
        size="medium"
        @click="evLoginBtnClick"
      >
        {{ $T("登录") }}
      </el-button>
    </el-form>
  </div>
</template>

<script>
// import { mhEvent } from "@/base/modules/event";
import FormLineItem from "./formLineItem.vue";
import common from "@/utils/common";
export default {
  name: "LoginMobile",
  components: { FormLineItem },
  data() {
    return {
      login: {
        phoneNumber: "",
        verificationCode: ""
      },
      rules: {
        phoneNumber: [
          {
            required: true,
            message: $T("手机号不能为空"),
            trigger: "change"
          },
          common.check_phone
        ],
        verificationCode: [
          {
            required: true,
            message: $T("验证码不能为空"),
            trigger: "change"
          }
        ]
      },
      timing: 0
    };
  },
  methods: {
    async evLoginBtnClick() {
      await this.$refs.form.validate();
      // TODO
      // const param = {
      //   userName: this.login.phoneNumber,
      //   password: this.login.verificationCode
      // };
      // mhEvent.send("login.service.login", param);
      // .catch(err => {
      // if (err.flag && err.data) {
      // msg.error(err.data.msg);
      // }
      // });
    },
    evVerificationClick() {
      this.handlerVerificationCodeTiming();
      // TODO
    },
    handlerVerificationCodeTiming() {
      this.timing = 59;
      // fix bug: 不知名原因箭头函数无效
      const _this = this;
      const cancel = mhjs.util.timer(function () {
        --_this.timing;

        if (_this.timing === 0) {
          cancel();
        }
      }, 1e3);
      this.$on("hook:beforeDestroy", () => {
        cancel();
        this.timing = 0;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.login-btn {
  @include margin_top(J4);
  @include font_color(T5);
  @include background(LOGIN_BTN);
  border: none;
  width: 100%;
}
.login-mobile-verification {
  position: relative;
  &-action {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 8px;
  }
}
</style>
