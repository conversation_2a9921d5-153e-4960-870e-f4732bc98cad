import _ from "lodash";

const collecter = {
  _items: [],
  async timer(fn) {
    if (this._start) {
      return;
    }
    this._start = true;

    while (true && this._start) {
      await new Promise(reslove => {
        setTimeout(() => {
          reslove();
        }, 1e3);
      });

      fn(this._items);
      this._items.length = 0;
      this._start = false;
    }
  },
  add(item, fn) {
    this._items.push(item);

    this.timer(fn);
  }
};

export default {
  namespaced: true,
  state: {
    //消息通知列表数据
    items: []
  },
  mutations: {
    SET_ITEMS(state, data) {
      state.items = data;
    },
    ADD_ITEM(state, item) {
      collecter.add(item, items => {
        items.forEach(item => {
          state.items.unshift({
            ...item
          });
          // 缓存数量达到100 后，去除最早的消息
          if (state.items.length > 100) {
            state.items.pop();
          }
        });
      });
    },
    REMOVE_ITEM(state, item) {
      state.items.forEach((v, i) => {
        if (_.isEqual(v, item)) {
          state.items.splice(i, 1);
        }
      });
    }
  }
};
