import _ from "lodash";
import moment from "moment";
//自定义格式化表格列数据的函数, 函数名不能重名, 配置column的formatter为函数名即可

function toFixed2(value, precision) {
  precision = precision || 0;
  var pow = Math.pow(10, precision);
  return (Math.round(value * pow) / pow).toFixed(precision);
}

function formatterDate(formatStr = "YYYY-MM-DD HH:mm:ss") {
  return function (row, column, cellValue, index) {
    //设置时间格式化字符串，如果配置有效则采用配置字符串，无效则采用默认格式化字符串
    if (cellValue) {
      return moment(cellValue).format(formatStr);
    } else if (cellValue === 0 || cellValue === "") {
      return cellValue;
    } else {
      return "--";
    }
  };
}

// 保留的小数点个数
function formatNumber(precision = 2) {
  return function (row, column, cellValue, index) {
    if (cellValue) {
      if (!_.isNumber(cellValue)) {
        //先转换成数字
        cellValue = parseFloat(cellValue);
      }

      return toFixed2(cellValue, precision); //保留两位小数
    } else if (cellValue === 0) {
      return cellValue;
    } else {
      return "--";
    }
  };
}

/***表格列自定义formatter函数 ***/
export const tableColumsFormatter = {
  formatDateColumn: formatterDate("YYYY-MM-DD HH:mm:ss"), // 格式化日期格式数据
  formatDateCol: formatterDate, //柯里化格式化表格时间列, 可以传入自定义时间格式字符串
  formatNumberColumn: formatNumber(), //
  formatNumberCol: formatNumber, //柯里化格式化表格数值列, 可以传入保留的小数位
};

export const formRules = {
  check_stringLessThan140: {
    min: 1,
    max: 140,
    message: "长度在 1 到 140 个字符",
    trigger: ["blur", "change"],
  },
  check_name: {
    min: 1,
    max: 20,
    message: "长度在 1 到 20 个字符",
    trigger: ["blur", "change"],
  },
  check_strongPassword: {
    pattern:
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@$%^&*()+=[\]{}\\|;:'"<,>.?/]).{8,18}$/,
    trigger: ["blur", "change"],
    message: "密码需含有大写、小写字母、数字和特殊字符，且长度为8~18位",
  },
  check_pattern_name: {
    // pattern: /^[a-z]+$/,
    // pattern: /((?=[\x21-\x7e]+)[^A-Za-z0-9])/,
    // pattern: /^((?!`~!@#$%^&*()_+-=[]{}\|;:\'"<,>.?\/).)*$/,
    pattern: /^((?![`~!@$%^&*()+=[\]{}\\|;:'"<,>.?/]).)*$/, //如果把-加进去就表示数字也算特殊字符了，所以-不能加进去
    message: "请不要输入特殊字符",
    trigger: ["blur", "change"],
  },
  check_phone: {
    pattern: /^1[3|4|5|7|8][0-9]\d{8}$/,
    trigger: ["blur", "change"],
    message: "请输入正确的手机号",
  },

  check_telephone: {
    pattern: /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/,
    trigger: ["blur", "change"],
    message: "请输入正确的固定电话 例：010-88888888-123",
  },
  check_phone_or_telephone: {
    pattern:
      /(^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$)|(^1[3|4|5|7|8][0-9]\d{8}$)/,
    trigger: ["blur", "change"],
    message: "请输入正确的手机号或固定电话",
  },
};

export default {
  ...tableColumsFormatter,
  ...formRules,
};

export const util = {
  formatNumber: function (config, value) {
    if (_.has(config, "precision") && _.isNumber(config.precision)) {
      value = tableColumsFormatter.formatNumberWithPrecision(
        value,
        config.precision
      );
    }

    return value;
  },
  formatNumberWithPrecision: function (value, precision) {
    if (_.isNumber(precision)) {
      if (!_.isNumber(value)) {
        //先转换成数字
        value = parseFloat(value);
      }
      if (isNaN(value)) {
        //如果为空直接返回空
        return null;
      }
      value = toFixed2(value, precision); //不为空的话就保留小数位
    }

    return value;
  },
  formatBoolean: function (config, value, vm) {
    if (_.has(config, "trueText") && _.has(config, "falseText")) {
      value = value ? config.trueText : config.falseText;
    }
    return value;
  },
};
