const CompressionWebpackPlugin = require("compression-webpack-plugin");
const omegaCliDevserverHandler = require("@omega/cli-devserver");
const package = require("./package.json");
module.exports = omegaCliDevserverHandler({
  productionSourceMap: false,
  transpileDependencies: ["@omega"],
  outputDir: package.name + "-" + package.version,
  publicPath: "./",
  css: {
    loaderOptions: {
      scss: {
        additionalData: `@import "@/resources/var.scss";`
      },
      css: {
        url: {
          filter(url, resourcePath) {
            // 根路径静态文件特殊处理
            if (url.startsWith("/")) {
              return false;
            }
            return true;
          }
        }
      },
      postcss: {
        postcssOptions: {
          plugins: [
            require("postcss-import"),
            require("tailwindcss/nesting"),
            require("tailwindcss")
          ]
        }
      }
    }
  },
  configureWebpack: {
    plugins: [
      // 开发环境下开启缓存
      ...(process.env.NODE_ENV === "development"
        ? // ? [new HardSourceWebpackPlugin()]
          []
        : [new CompressionWebpackPlugin()])
    ]
  },
  devServer: {
    port: 9528,
    open: false,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Headers": "*",
      "Access-Control-Allow-Methods": "*"
    },
    // writeToDisk: true,
    proxy: {
      "^/auth": {
        target: `http://************:2014`,
        changeOrigin: true,
        pathRewrite: {
          "^/auth": "/cloud/api/auth"
        }
      },
      "^/messageServer": {
        target: `http://************:5070`,
        changeOrigin: true,
        pathRewrite: {
          "^/messageServer": "/"
        }
      },
      // 台账等功能模型服务
      "^/model": {
        target: "http://************:8085",
        changeOrigin: true,
        pathRewrite: {
          "^/model": "/model"
        }
      },
      "^/devicedata|device-data": {
        target: "http://************:5050",
        changeOrigin: true,
        pathRewrite: {
          "^/devicedata|device-data": "/"
        }
      },
      "^/filemanager": {
        target: `http://************:8070`,
        changeOrigin: true,
        pathRewrite: {
          "^/filemanager": "/filemanager"
        }
      },
      "^/bff": {
        target: `http://************:3005`,
        // target: `http://localhost:3005`,
        changeOrigin: true,
        pathRewrite: {
          // "^/workorder": "/workorder"
        }
      },
      "^/workorder": {
        target: `http://************:8020`,
        changeOrigin: true,
        pathRewrite: {
          "^/workorder": "/workorder"
        }
      },
      "^/platform": {
        target: `http://***********:9998`,
        changeOrigin: true,
        pathRewrite: {}
      }
    }
  },
  chainWebpack(config) {
    const path = require("path");

    function resolve(dir) {
      return path.join(__dirname, dir);
    }
    /* svgicon支持 */
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "[name]"
      })
      .end();
  }
});
