{"files.autoSave": "after<PERSON>elay", "editor.formatOnSave": true, "vetur.validation.template": false, "vetur.format.enable": false, "editor.defaultFormatter": "esbenp.prettier-vscode", "javascript.preferences.importModuleSpecifierEnding": "js", "conventionalCommits.scopes": ["config", "cet-common"], "eslint.workingDirectories": ["./packages/cet-common", "./template", "./components/omega-weather"], "files.eol": "\n", "[vue]": {"editor.defaultFormatter": "Vue.volar"}}