[![生成状态](https://cetsoft-svr1/Platforms/PLT-Omega/_apis/build/status/PLT-Omega/post-merge)](https://cetsoft-svr1/Platforms/PLT-Omega/_build/latest?definitionId=161)

# 贡献者指南

1. 在项目根目录执行 `npm install`

2. 运行模板 `npm run dev:tpl` 跑起来看 template 文件夹 demo 效果

## 组件开发规范和指南

详见文档 docs\developer\组件开发指南.md

**组件开发人员**

详见文档

http://10.12.135.149:8080/developer/%E7%BB%84%E4%BB%B6%E5%BC%80%E5%8F%91%E6%8C%87%E5%8D%97.html#%E7%BB%84%E4%BB%B6%E5%8F%91%E5%B8%83
