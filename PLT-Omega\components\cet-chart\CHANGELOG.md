# cet-chart

## 1.6.1

- fix: echarts自适应大小的逻辑增加防抖;

## 1.6.0

- feat: 导出支持导出 echarts e.g. import echarts from "cet-chart/echarts";

## 1.5.1

- fix: 完善依赖关系

## 1.5.0

- feat: 【重要更新】按需整体加载 cet-charts 相关代码模块，减小首屏文件加载体积，提高首屏速度

## 1.4.1

- fix: 适配 echart-gl

## 1.4.0

- feat: 新增深蓝色皮肤
- fix: 取消默认皮肤以和 theme 包的默认皮肤一致

## 1.3.1

- fix: 修复在清空浏览器记录时初始化皮肤不匹配问题

## 1.3.0

- feat: 提供外部注册皮肤接口 registerTheme 方法

## 1.2.6

- fix: 修复皮肤设置无效

## 1.2.5

- feat: 暴露皮肤`themeMap`Map 类型对象，方便外部进行 chart 皮肤自定义@程开

## 1.2.4

- fix: 暴露 registerMap 方法@张翔

## 1.2.3

- feat: esmodule 入口添加
- Updated dependencies
  - @omega/i18n@1.1.5

## 1.2.2

- fix: 处理 light 风格下，桑基图 label 字体有白色阴影问题@周盛志

## 1.2.1

- fix: 调整依赖版本为外部任意版本

## 1.2.0

- feat: echarts 国际化功能适配

## 1.1.1

- fix: cet-chart 提示未注册问题修复

## 1.1.0

- feat: 新增皮肤自定义配置项

  feat: vue 插件形式引入方式添加

  ```js
  import CetChart from "cet-chart";
  Vue.use(CetChart, { themeConf: {} });
  ```

## 1.0.3

- fix: light 模式下 echarts 颜色取值顺序

## 1.0.2

- [2022-04-28] fix: 亮色皮肤坐标轴文字白色导致的文字不显示

## 1.0.1

- [2022-04-20] feat: 换肤方案适配
