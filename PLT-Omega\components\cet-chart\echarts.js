import { currentTheme, getActiveThemeConfig, getRegisterEchartsMaps } from "./util";

import * as echarts from "echarts";
import "echarts/dist/extension/bmap";
import "echarts/dist/extension/dataTool";
import localeZh from "echarts/i18n/langZH-obj.js";
import localeEn from "echarts/i18n/langEN-obj.js";

echarts.registerLocale("zh_cn", localeZh);
echarts.registerLocale("en", localeEn);
echarts.registerTheme(currentTheme(), getActiveThemeConfig());
getRegisterEchartsMaps().forEach(({ mapName, geoJSON, specialAreas }) => {
  echarts.registerMap(mapName, geoJSON, specialAreas);
});

export default echarts;