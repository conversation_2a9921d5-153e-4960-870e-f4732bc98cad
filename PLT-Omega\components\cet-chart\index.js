import { getActiveThemeConfig } from "./util.js";
import _ from "lodash";
function CetChartFactory() {
  return import("./chart.vue");
}

CetChartFactory.install = function (Vue, { themeConf } = {}) {
  Vue.component("CetChart", CetChartFactory);

  const activeThemeConf = getActiveThemeConfig();
  _.merge(activeThemeConf, themeConf)
}

export default CetChartFactory;
export { themeMap } from "./theme/index.js";
export {
  currentTheme,
  currentLang,
  registerTheme,
  registerEchartsMap as registerMap
} from "./util.js";
