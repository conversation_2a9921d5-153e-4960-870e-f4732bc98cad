export default {
  "color": [
    "#70d3ff",
    "#007bff",
    "#fac858",
    "#ee6666",
    "#19d4ae",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc"
  ],
  "backgroundColor": "rgba(16,77,162,1)",
  "textStyle": {},
  "title": {
    "textStyle": {
      "color": "#ddeeff"
    },
    "subtextStyle": {
      "color": "#ddeeff"
    }
  },
  "line": {
    "itemStyle": {
      "borderWidth": 1
    },
    "lineStyle": {
      "width": 2
    },
    "symbolSize": 4,
    "symbol": "emptyCircle",
    "smooth": false
  },
  "radar": {
    "itemStyle": {
      "borderWidth": 1
    },
    "lineStyle": {
      "width": 2
    },
    "symbolSize": 4,
    "symbol": "emptyCircle",
    "smooth": false
  },
  "bar": {
    "itemStyle": {
      "barBorderWidth": 0,
      "barBorderColor": "#ddeeff"
    }
  },
  "pie": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "scatter": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "boxplot": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "parallel": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "sankey": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "funnel": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "gauge": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    }
  },
  "candlestick": {
    "itemStyle": {
      "color": "#eb5454",
      "color0": "#47b262",
      "borderColor": "#eb5454",
      "borderColor0": "#47b262",
      "borderWidth": 1
    }
  },
  "graph": {
    "itemStyle": {
      "borderWidth": 0,
      "borderColor": "#ddeeff"
    },
    "lineStyle": {
      "width": 1,
      "color": "#3072d5"
    },
    "symbolSize": 4,
    "symbol": "emptyCircle",
    "smooth": false,
    "color": [
      "#70d3ff",
      "#007bff",
      "#fac858",
      "#ee6666",
      "#19d4ae",
      "#3ba272",
      "#fc8452",
      "#9a60b4",
      "#ea7ccc"
    ],
    "label": {
      "color": "#ffffff"
    }
  },
  "map": {
    "itemStyle": {
      "areaColor": "#eee",
      "borderColor": "#444",
      "borderWidth": 0.5
    },
    "label": {
      "color": "#000"
    },
    "emphasis": {
      "itemStyle": {
        "areaColor": "rgba(255,215,0,0.8)",
        "borderColor": "#444",
        "borderWidth": 1
      },
      "label": {
        "color": "rgb(100,0,0)"
      }
    }
  },
  "geo": {
    "itemStyle": {
      "areaColor": "#eee",
      "borderColor": "#444",
      "borderWidth": 0.5
    },
    "label": {
      "color": "#000"
    },
    "emphasis": {
      "itemStyle": {
        "areaColor": "rgba(255,215,0,0.8)",
        "borderColor": "#444",
        "borderWidth": 1
      },
      "label": {
        "color": "rgb(100,0,0)"
      }
    }
  },
  "categoryAxis": {
    "axisLine": {
      "show": true,
      "lineStyle": {
        "color": "#3072d5"
      }
    },
    "axisTick": {
      "show": true,
      "lineStyle": {
        "color": "#3072d5"
      }
    },
    "axisLabel": {
      "show": true,
      "color": "#ddeeff"
    },
    "splitLine": {
      "show": false,
      "lineStyle": {
        "color": [
          "#E0E6F1"
        ]
      }
    },
    "splitArea": {
      "show": false,
      "areaStyle": {
        "color": [
          "rgba(250,250,250,0.2)",
          "rgba(210,219,238,0.2)"
        ]
      }
    }
  },
  "valueAxis": {
    "axisLine": {
      "show": false,
      "lineStyle": {
        "color": "#6E7079"
      }
    },
    "axisTick": {
      "show": false,
      "lineStyle": {
        "color": "#6E7079"
      }
    },
    "axisLabel": {
      "show": true,
      "color": "#ffffff"
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": [
          "#3072d5"
        ]
      }
    },
    "splitArea": {
      "show": false,
      "areaStyle": {
        "color": [
          "rgba(250,250,250,0.2)",
          "rgba(210,219,238,0.2)"
        ]
      }
    }
  },
  "logAxis": {
    "axisLine": {
      "show": false,
      "lineStyle": {
        "color": "#6E7079"
      }
    },
    "axisTick": {
      "show": false,
      "lineStyle": {
        "color": "#6E7079"
      }
    },
    "axisLabel": {
      "show": true,
      "color": "#ddeeff"
    },
    "splitLine": {
      "show": true,
      "lineStyle": {
        "color": [
          "#3072d5"
        ]
      }
    },
    "splitArea": {
      "show": false,
      "areaStyle": {
        "color": [
          "rgba(250,250,250,0.2)",
          "rgba(210,219,238,0.2)"
        ]
      }
    }
  },
  "timeAxis": {
    "axisLine": {
      "show": true,
      "lineStyle": {
        "color": "#3072d5"
      }
    },
    "axisTick": {
      "show": true,
      "lineStyle": {
        "color": "#3072d5"
      }
    },
    "axisLabel": {
      "show": true,
      "color": "#ddeeff"
    },
    "splitLine": {
      "show": false,
      "lineStyle": {
        "color": [
          "#E0E6F1"
        ]
      }
    },
    "splitArea": {
      "show": false,
      "areaStyle": {
        "color": [
          "rgba(250,250,250,0.2)",
          "rgba(210,219,238,0.2)"
        ]
      }
    }
  },
  "toolbox": {
    "iconStyle": {
      "borderColor": "#7eb2ee"
    },
    "emphasis": {
      "iconStyle": {
        "borderColor": "#ddeeff"
      }
    }
  },
  "legend": {
    "textStyle": {
      "color": "#ddeeff"
    }
  },
  "tooltip": {
    "axisPointer": {
      "lineStyle": {
        "color": "#ddeeff",
        "width": "1"
      },
      "crossStyle": {
        "color": "#ddeeff",
        "width": "1"
      }
    }
  },
  "timeline": {
    "lineStyle": {
      "color": "#ddeeff",
      "width": "2"
    },
    "itemStyle": {
      "color": "#ddeeff",
      "borderWidth": 1
    },
    "controlStyle": {
      "color": "#ddeeff",
      "borderColor": "#ddeeff",
      "borderWidth": 1
    },
    "checkpointStyle": {
      "color": "#3072d5",
      "borderColor": "#ddeeff"
    },
    "label": {
      "color": "#ddeeff"
    },
    "emphasis": {
      "itemStyle": {
        "color": "#ffffff"
      },
      "controlStyle": {
        "color": "#ddeeff",
        "borderColor": "#ddeeff",
        "borderWidth": 1
      },
      "label": {
        "color": "#ddeeff"
      }
    }
  },
  "visualMap": {
    "color": [
      "#bf444c",
      "#d88273",
      "#f6efa6"
    ]
  },
  "dataZoom": {
    "handleSize": "undefined%",
    "textStyle": {}
  },
  "markPoint": {
    "label": {
      "color": "#ffffff"
    },
    "emphasis": {
      "label": {
        "color": "#ffffff"
      }
    }
  }
}
