export default {
  color: [
    "#29b061",
    "#ffc24c",
    "#7eb2ee",
    "#2b71c3",
    "#7fd0a0",
    "#ffe7b7",
    "#b38eca",
    "#d8c2e5",
    "#6bc1c3",
    "#a6e3e6",
    "#ffad81",
    "#f95e5a",
    "#9bc458",
    "#7a5193",
    "#249da4",
    "#9b2222",
    "#9d6630",
    "#2b551d",
    "#aaaaaa",
    "#cccccc"
  ],
  backgroundColor: "rgba(0,0,0,0)",
  textStyle: {},
  title: {
    textStyle: {
      color: "#333333"
    },
    subtextStyle: {
      color: "#989898"
    }
  },
  line: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false
  },
  radar: {
    itemStyle: {
      borderWidth: 1
    },
    lineStyle: {
      width: 2
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false
  },
  bar: {
    itemStyle: {
      barBorderWidth: 0,
      barBorderColor: "#ccc"
    }
  },
  pie: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  scatter: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  boxplot: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  parallel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  sankey: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    },
    label: {
      color: "#333"
    }
  },
  funnel: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  gauge: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    }
  },
  candlestick: {
    itemStyle: {
      color: "#eb5454",
      color0: "#47b262",
      borderColor: "#eb5454",
      borderColor0: "#47b262",
      borderWidth: 1
    }
  },
  graph: {
    itemStyle: {
      borderWidth: 0,
      borderColor: "#ccc"
    },
    lineStyle: {
      width: 1,
      color: "#aaaaaa"
    },
    symbolSize: 4,
    symbol: "emptyCircle",
    smooth: false,
    color: [
      "#29b061",
      "#7fd0a0",
      "#fac858",
      "#ee6666",
      "#73c0de",
      "#3ba272",
      "#fc8452",
      "#9a60b4",
      "#ea7ccc",
      "#333333",
      "#333333",
      "#333333",
      "#333333",
      "#d8c2e5",
      "#333333",
      "#333333",
      "#333333",
      "#333333",
      "#a6e3e6",
      "#333333",
      "#cccccc"
    ],
    label: {
      color: "#ffffff"
    }
  },
  map: {
    itemStyle: {
      areaColor: "#eee",
      borderColor: "#444",
      borderWidth: 0.5
    },
    label: {
      color: "#000"
    },
    emphasis: {
      itemStyle: {
        areaColor: "rgba(255,215,0,0.8)",
        borderColor: "#444",
        borderWidth: 1
      },
      label: {
        color: "rgb(100,0,0)"
      }
    }
  },
  geo: {
    itemStyle: {
      areaColor: "#eee",
      borderColor: "#444",
      borderWidth: 0.5
    },
    label: {
      color: "#000"
    },
    emphasis: {
      itemStyle: {
        areaColor: "rgba(255,215,0,0.8)",
        borderColor: "#444",
        borderWidth: 1
      },
      label: {
        color: "rgb(100,0,0)"
      }
    }
  },
  categoryAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#cccccc"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#cccccc"
      }
    },
    axisLabel: {
      show: true,
      color: "#666666"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  valueAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#6E7079"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#6E7079"
      }
    },
    axisLabel: {
      show: true,
      color: "#666666"
    },
    splitLine: {
      show: true,
      lineStyle: {
        color: ["#989898"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  logAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#d7d7d7"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#6E7079"
      }
    },
    axisLabel: {
      show: true,
      color: "#666666"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#989898"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  timeAxis: {
    axisLine: {
      show: true,
      lineStyle: {
        color: "#989898"
      }
    },
    axisTick: {
      show: true,
      lineStyle: {
        color: "#989898"
      }
    },
    axisLabel: {
      show: true,
      color: "#666666"
    },
    splitLine: {
      show: false,
      lineStyle: {
        color: ["#E0E6F1"]
      }
    },
    splitArea: {
      show: false,
      areaStyle: {
        color: ["rgba(250,250,250,0.2)", "rgba(210,219,238,0.2)"]
      }
    }
  },
  toolbox: {
    iconStyle: {
      borderColor: "#998899"
    },
    emphasis: {
      iconStyle: {
        borderColor: "#666666"
      }
    }
  },
  legend: {
    textStyle: {
      color: "#666666"
    }
  },
  tooltip: {
    axisPointer: {
      lineStyle: {
        color: "#cccccc",
        width: 1
      },
      crossStyle: {
        color: "#cccccc",
        width: 1
      }
    }
  },
  timeline: {
    lineStyle: {
      color: "#29b061",
      width: 2
    },
    itemStyle: {
      color: "#7fd0a0",
      borderWidth: 1
    },
    controlStyle: {
      color: "#7fd0a0",
      borderColor: "#7fd0a0",
      borderWidth: 1
    },
    checkpointStyle: {
      color: "#29b061",
      borderColor: "#ffffff"
    },
    label: {
      color: "#666666"
    },
    emphasis: {
      itemStyle: {
        color: "#ffffff"
      },
      controlStyle: {
        color: "#7fd0a0",
        borderColor: "#7fd0a0",
        borderWidth: 1
      },
      label: {
        color: "#666666"
      }
    }
  },
  visualMap: {
    color: ["#bf444c", "#d88273", "#f6efa6"]
  },
  dataZoom: {
    handleSize: "undefined%",
    textStyle: {}
  },
  markPoint: {
    label: {
      color: "#16191a"
    },
    emphasis: {
      label: {
        color: "#16191a"
      }
    }
  }
};
