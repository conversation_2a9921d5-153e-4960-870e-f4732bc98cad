import { themeMap } from "./theme/index.js";
export function currentTheme() {
  return window.localStorage.getItem("omega_theme");
}

export function currentLang() {
  return window.localStorage.getItem("omega_language");
}

export function getActiveThemeConfig() {
  return themeMap.get(currentTheme());
}

export function registerTheme(name, theme) {
  themeMap.set(name, theme);
}

const registerMaps = [];
export function registerEchartsMap(mapName, geoJSON, specialAreas) {
  registerMaps.push({
    mapName,
    geoJSON,
    specialAreas
  });
}

export function getRegisterEchartsMaps() {
  return registerMaps;
}