# @omega/dashboard

## 1.2.11

- fix: 删除图表改为串行调用，避免模型服务偶发死锁问题

## 1.2.10

- chore: 更新 corejs 版本至 3.36.0

## 1.2.9

- fix: 修复 warning 提示问题

## 1.2.8

- fix: 隐藏组件背景的配置项增加隐藏卡片 box-shadow.

## 1.2.7

- feat: 增加隐藏组件背景的配置项, 通过路由上的 transparentCompBg 来控制.

## 1.2.6

- fix: 修复自适应高度不随浏览器窗口尺寸变化的问题.

## 1.2.5

- 1146ded: 2023-7-21

  1. omega-dashboard 组件图表编辑界面去掉删除按钮;
  2. 所有的界面上的"dashboard"文本改为"看板";
  3. 修复新建预定义图表时直接保存报错的问题.

## 1.2.4

- feta: 调整 dashboard 高度自适应逻辑

## 1.2.3

- feat 增加 dashboard 自适应高度的功能，需要配置 autoHeight 与 containerHeight 两个参数

## 1.2.2

- [object Promise]: 同步了之前 3.x 版本更新问题后的代码.
- [object Promise]: 2023-6-7 dashboard 支持预定义组件分组展示
- Updated dependencies [94de520]
  - cet-common@1.3.7

## 1.2.1

- fix: 同步了之前 3.x 版本更新问题后的代码.

## 1.2.0

- fix:dashboradRender 组件重构, 满足自定义页面需要

## 1.1.4

- fix: 修复 dashboradRender 初始化配置报错问题

## 1.1.3

- fix: 解决 dashboard 的布局问题, 关联到看板报错问题.

## 1.1.2

- feat: 调整 dashborad 栅格由 24 调整为 60

## 1.1.1

- fix: dashboradRender 组件报错修复

## 1.1.0

- feat: 新增通过 ID 设置 Dashborad

## 1.0.3

- feat: esmodule 入口添加
- Updated dependencies
  - cet-chart@1.2.3
  - cet-common@1.3.4
  - @omega/auth@1.7.10
  - @omega/http@1.2.3
  - @omega/icon@1.3.6

## 1.0.2

- d04d0ca: [2022-4-14]refactor: 重构 dashboard 代码, 删除不必要的调试代码

## 1.0.1

- 383cced: [2022-04-13]
  添加 dashboard 缺失的依赖项, 避免隐式依赖
