(function(e,t){"object"===typeof exports&&"object"===typeof module?module.exports=t():"function"===typeof define&&define.amd?define([],t):"object"===typeof exports?exports["omega-dashboard"]=t():e["omega-dashboard"]=t()})("undefined"!==typeof self?self:this,(function(){return function(){var e={7543:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".panel[data-v-0dedb01c] .el-form-item__label{line-height:normal}.panel[data-v-0dedb01c] .el-form-item__content{line-height:normal}.drag-list-item[data-v-0dedb01c]{line-height:1.5;font-size:14px;cursor:-webkit-grab}.drag-list-item .item-i[data-v-0dedb01c]{font-size:12px}.property-list[data-v-0dedb01c]{overflow-y:auto;max-height:500px}",""]),t["default"]=o},7369:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".el-form-item[data-v-5ccc6782]{margin-bottom:20px}",""]),t["default"]=o},5663:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".el-form-item[data-v-34eb7e6b]{margin-bottom:20px}",""]),t["default"]=o},4006:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".el-form-item[data-v-10c94e3e]{margin-bottom:20px}",""]),t["default"]=o},5420:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,'.pane-container[data-v-18370355]{display:flex;height:100%}.pane-container .visualize-window[data-v-18370355]{width:100%}.pane-container .chart-style-panel[data-v-18370355]{width:250px;padding:10px}.pane-container .chart-style-panel .chart-type-list[data-v-18370355]{width:100%;display:grid;justify-items:center;grid-template-columns:repeat(5,1fr);grid-auto-rows:1fr;grid-gap:10px}.pane-container .chart-style-panel .chart-type-list span[data-v-18370355]{line-height:normal;height:100%;font-size:22px;cursor:pointer;text-align:center;width:100%;position:relative}.pane-container .chart-style-panel .chart-type-list span .icon[data-v-18370355]{position:absolute;top:0;left:0;right:0;bottom:0;margin:auto}.pane-container .chart-style-panel .chart-type-list span[data-v-18370355]:before{content:"";width:100%;display:block}.pane-container .chart-style-panel .chart-type-list .disabledIcon[data-v-18370355]{cursor:not-allowed}',""]),t["default"]=o},698:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".back-button[data-v-58ff3c02]{display:inline-block;padding-right:10px;margin-right:10px;border-right:1px solid;cursor:pointer}.back-button span[data-v-58ff3c02]{padding:5px;font-size:14px}.analysis-form[data-v-58ff3c02]{width:100%;padding-right:20px}.analysis-form[data-v-58ff3c02] .el-form-item--mini.el-form-item{margin-bottom:10px}.analysis-form .limit-input[data-v-58ff3c02],.analysis-form[data-v-58ff3c02] .el-form-item--mini .el-form-item__label{font-size:14px}.form-wrapper[data-v-58ff3c02]{display:flex}.chart-form[data-v-58ff3c02]{width:250px}.chart-form[data-v-58ff3c02] .el-form-item--mini.el-form-item{margin-bottom:10px}.draggable-wrapper[data-v-58ff3c02]{font-size:14px;min-height:30px;border-bottom:1px solid}.draggable-wrapper .draggable-item[data-v-58ff3c02]{margin-right:10px}.draggable-wrapper[data-v-58ff3c02] .el-select--mini{margin:0}.help-center-wrapper[data-v-58ff3c02]{cursor:pointer;position:fixed;bottom:25px;right:25px}.help-center-wrapper .help-center[data-v-58ff3c02]{width:45px;height:45px;background:#fff;border-radius:50%;box-shadow:0 2px 12px 0 rgba(0,0,0,.1);line-height:45px;font-size:20px;color:#205cd8;text-align:center}.help-center-wrapper .help-center[data-v-58ff3c02] .el-dropdown{font-size:20px;color:#205cd8}",""]),t["default"]=o},5204:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".pre_header[data-v-664687e6]{margin-bottom:10px}",""]),t["default"]=o},7659:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".pane-container[data-v-1d72f018]{display:flex;height:100%}.pane-container .visualize-window[data-v-1d72f018]{width:100%}",""]),t["default"]=o},7485:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".tool-bar[data-v-07ac80f8]{display:flex;justify-content:space-between;border-top:none;height:45px;line-height:45px;padding:0 10px;position:relative}.tool-bar .db-name[data-v-07ac80f8]{font-size:1.2em;font-weight:600;margin-left:0}.tool-bar span[data-v-07ac80f8]{font-size:.8em;margin-left:10px}.visualize-card[data-v-07ac80f8]{height:100%}.visualize-card[data-v-07ac80f8] .el-card__header{padding:0}.visualize-card[data-v-07ac80f8] .el-card__header .operation-bar{font-size:14px;display:flex;justify-content:space-between;height:35px;padding:0 10px;line-height:35px;z-index:9}.visualize-card[data-v-07ac80f8] .el-card__header .operation-bar i{margin-right:10px;cursor:pointer}.visualize-card[data-v-07ac80f8] .el-card__body{height:calc(100% - 36px)}.no-header[data-v-07ac80f8]{height:calc(100% - 2px)}.no-header[data-v-07ac80f8] .el-card__header{display:none}.no-header[data-v-07ac80f8] .el-card__body{height:100%}.no-header[data-v-07ac80f8] .visualize-window{height:100%!important}.edit-card[data-v-07ac80f8]:hover .el-card__header{display:block}.edit-card[data-v-07ac80f8]:hover .pane-container{height:calc(100% - 36px)}.no-padding[data-v-07ac80f8]{height:calc(100% - 2px)}.no-padding[data-v-07ac80f8] .el-card__body{padding:0!important}.background-item[data-v-07ac80f8]{position:absolute;top:55px;left:10px;right:10px;bottom:10px}",""]),t["default"]=o},3552:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".simple-panel[data-v-29f1aac0] .tool-bar{display:none}.simple-panel[data-v-29f1aac0] .background-item{top:10px}",""]),t["default"]=o},2366:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".simple-panel[data-v-d017c3d6] .tool-bar{display:none}.simple-panel[data-v-d017c3d6] .background-item{top:10px}[data-v-d017c3d6] *{box-sizing:border-box}.transparent-comp-background[data-v-d017c3d6] .visualize-card{background-color:transparent!important;border-color:transparent!important;box-shadow:none!important}.transparent-comp-background[data-v-d017c3d6] .el-card__header{border-color:transparent!important}",""]),t["default"]=o},9953:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".dashboard-container[data-v-1a7d7d44]{position:absolute;width:100%;height:100%;box-sizing:border-box}.dashboard-container[data-v-1a7d7d44] *{box-sizing:border-box}.dashboard-container .dashboard-list[data-v-1a7d7d44]{position:absolute;width:250px;height:100%;left:0;top:0;padding:20px 10px}.dashboard-container .dashboard-list[data-v-1a7d7d44] .el-card__header{padding:5px 0}.dashboard-container .dashboard-list[data-v-1a7d7d44] .el-card__header div{display:flex;justify-content:space-between;font-size:14px}.dashboard-container .dashboard-list[data-v-1a7d7d44] .el-card__header div i{cursor:pointer}.dashboard-container .dashboard-list .dashboard-list-item[data-v-1a7d7d44]{display:flex;justify-content:space-between;line-height:35px;font-size:14px;cursor:pointer}.dashboard-container .dashboard-list .dashboard-list-item i[data-v-1a7d7d44]{margin-right:10px;line-height:35px}.dashboard-container .dashboard-list .dashboard-list-item span[data-v-1a7d7d44]{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.dashboard-container .dashboard-wrapper[data-v-1a7d7d44]{position:absolute;width:calc(100% - 250px);height:100%;top:0;left:250px}.dashboard-container .dashboard-list-ul[data-v-1a7d7d44]{margin:0;padding:0}.no-list-panel .dashboard-wrapper[data-v-1a7d7d44]{position:absolute;width:100%;height:100%;top:0;left:0}",""]),t["default"]=o},740:function(e,t,a){"use strict";a.r(t);var r=a(429),i=a.n(r),n=a(1214),s=a.n(n),o=s()(i());o.push([e.id,".page[data-v-62c9c0f1]{width:100%;height:100%;position:relative;overflow-y:auto}",""]),t["default"]=o},1214:function(e){"use strict";e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var a="",r="undefined"!==typeof t[5];return t[4]&&(a+="@supports (".concat(t[4],") {")),t[2]&&(a+="@media ".concat(t[2]," {")),r&&(a+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),a+=e(t),r&&(a+="}"),t[2]&&(a+="}"),t[4]&&(a+="}"),a})).join("")},t.i=function(e,a,r,i,n){"string"===typeof e&&(e=[[null,e,void 0]]);var s={};if(r)for(var o=0;o<this.length;o++){var l=this[o][0];null!=l&&(s[l]=!0)}for(var c=0;c<e.length;c++){var d=[].concat(e[c]);r&&s[d[0]]||("undefined"!==typeof n&&("undefined"===typeof d[5]||(d[1]="@layer".concat(d[5].length>0?" ".concat(d[5]):""," {").concat(d[1],"}")),d[5]=n),a&&(d[2]?(d[1]="@media ".concat(d[2]," {").concat(d[1],"}"),d[2]=a):d[2]=a),i&&(d[4]?(d[1]="@supports (".concat(d[4],") {").concat(d[1],"}"),d[4]=i):d[4]="".concat(i)),t.push(d))}},t}},429:function(e){"use strict";e.exports=function(e){return e[1]}},5786:function(e,t,a){var r=a(7543);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("50eaa660",r,!0,{sourceMap:!1,shadowMode:!1})},992:function(e,t,a){var r=a(7369);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("2d5ec356",r,!0,{sourceMap:!1,shadowMode:!1})},920:function(e,t,a){var r=a(5663);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("347f47e2",r,!0,{sourceMap:!1,shadowMode:!1})},8921:function(e,t,a){var r=a(4006);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("d7912116",r,!0,{sourceMap:!1,shadowMode:!1})},6663:function(e,t,a){var r=a(5420);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("9b4a51f2",r,!0,{sourceMap:!1,shadowMode:!1})},5241:function(e,t,a){var r=a(698);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("1eff0665",r,!0,{sourceMap:!1,shadowMode:!1})},9669:function(e,t,a){var r=a(5204);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("7059966f",r,!0,{sourceMap:!1,shadowMode:!1})},3982:function(e,t,a){var r=a(7659);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("1ba7371c",r,!0,{sourceMap:!1,shadowMode:!1})},936:function(e,t,a){var r=a(7485);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("3b06bc49",r,!0,{sourceMap:!1,shadowMode:!1})},5365:function(e,t,a){var r=a(3552);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("0e976dd6",r,!0,{sourceMap:!1,shadowMode:!1})},6789:function(e,t,a){var r=a(2366);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("bd025cdc",r,!0,{sourceMap:!1,shadowMode:!1})},6400:function(e,t,a){var r=a(9953);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("fee7a5c0",r,!0,{sourceMap:!1,shadowMode:!1})},4037:function(e,t,a){var r=a(740);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[e.id,r,""]]),r.locals&&(e.exports=r.locals);var i=a(4825).A;i("58692ec7",r,!0,{sourceMap:!1,shadowMode:!1})},4825:function(e,t,a){"use strict";function r(e,t){for(var a=[],r={},i=0;i<t.length;i++){var n=t[i],s=n[0],o=n[1],l=n[2],c=n[3],d={id:e+":"+i,css:o,media:l,sourceMap:c};r[s]?r[s].parts.push(d):a.push(r[s]={id:s,parts:[d]})}return a}a.d(t,{A:function(){return m}});var i="undefined"!==typeof document;if("undefined"!==typeof DEBUG&&DEBUG&&!i)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var n={},s=i&&(document.head||document.getElementsByTagName("head")[0]),o=null,l=0,c=!1,d=function(){},h=null,u="data-vue-ssr-id",p="undefined"!==typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function m(e,t,a,i){c=a,h=i||{};var s=r(e,t);return f(s),function(t){for(var a=[],i=0;i<s.length;i++){var o=s[i],l=n[o.id];l.refs--,a.push(l)}t?(s=r(e,t),f(s)):s=[];for(i=0;i<a.length;i++){l=a[i];if(0===l.refs){for(var c=0;c<l.parts.length;c++)l.parts[c]();delete n[l.id]}}}}function f(e){for(var t=0;t<e.length;t++){var a=e[t],r=n[a.id];if(r){r.refs++;for(var i=0;i<r.parts.length;i++)r.parts[i](a.parts[i]);for(;i<a.parts.length;i++)r.parts.push(b(a.parts[i]));r.parts.length>a.parts.length&&(r.parts.length=a.parts.length)}else{var s=[];for(i=0;i<a.parts.length;i++)s.push(b(a.parts[i]));n[a.id]={id:a.id,refs:1,parts:s}}}}function g(){var e=document.createElement("style");return e.type="text/css",s.appendChild(e),e}function b(e){var t,a,r=document.querySelector("style["+u+'~="'+e.id+'"]');if(r){if(c)return d;r.parentNode.removeChild(r)}if(p){var i=l++;r=o||(o=g()),t=y.bind(null,r,i,!1),a=y.bind(null,r,i,!0)}else r=g(),t=C.bind(null,r),a=function(){r.parentNode.removeChild(r)};return t(e),function(r){if(r){if(r.css===e.css&&r.media===e.media&&r.sourceMap===e.sourceMap)return;t(e=r)}else a()}}var v=function(){var e=[];return function(t,a){return e[t]=a,e.filter(Boolean).join("\n")}}();function y(e,t,a,r){var i=a?"":r.css;if(e.styleSheet)e.styleSheet.cssText=v(t,i);else{var n=document.createTextNode(i),s=e.childNodes;s[t]&&e.removeChild(s[t]),s.length?e.insertBefore(n,s[t]):e.appendChild(n)}}function C(e,t){var a=t.css,r=t.media,i=t.sourceMap;if(r&&e.setAttribute("media",r),h.ssrId&&e.setAttribute(u,t.id),i&&(a+="\n/*# sourceURL="+i.sources[0]+" */",a+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(i))))+" */"),e.styleSheet)e.styleSheet.cssText=a;else{while(e.firstChild)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(a))}}},6796:function(e){"use strict";e.exports=require("file-saver")},345:function(e){"use strict";e.exports=require("xlsx")},4977:function(e,t,a){"use strict";var r=a(4188),i=a(3174),n=TypeError;e.exports=function(e){if(r(e))return e;throw new n(i(e)+" is not a function")}},2937:function(e,t,a){"use strict";var r=a(3243).has;e.exports=function(e){return r(e),e}},3770:function(e,t,a){"use strict";var r=a(831),i=String,n=TypeError;e.exports=function(e){if(r(e))return e;throw new n(i(e)+" is not an object")}},1458:function(e,t,a){"use strict";var r=a(380),i=a(675),n=a(9389),s=function(e){return function(t,a,s){var o=r(t),l=n(o);if(0===l)return!e&&-1;var c,d=i(s,l);if(e&&a!==a){while(l>d)if(c=o[d++],c!==c)return!0}else for(;l>d;d++)if((e||d in o)&&o[d]===a)return e||d||0;return!e&&-1}};e.exports={includes:s(!0),indexOf:s(!1)}},4224:function(e,t,a){"use strict";var r=a(6893),i=a(6719),n=TypeError,s=Object.getOwnPropertyDescriptor,o=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=o?function(e,t){if(i(e)&&!s(e,"length").writable)throw new n("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},8689:function(e,t,a){"use strict";var r=a(6881),i=r({}.toString),n=r("".slice);e.exports=function(e){return n(i(e),8,-1)}},8657:function(e,t,a){"use strict";var r=a(4418),i=a(3168),n=a(9304),s=a(4466);e.exports=function(e,t,a){for(var o=i(t),l=s.f,c=n.f,d=0;d<o.length;d++){var h=o[d];r(e,h)||a&&r(a,h)||l(e,h,c(t,h))}}},8088:function(e,t,a){"use strict";var r=a(6893),i=a(4466),n=a(9123);e.exports=r?function(e,t,a){return i.f(e,t,n(1,a))}:function(e,t,a){return e[t]=a,e}},9123:function(e){"use strict";e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},7509:function(e,t,a){"use strict";var r=a(4188),i=a(4466),n=a(4530),s=a(4798);e.exports=function(e,t,a,o){o||(o={});var l=o.enumerable,c=void 0!==o.name?o.name:t;if(r(a)&&n(a,c,o),o.global)l?e[t]=a:s(t,a);else{try{o.unsafe?e[t]&&(l=!0):delete e[t]}catch(d){}l?e[t]=a:i.f(e,t,{value:a,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return e}},4798:function(e,t,a){"use strict";var r=a(9117),i=Object.defineProperty;e.exports=function(e,t){try{i(r,e,{value:t,configurable:!0,writable:!0})}catch(a){r[e]=t}return t}},6893:function(e,t,a){"use strict";var r=a(5234);e.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},5926:function(e,t,a){"use strict";var r=a(9117),i=a(831),n=r.document,s=i(n)&&i(n.createElement);e.exports=function(e){return s?n.createElement(e):{}}},2197:function(e){"use strict";var t=TypeError,a=9007199254740991;e.exports=function(e){if(e>a)throw t("Maximum allowed index exceeded");return e}},1274:function(e){"use strict";e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8060:function(e,t,a){"use strict";var r=a(9117),i=r.navigator,n=i&&i.userAgent;e.exports=n?String(n):""},3008:function(e,t,a){"use strict";var r,i,n=a(9117),s=a(8060),o=n.process,l=n.Deno,c=o&&o.versions||l&&l.version,d=c&&c.v8;d&&(r=d.split("."),i=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!i&&s&&(r=s.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/),r&&(i=+r[1]))),e.exports=i},5613:function(e,t,a){"use strict";var r=a(9117),i=a(9304).f,n=a(8088),s=a(7509),o=a(4798),l=a(8657),c=a(8489);e.exports=function(e,t){var a,d,h,u,p,m,f=e.target,g=e.global,b=e.stat;if(d=g?r:b?r[f]||o(f,{}):r[f]&&r[f].prototype,d)for(h in t){if(p=t[h],e.dontCallGetSet?(m=i(d,h),u=m&&m.value):u=d[h],a=c(g?h:f+(b?".":"#")+h,e.forced),!a&&void 0!==u){if(typeof p==typeof u)continue;l(p,u)}(e.sham||u&&u.sham)&&n(p,"sham",!0),s(d,h,p,e)}}},5234:function(e){"use strict";e.exports=function(e){try{return!!e()}catch(t){return!0}}},9055:function(e,t,a){"use strict";var r=a(5234);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},9944:function(e,t,a){"use strict";var r=a(9055),i=Function.prototype.call;e.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},2735:function(e,t,a){"use strict";var r=a(6893),i=a(4418),n=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,o=i(n,"name"),l=o&&"something"===function(){}.name,c=o&&(!r||r&&s(n,"name").configurable);e.exports={EXISTS:o,PROPER:l,CONFIGURABLE:c}},1025:function(e,t,a){"use strict";var r=a(6881),i=a(4977);e.exports=function(e,t,a){try{return r(i(Object.getOwnPropertyDescriptor(e,t)[a]))}catch(n){}}},6881:function(e,t,a){"use strict";var r=a(9055),i=Function.prototype,n=i.call,s=r&&i.bind.bind(n,n);e.exports=r?s:function(e){return function(){return n.apply(e,arguments)}}},5604:function(e,t,a){"use strict";var r=a(9117),i=a(4188),n=function(e){return i(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?n(r[e]):r[e]&&r[e][t]}},6002:function(e){"use strict";e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},2913:function(e,t,a){"use strict";var r=a(4977),i=a(4318);e.exports=function(e,t){var a=e[t];return i(a)?void 0:r(a)}},5558:function(e,t,a){"use strict";var r=a(4977),i=a(3770),n=a(9944),s=a(6744),o=a(6002),l="Invalid size",c=RangeError,d=TypeError,h=Math.max,u=function(e,t){this.set=e,this.size=h(t,0),this.has=r(e.has),this.keys=r(e.keys)};u.prototype={getIterator:function(){return o(i(n(this.keys,this.set)))},includes:function(e){return n(this.has,this.set,e)}},e.exports=function(e){i(e);var t=+e.size;if(t!==t)throw new d(l);var a=s(t);if(a<0)throw new c(l);return new u(e,a)}},9117:function(e,t,a){"use strict";var r=function(e){return e&&e.Math===Math&&e};e.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof a.g&&a.g)||r("object"==typeof this&&this)||function(){return this}()||Function("return this")()},4418:function(e,t,a){"use strict";var r=a(6881),i=a(3628),n=r({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return n(i(e),t)}},7588:function(e){"use strict";e.exports={}},9622:function(e,t,a){"use strict";var r=a(6893),i=a(5234),n=a(5926);e.exports=!r&&!i((function(){return 7!==Object.defineProperty(n("div"),"a",{get:function(){return 7}}).a}))},7568:function(e,t,a){"use strict";var r=a(6881),i=a(5234),n=a(8689),s=Object,o=r("".split);e.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"===n(e)?o(e,""):s(e)}:s},3029:function(e,t,a){"use strict";var r=a(6881),i=a(4188),n=a(2694),s=r(Function.toString);i(n.inspectSource)||(n.inspectSource=function(e){return s(e)}),e.exports=n.inspectSource},3086:function(e,t,a){"use strict";var r,i,n,s=a(5945),o=a(9117),l=a(831),c=a(8088),d=a(4418),h=a(2694),u=a(168),p=a(7588),m="Object already initialized",f=o.TypeError,g=o.WeakMap,b=function(e){return n(e)?i(e):r(e,{})},v=function(e){return function(t){var a;if(!l(t)||(a=i(t)).type!==e)throw new f("Incompatible receiver, "+e+" required");return a}};if(s||h.state){var y=h.state||(h.state=new g);y.get=y.get,y.has=y.has,y.set=y.set,r=function(e,t){if(y.has(e))throw new f(m);return t.facade=e,y.set(e,t),t},i=function(e){return y.get(e)||{}},n=function(e){return y.has(e)}}else{var C=u("state");p[C]=!0,r=function(e,t){if(d(e,C))throw new f(m);return t.facade=e,c(e,C,t),t},i=function(e){return d(e,C)?e[C]:{}},n=function(e){return d(e,C)}}e.exports={set:r,get:i,has:n,enforce:b,getterFor:v}},6719:function(e,t,a){"use strict";var r=a(8689);e.exports=Array.isArray||function(e){return"Array"===r(e)}},4188:function(e){"use strict";var t="object"==typeof document&&document.all;e.exports="undefined"==typeof t&&void 0!==t?function(e){return"function"==typeof e||e===t}:function(e){return"function"==typeof e}},8489:function(e,t,a){"use strict";var r=a(5234),i=a(4188),n=/#|\.prototype\./,s=function(e,t){var a=l[o(e)];return a===d||a!==c&&(i(t)?r(t):!!t)},o=s.normalize=function(e){return String(e).replace(n,".").toLowerCase()},l=s.data={},c=s.NATIVE="N",d=s.POLYFILL="P";e.exports=s},4318:function(e){"use strict";e.exports=function(e){return null===e||void 0===e}},831:function(e,t,a){"use strict";var r=a(4188);e.exports=function(e){return"object"==typeof e?null!==e:r(e)}},1942:function(e){"use strict";e.exports=!1},6032:function(e,t,a){"use strict";var r=a(5604),i=a(4188),n=a(4578),s=a(9809),o=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return i(t)&&n(t.prototype,o(e))}},7032:function(e,t,a){"use strict";var r=a(9944);e.exports=function(e,t,a){var i,n,s=a?e:e.iterator,o=e.next;while(!(i=r(o,s)).done)if(n=t(i.value),void 0!==n)return n}},8500:function(e,t,a){"use strict";var r=a(9944),i=a(3770),n=a(2913);e.exports=function(e,t,a){var s,o;i(e);try{if(s=n(e,"return"),!s){if("throw"===t)throw a;return a}s=r(s,e)}catch(l){o=!0,s=l}if("throw"===t)throw a;if(o)throw s;return i(s),a}},9389:function(e,t,a){"use strict";var r=a(7611);e.exports=function(e){return r(e.length)}},4530:function(e,t,a){"use strict";var r=a(6881),i=a(5234),n=a(4188),s=a(4418),o=a(6893),l=a(2735).CONFIGURABLE,c=a(3029),d=a(3086),h=d.enforce,u=d.get,p=String,m=Object.defineProperty,f=r("".slice),g=r("".replace),b=r([].join),v=o&&!i((function(){return 8!==m((function(){}),"length",{value:8}).length})),y=String(String).split("String"),C=e.exports=function(e,t,a){"Symbol("===f(p(t),0,7)&&(t="["+g(p(t),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),a&&a.getter&&(t="get "+t),a&&a.setter&&(t="set "+t),(!s(e,"name")||l&&e.name!==t)&&(o?m(e,"name",{value:t,configurable:!0}):e.name=t),v&&a&&s(a,"arity")&&e.length!==a.arity&&m(e,"length",{value:a.arity});try{a&&s(a,"constructor")&&a.constructor?o&&m(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(i){}var r=h(e);return s(r,"source")||(r.source=b(y,"string"==typeof t?t:"")),e};Function.prototype.toString=C((function(){return n(this)&&u(this).source||c(this)}),"toString")},142:function(e){"use strict";var t=Math.ceil,a=Math.floor;e.exports=Math.trunc||function(e){var r=+e;return(r>0?a:t)(r)}},4466:function(e,t,a){"use strict";var r=a(6893),i=a(9622),n=a(3315),s=a(3770),o=a(2344),l=TypeError,c=Object.defineProperty,d=Object.getOwnPropertyDescriptor,h="enumerable",u="configurable",p="writable";t.f=r?n?function(e,t,a){if(s(e),t=o(t),s(a),"function"===typeof e&&"prototype"===t&&"value"in a&&p in a&&!a[p]){var r=d(e,t);r&&r[p]&&(e[t]=a.value,a={configurable:u in a?a[u]:r[u],enumerable:h in a?a[h]:r[h],writable:!1})}return c(e,t,a)}:c:function(e,t,a){if(s(e),t=o(t),s(a),i)try{return c(e,t,a)}catch(r){}if("get"in a||"set"in a)throw new l("Accessors not supported");return"value"in a&&(e[t]=a.value),e}},9304:function(e,t,a){"use strict";var r=a(6893),i=a(9944),n=a(4416),s=a(9123),o=a(380),l=a(2344),c=a(4418),d=a(9622),h=Object.getOwnPropertyDescriptor;t.f=r?h:function(e,t){if(e=o(e),t=l(t),d)try{return h(e,t)}catch(a){}if(c(e,t))return s(!i(n.f,e,t),e[t])}},5629:function(e,t,a){"use strict";var r=a(1843),i=a(1274),n=i.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return r(e,n)}},156:function(e,t){"use strict";t.f=Object.getOwnPropertySymbols},4578:function(e,t,a){"use strict";var r=a(6881);e.exports=r({}.isPrototypeOf)},1843:function(e,t,a){"use strict";var r=a(6881),i=a(4418),n=a(380),s=a(1458).indexOf,o=a(7588),l=r([].push);e.exports=function(e,t){var a,r=n(e),c=0,d=[];for(a in r)!i(o,a)&&i(r,a)&&l(d,a);while(t.length>c)i(r,a=t[c++])&&(~s(d,a)||l(d,a));return d}},4416:function(e,t){"use strict";var a={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!a.call({1:2},1);t.f=i?function(e){var t=r(this,e);return!!t&&t.enumerable}:a},2287:function(e,t,a){"use strict";var r=a(9944),i=a(4188),n=a(831),s=TypeError;e.exports=function(e,t){var a,o;if("string"===t&&i(a=e.toString)&&!n(o=r(a,e)))return o;if(i(a=e.valueOf)&&!n(o=r(a,e)))return o;if("string"!==t&&i(a=e.toString)&&!n(o=r(a,e)))return o;throw new s("Can't convert object to primitive value")}},3168:function(e,t,a){"use strict";var r=a(5604),i=a(6881),n=a(5629),s=a(156),o=a(3770),l=i([].concat);e.exports=r("Reflect","ownKeys")||function(e){var t=n.f(o(e)),a=s.f;return a?l(t,a(e)):t}},9509:function(e,t,a){"use strict";var r=a(4318),i=TypeError;e.exports=function(e){if(r(e))throw new i("Can't call method on "+e);return e}},679:function(e,t,a){"use strict";var r=a(3243),i=a(9800),n=r.Set,s=r.add;e.exports=function(e){var t=new n;return i(e,(function(e){s(t,e)})),t}},7059:function(e,t,a){"use strict";var r=a(2937),i=a(3243),n=a(679),s=a(7173),o=a(5558),l=a(9800),c=a(7032),d=i.has,h=i.remove;e.exports=function(e){var t=r(this),a=o(e),i=n(t);return s(t)<=a.size?l(t,(function(e){a.includes(e)&&h(i,e)})):c(a.getIterator(),(function(e){d(t,e)&&h(i,e)})),i}},3243:function(e,t,a){"use strict";var r=a(6881),i=Set.prototype;e.exports={Set:Set,add:r(i.add),has:r(i.has),remove:r(i["delete"]),proto:i}},3721:function(e,t,a){"use strict";var r=a(2937),i=a(3243),n=a(7173),s=a(5558),o=a(9800),l=a(7032),c=i.Set,d=i.add,h=i.has;e.exports=function(e){var t=r(this),a=s(e),i=new c;return n(t)>a.size?l(a.getIterator(),(function(e){h(t,e)&&d(i,e)})):o(t,(function(e){a.includes(e)&&d(i,e)})),i}},9978:function(e,t,a){"use strict";var r=a(2937),i=a(3243).has,n=a(7173),s=a(5558),o=a(9800),l=a(7032),c=a(8500);e.exports=function(e){var t=r(this),a=s(e);if(n(t)<=a.size)return!1!==o(t,(function(e){if(a.includes(e))return!1}),!0);var d=a.getIterator();return!1!==l(d,(function(e){if(i(t,e))return c(d,"normal",!1)}))}},4361:function(e,t,a){"use strict";var r=a(2937),i=a(7173),n=a(9800),s=a(5558);e.exports=function(e){var t=r(this),a=s(e);return!(i(t)>a.size)&&!1!==n(t,(function(e){if(!a.includes(e))return!1}),!0)}},7528:function(e,t,a){"use strict";var r=a(2937),i=a(3243).has,n=a(7173),s=a(5558),o=a(7032),l=a(8500);e.exports=function(e){var t=r(this),a=s(e);if(n(t)<a.size)return!1;var c=a.getIterator();return!1!==o(c,(function(e){if(!i(t,e))return l(c,"normal",!1)}))}},9800:function(e,t,a){"use strict";var r=a(6881),i=a(7032),n=a(3243),s=n.Set,o=n.proto,l=r(o.forEach),c=r(o.keys),d=c(new s).next;e.exports=function(e,t,a){return a?i({iterator:c(e),next:d},t):l(e,t)}},4471:function(e,t,a){"use strict";var r=a(5604),i=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};e.exports=function(e){var t=r("Set");try{(new t)[e](i(0));try{return(new t)[e](i(-1)),!1}catch(a){return!0}}catch(n){return!1}}},7173:function(e,t,a){"use strict";var r=a(1025),i=a(3243);e.exports=r(i.proto,"size","get")||function(e){return e.size}},1657:function(e,t,a){"use strict";var r=a(2937),i=a(3243),n=a(679),s=a(5558),o=a(7032),l=i.add,c=i.has,d=i.remove;e.exports=function(e){var t=r(this),a=s(e).getIterator(),i=n(t);return o(a,(function(e){c(t,e)?d(i,e):l(i,e)})),i}},5077:function(e,t,a){"use strict";var r=a(2937),i=a(3243).add,n=a(679),s=a(5558),o=a(7032);e.exports=function(e){var t=r(this),a=s(e).getIterator(),l=n(t);return o(a,(function(e){i(l,e)})),l}},168:function(e,t,a){"use strict";var r=a(746),i=a(6209),n=r("keys");e.exports=function(e){return n[e]||(n[e]=i(e))}},2694:function(e,t,a){"use strict";var r=a(1942),i=a(9117),n=a(4798),s="__core-js_shared__",o=e.exports=i[s]||n(s,{});(o.versions||(o.versions=[])).push({version:"3.38.1",mode:r?"pure":"global",copyright:"© 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.38.1/LICENSE",source:"https://github.com/zloirock/core-js"})},746:function(e,t,a){"use strict";var r=a(2694);e.exports=function(e,t){return r[e]||(r[e]=t||{})}},8944:function(e,t,a){"use strict";var r=a(3008),i=a(5234),n=a(9117),s=n.String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!s(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},675:function(e,t,a){"use strict";var r=a(6744),i=Math.max,n=Math.min;e.exports=function(e,t){var a=r(e);return a<0?i(a+t,0):n(a,t)}},380:function(e,t,a){"use strict";var r=a(7568),i=a(9509);e.exports=function(e){return r(i(e))}},6744:function(e,t,a){"use strict";var r=a(142);e.exports=function(e){var t=+e;return t!==t||0===t?0:r(t)}},7611:function(e,t,a){"use strict";var r=a(6744),i=Math.min;e.exports=function(e){var t=r(e);return t>0?i(t,9007199254740991):0}},3628:function(e,t,a){"use strict";var r=a(9509),i=Object;e.exports=function(e){return i(r(e))}},290:function(e,t,a){"use strict";var r=a(9944),i=a(831),n=a(6032),s=a(2913),o=a(2287),l=a(4282),c=TypeError,d=l("toPrimitive");e.exports=function(e,t){if(!i(e)||n(e))return e;var a,l=s(e,d);if(l){if(void 0===t&&(t="default"),a=r(l,e,t),!i(a)||n(a))return a;throw new c("Can't convert object to primitive value")}return void 0===t&&(t="number"),o(e,t)}},2344:function(e,t,a){"use strict";var r=a(290),i=a(6032);e.exports=function(e){var t=r(e,"string");return i(t)?t:t+""}},3174:function(e){"use strict";var t=String;e.exports=function(e){try{return t(e)}catch(a){return"Object"}}},6209:function(e,t,a){"use strict";var r=a(6881),i=0,n=Math.random(),s=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++i+n,36)}},9809:function(e,t,a){"use strict";var r=a(8944);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},3315:function(e,t,a){"use strict";var r=a(6893),i=a(5234);e.exports=r&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},5945:function(e,t,a){"use strict";var r=a(9117),i=a(4188),n=r.WeakMap;e.exports=i(n)&&/native code/.test(String(n))},4282:function(e,t,a){"use strict";var r=a(9117),i=a(746),n=a(4418),s=a(6209),o=a(8944),l=a(9809),c=r.Symbol,d=i("wks"),h=l?c["for"]||c:c&&c.withoutSetter||s;e.exports=function(e){return n(d,e)||(d[e]=o&&n(c,e)?c[e]:h("Symbol."+e)),d[e]}},9375:function(e,t,a){"use strict";var r=a(5613),i=a(3628),n=a(9389),s=a(4224),o=a(2197),l=a(5234),c=l((function(){return 4294967297!==[].push.call({length:4294967296},1)})),d=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},h=c||!d();r({target:"Array",proto:!0,arity:1,forced:h},{push:function(e){var t=i(this),a=n(t),r=arguments.length;o(a+r);for(var l=0;l<r;l++)t[a]=arguments[l],a++;return s(t,a),a}})},9033:function(e,t,a){"use strict";var r=a(5613),i=a(7059),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("difference")},{difference:i})},8903:function(e,t,a){"use strict";var r=a(5613),i=a(5234),n=a(3721),s=a(4471),o=!s("intersection")||i((function(){return"3,2"!==String(Array.from(new Set([1,2,3]).intersection(new Set([3,2]))))}));r({target:"Set",proto:!0,real:!0,forced:o},{intersection:n})},1018:function(e,t,a){"use strict";var r=a(5613),i=a(9978),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("isDisjointFrom")},{isDisjointFrom:i})},1415:function(e,t,a){"use strict";var r=a(5613),i=a(4361),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("isSubsetOf")},{isSubsetOf:i})},4448:function(e,t,a){"use strict";var r=a(5613),i=a(7528),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("isSupersetOf")},{isSupersetOf:i})},8871:function(e,t,a){"use strict";var r=a(5613),i=a(1657),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("symmetricDifference")},{symmetricDifference:i})},6539:function(e,t,a){"use strict";var r=a(5613),i=a(5077),n=a(4471);r({target:"Set",proto:!0,real:!0,forced:!n("union")},{union:i})}},t={};function a(r){var i=t[r];if(void 0!==i)return i.exports;var n=t[r]={id:r,exports:{}};return e[r].call(n.exports,n,n.exports,a),n.exports}a.m=e,function(){a.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return a.d(t,{a:t}),t}}(),function(){a.d=function(e,t){for(var r in t)a.o(t,r)&&!a.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}}(),function(){a.f={},a.e=function(e){return Promise.all(Object.keys(a.f).reduce((function(t,r){return a.f[r](e,t),t}),[]))}}(),function(){a.u=function(e){return"omega-dashboard.umd.min."+e+".js"}}(),function(){a.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="omega-dashboard:";a.l=function(r,i,n,s){if(e[r])e[r].push(i);else{var o,l;if(void 0!==n)for(var c=document.getElementsByTagName("script"),d=0;d<c.length;d++){var h=c[d];if(h.getAttribute("src")==r||h.getAttribute("data-webpack")==t+n){o=h;break}}o||(l=!0,o=document.createElement("script"),o.charset="utf-8",o.timeout=120,a.nc&&o.setAttribute("nonce",a.nc),o.setAttribute("data-webpack",t+n),o.src=r),e[r]=[i];var u=function(t,a){o.onerror=o.onload=null,clearTimeout(p);var i=e[r];if(delete e[r],o.parentNode&&o.parentNode.removeChild(o),i&&i.forEach((function(e){return e(a)})),t)return t(a)},p=setTimeout(u.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=u.bind(null,o.onerror),o.onload=u.bind(null,o.onload),l&&document.head.appendChild(o)}}}(),function(){a.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){a.p=""}(),function(){var e={845:0};a.f.j=function(t,r){var i=a.o(e,t)?e[t]:void 0;if(0!==i)if(i)r.push(i[2]);else{var n=new Promise((function(a,r){i=e[t]=[a,r]}));r.push(i[2]=n);var s=a.p+a.u(t),o=new Error,l=function(r){if(a.o(e,t)&&(i=e[t],0!==i&&(e[t]=void 0),i)){var n=r&&("load"===r.type?"missing":r.type),s=r&&r.target&&r.target.src;o.message="Loading chunk "+t+" failed.\n("+n+": "+s+")",o.name="ChunkLoadError",o.type=n,o.request=s,i[1](o)}};a.l(s,l,"chunk-"+t,t)}};var t=function(t,r){var i,n,s=r[0],o=r[1],l=r[2],c=0;if(s.some((function(t){return 0!==e[t]}))){for(i in o)a.o(o,i)&&(a.m[i]=o[i]);if(l)l(a)}for(t&&t(r);c<s.length;c++)n=s[c],a.o(e,n)&&e[n]&&e[n][0](),e[n]=0},r=("undefined"!==typeof self?self:this)["webpackChunkomega_dashboard"]=("undefined"!==typeof self?self:this)["webpackChunkomega_dashboard"]||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var r={};return function(){"use strict";if(a.r(r),a.d(r,{OmegaModelPlugin:function(){return Ra},dashboardRender:function(){return ja},default:function(){return qa},fullScreenDb:function(){return La},registerComponent:function(){return Ht}}),"undefined"!==typeof window){var e=window.document.currentScript,t=e&&e.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);t&&(a.p=t[1])}var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"page"},[t("Dashboard",{attrs:{"dashboard-title":e.dashboardTitle}})],1)},n=[],s=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"dashboard-container",class:{"no-list-panel":!this.showDashboardList}},[t("el-card",{directives:[{name:"show",rawName:"v-show",value:e.showDashboardList,expression:"showDashboardList"}],staticClass:"dashboard-list",attrs:{"body-style":"padding: 0px;",shadow:"never"}},[t("div",{attrs:{slot:"header"},slot:"header"},[t("span",[e._v(e._s(e.dashboardTitle))]),t("el-button",{directives:[{name:"show",rawName:"v-show",value:e.isEdit,expression:"isEdit"}],attrs:{type:"primary",size:"mini"},on:{click:e.addDashboard}},[e._v(" 新建 ")])],1),t("ul",{staticClass:"dashboard-list-ul"},[t("draggable",{staticClass:"draggable-wrapper",attrs:{group:{name:"dashboard",pull:!0}},on:{change:e.handleOrderChange},model:{value:e.dashboardList,callback:function(t){e.dashboardList=t},expression:"dashboardList"}},e._l(e.dashboardList,(function(a){return t("li",{key:a.id,class:{"dashboard-list-item":!0,"text-ZS":e.currentDashboard.id===a.id},on:{click:function(t){return e.switchDb(a)}}},[t("span",[t("i",{staticClass:"el-icon-document"}),t("el-tooltip",{attrs:{effect:"dark",content:a.name,placement:"top","open-delay":888}},[t("span",[e._v(e._s(a.name))])])],1),t("div",{on:{click:function(e){e.stopPropagation()}}},[t("el-dropdown",{directives:[{name:"show",rawName:"v-show",value:e.isEdit,expression:"isEdit"}],attrs:{size:"mini",trigger:"click"},on:{command:e.handleCommand}},[t("span",{staticClass:"el-dropdown-link"},[t("i",{staticClass:"el-icon-more"})]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[t("el-dropdown-item",{attrs:{command:{type:"edit",target:a}}},[e._v(" 编辑 ")]),t("el-dropdown-item",{attrs:{command:{type:"delete",target:a}}},[e._v(" 删除 ")])],1)],1)],1)])})),0)],1)]),t("dashboardItem",{staticClass:"dashboard-wrapper",attrs:{dashboard:e.currentDashboard,mode:e.mode},on:{handleShowDashboardList:e.handleShowDashboardList}}),t("el-dialog",{attrs:{width:"500px",title:e.title,visible:e.editDialogVisible},on:{"update:visible":function(t){e.editDialogVisible=t}}},[t("el-form",{attrs:{"label-width":"100px;"}},[t("el-form-item",{attrs:{label:" 名称："}},[t("el-input",{staticStyle:{width:"350px"},attrs:{size:"small",placeholder:"请输入名称",maxlength:20,"show-word-limit":""},model:{value:e.dbObj.name,callback:function(t){e.$set(e.dbObj,"name",t)},expression:"dbObj.name"}})],1),t("el-form-item",{attrs:{label:" 描述："}},[t("el-input",{staticStyle:{width:"350px"},attrs:{type:"textarea",rows:3,size:"small",placeholder:"请输入描述",maxlength:50,"show-word-limit":""},model:{value:e.dbObj.description,callback:function(t){e.$set(e.dbObj,"description",t)},expression:"dbObj.description"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:e.handleSubmit}},[e._v(" 确定 ")]),t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(t){e.editDialogVisible=!1}}},[e._v(" 取消 ")])],1)],1)],1)},o=[],l=require("vuedraggable"),c=a.n(l),d=function(){var e=this,t=e._self._c;return t("div",{ref:"container",class:{fullheight:e.autoHeight}},[t("div",{directives:[{name:"show",rawName:"v-show",value:!e.showChartPanel,expression:"!showChartPanel"}],class:{fullheight:e.autoHeight}},[t("div",{staticClass:"tool-bar text-T1"},[t("div",[t("span",{staticClass:"db-name text-T1"},[e._v(e._s(e.dashboard.name))]),t("span",{staticClass:"text-T3"},[e._v(e._s(e.dashboard.description))])]),t("div",{directives:[{name:"show",rawName:"v-show",value:"edit"===e.mode,expression:"mode === 'edit'"}]},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handleBackGroundItem}},[e._v(" 设置底图组件 ")]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.editBackGroundItem}},[e._v(" 编辑底图组件 ")]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.cancelBackGroundItem}},[e._v(" 取消底图组件 ")]),t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.handleLinkChart}},[e._v(" 我的图表 ")])],1)]),e.bgItemId>0?t("div",{staticClass:"background-item"},["predefined"===e.getChartItem(e.bgItemId).content.componentType?t("preView",{key:e.bgItemId,ref:`chartInstance${e.bgItemId}`,attrs:{chartType:e.getChartItem(e.bgItemId).content.chartType,content:JSON.stringify(e.getChartItem(e.bgItemId).content),chartStyle:{height:"100%"}}}):t("visualize-panel",{key:e.bgItemId,ref:`chartInstance${e.bgItemId}`,attrs:{data:e.results[e.bgItemId]?e.results[e.bgItemId]:[],schema:e.getChartItem(e.bgItemId).content.allSelected,"chart-type":e.getChartItem(e.bgItemId).content.chartType,"is-edit-mode":!1,"chart-style":{height:"100%"}},on:{"update:chartType":function(t){e.$set(e.getChartItem(e.bgItemId).content,"chartType",t)},"update:chart-type":function(t){e.$set(e.getChartItem(e.bgItemId).content,"chartType",t)}}})],1):e._e(),0!==e.charts.length?t("grid-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{"min-height":"500px"},attrs:{layout:e.layout,"col-num":60,"row-height":e.gridRowHeight,"is-draggable":"edit"===e.mode,"is-resizable":"edit"===e.mode,"is-mirrored":!1,"vertical-compact":!1,"pane-container":!1,"prevent-collision":!1,margin:[e.gridMarginTB,e.gridMarginLR],"use-css-transforms":!1},on:{"update:layout":function(t){e.layout=t},"layout-updated":e.handleLayoutChange}},e._l(e.layout||[],(function(a){return t("grid-item",{key:a.i,attrs:{x:a.x,y:a.y,w:a.w,h:a.h,i:a.i,dragAllowFrom:".operation-bar"},on:{resized:e.handleResize}},[t("el-card",{directives:[{name:"loading",rawName:"v-loading",value:e.chartLoading[a.i],expression:"chartLoading[item.i]"}],staticClass:"visualize-card",class:{"no-header":!0===e.getChartItem(a.i).content.hideHeader,"no-padding":!0===e.getChartItem(a.i).content.noPadding,"edit-card":!0===e.getChartItem(a.i).content.hideHeader&&"edit"===e.mode},attrs:{"body-style":"padding: 10px;"}},[t("div",{staticClass:"operation-bar",attrs:{slot:"header"},slot:"header"},[t("div",[t("span",[e._v(e._s(e.getChartItem(a.i).chart_name))])]),t("div",[t("i",{directives:[{name:"show",rawName:"v-show",value:e.getJumpPath(e.getChartItem(a.i)),expression:"getJumpPath(getChartItem(item.i))"}],staticClass:"el-icon-share text-ZS",on:{click:function(t){e.handleJumpto(e.getChartItem(a.i))}}}),t("i",{directives:[{name:"show",rawName:"v-show",value:"edit"===e.mode,expression:"mode === 'edit'"}],staticClass:"el-icon-edit text-ZS",on:{click:function(t){e.handleEdit(e.getChartItem(a.i))}}}),t("i",{directives:[{name:"show",rawName:"v-show",value:"edit"===e.mode,expression:"mode === 'edit'"}],staticClass:"el-icon-delete text-ZS",on:{click:function(t){e.handleDelete(e.getChartItem(a.i))}}}),t("el-tooltip",{directives:[{name:"show",rawName:"v-show",value:e.getChartItem(a.i).description,expression:"getChartItem(item.i).description"}],staticClass:"item",attrs:{content:e.getChartItem(a.i).description,effect:"dark",placement:"top-end"}},[t("i",{staticClass:"el-icon-info text-ZS",staticStyle:{cursor:"pointer"}})])],1)]),"predefined"===e.getChartItem(a.i).content.componentType?t("preView",{key:a.i,ref:`chartInstance${a.i}`,refInFor:!0,attrs:{chartType:e.getChartItem(a.i).content.chartType,content:JSON.stringify(e.getChartItem(a.i).content),chartStyle:{height:e.getChartHeight(a)}}}):t("visualize-panel",{key:a.i,ref:`chartInstance${a.i}`,refInFor:!0,attrs:{data:e.results[a.i]?e.results[a.i]:[],schema:e.getChartItem(a.i).content.allSelected,"chart-type":e.getChartItem(a.i).content.chartType,"is-edit-mode":!1,"chart-style":{height:e.getChartHeight(a)}},on:{"update:chartType":function(t){e.$set(e.getChartItem(a.i).content,"chartType",t)},"update:chart-type":function(t){e.$set(e.getChartItem(a.i).content,"chartType",t)}}})],1)],1)})),1):e._e(),t("el-dialog",{attrs:{title:"我的图表",visible:e.showChartList},on:{"update:visible":function(t){e.showChartList=t}}},[t("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.createChart}},[e._v(" 新建 ")]),t("el-table",{attrs:{data:e.myChartList,"max-height":500}},[t("el-table-column",{attrs:{type:"index"}}),t("el-table-column",{attrs:{label:"名称",width:"200",prop:"chart_name"}}),t("el-table-column",{attrs:{label:"描述",prop:"description"}}),t("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{size:"mini",type:"primary",disabled:e.isExisted(a.row)},on:{click:function(t){return e.linkChart(a.row)}}},[e._v(" 添加 ")]),t("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(t){return e.handleEdit(a.row)}}},[e._v(" 编辑 ")]),t("el-button",{attrs:{size:"mini",type:"danger"},on:{click:function(t){return e.deleteChart(a.row)}}},[e._v(" 删除 ")])]}}])})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(t){e.showChartList=!1}}},[e._v(" 取消 ")])],1)],1),t("SetBackground",e._g(e._b({},"SetBackground",e.SetBackground,!1),e.SetBackground.event))],1),e.showChartPanel?t("ChartPanel",{attrs:{currentChartId:e.currentChartId},on:{handleReturn:e.handleReturn,changeCurrentChart:e.changeCurrentChart}}):e._e()],1)},h=[],u=(a(9375),require("vue-grid-layout")),p=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pane-container"},[t(e.currentType.componentName,{tag:"component",staticClass:"visualize-window",attrs:{data:e.chartData,schema:e.schema,"chart-style":e.chartStyle}}),e.isEditMode?t("div",{staticClass:"chart-style-panel"},[t("el-form",{attrs:{"label-position":"top",size:"mini"}},[t("el-form-item",{attrs:{label:"图表类型:"}},[t("div",{staticClass:"chart-type-list"},e._l(e.chartTypeList,(function(a){return t("span",{key:a.type,class:{"bg-BG3":a.type===e.chartType,disabledIcon:!e.isUsable(a),"text-ZS":e.isUsable(a),"text-T4":!e.isUsable(a)},on:{click:function(t){return e.switchChartType(a)}}},[t("el-tooltip",{attrs:{content:a.matchRule.description,placement:"top",effect:"light"}},[t("omega-icon",{attrs:{symbolId:a.icon}})],1)],1)})),0)])],1)],1):e._e()],1)},m=[],f=function(){var e=this,t=e._self._c;return t("div",{ref:"chart",style:e.chartStyle})},g=[],b=require("echarts");function v(e){return e/1e3>=.1&&e/1e4<1?e/1e3+"k":e/1e4>=1?e/1e4+"w":e}var y=require("lodash"),C=a.n(y),x=require("moment"),w=a.n(x);const S=[null,-2147483648,"NaN","Infinity",2147483648];function T(e,t,a="--"){const r=C().get(e,t,a);return S.includes(r)?a:r}const I=function(e,t){t=t||0;var a=Math.pow(10,t);return(Math.round(e*a)/a).toFixed(t)};var N={get:T,formatNumberWithPrecision:function(e,t){if(C().isNumber(t)){if(C().isNumber(e)||(e=parseFloat(e)),isNaN(e))return null;e=I(e,t)}return e}},D={props:{widgetMeta:{required:!1,type:Object,default:()=>{}},data:{required:!0,default:()=>{}},schema:{type:Array,required:!0},chartOpt:{type:Object,required:!1},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},data(){return{chart:null}},watch:{data:{deep:!0,handler:function(e){this.renderChart(e)}},schema:{deep:!0,handler:function(){this.renderChart(this.data)}}},mounted(){this.renderChart(this.data),this.$on("resized",this.handleResize),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){this.chart&&this.chart.resize()},validateData(e){Array.isArray(e)||this.$message({message:"线图的数据格式必须为数组，请检查你的数据格式"})},renderChart(e){if(!this.$refs.chart)return;const t=[],a=[],r={};this.schema.forEach(((i,n)=>{t.push(i.alias||i.name),i.asxAxis||(r[i.name]={name:i.alias||i.name,data:[],type:"line"}),e.forEach((e=>{i.asxAxis?a.push(e[i.name]):("float"===i.Type&&(e[i.name]=N.formatNumberWithPrecision(e[i.name],2)),r[i.name].data.push(e[i.name]))}))}));const i={legend:{bottom:0,type:"scroll",data:t},toolbox:{show:!0,itemSize:12,top:-5,feature:{saveAsImage:{show:!0},magicType:{type:["line","bar"]},restore:{show:!0},dataZoom:{show:!0}}},grid:{top:"20px",left:"45px",right:"0",bottom:"45px"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},xAxis:{type:"category",axisLabel:{color:"#95a4bd"},axisLine:{lineStyle:{color:"#95a4bd"}},data:a},yAxis:{axisLabel:{show:!0,color:"#95a4bd",formatter:v},axisLine:{lineStyle:{color:"#95a4bd"}},splitLine:{lineStyle:{type:"dashed"}}},series:Object.values(r)};setTimeout((()=>{this.chart||(this.chart=b.init(this.$refs.chart)),this.chart.clear(),this.chart.setOption(i),this.chartOpt&&this.chart.setOption(this.chartOpt)}),0)}}},k=D;function O(e,t,a,r,i,n,s,o){var l,c="function"===typeof e?e.options:e;if(t&&(c.render=t,c.staticRenderFns=a,c._compiled=!0),r&&(c.functional=!0),n&&(c._scopeId="data-v-"+n),s?(l=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"===typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),i&&i.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(s)},c._ssrRegister=l):i&&(l=o?function(){i.call(this,(c.functional?this.parent:this).$root.$options.shadowRoot)}:i),l)if(c.functional){c._injectStyles=l;var d=c.render;c.render=function(e,t){return l.call(t),d(e,t)}}else{var h=c.beforeCreate;c.beforeCreate=h?[].concat(h,l):[l]}return{exports:e,options:c}}var L=O(k,f,g,!1,null,null,null),z=L.exports,$=function(){var e=this,t=e._self._c;return t("el-table",{style:e.chartStyle,attrs:{data:e.data,height:e.chartStyle.height,"highlight-current-row":"",border:"","header-cell-style":{padding:"5px 0"},"cell-style":{padding:"5px 0"}}},e._l(e.schema,(function(a){return t("el-table-column",{key:a.Column,attrs:{prop:a.Column,label:a.alias,align:"center",formatter:e.columnFormatter}})})),1)},E=[],P={props:{data:{type:Array,default:()=>[]},schema:{type:Array,default:()=>[]},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},methods:{columnFormatter(e,t,a){const r=C().find(this.schema,{Column:t.property});return"float"===r.Type?N.formatNumberWithPrecision(a,2):a}}},B=P,A=O(B,$,E,!1,null,null,null),j=A.exports,M=function(){var e=this,t=e._self._c;return t("div",{ref:"chart",style:e.chartStyle})},R=[],q=(a(9033),a(8903),a(1018),a(1415),a(4448),a(8871),a(6539),{props:{data:{required:!0,default:()=>{}},schema:{type:Array,required:!0},chartOpt:{type:Object,required:!1},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},data(){return{chart:null}},watch:{data:{deep:!0,handler:function(e){this.renderChart(e)}},schema:{deep:!0,handler:function(){this.renderChart(this.data)}}},mounted(){this.renderChart(this.data),this.$on("resized",this.handleResize),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){this.chart&&this.chart.resize()},validateData(e){Array.isArray(e)||this.$message({message:"柱状图的数据格式必须为数组，请检查你的数据格式"})},renderChart(e){if(!this.$refs.chart)return;let t=[],a=[];const r={};if(2===this.schema.filter((e=>e.asxAxis)).length){const i=this.schema.filter((e=>e.asxAxis)),n=i[0].alias,s=i[1].alias,o=this.schema.find((e=>!e.asxAxis)).alias;a=this.data.map((e=>(t.push(e[s]),e[n]))),a=Array.from(new Set(a)),t=Array.from(new Set(t)),t=t.map(((t,i)=>{const l=a.map((a=>{const r=e.find((e=>e[s]===t&&e[n]===a));return r?r[o]:"-"}));return t+="",r[t]={name:t,data:l,type:"bar"},t}))}else this.schema.forEach(((i,n)=>{t.push(i.alias||i.name),i.asxAxis||(r[i.name]={name:i.alias||i.name,data:[],type:"bar"}),e.forEach((e=>{i.asxAxis?a.push(e[i.name]):("float"===i.Type&&(e[i.name]=N.formatNumberWithPrecision(e[i.name],2)),r[i.name].data.push(e[i.name]))}))}));const i={legend:{bottom:0,type:"scroll",data:t},toolbox:{show:!0,top:-5,itemSize:12,feature:{saveAsImage:{show:!0},magicType:{type:["line","bar"]},restore:{show:!0},dataZoom:{show:!0}}},grid:{top:"20px",left:"45px",right:"0",bottom:"45px"},tooltip:{trigger:"axis",axisPointer:{type:"shadow"}},xAxis:{type:"category",axisLabel:{color:"#95a4bd"},axisLine:{lineStyle:{color:"#95a4bd"}},splitArea:{show:!0,interval:0},data:a},yAxis:{axisLabel:{show:!0,color:"#95a4bd",formatter:v},axisLine:{lineStyle:{color:"#95a4bd"}},splitLine:{lineStyle:{type:"dashed"}}},series:Object.values(r)};setTimeout((()=>{this.chart||(this.chart=b.init(this.$refs.chart)),this.chart.clear(),this.chart.setOption(i),this.chartOpt&&this.chart.setOption(this.chartOpt)}),0)}}}),H=q,F=O(H,M,R,!1,null,null,null),G=F.exports,V=function(){var e=this,t=e._self._c;return t("div",{ref:"chart",style:e.chartStyle})},Q=[],U={props:{data:{required:!0,default:()=>{}},schema:{type:Array,required:!0},chartOpt:{type:Object,required:!1},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},data(){return{chart:null}},watch:{data:{deep:!0,handler:function(e){this.renderChart(e)}},schema:{deep:!0,handler:function(){this.renderChart(this.data)}}},mounted(){this.renderChart(this.data),this.$on("resized",this.handleResize),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){this.chart&&this.chart.resize()},validateData(e){Array.isArray(e)||this.$message({message:"柱状图的数据格式必须为数组，请检查你的数据格式"})},renderChart(e){if(!this.$refs.chart)return;const t=[],a=[],r={};this.schema.forEach(((i,n)=>{t.push(i.alias||i.name),i.asxAxis||(r[i.name]={name:i.alias||i.name,data:[],stack:"All",type:"bar"}),e.forEach((e=>{i.asxAxis?a.push(e[i.name]):("float"===i.Type&&(e[i.name]=N.formatNumberWithPrecision(e[i.name],2)),r[i.name].data.push(e[i.name]))}))}));const i={legend:{type:"scroll",data:t,bottom:0},toolbox:{show:!0,top:-5,itemSize:12,feature:{saveAsImage:{show:!0},magicType:{type:["line","bar"]},restore:{show:!0},dataZoom:{show:!0}}},grid:{top:"20px",left:"45px",right:"0",bottom:"45px"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},xAxis:{type:"category",axisLabel:{color:"#95a4bd"},axisLine:{lineStyle:{color:"#95a4bd"}},data:a},yAxis:{axisLabel:{show:!0,color:"#95a4bd",formatter:v},axisLine:{lineStyle:{color:"#95a4bd"}},splitLine:{lineStyle:{type:"dashed"}}},series:Object.values(r)};setTimeout((()=>{this.chart||(this.chart=b.init(this.$refs.chart)),this.chart.clear(),this.chart.setOption(i),this.chartOpt&&this.chart.setOption(this.chartOpt)}),0)}}},W=U,J=O(W,V,Q,!1,null,null,null),Y=J.exports,Z=function(){var e=this,t=e._self._c;return t("div",{ref:"chart",style:e.chartStyle})},X=[],K={props:{widgetMeta:{required:!1,type:Object,default:()=>{}},data:{required:!0,default:()=>{}},schema:{type:Array,required:!0},chartOpt:{type:Object,required:!1},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},data(){return{chart:null}},watch:{data:{deep:!0,handler:function(e){this.renderChart(e)}},schema:{deep:!0,handler:function(){this.renderChart(this.data)}}},mounted(){this.renderChart(this.data),this.$on("resized",this.handleResize),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){this.chart&&this.chart.resize()},validateData(e){Array.isArray(e)||this.$message({message:"线图的数据格式必须为数组，请检查你的数据格式"})},renderChart(e){if(!this.$refs.chart)return;if(0===e.length&&this.chart)return this.chart.clear(),void this.chart.setOption({graphic:[{type:"group",left:"center",top:"center",children:[{type:"text",z:100,left:"center",top:"middle",style:{fill:"#333",text:this.noDataDesc,font:"14px Microsoft YaHei"}}]}]});const t=[],a=[],r={};this.schema.forEach(((i,n)=>{t.push(i.alias||i.name),i.asxAxis||(r[i.name]={name:i.alias||i.name,data:[],type:"bar"}),e.forEach((e=>{i.asxAxis?a.push(e[i.name]):("float"===i.Type&&(e[i.name]=N.formatNumberWithPrecision(e[i.name],2)),r[i.name].data.push(e[i.name]))}))}));const i={legend:{data:t},toolbox:{show:!0,top:-5,feature:{saveAsImage:{show:!0},restore:{show:!0},dataZoom:{show:!1}}},grid:{top:"20px",left:"80px",bottom:"10%",right:"20px"},tooltip:{trigger:"axis",axisPointer:{type:"cross"}},xAxis:{type:"value",axisLabel:{color:"#95a4bd"},axisLine:{lineStyle:{color:"#95a4bd"}}},yAxis:{type:"category",data:a,axisLabel:{show:!0,color:"#95a4bd"},axisLine:{lineStyle:{color:"#95a4bd"}},splitLine:{lineStyle:{type:"dashed"}}},series:Object.values(r)};setTimeout((()=>{this.chart||(this.chart=b.init(this.$refs.chart)),this.chart.clear(),this.chart.setOption(i),this.chartOpt&&this.chart.setOption(this.chartOpt)}),0)}}},ee=K,te=O(ee,Z,X,!1,null,null,null),ae=te.exports,re=function(){var e=this,t=e._self._c;return t("div",{ref:"chart",style:e.chartStyle})},ie=[],ne={props:{data:{required:!0,default:()=>{}},schema:{type:Array,required:!0},chartOpt:{type:Object,required:!1},chartStyle:{require:!1,type:Object,default:()=>({height:"420px"})}},data(){return{chart:null}},watch:{data:{deep:!0,handler:function(e){this.renderChart(e)}},schema:{deep:!0,handler:function(){this.renderChart(this.data)}}},mounted(){this.renderChart(this.data),this.$on("resized",this.handleResize),window.addEventListener("resize",this.handleResize)},beforeDestroy(){this.chart&&this.chart.dispose(),window.removeEventListener("resize",this.handleResize)},methods:{handleResize(){this.chart&&this.chart.resize()},validateData(e){Array.isArray(e)||this.$message({message:"线图的数据格式必须为数组，请检查你的数据格式"})},renderChart(e){if(!this.$refs.chart||!e[0])return;const t=[];let a={};0===this.schema.filter((e=>e.asxAxis)).length?(a={name:"",type:"pie",radius:"55%",center:["50%","60%"],data:[],tooltip:{formatter:e=>`${e.name}: ${e.percent}%`},itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}},e=e[0],this.schema.forEach(((r,i)=>{t.push(r.name),a.data[i]=a.data[i]||{},a.data[i].name=r.alias,a.data[i].value=e[r.name]}))):(a={name:"",type:"pie",radius:"55%",center:["50%","40%"],data:[],tooltip:{formatter:e=>`${e.name}: ${e.percent}%`},itemStyle:{emphasis:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}},this.schema.forEach((r=>{e.forEach(((e,i)=>{a.data[i]=a.data[i]||{},r.asxAxis?(t.push(e[r.name]),a.data[i].name=e[r.name]):a.data[i].value=e[r.name]}))})));const r={legend:{type:"scroll",data:t,bottom:0},toolbox:{show:!0,top:-5,itemSize:12,feature:{saveAsImage:{show:!0}}},grid:{top:"20px",left:"80px",right:"0",bottom:45},tooltip:{trigger:"item"},series:[a]};setTimeout((()=>{this.chart||(this.chart=b.init(this.$refs.chart)),this.chart.clear(),this.chart.setOption(r),this.chartOpt&&this.chart.setOption(this.chartOpt)}),0)}}},se=ne,oe=O(se,re,ie,!1,null,null,null),le=oe.exports;const ce=[{operator:"EQ",name:"等于",paramNum:1},{operator:"GT",name:"大于",paramNum:1},{operator:"LT",name:"小于",paramNum:1},{operator:"GE",name:"大于等于",paramNum:1},{operator:"LE",name:"小于等于",paramNum:1},{operator:"NE",name:"不等于",paramNum:1},{operator:"BETWEEN",name:"区间",paramNum:2},{operator:"TIMEBETWEEN",name:"区间(时间)",paramNum:3},{operator:"IN",name:"包含",paramNum:-1},{operator:"LIKE",name:"模糊匹配",paramNum:1},{operator:"NODEIDEQ",name:"关联节点ID",paramNum:4}],de=[{name:"tinyint",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"smallint",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"mediumint",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"int",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"int8",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"bigint",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"float",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"double",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"real",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"decimal",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]},{name:"timestamp",needQuotation:!1,availableFunc:["count","none"]},{name:"date",needQuotation:!0,availableFunc:["count","none"]},{name:"time",needQuotation:!0,availableFunc:["count","none"]},{name:"datetime",needQuotation:!0,availableFunc:["count","none"]},{name:"year",needQuotation:!0,availableFunc:["count","none"]},{name:"char",needQuotation:!0,availableFunc:["count","none"]},{name:"varchar",needQuotation:!0,availableFunc:["count","none"]},{name:"tinytext",needQuotation:!0,availableFunc:["count","none"]},{name:"text",needQuotation:!0,availableFunc:["count","none"]},{name:"mediumtext",needQuotation:!0,availableFunc:["count","none"]},{name:"longtext",needQuotation:!0,availableFunc:["count","none"]},{name:"integer",needQuotation:!1,availableFunc:["sum","avg","max","min","count","none"]}];function he(e,t){const a=e.map((e=>{const a={};return t.forEach((t=>{t.calculFunc,t.lable=`${t.Column}`,t.name=t.Column,t.asxAxis=t.isDimension,"int8"!==t.Type||-1===t.Column.indexOf("time")&&-1===t.Column.indexOf("date")?a[t.Column]=e[t.Column]:a[t.Column]=w()(e[t.Column]).format("YYYY-MM-DD HH:mm")})),a}));return a}const ue=[{name:"表格",icon:"chart-table",type:"table",matchRule:{description:"任意维度和数值",isUsable(e,t){return!0}},componentName:"DataTable",dataTransfer(e,t){const a=e.map((e=>{const a={};return t.forEach((t=>{t.calculFunc,t.name=`${t.Column}`,"int8"!==t.Type||-1===t.Column.indexOf("time")&&-1===t.Column.indexOf("date")?a[t.Column]=e[t.Column]:a[t.Column]=w()(e[t.Column]).format("YYYY-MM-DD HH:mm")})),a}));return a}},{name:"折线图",icon:"chart-line",type:"line",matchRule:{description:"1 或 2个维度;1或多个数值",isUsable(e,t){return(1===e.length||2===e.length)&&t.length>=1}},componentName:"lineChart",dataTransfer:he},{name:"柱状图",icon:"chart-bar",type:"bar",matchRule:{description:"1 或 2个维度;1或多个数值",isUsable(e,t){return(1===e.length||2===e.length)&&t.length>=1}},componentName:"BarChart",dataTransfer:he},{name:"堆积柱状图",icon:"stack-bar",type:"stackBar",matchRule:{description:"1 或 2个维度;2或多个数值",isUsable(e,t){return(1===e.length||2===e.length)&&t.length>=2}},componentName:"StackBarChart",dataTransfer:he},{name:"饼图",icon:"chart-pie",type:"pie",matchRule:{description:"1个维度1个数值;0个维度多个数值",isUsable(e,t){return 1===e.length&&1===t.length||0===e.length&&t.length>=1}},componentName:"PieChart",dataTransfer:he},{name:"条形图",icon:"horizontal-bar",type:"horizontalBar",matchRule:{description:"1个维度;1或多个数值",isUsable(e,t){return(1===e.length||2===e.length)&&t.length>=1}},componentName:"HorizontalBar",dataTransfer:he}];var pe=ue;function me(e){let t;return t=e.indexOf("(")>=0?de.find((t=>t.name===e.split("(")[0])):de.find((t=>t.name===e)),t||(t={name:e,needQuotation:!1}),t}function fe(e){let t,a=e.value;return me(e.colType).needQuotation&&(a=r(e.value)),t=1===e.operatorParamNum||4===e.operatorParamNum?`${e.filteCol} ${e.filterOperator} ${a.value1}`:2===e.operatorParamNum?`${e.filteCol} ${e.filterOperator} ${a.value1} and ${a.value2}`:3===e.operatorParamNum?`${e.filteCol} ${e.filterOperator} ${w()(a.timeValue[0]).format("YYYY-MM-DD HH:mm:ss")} and ${w()(a.timeValue[1]).format("YYYY-MM-DD HH:mm:ss")}`:`${e.filteCol} ${e.filterOperator} ('${a.arrValue.join(",")}')`,t;function r(e){return{value1:`'${e.value1}'`,value2:`'${e.value2}'`,arrValue:e.arrValue.map((e=>`'${e}'`))}}}const ge={debug:!1,state:{dimensions:[],caculCols:[],allCols:[],treeNodeModel:[]},setTreeNodeModel(e){this.state.treeNodeModel=e},addDimensionAction(e){this.debug&&console.log("adddimensionAction triggered with",e),e.isDimension=!0},deleteDimensionAction(e){this.debug&&console.log("deleteDimensionAction triggered with",e);const t=this.state.dimensions.findIndex((t=>t.Column===e.Column));this.state.dimensions[t].isDimension=!1,this.state.dimensions.splice(t,1)},setDimensionsAction(e){this.debug&&console.log("setDimensionsAction triggered"),this.state.dimensions=e},addCaculColAction(e){this.debug&&console.log("addCaculColAction triggered with",e);const t=me(e.Type),a=this.state.caculCols.findIndex((t=>t.Column===e.Column)),r={Column:e.Column,calculFunc:t,Type:e.Type};this.state.caculCols.splice(a,1,r)},deleteCaculColAction(e){this.debug&&console.log("deleteCaculColAction triggered with",e);const t=this.state.caculCols.findIndex((t=>t.Column===e.Column));this.state.caculCols.splice(t,1)},setCaculColsAction(e){this.debug&&console.log("setCaculColsAction triggered"),this.state.caculCols=e},setAllColsAction(e){this.debug&&console.log("setAllColsAction triggered width",e),this.state.allCols=e}};var be=ge,ve={components:{lineChart:z,DataTable:j,BarChart:G,StackBarChart:Y,PieChart:le,HorizontalBar:ae},props:{data:{type:Array,required:!0},schema:{type:Array,required:!0},chartStyle:{require:!1,type:Object},chartType:{type:String,default:"table"},isEditMode:{type:Boolean,default:!0}},data(){return{chartTypeList:pe}},computed:{allSelected(){return be.state.caculCols.concat(be.state.dimensions)},chartData(){return this.currentType.dataTransfer?this.currentType.dataTransfer(this.data,this.schema):this.data},currentType(){return pe.find((e=>e.type===this.chartType))}},watch:{allSelected:{deep:!0,handler(){this.currentType.matchRule.isUsable(be.state.dimensions,be.state.caculCols)||this.$emit("update:chartType","table")}}},methods:{isUsable(e){return e.matchRule.isUsable(be.state.dimensions,be.state.caculCols)},switchChartType(e){e.matchRule.isUsable(be.state.dimensions,be.state.caculCols)&&this.$emit("update:chartType",e.type)}}},ye=ve,Ce=(a(6663),O(ye,p,m,!1,null,"18370355",null)),_e=Ce.exports,xe=require("@omega/http");const we=new xe.HttpBase({},{responseType:"json"}),Se=new xe.HttpBase({auth:!1,silent:!0});function Te(e){const t=Ra.apiOptions,a=Object.keys(t.prefix);if(a.length){const r=new RegExp(`{{(${a.join("|")})}}`),i=e.url;e.url=i.replace(r,((e,a)=>t.prefix[a]))}return t.requestInterceptor?t.requestInterceptor(e):e}function Ie(e,t){if(0===arguments.length)return null;const a=t||"{y}-{m}-{d} {h}:{i}:{s}";let r;"object"===typeof e?r=e:("string"===typeof e&&/^[0-9]+$/.test(e)&&(e=parseInt(e)),"number"===typeof e&&10===e.toString().length&&(e*=1e3),r=new Date(e));const i={y:r.getFullYear(),m:r.getMonth()+1,d:r.getDate(),h:r.getHours(),i:r.getMinutes(),s:r.getSeconds(),a:r.getDay()},n=a.replace(/{(y|m|d|h|i|s|a)+}/g,((e,t)=>{let a=i[t];return"a"===t?["日","一","二","三","四","五","六"][a]:(e.length>0&&a<10&&(a="0"+a),a||0)}));return n}function Ne(e,t){return e.modelLabel=t,[e]}function De(){let e=localStorage.getItem("projectId"),t=e?parseInt(e):-1;return t}we.interceptors.request.use(Te),Se.interceptors.request.use(Te);var ke=require("@omega/auth"),Oe=a.n(ke);function Le(e){return e=Ne(e,"dashboard"),e[0].content={},e[0].content.projectId=De(),we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:e})}function ze(e){return e=Ne(e,"dashboard"),e[0].content.projectId=De(),we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:e})}function $e(e){return we({url:"{{model-service}}/v1/query",method:"POST",data:{rootLabel:"dashboard",rootCondition:{filter:{expressions:[{limit:e,operator:"EQ",prop:"name"}]}}}})}function Ee(e){return we({url:"{{model-service}}/v1/dashboard",method:"DELETE",data:[e.id]})}function Pe(){let e=Oe().user.getUserCustomConfig();return!!C().isNil(e)||4===e.userType}function Be(e){let t=e;if(!Oe().user.isRoot()&&Pe()){let e=t.data,a=Oe().user.getUserCustomConfig().dashboard;C().isNil(a)&&(a=[]),t.data=e.filter((e=>-1!==a.indexOf(e.id)))}return t}function Ae(e){let t=[],a=e.data,r=De();return t=0===r?a.filter((e=>0===e.content.projectId)):r>0?a.filter((e=>e.content.projectId===r)):a,e.data=t,e}function je(){return we({url:"{{model-service}}/v1/query",method:"POST",responseType:"json",data:{rootID:0,rootLabel:"dashboard"},transformResponse:[Be,Ae]})}function Me(e){let t=[{id:e.dashboard_id,modelLabel:"dashboard",chart_model:[{id:e.chart_id,modelLabel:"chart"}]}];return we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:t})}function Re(e){let t=[{id:e.dashboard_id,modelLabel:"dashboard",chart_model:[{id:e.chart_id,modelLabel:"chart",move_from:{id:e.old_chart_id,modelLabel:"chart"}}]}];return we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:t})}function qe(e){return we({url:"{{model-service}}/v1/query",method:"POST",data:{rootCondition:{treeNode:{id:e,modelLabel:"dashboard"}},rootLabel:"chart"}})}function He(e){return we({url:"{{model-service}}/v1/query",method:"POST",data:{rootCondition:{treeNode:{id:e,modelLabel:"chart"}},rootLabel:"dashboard"}})}function Fe(e){let t=[{id:e.dashboard_id,modelLabel:"dashboard",chart_model:[{id:e.chart_id,modelLabel:"chart",moveout_flag:!0}]}];return we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:t})}function Ge(e){let t={};return t.userid=e.userId,t.userorder=e.order.join("|"),t.modelLabel="dashboardorder",we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:[t]})}function Ve(e){return e=Ne(e,"chart"),e[0].content.projectId=De(),we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:e})}function Qe(e){return e=Ne(e,"chart"),e[0].content.projectId=De(),we({url:"{{model-service}}/v1/write/hierachy",method:"POST",data:e})}function Ue(e){return we({url:"{{model-service}}/v1/query",method:"POST",data:{rootID:e,rootLabel:"chart"}})}function We(e){return we({url:"{{model-service}}/v1/chart",method:"DELETE",data:[e.id]})}function Je(e){let t=[],a=e.data,r=De();return t=0===r?a.filter((e=>0===e.content.projectId)):r>0?a.filter((e=>e.content.projectId===r)):a,e.data=t,e}function Ye(){return we({url:"{{model-service}}/v1/query",method:"POST",data:{rootID:0,rootLabel:"chart"},transformResponse:[Je]})}function Ze({dataSrc:e,selectedCalcul:t,selectedDimension:a,orderByStrs:r,filters:i,treeNode:n,limit:s}){const o={rootID:0};return o.rootLabel=e,o.rootCondition={},o.rootCondition.filter=Xe(i),o.rootCondition.orders=Ke(r),o.rootCondition.treeNode=n,o.rootCondition.page={index:0,limit:s},o}function Xe(e){var t=[];if(C().isArray(e)){for(let a=0;a<e.length;a++){let r="";if(1===e[a].operatorParamNum||4===e[a].operatorParamNum?r=e[a].value.value1:2===e[a].operatorParamNum?r=[e[a].value.value1,e[a].value.value2]:-1===e[a].operatorParamNum?r=e[a].value.arrValue:3===e[a].operatorParamNum&&(r=e[a].value.timeValue),""!==r&&!C().isNull(r)&&!C().isUndefined(r)){let i=e[a].filterOperator;"TIMEBETWEEN"===i&&(i="BETWEEN"),"NODEIDEQ"===i&&(i="EQ"),t.push({prop:e[a].filteCol,operator:i,limit:r})}}return{expressions:t}}}function Ke(e){return e.map(((e,t)=>{const a=e.split(" ");return{orderType:a[1],priority:t,propertyLabel:a[0]}}))}const et="v1";function tt(e){return e}function at(e,t){return we({url:`{{model-service}}/${et}/query`,method:"POST",headers:{hideNotice:t},transformResponse:[tt],timeout:6e4,data:e})}function rt(){return we({url:"{{model-service}}-meta/v1/models/",method:"GET"})}function it(e){return we({url:`{{model-service}}-meta/v1/models/label/${e}`,method:"GET"})}var nt=function(){var e=this,t=e._self._c;return t("div",[t("el-card",{staticClass:"panel-header",staticStyle:{"margin-bottom":"20px"},attrs:{"body-style":"padding:0;"}},[t("div",{staticStyle:{display:"flex","justify-content":"space-between"},attrs:{slot:"header"},slot:"header"},[t("span",[t("span",{staticClass:"back-button",on:{click:e.backToDashboard}},[t("i",{staticClass:"el-icon-back"}),t("span",[e._v("返回")])]),0!==this.currentChartId?t("span",[e._v("编辑图表")]):t("span",[e._v("新建图表")])]),t("span",["custom"===this.componentType?t("el-button",{staticStyle:{float:"right",margin:"0 10px 0 0"},attrs:{size:"mini",type:"primary",icon:"el-icon-download"},on:{click:e.handleDownload}}):e._e(),0!==this.currentChartId?t("el-button",{staticStyle:{float:"right",margin:"0 10px 0 0"},attrs:{size:"mini",type:"primary"},on:{click:e.handleLinkDB}},[e._v(" 添加到看板 ")]):e._e(),t("el-button",{staticStyle:{float:"right",margin:"0 10px 0 0"},attrs:{size:"mini",type:"primary",icon:"el-icon-save"},on:{click:e.handleSave}},[e._v(" 保存 ")]),0!==this.currentChartId?t("el-button",{staticStyle:{float:"right",margin:"0 10px 0 0"},attrs:{size:"mini",type:"primary"},on:{click:e.handleCreate}},[e._v(" 新建图表 ")]):e._e(),t("el-button",{staticStyle:{float:"right","margin-right":"20px"},attrs:{size:"mini",type:"primary"},on:{click:e.viewAllChart}},[e._v(" 我的图表 ")])],1)])]),t("el-tabs",{attrs:{type:"card"},model:{value:e.componentType,callback:function(t){e.componentType=t},expression:"componentType"}},[t("el-tab-pane",{attrs:{name:"predefined",disabled:e.disablePrePanel,label:"预定义图表"}},[t("preDefinedCfg",{attrs:{isEdit:0!==this.currentChartId,preCompCfg:e.preCompCfg},on:{"update:preCompCfg":function(t){e.preCompCfg=t},"update:pre-comp-cfg":function(t){e.preCompCfg=t}}})],1),t("el-tab-pane",{attrs:{name:"custom",disabled:e.disableCustomPanel,label:"自定义图表"}},[t("div",{staticClass:"app-container",staticStyle:{display:"flex"}},[t("el-card",{staticStyle:{width:"400px","margin-right":"20px","text-align":"center"},attrs:{id:"dataPanel"}},[t("data-panel",{ref:"dataPanel",attrs:{"result-loading":e.loading,"data-src":e.dataSrc,currentChartId:e.currentChartId},on:{getTable:e.getTableSchma,change:e.handleDataSrcChange}})],1),t("el-card",{staticStyle:{width:"100%"},attrs:{"body-style":"padding: 10px 20px;"}},[t("div",{staticClass:"form-wrapper"},[t("el-form",{staticClass:"analysis-form",attrs:{id:"formPanel",size:"mini"}},[t("el-form-item",{attrs:{id:"dimensionInput",label:"维度(X轴)"}},[t("draggable",{staticClass:"draggable-wrapper",attrs:{group:{name:"col",pull:!0,put:!0}},on:{change:e.handleDimensionChange},model:{value:e.sharedState.dimensions,callback:function(t){e.$set(e.sharedState,"dimensions",t)},expression:"sharedState.dimensions"}},e._l(e.currentDimensions,(function(a){return t("el-tag",{key:a.Column,staticClass:"draggable-item",attrs:{size:"small",closable:""},on:{close:function(t){return e.handleCloseDimensionTag(a)}}},[e._v(" "+e._s(a.alias)+" ")])})),1)],1),t("el-form-item",{attrs:{id:"fieldInput",label:"数值(Y轴)"}},[t("draggable",{staticClass:"draggable-wrapper",attrs:{group:{name:"col",pull:!0,put:!0}},on:{change:e.handleColChange},model:{value:e.sharedState.caculCols,callback:function(t){e.$set(e.sharedState,"caculCols",t)},expression:"sharedState.caculCols"}},e._l(e.currentCacuCols,(function(a){return t("el-tag",{key:a.Column,staticClass:"draggable-item",attrs:{size:"small",closable:""},on:{close:function(t){return e.handleCloseColTag(a)}}},[e._v(" "+e._s(a.alias)+" ")])})),1)],1),t("orderPanel",{attrs:{tableSchma:e.tableSchma},model:{value:e.orderByStrs,callback:function(t){e.orderByStrs=t},expression:"orderByStrs"}}),t("filterPanel",{attrs:{filters:e.currentFilters,disabled:!e.allSelected||0===e.allSelected.length,tableSchma:e.tableSchma},on:{"update:filters":function(t){e.currentFilters=t},change:e.handleAddFilter}}),t("treeNodeSelect",{attrs:{treeNode:e.currentNode},on:{"update:treeNode":function(t){e.currentNode=t},"update:tree-node":function(t){e.currentNode=t}}}),t("el-form-item",[t("div",{staticClass:"limit-input"},[t("span",{directives:[{name:"show",rawName:"v-show",value:!e.editLimit,expression:"!editLimit"}]},[e._v(" 查询前"+e._s(e.limit)+"条数据 "),t("el-button",{attrs:{type:"text"},on:{click:function(t){e.editLimit=!0}}},[e._v(" 修改 ")])],1),t("span",{directives:[{name:"show",rawName:"v-show",value:e.editLimit,expression:"editLimit"}]},[t("el-input-number",{staticStyle:{width:"200px"},attrs:{step:1,"step-strictly":"",min:1,max:1e4,disabled:e.loading,size:"mini",placeholder:"数据条数"},on:{blur:function(t){e.editLimit=!1}},model:{value:e.limit,callback:function(t){e.limit=t},expression:"limit"}}),t("el-button",{attrs:{size:"mini"},on:{click:function(t){e.editLimit=!1}}},[e._v(" 确认 ")])],1)])])],1),t("el-form",{staticClass:"chart-form",attrs:{size:"mini","label-position":"top"}},[t("el-form-item",{attrs:{label:"图表名称:"}},[t("el-input",{attrs:{size:"mini",placeholder:"未命名",maxlength:20,"show-word-limit":""},model:{value:e.chartName,callback:function(t){e.chartName=t},expression:"chartName"}})],1),t("el-form-item",{attrs:{label:"图表描述:"}},[t("el-input",{attrs:{size:"mini",placeholder:"请输入图表描述",maxlength:20,"show-word-limit":""},model:{value:e.chartDesc,callback:function(t){e.chartDesc=t},expression:"chartDesc"}})],1)],1)],1),t("visualize-panel",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{id:"vizPanel",data:e.result,"chart-type":e.chartType,schema:e.allSelected},on:{"update:chartType":function(t){e.chartType=t},"update:chart-type":function(t){e.chartType=t}}})],1)],1)])],1),t("el-dialog",{attrs:{title:"我的图表",visible:e.showMyCharts},on:{"update:visible":function(t){e.showMyCharts=t}}},[t("el-table",{attrs:{data:e.myChartList,"max-height":500}},[t("el-table-column",{attrs:{type:"index"}}),t("el-table-column",{attrs:{label:"名称",width:"200",prop:"chart_name"}}),t("el-table-column",{attrs:{label:"描述",prop:"description"}}),t("el-table-column",{attrs:{label:"操作",width:"240",align:"center"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{attrs:{size:"mini",type:"warning"},on:{click:function(t){return e.switchChart(a.row)}}},[e._v(" 编辑 ")])]}}])})],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){e.showMyCharts=!1}}},[e._v(" 取消 ")])],1)],1),t("el-dialog",{attrs:{title:"看板列表",width:"500px",visible:e.showDashboards},on:{"update:visible":function(t){e.showDashboards=t}}},[t("div",{staticStyle:{"text-align":"center"}},[t("el-select",{staticStyle:{width:"350px"},attrs:{size:"small"},model:{value:e.selectedDb,callback:function(t){e.selectedDb=t},expression:"selectedDb"}},e._l(e.dashboardList,(function(a){return t("el-option",{key:a.id,attrs:{label:a.name,disabled:e.isDbDisbaled(a),value:a.id}})})),1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:e.linkDb}},[e._v(" 确定 ")]),t("el-button",{attrs:{type:"primary",plain:"",size:"small"},on:{click:function(t){e.showDashboards=!1}}},[e._v(" 取消 ")])],1)])],1)},st=[],ot=function(){var e=this,t=e._self._c;return t("el-form-item",{attrs:{label:"筛选"}},[e._l(e.currentFilters,(function(a,r){return t("el-tag",{key:r,attrs:{closable:""},on:{close:function(t){return e.handleClosefilter(r)},click:function(t){return e.handleEditFilter(a)}}},[e._v(" "+e._s(e.generateFilterSentence(a))+" ")])})),t("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.disabled,type:"primary",size:"mini"},on:{click:function(t){e.visible=!0}}},[e._v(" 添加筛选条件 ")]),t("el-dialog",{attrs:{visible:e.visible,width:"700px",title:"添加筛选项"},on:{"update:visible":function(t){e.visible=t}}},[t("el-form",{attrs:{"label-width":"150px"}},[t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请选择筛选字段"}},[t("el-select",{attrs:{size:"mini",placeholder:"选择筛选字段"},model:{value:e.filteCol,callback:function(t){e.filteCol=t},expression:"filteCol"}},e._l(e.sharedState.allCols,(function(e){return t("el-option",{key:e.Column,attrs:{label:e.alias,value:e.Column}})})),1)],1),t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请选择筛选方式"}},[t("el-select",{attrs:{size:"mini",placeholder:"选择筛选方式"},model:{value:e.filterOperator,callback:function(t){e.filterOperator=t},expression:"filterOperator"}},e._l(e.filterOperatorOpts,(function(e){return t("el-option",{key:e.name,attrs:{label:e.name,value:e.operator}})})),1)],1),-1===e.currentOperatorParamNum?t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请输入判断条件值"}},[e._l(e.arrValue,(function(a,r){return t("el-tag",{key:r,attrs:{closable:""},on:{close:e.handleRemove}},[e._v(" "+e._s(a)+" ")])})),t("el-input",{staticStyle:{width:"200px"},attrs:{size:"mini",type:"text"},model:{value:e.value3,callback:function(t){e.value3=t},expression:"value3"}}),t("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v(" 新增 ")])],2):3===e.currentOperatorParamNum?t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请输入判断条件值"}},[t("CetDateSelect",{staticStyle:{width:"440px"},on:{date_out:e.timeChange}})],1):4===e.currentOperatorParamNum?t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请输入判断条件值"}},[t("nodeIdSelect",{attrs:{"node-id":e.value1},on:{"update:nodeId":function(t){e.value1=t},"update:node-id":function(t){e.value1=t}}})],1):t("el-form-item",{staticClass:"el-form-item",attrs:{label:"请输入判断条件值"}},[t("el-input",{staticStyle:{width:"200px"},attrs:{size:"mini",type:"text"},model:{value:e.value1,callback:function(t){e.value1=t},expression:"value1"}}),t("span",{directives:[{name:"show",rawName:"v-show",value:2===e.currentOperatorParamNum,expression:"currentOperatorParamNum === 2"}]},[e._v("~")]),t("el-input",{directives:[{name:"show",rawName:"v-show",value:2===e.currentOperatorParamNum,expression:"currentOperatorParamNum === 2"}],staticStyle:{width:"200px"},attrs:{size:"mini",type:"text"},model:{value:e.value2,callback:function(t){e.value2=t},expression:"value2"}})],1)],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{plain:"",size:"mini",type:"primary"},on:{click:e.handleConfirm}},[e._v(" 确定 ")]),t("el-button",{attrs:{plain:"",size:"mini",type:"primary"},on:{click:function(t){e.visible=!1}}},[e._v(" 取消 ")])],1)],1)],2)},lt=[],ct=function(){var e=this,t=e._self._c;return t("div",[t("span",{staticStyle:{"padding-right":"15px"}},[e._v(e._s(e.currentTreeNodeId))]),t("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.disabled,type:"primary",size:"mini"},on:{click:function(t){e.visible=!0}}},[e._v(" 选择节点 ")]),t("el-dialog",{attrs:{"append-to-body":"",visible:e.visible,width:"500px",title:"选择节点"},on:{"update:visible":function(t){e.visible=t},open:e.openHandler}},[t("div",{staticStyle:{height:"500px"}},[t("CetGiantTree",e._g(e._b({},"CetGiantTree",e.CetGiantTree_selectNode,!1),e.CetGiantTree_selectNode.event))],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"mini",type:"primary",plain:""},on:{click:e.handleConfirm}},[e._v("确定")]),t("el-button",{attrs:{size:"mini",type:"primary",plain:""},on:{click:function(t){e.visible=!1}}},[e._v("取消")])],1)]),t("CetInterface",e._g(e._b({attrs:{data:e.CetInterface_tree.data,"dynamic-input":e.CetInterface_tree.dynamicInput},on:{"update:data":function(t){return e.$set(e.CetInterface_tree,"data",t)},"update:dynamicInput":function(t){return e.$set(e.CetInterface_tree,"dynamicInput",t)},"update:dynamic-input":function(t){return e.$set(e.CetInterface_tree,"dynamicInput",t)}}},"CetInterface",e.CetInterface_tree,!1),e.CetInterface_tree.event))],1)},dt=[],ht={components:{},props:{disabled:{default:!1},nodeId:{type:Number}},data(){return{visible:!1,currentTreeNodeId:"",CetGiantTree_selectNode:{inputData_in:[],checkedNodes:[],selectNode:{},setting:{check:{enable:!1},data:{simpleData:{enable:!0,idKey:"tree_id"}}},event:{Created_out:this.CetGiantTree_selectNode_created_out,currentNode_out:this.CetGiantTree_selectNode_currentNode_out,checkedNodes_out:this.CetGiantTree_selectNode_checkedNodes_out}},CetInterface_tree:{queryMode:"trigger",data:[],dataConfig:{queryFunc:"",modelLabel:"",dataIndex:[],modelList:[],filters:[],treeReturnEnable:!0,hasQueryNode:!1,hasQueryId:!1},queryNode_in:null,queryId_in:-1,queryTrigger_in:(new Date).getTime(),dynamicInput:{},page_in:null,event:{result_out:this.CetInterface_tree_result_out,finishTrigger_out:this.CetInterface_tree_finishTrigger_out,failTrigger_out:this.CetInterface_tree_failTrigger_out,totalNum_out:this.CetInterface_tree_totalNum_out}}}},watch:{nodeId(e){e?(this.currentTreeNodeId=e,this.CetGiantTree_selectNode.selectNode={}):(this.currentTreeNodeId="",this.CetGiantTree_selectNode.selectNode={})}},methods:{openHandler(){this.setTreeModelList(),this.CetInterface_tree.queryTrigger_in=(new Date).getTime()},setTreeModelList(){const e=C().cloneDeep(be.state.treeNodeModel);this.CetInterface_tree.dataConfig.modelLabel=e[0],this.CetInterface_tree.dataConfig.modelList=e.splice(1)},handleConfirm(){this.currentTreeNodeId?(this.visible=!1,this.$emit("update:nodeId",this.currentTreeNodeId)):this.$message({type:"warning",message:"请选择节点"})},CetGiantTree_selectNode_created_out(e,t){},CetGiantTree_selectNode_currentNode_out(e){this.currentTreeNodeId=e.id},CetGiantTree_selectNode_checkedNodes_out(e){},CetInterface_tree_finishTrigger_out(e){},CetInterface_tree_failTrigger_out(e){},CetInterface_tree_totalNum_out(e){},CetInterface_tree_result_out(e){this.CetGiantTree_selectNode.inputData_in=this._.cloneDeep(e)}}},ut=ht,pt=(a(920),O(ut,ct,dt,!1,null,"34eb7e6b",null)),mt=pt.exports,ft={components:{nodeIdSelect:mt},props:{disabled:{default:!1},filters:{type:Array}},data(){return{currentNode:null,visible:!1,filterOperatorOpts:ce,filterStrs:[],filteCol:void 0,filterOperator:void 0,value1:void 0,value2:void 0,value3:void 0,timeValue:void 0,arrValue:[],currentFilters:[],sharedState:be.state,pickerOptions:{firstDayOfWeek:1}}},computed:{currentOperatorParamNum(){const e=ce.find((e=>e.operator===this.filterOperator));return e?e.paramNum:1}},watch:{filters:{deep:!0,handler(e){this.currentFilters=e}},testValue(e){console.log(this.$moment(e).startOf("week"))}},methods:{timeChange(e){this.timeValue=e},handleClosefilter(e){this.currentFilters.splice(e,1),this.handleSubmit()},handleEditFilter(e){},handleAdd(){this.arrValue.push(this.value3),this.value3=void 0},handleRemove(e){this.arrValue.splice(this.arrValue.indexOf(e),1)},handleConfirm(){if(!this.filteCol||!this.filterOperator)return void this.$message({type:"warning",message:"筛选字段和筛选方式不可为空"});const e={filteCol:this.filteCol,colType:this.sharedState.allCols.find((e=>e.Column===this.filteCol)).Type,filterOperator:this.filterOperator,value:{value1:this.value1,value2:this.value2,arrValue:this.arrValue,timeValue:this.timeValue},operatorParamNum:this.operatorParamNum(this.filterOperator)},t=["boolean","string","jsonb"];if(!t.includes(e.colType)){const t=parseFloat(this.value1);isNaN(t)||(e.value.value1=t);const a=parseFloat(this.value2);isNaN(a)||(e.value.value2=a);const r=[];this.arrValue.forEach((e=>{const t=parseFloat(e);isNaN(t)||r.push(t)})),e.value.arrValue=r}const a=this.currentFilters.findIndex((e=>e.filteCol===this.filteCol&&e.filterOperator===this.filterOperator));a>=0?this.currentFilters.splice(a,1,e):this.currentFilters.push(e),this.handleSubmit()},handleSubmit(){const e=this.currentFilters.map(fe);this.$emit("change",e.join(" and ")),this.$emit("update:filters",this.currentFilters),this.visible=!1},generateFilterSentence(e){var t=fe(e),a=this.sharedState.allCols.find((e=>e.Column===t.split(" ")[0]));if(a){const e=C().cloneDeep(t).split(" ");e.shift();const r=e.join(" ");return a.alias+" "+r}},operatorParamNum(e){const t=ce.find((t=>t.operator===e));return t?t.paramNum:1},needQuotation(e){const t=this.sharedState.allCols.find((t=>t.Column===e)).Type;return t.indexOf("(")>=0?de.find((e=>e.name===t.split("(")[0].toLowerCase())).needQuotation:de.find((e=>e.name===t.toLowerCase())).needQuotation},addQuotation(e){return{value1:`'${e.value1}'`,value2:`'${e.value2}'`,arrValue:e.arrValue.map((e=>`'${e}'`))}}}},gt=ft,bt=(a(992),O(gt,ot,lt,!1,null,"5ccc6782",null)),vt=bt.exports,yt=function(){var e=this,t=e._self._c;return t("el-form-item",{attrs:{label:"排序"}},[t("draggable",{staticStyle:{display:"inline-block"},attrs:{group:{name:"orderBy",pull:!1,put:!1}},model:{value:e.orderByStrs,callback:function(t){e.orderByStrs=t},expression:"orderByStrs"}},e._l(e.myOrderByStrs,(function(a,r){return t("el-tag",{key:r,attrs:{closable:"",size:"small"},on:{close:e.handleCloseOrderBy}},[e._v(e._s(a))])})),1),t("el-cascader",{staticStyle:{width:"120px"},attrs:{options:e.orderByOption,disabled:0===e.orderByOption.length,size:"mini",placeholder:"选择排序方式"},on:{change:e.handleOrderByChange},model:{value:e.orderBy,callback:function(t){e.orderBy=t},expression:"orderBy"}})],1)},Ct=[],_t={components:{draggable:c()},props:{value:{required:!0,type:Array},tableSchma:{required:!0}},data(){return{orderBy:[]}},computed:{allSelected(){return be.state.caculCols.concat(be.state.dimensions)},orderByStrs:{set(e){this.$emit("input",e)},get(){return this.value}},myOrderByStrs(){var e=[];if(this.tableSchma)for(let t=0;t<this.orderByStrs.length;t++)e[t]="按 "+this.tableSchma.find((e=>e.Column===this.orderByStrs[t].split(" ")[0])).alias+" "+this.descOrAsc(this.orderByStrs[t].split(" ")[1]);return e},orderByOption(){return this.allSelected.map((e=>({value:e.Column,label:e.alias,children:[{value:"desc",label:"降序"},{value:"asc",label:"升序"}]})))}},watch:{"store.state.dimensions":function(e){this.watchHandler(e)},"store.state.caculCols":function(e){this.watchHandler(e)}},methods:{descOrAsc(e){var t="desc"===e?"降序":"升序";return t},watchHandler(e){this.orderByStrs.forEach(((t,a)=>{const r=t.split(" ")[0];e.findIndex((e=>e.Column===r))||this.orderByStrs.splice(a,1)}))},handleOrderByChange(e){this.orderBy=[];const t=this.orderByStrs.findIndex((t=>t.indexOf(e[0])>=0));t>=0?this.orderByStrs.splice(t,1,`${e[0]} ${e[1]}`):this.orderByStrs.push(`${e[0]} ${e[1]}`)},handleCloseOrderBy(e){this.orderByStrs.splice(this.orderByStrs.indexOf(e),1)}}},xt=_t,wt=O(xt,yt,Ct,!1,null,null,null),St=wt.exports,Tt=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{staticClass:"panel",staticStyle:{"text-align":"left"},attrs:{"label-position":"top"}},[t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.dataSrcVisible,expression:"dataSrcVisible"}],attrs:{label:"数据源："}},[t("el-select",{staticStyle:{width:"200px"},attrs:{size:"mini",filterable:"",placeholder:"选择数据源",clearable:""},on:{change:e.handleDataSrcChange},model:{value:e.selectedTable,callback:function(t){e.selectedTable=t},expression:"selectedTable"}},e._l(e.dataSourceList,(function(e){return t("el-option",{key:e.id,attrs:{label:e.alias,value:e.label}})})),1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:!e.dataSrcVisible,expression:"!dataSrcVisible"}],attrs:{label:"数据源："}},[t("span",{staticStyle:{"font-size":"12px","margin-right":"5px"}},[e._v(" "+e._s(e.selectedTableName)+" ")]),t("el-button",{attrs:{type:"text",size:"mini"},on:{click:e.editDataSrc}},[e._v("修改")])],1),t("el-form-item",{staticClass:"property-list",attrs:{label:"字段(拖拽到维度或者数值栏)："}},[t("draggable",{directives:[{name:"loading",rawName:"v-loading",value:e.schemaLoading,expression:"schemaLoading"}],attrs:{group:{name:"col",pull:"clone",put:!1},move:e.handleMove},model:{value:e.tableSchema,callback:function(t){e.tableSchema=t},expression:"tableSchema"}},e._l(e.tableSchema,(function(a){return t("div",{key:a.Column,staticClass:"drag-list-item"},[t("i",{staticClass:"el-icon-rank item-i text-ZS"}),e._v(" "+e._s(a.alias)+" ")])})),0)],1)],1)],1)},It=[],Nt={components:{draggable:c()},props:{resultLoading:{default:!1},dataSrc:{required:!0},currentChartId:{required:!0}},data(){return{schemaLoading:!1,dataSourceList:[],selectedTable:void 0,tableSchema:void 0,dataSrcVisible:0===this.currentChartId,existWarning:null,selectedTableName:void 0}},computed:{allSelected(){return be.state.dimensions.concat(be.state.caculCols)}},created(){this.getModels()},methods:{getModels(){rt().then((e=>{let t=e.data,a=t.sort(((e,t)=>e.alias===t.alias?0:e.alias<t.alias?-1:1));this.dataSourceList=a.filter((e=>"enumeration"!==e.constructionType)),this.dataSourceList.find((e=>e.label===this.selectedTable))&&(this.selectedTableName=this.dataSourceList.find((e=>e.label===this.selectedTable)).alias)}))},initWithDataSrc(e){e?(this.selectedTable=e,this.dataSourceList.find((e=>e.label===this.selectedTable))?this.selectedTableName=this.dataSourceList.find((e=>e.label===this.selectedTable)).alias:this.selectedTableName=void 0,this.fetchSchema()):(this.selectedTable=e,this.tableSchema=[],this.dataSrcVisible=!0)},editDataSrc(){this.dataSrcVisible=!0,this.selectedTable=void 0},handleDataSrcChange(){this.selectedTableName=this.dataSourceList.find((e=>e.label===this.selectedTable)).alias,this.dataSrcVisible=!1,this.fetchSchema(),be.setAllColsAction([]),this.$emit("change",this.selectedTable)},fetchSchema(){this.selectedTable?(this.schemaLoading=!0,it(this.selectedTable).then((e=>{this.schemaLoading=!1,this.tableSchema=e.data.propertyList.map(((e,t)=>({Column:e.propertyLabel,Type:e.dataType,id:e.id,alias:e.alias}))),be.setAllColsAction(this.tableSchema),this.$emit("getTable",this.tableSchema)}))):this.tableSchema=[]},handleCloseDialog(e){this.selectedTable||this.$message({type:"warning",message:"You Need Select Data Source First."}),e()},handleMove(e,t){if(this.allSelected.find((t=>t.Column===e.draggedContext.element.Column)))return this.existWarning||(this.existWarning=this.$message({type:"warning",message:"该字段已存在, 请不要重复添加",onClose:e=>{this.existWarning=null}})),!1}}},Dt=Nt,kt=(a(5786),O(Dt,Tt,It,!1,null,"0dedb01c",null)),Ot=kt.exports,Lt=function(){var e=this,t=e._self._c;return t("el-form-item",{attrs:{label:"节点选择(模型接口treeNode)"}},[e.nodeName?t("el-tag",{attrs:{closable:""},on:{close:function(t){return e.handleClose()}}},[e._v(e._s(e.nodeName))]):e._e(),t("el-button",{staticStyle:{width:"120px"},attrs:{disabled:e.disabled,type:"primary",size:"mini"},on:{click:function(t){e.visible=!0}}},[e._v("选择节点")]),t("el-dialog",{attrs:{"append-to-body":"",visible:e.visible,width:"500px",title:"选择节点"},on:{"update:visible":function(t){e.visible=t},open:e.openHandler}},[t("div",{staticStyle:{height:"500px"}},[t("CetGiantTree",e._g(e._b({},"CetGiantTree",e.CetGiantTree_selectNode,!1),e.CetGiantTree_selectNode.event))],1),t("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{size:"mini",type:"primary",plain:""},on:{click:e.handleConfirm}},[e._v("确定")]),t("el-button",{attrs:{size:"mini",type:"primary",plain:""},on:{click:function(t){e.visible=!1}}},[e._v("取消")])],1)]),t("CetInterface",e._g(e._b({attrs:{data:e.CetInterface_tree.data,dynamicInput:e.CetInterface_tree.dynamicInput},on:{"update:data":function(t){return e.$set(e.CetInterface_tree,"data",t)},"update:dynamicInput":function(t){return e.$set(e.CetInterface_tree,"dynamicInput",t)},"update:dynamic-input":function(t){return e.$set(e.CetInterface_tree,"dynamicInput",t)}}},"CetInterface",e.CetInterface_tree,!1),e.CetInterface_tree.event))],1)},zt=[],$t={components:{},props:{disabled:{default:!1},treeNode:{type:Object}},data(){return{visible:!1,currentTreeNode:null,nodeName:"",CetGiantTree_selectNode:{inputData_in:[],checkedNodes:[],selectNode:{},setting:{check:{enable:!1},data:{simpleData:{enable:!0,idKey:"tree_id"}}},event:{Created_out:this.CetGiantTree_selectNode_created_out,currentNode_out:this.CetGiantTree_selectNode_currentNode_out,checkedNodes_out:this.CetGiantTree_selectNode_checkedNodes_out}},CetInterface_tree:{queryMode:"trigger",data:[],dataConfig:{queryFunc:"",modelLabel:"",dataIndex:[],modelList:[],filters:[],treeReturnEnable:!0,hasQueryNode:!1,hasQueryId:!1},queryNode_in:null,queryId_in:-1,queryTrigger_in:(new Date).getTime(),dynamicInput:{},page_in:null,event:{result_out:this.CetInterface_tree_result_out,finishTrigger_out:this.CetInterface_tree_finishTrigger_out,failTrigger_out:this.CetInterface_tree_failTrigger_out,totalNum_out:this.CetInterface_tree_totalNum_out}}}},watch:{treeNode(e){e?(this.currentTreeNode=e,this.nodeName=this.currentTreeNode.name,this.CetGiantTree_selectNode.selectNode=e):(this.nodeName="",this.currentTreeNode=null,this.CetGiantTree_selectNode.selectNode={})}},methods:{openHandler(){this.setTreeModelList(),this.CetInterface_tree.queryTrigger_in=(new Date).getTime()},setTreeModelList(){let e=C().cloneDeep(be.state.treeNodeModel);this.CetInterface_tree.dataConfig.modelLabel=e[0],this.CetInterface_tree.dataConfig.modelList=e.splice(1)},handleConfirm(){if(this.currentTreeNode){this.visible=!1,this.nodeName=this.currentTreeNode.name;let e={tree_id:this.currentTreeNode.tree_id,name:this.currentTreeNode.name,modelLabel:this.currentTreeNode.modelLabel,id:this.currentTreeNode.id};this.$emit("update:treeNode",e)}else this.$message({type:"warning",message:"请选择节点"})},handleClose(){this.currentTreeNode=null,this.nodeName="",this.$emit("update:treeNode",this.currentTreeNode)},CetGiantTree_selectNode_created_out(e,t){},CetGiantTree_selectNode_currentNode_out(e){this.currentTreeNode=e},CetGiantTree_selectNode_checkedNodes_out(e){},CetInterface_tree_finishTrigger_out(e){},CetInterface_tree_failTrigger_out(e){},CetInterface_tree_totalNum_out(e){},CetInterface_tree_result_out(e){this.CetGiantTree_selectNode.inputData_in=this._.cloneDeep(e)}}},Et=$t,Pt=(a(8921),O(Et,Lt,zt,!1,null,"10c94e3e",null)),Bt=Pt.exports,At=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container",staticStyle:{display:"flex"}},[t("el-card",{staticStyle:{width:"400px","margin-right":"20px","text-align":"left"},attrs:{id:"dataPanel"}},[t("el-form",{staticClass:"chart-form",attrs:{size:"mini","label-position":"top"}},[t("el-form-item",{attrs:{label:"图表名称:"}},[t("el-input",{attrs:{size:"mini",placeholder:"未命名",maxlength:20,"show-word-limit":""},model:{value:e.chartName,callback:function(t){e.chartName=t},expression:"chartName"}})],1),t("el-form-item",{attrs:{label:"图表描述:"}},[t("el-input",{attrs:{size:"mini",placeholder:"请输入图表描述",maxlength:20,"show-word-limit":""},model:{value:e.chartDesc,callback:function(t){e.chartDesc=t},expression:"chartDesc"}})],1),t("el-form-item",{attrs:{label:"隐藏图表头部:"}},[t("el-switch",{model:{value:e.hideHeader,callback:function(t){e.hideHeader=t},expression:"hideHeader"}})],1),t("el-form-item",{attrs:{label:"去掉组件边距:"}},[t("el-switch",{model:{value:e.noPadding,callback:function(t){e.noPadding=t},expression:"noPadding"}})],1)],1),t("div",{staticClass:"pre_header"},[e._v("预定义图表:")]),e.isEdit?t("div",[e._v(e._s(e.componentName))]):t("CetTree",e._g(e._b({attrs:{selectNode:e.CetTree_components.selectNode,checkedNodes:e.CetTree_components.checkedNodes,searchText_in:e.CetTree_components.searchText_in},on:{"update:selectNode":function(t){return e.$set(e.CetTree_components,"selectNode",t)},"update:select-node":function(t){return e.$set(e.CetTree_components,"selectNode",t)},"update:checkedNodes":function(t){return e.$set(e.CetTree_components,"checkedNodes",t)},"update:checked-nodes":function(t){return e.$set(e.CetTree_components,"checkedNodes",t)},"update:searchText_in":function(t){return e.$set(e.CetTree_components,"searchText_in",t)},"update:search-text_in":function(t){return e.$set(e.CetTree_components,"searchText_in",t)}}},"CetTree",e.CetTree_components,!1),e.CetTree_components.event))],1),t("el-card",{staticStyle:{width:"100%"},attrs:{"body-style":"padding: 10px 20px;"}},[t("preConfig",{attrs:{chartType:e.chartType,content:e.content},on:{"update:content":function(t){e.content=t}}}),t("preView",{attrs:{chartType:e.chartType,content:e.content}})],1)],1)},jt=[],Mt=function(){var e=this,t=e._self._c;return t("div",{staticClass:"app-container",staticStyle:{display:"flex"}},[t(e.cfgComponent,{tag:"component",staticClass:"visualize-window",attrs:{content:e.chartData},on:{"update:content":function(t){e.chartData=t}}})],1)},Rt=[];let qt;function Ht(e){qt=e}var Ft={components:{},props:{chartType:{type:String},content:{type:String}},data(){return{chartData:""}},watch:{content:{handler(e){this.chartData=e}},chartData(e){this.$emit("update:content",e)}},computed:{cfgComponent(){let e=qt.find((e=>e.id===this.chartType));return e?e.cfgCmp:""}},methods:{}},Gt=Ft,Vt=O(Gt,Mt,Rt,!1,null,"00fc6be9",null),Qt=Vt.exports,Ut=function(){var e=this,t=e._self._c;return t("div",{staticClass:"pane-container"},[t(e.viewComponent,{tag:"component",staticClass:"visualize-window",attrs:{content:e.content,"chart-style":e.chartStyle}})],1)},Wt=[],Jt={components:{},props:{content:{type:String,required:!0},chartStyle:{require:!1,type:Object},chartType:{type:String,default:"table"}},data(){return{}},computed:{viewComponent(){let e=qt.find((e=>e.id===this.chartType));return e?e.viewCmp:""}}},Yt=Jt,Zt=(a(3982),O(Yt,Ut,Wt,!1,null,"1d72f018",null)),Xt=Zt.exports,Kt={components:{preConfig:Qt,preView:Xt},props:{preCompCfg:{type:String},isEdit:{type:Boolean}},computed:{componentName(){let e=C().find(qt,{id:this.chartType});return e?e.name:""}},watch:{preCompCfg:{handler(){if(this.preCompCfg){let e=JSON.parse(this.preCompCfg);this.content=JSON.stringify(e.content),this.chartName=e.chart_name,this.chartDesc=e.description,this.chartType=e.content.chartType,this.hideHeader=e.content.hideHeader,this.noPadding=e.content.noPadding}else this.content="",this.chartName="",this.chartDesc="",this.chartType="",this.hideHeader=!1,this.noPadding=!1}},chartName(){this.updatePreCompCfg()},chartDesc(){this.updatePreCompCfg()},content(){this.updatePreCompCfg()},chartType(){this.updatePreCompCfg()},hideHeader(){this.updatePreCompCfg()},noPadding(){this.updatePreCompCfg()}},data(){return{chartName:void 0,chartDesc:void 0,chartType:"",hideHeader:!1,noPadding:!1,content:"",CetTree_components:{inputData_in:qt,selectNode:{},checkedNodes:[],filterNodes_in:null,searchText_in:"",showFilter:!1,nodeKey:"id",props:{label:"name",children:"children"},highlightCurrent:!0,event:{currentNode_out:this.CetTree_components_currentNode_out,parentList_out:this.CetTree_components_parentList_out,checkedNodes_out:this.CetTree_components_checkedNodes_out,halfCheckNodes_out:this.CetTree_components_halfCheckNodes_out,allCheckNodes_out:this.CetTree_components_allCheckNodes_out}}}},created(){let e=qt,t=De(),a=e.filter((e=>!0===e.paltform)),r=e.filter((e=>!0!==e.paltform));this.CetTree_components.inputData_in=0===t?this.processCompListTag(a):t>0?this.processCompListTag(r):this.processCompListTag(e)},methods:{updatePreCompCfg(){let e={};this.preCompCfg&&(e=JSON.parse(this.preCompCfg)),e.chart_name=this.chartName,e.description=this.chartDesc,this.content?e.content=JSON.parse(this.content):e.content={},e.content.chartType=this.chartType,e.content.hideHeader=this.hideHeader,e.content.noPadding=this.noPadding,this.$emit("update:preCompCfg",JSON.stringify(e))},CetTree_components_currentNode_out(e){this.content="",this.chartType=e.id},CetTree_components_parentList_out(e){},CetTree_components_checkedNodes_out(e){},CetTree_components_halfCheckNodes_out(e){},CetTree_components_allCheckNodes_out(e){},processCompListTag(e){const t={};return e.forEach((e=>{const{tag:a}=e;a&&(t[a]||(t[a]={name:a,children:[]}),t[a].children.push(e))})),Object.values(t).length?Object.values(t):e}}},ea=Kt,ta=(a(9669),O(ea,At,jt,!1,null,"664687e6",null)),aa=ta.exports,ra={name:"ChartPanel",components:{visualizePanel:_e,dataPanel:Ot,draggable:c(),filterPanel:vt,orderPanel:St,treeNodeSelect:Bt,preDefinedCfg:aa},props:{currentChartId:{type:Number,required:!0}},data(){return{componentType:"predefined",preCompCfg:"",loading:!1,result:[],dataSrc:void 0,limit:200,orderByStrs:[],filterStr:void 0,editLimit:!1,currentFilters:[],currentNode:null,pecCurrentNode:null,measurePoint:null,sharedState:be.state,chartType:"table",chartName:void 0,chartDesc:void 0,showMyCharts:!1,myChartList:[],showDashboards:!1,dashboardList:[],selectedDb:void 0,linkedDbIds:[],tableSchma:void 0}},computed:{disablePrePanel(){return 0!==this.currentChartId&&"custom"===this.componentType},disableCustomPanel(){return 0!==this.currentChartId&&"predefined"===this.componentType},allSelected(){return be.state.dimensions.concat(be.state.caculCols)},queryStr(){return JSON.stringify({dataSrc:this.dataSrc,selectedCalcul:this.sharedState.caculCols,selectedDimension:this.sharedState.dimensions,orderByStrs:this.orderByStrs,filterStr:this.filterStr,treeNode:this.currentNode,limit:this.limit})},currentDimensions(){if(this.tableSchma)for(let e=0;e<this.sharedState.dimensions.length;e++)for(let t=0;t<this.tableSchma.length;t++)this.sharedState.dimensions[e].id===this.tableSchma[t].id&&this.$set(this.sharedState.dimensions[e],"alias",this.tableSchma[t].alias);return this.sharedState.dimensions},currentCacuCols(){if(this.tableSchma)for(let e=0;e<this.sharedState.caculCols.length;e++)for(let t=0;t<this.tableSchma.length;t++)this.sharedState.caculCols[e].Column===this.tableSchma[t].Column&&this.$set(this.sharedState.caculCols[e],"alias",this.tableSchma[t].alias);return this.sharedState.caculCols}},watch:{queryStr(e){e&&0!==this.currentChartId?this.fetchData(e):this.result=[]},currentChartId:{immediate:!0,handler(e){e<0||(0!==e?Ue(e).then((e=>{const t=e.data[0],a=t.content||{};this.componentType=a.componentType||"custom","custom"===this.componentType?this.setCustomCfg(t):"predefined"===this.componentType&&this.setPredinedCfg(t)})):(this.chartName=void 0,this.chartDesc=void 0,be.setCaculColsAction([]),be.setDimensionsAction([]),this.dataSrc=void 0,this.currentFilters=[],this.currentNode=null,this.orderByStrs=[],this.$nextTick((()=>{this.$refs.dataPanel.initWithDataSrc()})),this.preCompCfg=""))}}},methods:{setCustomCfg(e){this.chartName=e.chart_name,this.chartDesc=e.description;const t=e.content||{};this.dataSrc=t.dataSrc,this.chartType=t.chartType,this.limit=t.limit||200,this.currentFilters=t.filters,this.currentNode=t.treeNode,this.orderByStrs=t.orderByStrs,be.setCaculColsAction(t.selectedCalcul),be.setDimensionsAction(t.selectedDimension),this.$refs.dataPanel.initWithDataSrc(this.dataSrc)},setPredinedCfg(e){this.preCompCfg=JSON.stringify(e)},getTableSchma(e){this.tableSchma=e},handleCreate(){this.$emit("changeCurrentChart",0)},backToDashboard(){this.$emit("handleReturn")},fetchData(e){this.loading=!0;let t=Ze({dataSrc:this.dataSrc,selectedCalcul:this.sharedState.caculCols,selectedDimension:this.sharedState.dimensions,orderByStrs:this.orderByStrs,filters:this.currentFilters,treeNode:this.currentNode,limit:this.limit});at(t).then((e=>{this.loading=!1,this.result=e.data}))},handleDataSrcChange(e){this.dataSrc=e,be.setCaculColsAction([]),be.setDimensionsAction([]),this.filterStr=void 0,this.currentFilters=[],this.currentNode=null,this.orderByStrs=[]},handleColChange(e){e.added&&be.addCaculColAction(e.added.element)},handleDimensionChange(e){e.added&&be.addDimensionAction(e.added.element)},handleCloseColTag(e){be.deleteCaculColAction(e)},handleCloseDimensionTag(e){be.deleteDimensionAction(e)},handleAddFilter(e){this.filterStr=e},handleSave(){const e=0===this.currentChartId?void 0:this.currentChartId;let t=this.getSaveData();t&&(e?Qe(t).then((e=>{this.$message({type:"success",message:"保存成功！"})})):Ve(t).then((e=>{this.$emit("changeCurrentChart",e.data[0].id),this.$message({type:"success",message:"保存成功！"})})))},getSaveData(){let e=null;const t=0===this.currentChartId?void 0:this.currentChartId;let a,r=this.componentType;if("custom"===r){if(!this.chartName)return void this.$message({type:"warning",message:"保存失败，请输入图表名称"});a={dataSrc:this.dataSrc,orderByStrs:this.orderByStrs,limit:this.limit,selectedCalcul:this.sharedState.caculCols,selectedDimension:this.sharedState.dimensions,chartType:this.chartType,filters:this.currentFilters,treeNode:this.currentNode,componentType:this.componentType},e={id:t,chart_name:this.chartName,description:this.chartDesc,content:a}}else if("predefined"===r){if(""===this.preCompCfg)return void this.$message({type:"warning",message:"保存失败，请输入图表名称"});let a=JSON.parse(this.preCompCfg);if(!a.chart_name)return void this.$message({type:"warning",message:"保存失败，请输入图表名称"});a.content.componentType=r,a.id=t,e=a}return e},getContent(){},handleLinkDB(){this.showDashboards=!0,this.getDbByChart(this.currentChartId),je().then((e=>{let t=e.data;this.dashboardList=t.sort(((e,t)=>e.name===t.name?0:e.name<t.name?-1:1))}))},getDbByChart(e){He(e).then((e=>{this.linkedDbIds=e.data||[]}))},isDbDisbaled(e){return!!this.linkedDbIds.find((t=>t===e.id))},linkDb(){const e={chart_id:this.currentChartId,dashboard_id:this.selectedDb};e.dashboard_id?(this.showDashboards=!1,Me(e).then((e=>{this.getDbByChart(this.currentChartId),this.$message({type:"success",message:"添加成功！"})}))):this.$message({type:"error",message:"请选择看板！"})},viewAllChart(){this.showMyCharts=!0,Ye().then((e=>{this.myChartList=e.data}))},switchChart(e){this.$confirm("确定要离开当前页面吗?系统可能不会保存您所做的更改。","提示").then((()=>{this.$emit("changeCurrentChart",e.id),this.showMyCharts=!1}))},handleDownload(){a.e(76).then(a.bind(a,3076)).then((e=>{const t=this.allSelected.map((e=>e.Column)),a=t,r=this.formatJson(a,this.result);e.export_json_to_excel({header:t,data:r,filename:"DataExport"+Ie(Date.now(),"{m}{d}{h}{i}{s}"),autoWidth:!0})}))},formatJson(e,t){return t.map((t=>e.map((e=>{const a=e.split(".");return a.length<=1?t[e]:a.reduce(((e,t)=>e[t]?e[t]:"--"),t)}))))}}},ia=ra,na=(a(5241),O(ia,nt,st,!1,null,"58ff3c02",null)),sa=na.exports,oa=function(){var e=this,t=e._self._c;return t("div",[t("CetDialog",e._g(e._b({scopedSlots:e._u([{key:"footer",fn:function(){return[t("span",[t("CetButton",e._g(e._b({},"CetButton",e.CetButton_preserve,!1),e.CetButton_preserve.event)),t("CetButton",e._g(e._b({},"CetButton",e.CetButton_cancel,!1),e.CetButton_cancel.event))],1)]},proxy:!0}])},"CetDialog",e.CetDialog_pagedialog,!1),e.CetDialog_pagedialog.event),[t("el-form",{ref:"form",attrs:{model:e.form}},[t("el-form-item",{attrs:{prop:"chartid",label:"请选择作为底图的组件","label-width":"200px"}},[t("ElSelect",e._g(e._b({model:{value:e.form.chartid,callback:function(t){e.$set(e.form,"chartid",t)},expression:"form.chartid"}},"ElSelect",e.ElSelect_backgroundItem,!1),e.ElSelect_backgroundItem.event),e._l(e.ElOption_backgroundItem.options_in,(function(a){return t("ElOption",{key:a[e.ElOption_backgroundItem.key],attrs:{label:a[e.ElOption_backgroundItem.label],value:a[e.ElOption_backgroundItem.value],disabled:a[e.ElOption_backgroundItem.disabled]}})})),1)],1)],1)],1)],1)},la=[],ca={name:"SetBackground",components:{},computed:{},props:{openTrigger_in:{type:Number},closeTrigger_in:{type:Number},queryId_in:{type:Number,default:-1},inputData_in:{type:Object}},data(){return{form:{chartid:-1},CetDialog_pagedialog:{openTrigger_in:(new Date).getTime(),closeTrigger_in:(new Date).getTime(),title:"弹窗表单",width:"500px",event:{openTrigger_out:this.CetDialog_pagedialog_openTrigger_out,closeTrigger_out:this.CetDialog_pagedialog_closeTrigger_out}},CetButton_preserve:{visible_in:!0,disable_in:!1,title:"确定",type:"primary",plain:!0,event:{statusTrigger_out:this.CetButton_preserve_statusTrigger_out}},CetButton_cancel:{visible_in:!0,disable_in:!1,title:"取消",type:"primary",plain:!0,event:{statusTrigger_out:this.CetButton_cancel_statusTrigger_out}},ElSelect_backgroundItem:{value:"",style:{width:"200px"},event:{change:this.ElSelect_backgroundItem_change_out}},ElOption_backgroundItem:{options_in:[],key:"id",value:"id",label:"chart_name",disabled:"disabled"}}},watch:{openTrigger_in(e){this.CetDialog_pagedialog.openTrigger_in=this._.cloneDeep(e)},closeTrigger_in(e){this.CetDialog_pagedialog.closeTrigger_in=this._.cloneDeep(e)},queryId_in(e){},inputData_in(e){this.form.chartid=e.backGroundItemId>0?e.backGroundItemId:"",this.ElOption_backgroundItem.options_in=e.myChartList}},methods:{CetDialog_pagedialog_openTrigger_out(e){this.$emit("openTrigger_out",e)},CetDialog_pagedialog_closeTrigger_out(e){this.$emit("closeTrigger_out",e)},CetButton_preserve_statusTrigger_out(e){this.$emit("save",this.form.chartid),this.CetDialog_pagedialog.closeTrigger_in=this._.cloneDeep(e)},CetButton_cancel_statusTrigger_out(e){this.CetDialog_pagedialog.closeTrigger_in=this._.cloneDeep(e)},ElSelect_backgroundItem_change_out(){},no(){}},created:function(){}},da=ca,ha=O(da,oa,la,!1,null,"79bf88b3",null),ua=ha.exports;function pa(e,t){const a={x:e[0][0],y:e[0][1]},r={x:e[1][0],y:e[1][1]},i={x:t[0][0],y:t[0][1]},n={x:t[1][0],y:t[1][1]};return a.y===i.y&&r.y===n.y&&(a.x>=i.x&&a.x<=n.x)}var ma={components:{GridLayout:u.GridLayout,GridItem:u.GridItem,visualizePanel:_e,ChartPanel:sa,preView:Xt,SetBackground:ua},props:{dashboard:{required:!1,type:Object,default:()=>({})},mode:{required:!1,type:String,default:"view"},autoHeight:{type:Boolean,default:!1},viewMode:{type:Number}},data(){return{charts:[],results:{},loading:!1,layout:[],myChartList:[],showChartList:!1,chartLoading:{},currentChartId:-1,showChartPanel:!1,gridMarginTB:10,gridMarginLR:10,gridRowHeight:30,bgItemId:-1,SetBackground:{openTrigger_in:(new Date).getTime(),closeTrigger_in:(new Date).getTime(),queryId_in:0,inputData_in:null,event:{openTrigger_out:this.SetBackground_openTrigger_out,closeTrigger_out:this.SetBackground_closeTrigger_out,save:this.processBackGround}}}},activated(){this.getRelatedChartList()},watch:{"dashboard.id":{immediate:!0,handler(e){e?this.getRelatedChartList():(this.charts=[],this.layout=[])}},async autoHeight(){await this.$nextTick(),this.gridRowHeight=this.getRowHeight()},async layout(){await this.$nextTick(),this.gridRowHeight=this.getRowHeight()},async viewMode(){await this.$nextTick(),this.gridRowHeight=this.getRowHeight()}},methods:{isPreDefinedComp(){},changeCurrentChart(e){this.currentChartId=e},handleReturn(){this.showChartPanel=!this.showChartPanel,this.currentChartId=-1,this.$emit("handleShowDashboardList"),this.getRelatedChartList()},createChart(){this.showChartList=!1,this.$emit("handleShowDashboardList"),this.showChartPanel=!this.showChartPanel,this.currentChartId=0},getRelatedChartList(){this.dashboard.id&&(this.loading=!0,qe(this.dashboard.id).then((e=>{this.loading=!1,this.charts=e.data||[],this.getChartListData(),this.bgItemId=this.dashboard.content.bgId?this.dashboard.content.bgId:-1,this.processLayout(),this.handleLayoutChange()})))},getChartListData(){this.charts.forEach((e=>{this.$set(this.results,e.id,[]),this.$set(this.chartLoading,e.id,!1),e.content.allSelected=[],e.content.allSelected=e.content.allSelected.concat(e.content.selectedCalcul).concat(e.content.selectedDimension),"predefined"!==e.content.componentType&&this.queryChartData({dataSrc:e.content.dataSrc,selectedCalcul:e.content.selectedCalcul,selectedDimension:e.content.selectedDimension,orderByStrs:e.content.orderByStrs,filters:e.content.filters,treeNode:e.content.treeNode,limit:e.content.limit},e)}))},queryChartData(e,t){this.$set(this.chartLoading,t.id,!0);let a=Ze(e);at(a).then((e=>{this.$set(this.chartLoading,t.id,!1),this.$set(this.results,t.id,e.data)}))},processLayout(){this.layout=[];const e=this.dashboard.content&&this.dashboard.content.layout||[];this.charts.forEach((t=>{e.find((e=>e.id===t.id))||t.id===this.bgItemId||this.generatePosition(t,e)})),this.layout=e.filter((e=>this.charts.find((t=>t.id===e.id))))},handleCaculPos(e){const t=[];return e.forEach((e=>{e.yOffSet=e.y+e.h,e.xOffSet=e.x+e.w,e.bottomLine=[[e.x,e.yOffSet],[e.xOffSet,e.yOffSet]],e.topLine=[[e.x,e.y],[e.xOffSet,e.y]]})),e.forEach((a=>{const r=e.every((e=>!pa(a.bottomLine,e.topLine)));r&&t.push(a)})),t},generatePosition(e,t){let a;if(0===t.length)a={id:e.id,x:0,y:0,w:12,h:9,i:e.id};else{const r=this.handleCaculPos(t),i=r.reduce(((e,t)=>(e.bottomLine[0][1]>t.bottomLine[0][1]&&(e=t),e)),r[0]);a={id:e.id,x:i.x,y:i.yOffSet,w:i.w,h:9,i:e.id}}t.push(a)},handleLayoutChange(){this.dashboard.content=this.dashboard.content||{},this.dashboard.content.layout=this.layout,ze(this.dashboard).then((()=>{}))},handleResize(e){this.$refs[`chartInstance${e}`][0].$children[0].$emit("resized")},getChartItem(e){return this.charts.find((t=>t.id===e))},isExisted(e){return this.charts.findIndex((t=>t.id===e.id))>=0},handleLinkChart(){Ye().then((e=>{this.myChartList=e.data;for(let t=0;t<this.myChartList.length;)this.isExisted(this.myChartList[t])?this.myChartList.splice(t,1):t++;this.showChartList=!0}))},linkChart(e){const t={chart_id:e.id,dashboard_id:this.dashboard.id};Me(t).then((()=>{this.showChartList=!1,this.getRelatedChartList(),this.$message({type:"success",message:"添加成功！"})}))},getJumpPath(e){let t=this._.get(e,"content.chartType"),a=this._.find(qt,["id",t]);return this._.get(a,"route","")},handleJumpto(e){let t=this.getJumpPath(e);t&&this.$router.push({path:t})},handleEdit(e){this.showChartList=!1,this.$emit("handleShowDashboardList"),this.showChartPanel=!this.showChartPanel,this.currentChartId=e.id},handleDelete(e){this.$confirm("该操作将从该看板中删除该图表，并不会删除原图表，确认继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{const t=this.layout.findIndex((t=>t.id===e.id)),a=JSON.parse(JSON.stringify(this.layout));a.splice(t,1),this.dashboard.content.layout=a;const r={chart_id:e.id,dashboard_id:this.dashboard.id},{code:i}=await ze(this.dashboard);0===i&&Fe(r).then((()=>{this.getRelatedChartList(),this.$message({type:"success",message:"删除成功!"})}))}))},deleteChart(e){this.$confirm(`确定要删除图表：${e.chart_name}？删除后会影响所有使用该图表的看板。`,"提示").then((()=>{We({id:e.id}).then((()=>{this.handleLinkChart(),this.$message({type:"success",message:"删除成功！"})}))}))},handleBackGroundItem(){let e=[];Ye().then((t=>{e=t.data;for(let r=0;r<e.length;)this.isExisted(e[r])&&this.bgItemId!==e[r].id?e.splice(r,1):r++;this.SetBackground.openTrigger_in=(new Date).getTime();let a={myChartList:e,backGroundItemId:this.bgItemId};this.SetBackground.inputData_in=a}))},editBackGroundItem(){!this.bgItemId||this.bgItemId<0?this.$message({type:"warning",message:"请先设置底图组件"}):this.handleEdit(this.getChartItem(this.bgItemId))},async cancelBackGroundItem(){this.dashboard.content.bgId=-1;let e={chart_id:this.bgItemId,dashboard_id:this.dashboard.id};const{code:t}=await ze(this.dashboard);0===t&&Fe(e).then((()=>{this.getRelatedChartList(),this.$message({type:"success",message:"删除成功!"})}))},SetBackground_openTrigger_out(e){},SetBackground_closeTrigger_out(e){},processBackGround(e){e>0&&(this.bgItemId<1?this.setBGItem(e):this.bgItemId!==e&&this.changeBGItem(e))},setBGItem(e){let t={chart_id:e,dashboard_id:this.dashboard.id};this.dashboard.content=this.dashboard.content||{},this.dashboard.content.bgId=e,Me(t).then((()=>{ze(this.dashboard).then((()=>{this.getRelatedChartList()}))}))},changeBGItem(e){let t={chart_id:e,old_chart_id:this.bgItemId,dashboard_id:this.dashboard.id};this.dashboard.content=this.dashboard.content||{},this.dashboard.content.bgId=e,Re(t).then((()=>{ze(this.dashboard).then((()=>{this.getRelatedChartList()}))}))},getChartHeight(e){let t=!0===this.getChartItem(e.i).content.hideHeader,a=!0===this.getChartItem(e.i).content.noPadding,r=30*e.h+10*(e.h-1)-60;return t&&(r+=36),a&&(r+=20),`${r}px`},calcRowHeight(e,t,a){const r=_.maxBy(a,(e=>e.y+e.h));if(!r)return 30;const i=r.y+r.h,n=(e-2*t-(i-1)*t)/i;return n},getRowHeight(){if(!this.autoHeight)return 30;const e=this.$refs.container?.offsetHeight;return e?this.calcRowHeight(1==this.viewMode?e:e-45,this.gridMarginTB,this.layout):30},setRowHeight:_.debounce((async function(){console.log("resize----"),await this.$nextTick(),this.gridRowHeight=this.getRowHeight()}),300)},mounted(){window.removeEventListener("resize",this.setRowHeight),window.addEventListener("resize",this.setRowHeight)},activated(){window.removeEventListener("resize",this.setRowHeight),window.addEventListener("resize",this.setRowHeight)},deactivated(){window.removeEventListener("resize",this.setRowHeight)},beforeDestroy(){window.removeEventListener("resize",this.setRowHeight)}},fa=ma,ga=(a(936),O(fa,d,h,!1,null,"07ac80f8",null)),ba=ga.exports,va={components:{dashboardItem:ba,draggable:c()},props:{dashboardTitle:{type:String,default:"看板"}},data(){return{dashboardList:[],currentDashboard:void 0,editDialogVisible:!1,dbObj:{},loading:!1,isCollapse:!1,mode:"read",showDashboardList:!0,title:""}},computed:{isEdit(){return"edit"===this.mode}},created(){Oe().user.isRoot()?this.mode="edit":this.mode="read",this.getList()},methods:{handleShowDashboardList(){this.showDashboardList=!this.showDashboardList,this.getList()},getList(){this.showDashboardList&&(this.loading=!0,je().then((e=>{this.loading=!1;let t=e.data;this.dashboardList=t.sort(((e,t)=>e.name===t.name?0:e.name<t.name?-1:1)),this.currentDashboard=this.dashboardList[0]})))},switchDb(e){e.id!==this.currentDashboard.id&&(this.currentDashboard=e)},addDashboard(){this.dbObj={},this.title="新建看板",this.editDialogVisible=!0},editDashboard(e){this.dbObj=Object.assign({},e),this.title="编辑看板",this.editDialogVisible=!0},handleCommand(e){"edit"===e.type?this.editDashboard(e.target):this.deleteDashboard(e.target)},handleSubmit(){this.dbObj.name?this.dbObj.id?ze(this.dbObj).then((e=>{this.getList(),this.editDialogVisible=!1})):Le(this.dbObj).then((e=>{this.getList(),this.editDialogVisible=!1})):this.$message({type:"warning",message:"保存失败，请输入名称"})},handleOrderChange(){const e={order:this.dashboardList.map((e=>e.id))};e.userId=Oe().user.getUserId(),Ge(e).then((()=>{console.log("order")}))},deleteDashboard(e){this.$confirm(`确定要删除${e.name}看板吗？`,"提示").then((()=>{Ee({id:e.id}).then((()=>{this.getList(),this.$message({type:"success",message:"删除成功！"})}))}))}}},ya=va,Ca=(a(6400),O(ya,s,o,!1,null,"1a7d7d44",null)),_a=Ca.exports,xa={name:"CetDashboard",components:{Dashboard:_a},computed:{},props:{dashboardTitle:{type:String},treeNodeModel:{type:Array}},created(){be.setTreeNodeModel(this.treeNodeModel)},data(){return{}}},wa=xa,Sa=(a(4037),O(wa,i,n,!1,null,"62c9c0f1",null)),Ta=Sa.exports,Ia=function(){var e=this,t=e._self._c;return t("div",{class:{"simple-panel":1===this.viewMode,fullheight:e.autoHeight,"transparent-comp-background":e.isTransparentCompBg}},[t("dashboardItem",{attrs:{dashboard:e.currentDashboard,mode:"view",viewMode:e.viewMode,autoHeight:e.autoHeight}})],1)},Na=[],Da={components:{dashboardItem:ba},data(){return{currentDashboard:void 0,viewMode:0}},computed:{routeMode(){return this.$route.params.mode},routeName(){return this.$route.params.name},autoHeight(){return"1"===this.$route.query.autoHeight},isTransparentCompBg(){return"1"===this.$route.query.transparentCompBg}},watch:{routeMode(){this.setDashboard()},routeName(){this.setDashboard()},isTransparentCompBg(){this.setDashboard()}},created(){this.setDashboard()},methods:{setDashboard(){$e(this.$route.params.name).then((e=>{this.currentDashboard=e.data[0]}));let e=parseInt(this.$route.params.mode);this._.isNaN(e)&&(e=0),this.viewMode=e}}},ka=Da,Oa=(a(6789),O(ka,Ia,Na,!1,null,"d017c3d6",null)),La=Oa.exports,za=function(){var e=this,t=e._self._c;return t("div",{class:{"simple-panel":this.isSimplePanel,fullheight:e.autoHeight}},[t("dashboardItem",{attrs:{dashboard:e.dashboard,mode:"view",viewMode:e.mode,autoHeight:e.aHeight}})],1)},$a=[];const Ea={Normal:0,NoHeader:1,AutoHeight:1};var Pa={components:{dashboardItem:ba},props:{dashboard:Object,mode:Number,autoHeight:Number},computed:{isSimplePanel(){return this.mode===Ea.NoHeader},aHeight(){return this.autoHeight===Ea.AutoHeight}}},Ba=Pa,Aa=(a(5365),O(Ba,za,$a,!1,null,"29f1aac0",null)),ja=Aa.exports,Ma=Ta;class Ra{static register({apiPrefix:e={},apiRequestInterceptor:t}={},a){Ra.apiOptions={prefix:Object.assign({},{"model-service":"/model"},e),requestInterceptor:t}}}var qa=Ma}(),r}()}));
//# sourceMappingURL=omega-dashboard.umd.min.js.map