{"name": "@omega/dashboard", "version": "1.2.12", "private": false, "description": "dashboard组件", "main": "src/index.js", "module": "src/index.js", "publishConfig": {"main": "lib/omega-dashboard.umd.min.js"}, "author": "NS", "files": ["lib", "src", "CHANGELOG.md"], "scripts": {"build": "vue-cli-service build --target lib --name omega-dashboard --dest lib --formats umd-min src/index.js", "lint": "vue-cli-service lint", "release": "npm publish --registry http://*************:4873", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "peerDependencies": {"@omega/auth": "^1.7.10", "@omega/http": "^1.2.3", "@omega/icon": "^1.3.6", "cet-common": "^1.3.4", "cet-chart": "^1.2.3", "vue": "^2.7.8", "element-ui": "^2.11.1", "lodash": "^4.17.21", "moment": "^2.29.1", "core-js": "^3.36.0"}, "dependencies": {"vuedraggable": "^2.23.2", "vue-grid-layout": "^2.3.12", "echarts": "^5.3.0", "file-saver": "^2.0.5", "xlsx": "^0.14.4"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "autoprefixer": "^9.8.8", "babel-eslint": "^10.1.0", "core-js": "^3.36.0", "extract-loader": "^5.1.0", "postcss": "^7.0.39", "sass": "^1.26.5", "sass-loader": "^9.0.3"}}