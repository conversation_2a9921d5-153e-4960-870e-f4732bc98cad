<template>
  <div>
    <el-form label-position="top" class="panel" style="text-align: left">
      <el-form-item v-show="dataSrcVisible" label="数据源：">
        <el-select
          v-model="selectedTable"
          size="mini"
          filterable
          placeholder="选择数据源"
          style="width: 200px"
          clearable
          @change="handleDataSrcChange"
        >
          <el-option
            v-for="item in dataSourceList"
            :key="item.id"
            :label="item.alias"
            :value="item.label"
          />
        </el-select>
      </el-form-item>

      <el-form-item v-show="!dataSrcVisible" label="数据源：">
        <span style="font-size: 12px; margin-right: 5px">
          {{ selectedTableName }}
        </span>
        <el-button type="text" size="mini" @click="editDataSrc">修改</el-button>
      </el-form-item>
      <el-form-item class="property-list" label="字段(拖拽到维度或者数值栏)：">
        <draggable
          v-model="tableSchema"
          v-loading="schemaLoading"
          :group="{ name: 'col', pull: 'clone', put: false }"
          :move="handleMove"
        >
          <div
            v-for="col in tableSchema"
            :key="col.Column"
            class="drag-list-item"
          >
            <i class="el-icon-rank item-i text-ZS" />
            {{ col.alias }}
          </div>
        </draggable>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import store from "../store";
import { queryAllModelMeta, queryModelMetaByLabel } from "../../api/model";
export default {
  components: { draggable },
  props: {
    resultLoading: {
      default: false
    },
    dataSrc: {
      required: true
    },
    currentChartId: {
      required: true
    }
  },
  data() {
    return {
      schemaLoading: false,
      dataSourceList: [],
      selectedTable: undefined,
      tableSchema: undefined,
      dataSrcVisible: this.currentChartId === 0,
      existWarning: null,
      selectedTableName: undefined
    };
  },
  computed: {
    allSelected() {
      return store.state.dimensions.concat(store.state.caculCols);
    }
  },
  created() {
    this.getModels();
  },
  methods: {
    getModels() {
      queryAllModelMeta().then(resp => {
        let rawData = resp.data;
        let models = rawData.sort((v1, v2) => {
          if (v1.alias === v2.alias) {
            return 0;
          } else {
            return v1.alias < v2.alias ? -1 : 1;
          }
        });
        this.dataSourceList = models.filter(
          item => item.constructionType !== "enumeration"
        );

        if (
          this.dataSourceList.find(item => item.label === this.selectedTable)
        ) {
          this.selectedTableName = this.dataSourceList.find(
            item => item.label === this.selectedTable
          ).alias;
        }
      });
    },
    initWithDataSrc(dataSrc) {
      if (dataSrc) {
        this.selectedTable = dataSrc;
        if (
          this.dataSourceList.find(item => item.label === this.selectedTable)
        ) {
          this.selectedTableName = this.dataSourceList.find(
            item => item.label === this.selectedTable
          ).alias;
        } else this.selectedTableName = undefined;
        this.fetchSchema();
      } else {
        this.selectedTable = dataSrc;
        this.tableSchema = [];
        this.dataSrcVisible = true;
      }
    },
    editDataSrc() {
      this.dataSrcVisible = true;
      this.selectedTable = undefined;
    },
    handleDataSrcChange() {
      this.selectedTableName = this.dataSourceList.find(
        item => item.label === this.selectedTable
      ).alias;
      this.dataSrcVisible = false;
      this.fetchSchema();
      store.setAllColsAction([]);
      this.$emit("change", this.selectedTable);
    },
    fetchSchema() {
      if (!this.selectedTable) {
        this.tableSchema = [];
        return;
      }
      this.schemaLoading = true;
      queryModelMetaByLabel(this.selectedTable).then(resp => {
        this.schemaLoading = false;
        this.tableSchema = resp.data.propertyList.map((item, index) => {
          return {
            Column: item.propertyLabel,
            Type: item.dataType,
            id: item.id,
            alias: item.alias
          };
        });
        store.setAllColsAction(this.tableSchema);
        this.$emit("getTable", this.tableSchema);
      });
    },
    handleCloseDialog(done) {
      if (this.selectedTable) {
        done();
      } else {
        this.$message({
          type: "warning",
          message: "You Need Select Data Source First."
        });
        done();
      }
    },
    handleMove(evt, originalEvent) {
      if (
        this.allSelected.find(
          item => item.Column === evt.draggedContext.element.Column
        )
      ) {
        if (!this.existWarning) {
          this.existWarning = this.$message({
            type: "warning",
            message: "该字段已存在, 请不要重复添加",
            onClose: instance => {
              this.existWarning = null;
            }
          });
        }
        return false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.panel {
  ::v-deep .el-form-item__label {
    line-height: initial;
  }
  ::v-deep .el-form-item__content {
    line-height: initial;
  }
}
.drag-list-item {
  line-height: 1.5;
  font-size: 14px;
  cursor: -webkit-grab;
  .item-i {
    font-size: 12px;
  }
}

.property-list {
  overflow-y: auto;
  max-height: 500px;
}
</style>
