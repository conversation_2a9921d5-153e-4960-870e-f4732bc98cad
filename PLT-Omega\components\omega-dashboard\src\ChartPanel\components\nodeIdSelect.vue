<template>
  <div>
    <span style="padding-right: 15px">{{ currentTreeNodeId }}</span>
    <el-button
      :disabled="disabled"
      type="primary"
      size="mini"
      style="width: 120px"
      @click="visible = true"
    >
      选择节点
    </el-button>

    <el-dialog
      append-to-body
      :visible.sync="visible"
      width="500px"
      title="选择节点"
      @open="openHandler"
    >
      <!-- selectNode组件 -->
      <div style="height: 500px">
        <CetGiantTree
          v-bind="CetGiantTree_selectNode"
          v-on="CetGiantTree_selectNode.event"
        />
      </div>

      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          size="mini"
          type="primary"
          plain
          @click="handleConfirm"
        >确定</el-button>
        <el-button
          size="mini"
          type="primary"
          plain
          @click="visible = false"
        >取消</el-button>
      </span>
    </el-dialog>
    <CetInterface
      :data.sync="CetInterface_tree.data"
      :dynamic-input.sync="CetInterface_tree.dynamicInput"
      v-bind="CetInterface_tree"
      v-on="CetInterface_tree.event"
    />
  </div>
</template>
<script>
import _ from "lodash";
import store from "../store";

export default {
  components: {},
  props: {
    disabled: {
      default: false
    },
    nodeId: {
      type: Number
    }
  },
  data() {
    return {
      visible: false,
      currentTreeNodeId: "", //当前选中的节点
      // selectNode组件
      CetGiantTree_selectNode: {
        inputData_in: [],
        checkedNodes: [], //设置勾选多个节点{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {}, //设置选中某一行
        setting: {
          check: {
            enable: false //多选，不配置则默认单选
          },
          data: {
            simpleData: {
              enable: true,
              idKey: "tree_id"
            }
          }
        },
        event: {
          Created_out: this.CetGiantTree_selectNode_created_out, //创建完成后输出
          currentNode_out: this.CetGiantTree_selectNode_currentNode_out, //选中单行输出
          checkedNodes_out: this.CetGiantTree_selectNode_checkedNodes_out //勾选节点输出
        }
      },
      CetInterface_tree: {
        queryMode: "trigger", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [],
          treeReturnEnable: true,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        page_in: null, // exp:{ index: 1, limit: 20 }
        //defaultSort: { prop: "code"  order: "descending" },
        event: {
          result_out: this.CetInterface_tree_result_out,
          finishTrigger_out: this.CetInterface_tree_finishTrigger_out,
          failTrigger_out: this.CetInterface_tree_failTrigger_out,
          totalNum_out: this.CetInterface_tree_totalNum_out
        }
      }
    };
  },
  watch: {
    nodeId(val) {
      if (!val) {
        this.currentTreeNodeId = "";
        this.CetGiantTree_selectNode.selectNode = {};
      } else {
        this.currentTreeNodeId = val;
        this.CetGiantTree_selectNode.selectNode = {};
      }
    }
  },
  methods: {
    openHandler() {
      this.setTreeModelList();
      this.CetInterface_tree.queryTrigger_in = new Date().getTime();
    },
    setTreeModelList() {
      const treeNodeModel = _.cloneDeep(store.state.treeNodeModel);
      this.CetInterface_tree.dataConfig.modelLabel = treeNodeModel[0];
      this.CetInterface_tree.dataConfig.modelList = treeNodeModel.splice(1);
    },
    handleConfirm() {
      if (!this.currentTreeNodeId) {
        this.$message({
          type: "warning",
          message: "请选择节点"
        });
      } else {
        this.visible = false;
        // let treeNode = {
        //   tree_id: this.currentTreeNodeId.tree_id,
        //   name: this.currentTreeNodeId.name,
        //   modelLabel: this.currentTreeNodeId.modelLabel,
        //   id: this.currentTreeNodeId.id
        // };
        this.$emit("update:nodeId", this.currentTreeNodeId);
      }
    },

    // selectNode组件
    CetGiantTree_selectNode_created_out(val, obj) {},
    CetGiantTree_selectNode_currentNode_out(val) {
      this.currentTreeNodeId = val.id;
    },
    CetGiantTree_selectNode_checkedNodes_out(val) {},

    // tree展示文字输出
    CetInterface_tree_finishTrigger_out(val) {},
    CetInterface_tree_failTrigger_out(val) {},
    CetInterface_tree_totalNum_out(val) {},
    CetInterface_tree_result_out(val) {
      this.CetGiantTree_selectNode.inputData_in = this._.cloneDeep(val);
    }
  }
};
</script>
<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 20px;
}
</style>
