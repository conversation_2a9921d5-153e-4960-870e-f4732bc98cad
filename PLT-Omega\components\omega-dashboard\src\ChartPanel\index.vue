<template>
  <div>
    <el-card
      body-style="padding:0;"
      style="margin-bottom: 20px"
      class="panel-header"
    >
      <div slot="header" style="display: flex; justify-content: space-between">
        <span>
          <span class="back-button" @click="backToDashboard">
            <i class="el-icon-back" />
            <span>返回</span>
          </span>
          <span v-if="this.currentChartId !== 0">编辑图表</span>
          <span v-else>新建图表</span>
        </span>
        <span>
          <el-button
            size="mini"
            type="primary"
            style="float: right; margin: 0 10px 0 0"
            icon="el-icon-download"
            v-if="this.componentType === 'custom'"
            @click="handleDownload"
          />
          <el-button
            v-if="this.currentChartId !== 0"
            size="mini"
            type="primary"
            style="float: right; margin: 0 10px 0 0"
            @click="handleLinkDB"
          >
            添加到看板
          </el-button>
          <el-button
            size="mini"
            type="primary"
            style="float: right; margin: 0 10px 0 0"
            icon="el-icon-save"
            @click="handleSave"
          >
            保存
          </el-button>
          <el-button
            v-if="this.currentChartId !== 0"
            size="mini"
            type="primary"
            style="float: right; margin: 0 10px 0 0"
            @click="handleCreate"
          >
            新建图表
          </el-button>

          <el-button
            size="mini"
            type="primary"
            style="float: right; margin-right: 20px"
            @click="viewAllChart"
          >
            我的图表
          </el-button>
        </span>
      </div>
    </el-card>
    <el-tabs v-model="componentType" type="card">
      <el-tab-pane
        name="predefined"
        :disabled="disablePrePanel"
        label="预定义图表"
      >
        <preDefinedCfg
          :isEdit="this.currentChartId !== 0"
          :preCompCfg.sync="preCompCfg"
        ></preDefinedCfg>
      </el-tab-pane>
      <el-tab-pane
        name="custom"
        :disabled="disableCustomPanel"
        label="自定义图表"
      >
        <div class="app-container" style="display: flex">
          <el-card
            id="dataPanel"
            style="width: 400px; margin-right: 20px; text-align: center"
          >
            <data-panel
              ref="dataPanel"
              :result-loading="loading"
              :data-src="dataSrc"
              :currentChartId="currentChartId"
              @getTable="getTableSchma"
              @change="handleDataSrcChange"
            />
          </el-card>

          <el-card style="width: 100%" body-style="padding: 10px 20px;">
            <div class="form-wrapper">
              <el-form id="formPanel" size="mini" class="analysis-form">
                <el-form-item id="dimensionInput" label="维度(X轴)">
                  <draggable
                    v-model="sharedState.dimensions"
                    :group="{ name: 'col', pull: true, put: true }"
                    class="draggable-wrapper"
                    @change="handleDimensionChange"
                  >
                    <el-tag
                      v-for="col in currentDimensions"
                      :key="col.Column"
                      class="draggable-item"
                      size="small"
                      closable
                      @close="handleCloseDimensionTag(col)"
                    >
                      {{ col.alias }}
                    </el-tag>
                  </draggable>
                </el-form-item>
                <el-form-item id="fieldInput" label="数值(Y轴)">
                  <draggable
                    v-model="sharedState.caculCols"
                    :group="{ name: 'col', pull: true, put: true }"
                    class="draggable-wrapper"
                    @change="handleColChange"
                  >
                    <el-tag
                      v-for="col in currentCacuCols"
                      :key="col.Column"
                      size="small"
                      closable
                      class="draggable-item"
                      @close="handleCloseColTag(col)"
                    >
                      {{ col.alias }}
                    </el-tag>
                  </draggable>
                </el-form-item>

                <orderPanel :tableSchma="tableSchma" v-model="orderByStrs" />

                <filterPanel
                  :filters.sync="currentFilters"
                  :disabled="!allSelected || allSelected.length === 0"
                  @change="handleAddFilter"
                  :tableSchma="tableSchma"
                />

                <treeNodeSelect :treeNode.sync="currentNode"></treeNodeSelect>

                <el-form-item>
                  <div class="limit-input">
                    <span v-show="!editLimit">
                      查询前{{ limit }}条数据
                      <el-button type="text" @click="editLimit = true">
                        修改
                      </el-button>
                    </span>
                    <span v-show="editLimit">
                      <el-input-number
                        :step="1"
                        step-strictly
                        :min="1"
                        :max="10000"
                        v-model="limit"
                        :disabled="loading"
                        size="mini"
                        placeholder="数据条数"
                        style="width: 200px"
                        @blur="editLimit = false"
                      />
                      <el-button size="mini" @click="editLimit = false">
                        确认
                      </el-button>
                    </span>
                  </div>
                </el-form-item>
              </el-form>
              <el-form class="chart-form" size="mini" label-position="top">
                <el-form-item label="图表名称:">
                  <el-input
                    v-model="chartName"
                    size="mini"
                    placeholder="未命名"
                    :maxlength="20"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="图表描述:">
                  <el-input
                    v-model="chartDesc"
                    size="mini"
                    placeholder="请输入图表描述"
                    :maxlength="20"
                    show-word-limit
                  />
                </el-form-item>
              </el-form>
            </div>

            <visualize-panel
              id="vizPanel"
              v-loading="loading"
              :data="result"
              :chart-type.sync="chartType"
              :schema="allSelected"
            />
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="我的图表" :visible.sync="showMyCharts">
      <el-table :data="myChartList" :max-height="500">
        <el-table-column type="index"></el-table-column>
        <el-table-column label="名称" width="200" prop="chart_name" />
        <el-table-column label="描述" prop="description" />
        <el-table-column label="操作" width="240" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="warning"
              @click="switchChart(scope.row)"
            >
              编辑
            </el-button>
            <!-- <el-button
              size="mini"
              type="danger"
              @click="deleteChart(scope.row)"
            >
              删除
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          plain
          size="mini"
          @click="showMyCharts = false"
        >
          取消
        </el-button>
      </span>
    </el-dialog>

    <el-dialog title="看板列表" width="500px" :visible.sync="showDashboards">
      <div style="text-align: center">
        <el-select v-model="selectedDb" size="small" style="width: 350px">
          <el-option
            v-for="item in dashboardList"
            :key="item.id"
            :label="item.name"
            :disabled="isDbDisbaled(item)"
            :value="item.id"
          />
        </el-select>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain size="small" @click="linkDb">
          确定
        </el-button>
        <el-button
          type="primary"
          plain
          size="small"
          @click="showDashboards = false"
        >
          取消
        </el-button>
      </span>
    </el-dialog>
    <!-- <el-tooltip content="帮助中心" placement="top"> -->
    <!-- <el-dropdown class="help-center-wrapper" placement="top" size="mini" @command="handleHelp">
      <div class="help-center">
        <i class="el-icon-question" />
      </div>
      <el-dropdown-menu slot="dropdown" size="mini">
        <el-dropdown-item command="guide">开启新手引导</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown> -->
    <!-- </el-tooltip> -->
  </div>
</template>
<script>
import draggable from "vuedraggable";
// import Driver from "driver.js"; // import driver.js
// import "driver.js/dist/driver.min.css"; // import driver.js css

import filterPanel from "./components/filterPanel";
import orderPanel from "./components/orderPanel";
import visualizePanel from "./components/visualizePanel";
import dataPanel from "./components/dataPanel";
import treeNodeSelect from "./components/treeNodeSelect";
import preDefinedCfg from "./preDefined/index.vue";

import {
  createChart,
  updateChart,
  getChartById,
  chartList,
  deleteChart
} from "../api/chart";
import { dashboardList, addChartToDB, dbByChart } from "../api/dashboard";
import { parseTime } from "../utils";
import { buildQueryData } from "../utils/buildQuery";
import { queryModel } from "../api/model";

// import steps from "./guideSteps";
import store from "./store";
import _ from "lodash";

// const driver = new Driver();

export default {
  name: "ChartPanel",
  components: {
    visualizePanel,
    dataPanel,
    draggable,
    filterPanel,
    orderPanel,
    treeNodeSelect,
    preDefinedCfg
  },
  props: {
    currentChartId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      componentType: "predefined", //预定义图表  predefined 或者自定义图表 custom
      preCompCfg: "",
      //预定义图表
      loading: false,
      result: [],
      dataSrc: undefined,
      limit: 200,
      orderByStrs: [],
      filterStr: undefined,
      editLimit: false,
      currentFilters: [],
      currentNode: null,
      pecCurrentNode: null,
      measurePoint: null,
      sharedState: store.state,
      chartType: "table",
      chartName: undefined,
      chartDesc: undefined,
      showMyCharts: false,
      myChartList: [],
      showDashboards: false,
      dashboardList: [],
      selectedDb: undefined,
      linkedDbIds: [],
      tableSchma: undefined
    };
  },
  computed: {
    disablePrePanel() {
      return this.currentChartId !== 0 && this.componentType === "custom";
    },
    disableCustomPanel() {
      return this.currentChartId !== 0 && this.componentType === "predefined";
    },
    allSelected() {
      return store.state.dimensions.concat(store.state.caculCols);
    },
    queryStr() {
      return JSON.stringify({
        dataSrc: this.dataSrc,
        selectedCalcul: this.sharedState.caculCols,
        selectedDimension: this.sharedState.dimensions,
        orderByStrs: this.orderByStrs,
        filterStr: this.filterStr,
        treeNode: this.currentNode,
        limit: this.limit
      });
    },
    currentDimensions() {
      if (this.tableSchma) {
        for (let i = 0; i < this.sharedState.dimensions.length; i++) {
          for (let j = 0; j < this.tableSchma.length; j++) {
            if (this.sharedState.dimensions[i].id === this.tableSchma[j].id) {
              this.$set(
                this.sharedState.dimensions[i],
                "alias",
                this.tableSchma[j].alias
              );
            }
          }
        }
      }
      return this.sharedState.dimensions;
    },
    currentCacuCols() {
      if (this.tableSchma) {
        for (let i = 0; i < this.sharedState.caculCols.length; i++) {
          for (let j = 0; j < this.tableSchma.length; j++) {
            if (
              this.sharedState.caculCols[i].Column === this.tableSchma[j].Column
            ) {
              this.$set(
                this.sharedState.caculCols[i],
                "alias",
                this.tableSchma[j].alias
              );
            }
          }
        }
      }
      return this.sharedState.caculCols;
    }
  },
  watch: {
    queryStr(value) {
      if (value && this.currentChartId !== 0) {
        this.fetchData(value);
      } else {
        this.result = [];
      }
    },
    currentChartId: {
      immediate: true,
      handler(value) {
        if (value < 0) {
          return;
        }
        if (value !== 0) {
          getChartById(value).then(resp => {
            const chart = resp.data[0];
            const content = chart.content || {};
            this.componentType = content.componentType || "custom";
            if (this.componentType === "custom") {
              this.setCustomCfg(chart);
            } else if (this.componentType === "predefined") {
              this.setPredinedCfg(chart);
            }
          });
        } else {
          this.chartName = undefined;
          this.chartDesc = undefined;
          store.setCaculColsAction([]);
          store.setDimensionsAction([]);
          this.dataSrc = undefined;
          this.currentFilters = [];
          this.currentNode = null;
          this.orderByStrs = [];
          this.$nextTick(() => {
            this.$refs.dataPanel.initWithDataSrc();
          });
          //清空预定义图表配置
          this.preCompCfg = "";
        }
      }
    }
  },
  methods: {
    setCustomCfg(chart) {
      this.chartName = chart.chart_name;
      this.chartDesc = chart.description;
      const content = chart.content || {};
      this.dataSrc = content.dataSrc;
      this.chartType = content.chartType;
      this.limit = content.limit || 200;
      this.currentFilters = content.filters;
      this.currentNode = content.treeNode;
      this.orderByStrs = content.orderByStrs;
      store.setCaculColsAction(content.selectedCalcul);
      store.setDimensionsAction(content.selectedDimension);
      this.$refs.dataPanel.initWithDataSrc(this.dataSrc);
    },
    //自定义
    //预定义
    setPredinedCfg(chart) {
      this.preCompCfg = JSON.stringify(chart);
    },

    //预定义
    getTableSchma(val) {
      this.tableSchma = val;
    },
    handleCreate() {
      this.$emit("changeCurrentChart", 0);
    },
    backToDashboard() {
      this.$emit("handleReturn");
    },
    fetchData(sqlSentence) {
      //TODO 这里需要完善获取模型数据的逻辑
      this.loading = true;

      let queryData = buildQueryData({
        dataSrc: this.dataSrc,
        selectedCalcul: this.sharedState.caculCols,
        selectedDimension: this.sharedState.dimensions,
        orderByStrs: this.orderByStrs,
        filters: this.currentFilters,
        treeNode: this.currentNode,
        limit: this.limit
      });
      queryModel(queryData).then(resp => {
        this.loading = false;
        this.result = resp.data;
      });
    },
    handleDataSrcChange(value) {
      this.dataSrc = value;
      store.setCaculColsAction([]);
      store.setDimensionsAction([]);
      this.filterStr = undefined;
      this.currentFilters = [];
      this.currentNode = null;
      this.orderByStrs = [];
    },
    handleColChange(evt) {
      if (evt.added) {
        store.addCaculColAction(evt.added.element);
      }
    },
    handleDimensionChange(evt) {
      if (evt.added) {
        store.addDimensionAction(evt.added.element);
      }
    },
    handleCloseColTag(col) {
      store.deleteCaculColAction(col);
    },
    handleCloseDimensionTag(col) {
      store.deleteDimensionAction(col);
    },
    handleAddFilter(value) {
      this.filterStr = value;
    },
    handleSave() {
      const chartId =
        this.currentChartId === 0 ? undefined : this.currentChartId;
      let data = this.getSaveData();
      if (!data) {
        return;
      }

      if (chartId) {
        updateChart(data).then(resp => {
          this.$message({
            type: "success",
            message: "保存成功！"
          });
        });
      } else {
        createChart(data).then(resp => {
          this.$emit("changeCurrentChart", resp.data[0].id);
          this.$message({
            type: "success",
            message: "保存成功！"
          });
        });
      }
    },
    getSaveData() {
      let saveData = null;
      const chartId =
        this.currentChartId === 0 ? undefined : this.currentChartId;
      let obj;
      let componentType = this.componentType;
      if (componentType === "custom") {
        if (!this.chartName) {
          this.$message({
            type: "warning",
            message: "保存失败，请输入图表名称"
          });
          return;
        }

        obj = {
          dataSrc: this.dataSrc,
          orderByStrs: this.orderByStrs,
          limit: this.limit,
          selectedCalcul: this.sharedState.caculCols,
          selectedDimension: this.sharedState.dimensions,
          chartType: this.chartType,
          filters: this.currentFilters,
          treeNode: this.currentNode,
          componentType: this.componentType
        };
        saveData = {
          id: chartId,
          chart_name: this.chartName,
          description: this.chartDesc,
          content: obj
        };
      } else if (componentType === "predefined") {
        if (this.preCompCfg === "") {
          this.$message({
            type: "warning",
            message: "保存失败，请输入图表名称"
          });
          return;
        }
        let tempPreCompCfg = JSON.parse(this.preCompCfg);
        if (!tempPreCompCfg.chart_name) {
          this.$message({
            type: "warning",
            message: "保存失败，请输入图表名称"
          });
          return;
        }
        tempPreCompCfg.content.componentType = componentType;
        tempPreCompCfg.id = chartId;
        saveData = tempPreCompCfg;
      }

      return saveData;
    },
    getContent() {},
    handleLinkDB() {
      this.showDashboards = true;
      this.getDbByChart(this.currentChartId);
      dashboardList().then(resp => {
        let rawData = resp.data;

        this.dashboardList = rawData.sort((v1, v2) => {
          if (v1.name === v2.name) {
            return 0;
          } else {
            return v1.name < v2.name ? -1 : 1;
          }
        });
      });
    },
    getDbByChart(id) {
      dbByChart(id).then(resp => {
        this.linkedDbIds = resp.data || [];
      });
    },
    isDbDisbaled(db) {
      return !!this.linkedDbIds.find(id => id === db.id);
    },
    linkDb() {
      const data = {
        chart_id: this.currentChartId,
        dashboard_id: this.selectedDb
      };
      if (!data.dashboard_id) {
        this.$message({
          type: "error",
          message: "请选择看板！"
        });
        return;
      }
      this.showDashboards = false;
      addChartToDB(data).then(resp => {
        this.getDbByChart(this.currentChartId);
        this.$message({
          type: "success",
          message: "添加成功！"
        });
      });
    },
    viewAllChart() {
      this.showMyCharts = true;
      chartList().then(resp => {
        this.myChartList = resp.data;
      });
    },
    switchChart(chart) {
      this.$confirm(
        "确定要离开当前页面吗?系统可能不会保存您所做的更改。",
        "提示"
      ).then(() => {
        this.$emit("changeCurrentChart", chart.id);
        this.showMyCharts = false;
      });
    },
    // deleteChart(chart) {
    //   this.$confirm(
    //     `确定要删除图表：${chart.chart_name}？删除后会影响所有使用该图表的看板。`,
    //     "提示"
    //   ).then(() => {
    //     deleteChart({ id: chart.id }).then(() => {
    //       this.viewAllChart();
    //       this.$message({
    //         type: "success",
    //         message: "删除成功！"
    //       });
    //     });
    //   });
    // },
    // handleHelp(command) {
    //   console.log(command);
    //   if (command === "guide") {
    //     driver.defineSteps(steps);
    //     driver.start();
    //   }
    // },
    handleDownload() {
      import("../utils/Export2Excel").then(excel => {
        const tHeader = this.allSelected.map(item => item.Column);
        const filterVal = tHeader;
        const data = this.formatJson(filterVal, this.result);
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: "DataExport" + parseTime(Date.now(), "{m}{d}{h}{i}{s}"),
          autoWidth: true
        });
      });
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v =>
        filterVal.map(j => {
          const tempArr = j.split(".");
          if (tempArr.length <= 1) {
            return v[j];
          } else {
            return tempArr.reduce(
              (pre, cur) => (pre[cur] ? pre[cur] : "--"),
              v
            );
          }
        })
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.back-button {
  display: inline-block;
  padding-right: 10px;
  margin-right: 10px;
  border-right: 1px solid;
  cursor: pointer;
  span {
    padding: 5px;
    font-size: 14px;
  }
}
.analysis-form {
  width: 100%;
  padding-right: 20px;
  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 10px;
  }
  ::v-deep .el-form-item--mini .el-form-item__label,
  .limit-input {
    font-size: 14px;
  }
}
.form-wrapper {
  display: flex;
}
.chart-form {
  width: 250px;
  ::v-deep .el-form-item--mini.el-form-item {
    margin-bottom: 10px;
  }
}

.draggable-wrapper {
  font-size: 14px;
  min-height: 30px;
  border-bottom: 1px solid;
  .draggable-item {
    margin-right: 10px;
  }
  ::v-deep .el-select--mini {
    margin: 0;
  }
}

.help-center-wrapper {
  cursor: pointer;
  position: fixed;
  bottom: 25px;
  right: 25px;
  .help-center {
    width: 45px;
    height: 45px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    line-height: 45px;
    font-size: 20px;
    color: #205cd8;
    text-align: center;
    ::v-deep .el-dropdown {
      font-size: 20px;
      color: #205cd8;
    }
  }
}
</style>
