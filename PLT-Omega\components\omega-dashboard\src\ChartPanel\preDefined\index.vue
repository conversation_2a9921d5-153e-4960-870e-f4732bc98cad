<template>
  <div class="app-container" style="display: flex">
    <el-card
      id="dataPanel"
      style="width: 400px; margin-right: 20px; text-align: left"
    >
      <el-form class="chart-form" size="mini" label-position="top">
        <el-form-item label="图表名称:">
          <el-input
            v-model="chartName"
            size="mini"
            placeholder="未命名"
            :maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="图表描述:">
          <el-input
            v-model="chartDesc"
            size="mini"
            placeholder="请输入图表描述"
            :maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="隐藏图表头部:">
          <el-switch v-model="hideHeader"></el-switch>
        </el-form-item>
        <el-form-item label="去掉组件边距:">
          <el-switch v-model="noPadding"></el-switch>
        </el-form-item>
      </el-form>
      <div class="pre_header">预定义图表:</div>
      <CetTree
        v-if="!isEdit"
        :selectNode.sync="CetTree_components.selectNode"
        :checkedNodes.sync="CetTree_components.checkedNodes"
        :searchText_in.sync="CetTree_components.searchText_in"
        v-bind="CetTree_components"
        v-on="CetTree_components.event"
      ></CetTree>
      <div v-else>{{ componentName }}</div>
    </el-card>

    <el-card style="width: 100%" body-style="padding: 10px 20px;">
      <preConfig :chartType="chartType" :content.sync="content"></preConfig>
      <preView :chartType="chartType" :content="content"></preView>
    </el-card>
  </div>
</template>
<script>
import _ from "lodash";
import preConfig from "./preConfig.vue";
import preView from "./preView.vue";
import { componentList } from "../../componentList.js";
import { getProjectId } from "../../utils";

export default {
  components: { preConfig, preView },
  props: {
    preCompCfg: {
      type: String
    },
    isEdit: {
      type: Boolean
    }
  },
  computed: {
    componentName() {
      let comp = _.find(componentList, { id: this.chartType });
      return comp ? comp.name : "";
    }
  },
  watch: {
    preCompCfg: {
      handler() {
        if (!this.preCompCfg) {
          this.content = "";
          this.chartName = "";
          this.chartDesc = "";
          this.chartType = "";
          this.hideHeader = false;
          this.noPadding = false;
        } else {
          let preCompCfg = JSON.parse(this.preCompCfg);
          this.content = JSON.stringify(preCompCfg.content);
          this.chartName = preCompCfg.chart_name;
          this.chartDesc = preCompCfg.description;
          this.chartType = preCompCfg.content.chartType;
          this.hideHeader = preCompCfg.content.hideHeader;
          this.noPadding = preCompCfg.content.noPadding;
        }
      }
    },
    chartName() {
      this.updatePreCompCfg();
    },
    chartDesc() {
      this.updatePreCompCfg();
    },
    content() {
      this.updatePreCompCfg();
    },
    chartType() {
      this.updatePreCompCfg();
    },
    hideHeader() {
      this.updatePreCompCfg();
    },
    noPadding() {
      this.updatePreCompCfg();
    }
  },
  data() {
    return {
      chartName: undefined,
      chartDesc: undefined,
      chartType: "",
      hideHeader: false,
      noPadding: false,
      content: "",
      // components树组件
      CetTree_components: {
        inputData_in: componentList,
        selectNode: {}, //要包含nodeKey属性, 例:{ id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_components_currentNode_out,
          parentList_out: this.CetTree_components_parentList_out,
          checkedNodes_out: this.CetTree_components_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_components_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_components_allCheckNodes_out
        }
      }
    };
  },
  created() {
    let cList = componentList;
    let projectId = getProjectId();
    let paltformList = cList.filter(item => item.paltform === true);
    let projectList = cList.filter(item => item.paltform !== true);
    if (projectId === 0) {
      this.CetTree_components.inputData_in =
        this.processCompListTag(paltformList);
    } else if (projectId > 0) {
      this.CetTree_components.inputData_in =
        this.processCompListTag(projectList);
    } else {
      this.CetTree_components.inputData_in = this.processCompListTag(cList);
    }
  },
  methods: {
    updatePreCompCfg() {
      let cfg = {};
      if (this.preCompCfg) {
        cfg = JSON.parse(this.preCompCfg);
      }

      cfg.chart_name = this.chartName;
      cfg.description = this.chartDesc;
      if (this.content) {
        cfg.content = JSON.parse(this.content);
      } else {
        cfg.content = {};
      }

      cfg.content.chartType = this.chartType;
      cfg.content.hideHeader = this.hideHeader;
      cfg.content.noPadding = this.noPadding;
      this.$emit("update:preCompCfg", JSON.stringify(cfg));
    },
    // components 输出
    CetTree_components_currentNode_out(val) {
      this.content = "";
      this.chartType = val.id;
    },
    CetTree_components_parentList_out(val) {},
    CetTree_components_checkedNodes_out(val) {},
    CetTree_components_halfCheckNodes_out(val) {},
    CetTree_components_allCheckNodes_out(val) {},
    processCompListTag(data) {
      const tree = {};

      data.forEach(item => {
        const { tag } = item;

        if (!tag) {
          return; // 如果没有配置 tag，则直接返回
        }

        if (!tree[tag]) {
          tree[tag] = {
            name: tag,
            children: []
          };
        }

        tree[tag].children.push(item);
      });

      return Object.values(tree).length ? Object.values(tree) : data;
    }
  }
};
</script>
<style lang="scss" scoped>
.pre_header {
  margin-bottom: 10px;
}
</style>
