<!--
 * @Author: your name
 * @Date: 2021-07-27 15:33:15
 * @LastEditTime: 2021-08-05 10:27:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\ChartPanel\preDefined\preConfig.vue
-->
<template>
  <div class="app-container" style="display: flex">
    <component
      :is="cfgComponent"
      :content.sync="chartData"
      class="visualize-window"
    />
  </div>
</template>
<script>
import { componentList } from "../../componentList.js";
export default {
  components: {
    // pue: () => import(`../../preDefinedComponents/pue/config`)
  },
  props: {
    chartType: {
      type: String
    },
    content: {
      type: String
    }
  },
  data() {
    return {
      chartData: ""
    };
  },
  watch: {
    content: {
      handler(val) {
        this.chartData = val;
      }
    },
    chartData(val) {
      this.$emit("update:content", val);
    }
  },
  computed: {
    cfgComponent() {
      let component = componentList.find(item => item.id === this.chartType);
      if (component) {
        return component.cfgCmp;
      }
      return "";
    }
  },
  methods: {}
};
</script>
<style lang="scss" scoped></style>
