<!--
 * @Author: your name
 * @Date: 2021-05-31 17:54:53
 * @LastEditTime: 2022-03-16 16:21:53
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\ChartPanel\preDefined\preView.vue
-->
<template>
  <div class="pane-container">
    <component
      :is="viewComponent"
      :content="content"
      :chart-style="chartStyle"
      class="visualize-window"
    />
  </div>
</template>
<script>
import { componentList } from "../../componentList.js";

export default {
  components: {
    // pue: () => import(`../../preDefinedComponents/pue/view`)
  },
  props: {
    content: {
      type: String,
      required: true
    },
    chartStyle: {
      require: false,
      type: Object
    },
    chartType: {
      type: String,
      default: "table"
    }
  },
  data() {
    return {};
  },
  computed: {
    viewComponent() {
      let component = componentList.find(item => item.id === this.chartType);
      if (component) {
        return component.viewCmp;
      }
      return "";
    }
  }
};
</script>
<style lang="scss" scoped>
.pane-container {
  display: flex;
  height: 100%;
  .visualize-window {
    width: 100%;
  }
}
</style>
