<template>
  <div>
    <!-- 弹窗组件 -->
    <CetDialog v-bind="CetDialog_pagedialog" v-on="CetDialog_pagedialog.event">
      <template v-slot:footer>
        <span>
          <!-- preserve按钮组件 -->
          <CetButton
            v-bind="CetButton_preserve"
            v-on="CetButton_preserve.event"
          ></CetButton>
          <!-- cancel按钮组件 -->
          <CetButton
            v-bind="CetButton_cancel"
            v-on="CetButton_cancel.event"
          ></CetButton>
        </span>
      </template>
      <el-form ref="form" :model="form">
        <el-form-item
          prop="chartid"
          label="请选择作为底图的组件"
          label-width="200px"
        >
          <ElSelect
            v-model="form.chartid"
            v-bind="ElSelect_backgroundItem"
            v-on="ElSelect_backgroundItem.event"
          >
            <ElOption
              v-for="item in ElOption_backgroundItem.options_in"
              :key="item[ElOption_backgroundItem.key]"
              :label="item[ElOption_backgroundItem.label]"
              :value="item[ElOption_backgroundItem.value]"
              :disabled="item[ElOption_backgroundItem.disabled]"
            ></ElOption>
          </ElSelect>
        </el-form-item>
      </el-form>
    </CetDialog>
  </div>
</template>
<script>
export default {
  name: "SetBackground",
  components: {},
  computed: {},
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    inputData_in: {
      type: Object
    }
  },
  data() {
    return {
      form: {
        chartid: -1
      },
      CetDialog_pagedialog: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        title: "弹窗表单",
        width: "500px",
        event: {
          openTrigger_out: this.CetDialog_pagedialog_openTrigger_out,
          closeTrigger_out: this.CetDialog_pagedialog_closeTrigger_out
        }
      },

      // preserve组件
      CetButton_preserve: {
        visible_in: true,
        disable_in: false,
        title: "确定",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_preserve_statusTrigger_out
        }
      },
      // preserve组件
      CetButton_cancel: {
        visible_in: true,
        disable_in: false,
        title: "取消",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_cancel_statusTrigger_out
        }
      },
      // backgroundItem组件
      ElSelect_backgroundItem: {
        value: "",
        style: {
          width: "200px"
        },
        event: {
          change: this.ElSelect_backgroundItem_change_out
        }
      },
      // backgroundItem组件
      ElOption_backgroundItem: {
        options_in: [],
        key: "id",
        value: "id",
        label: "chart_name",
        disabled: "disabled"
      }
    };
  },
  watch: {
    //弹窗页面默认和弹窗的交互
    openTrigger_in(val) {
      this.CetDialog_pagedialog.openTrigger_in = this._.cloneDeep(val);
    },
    closeTrigger_in(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    queryId_in(val) {},
    inputData_in(val) {
      this.form.chartid = val.backGroundItemId > 0 ? val.backGroundItemId : "";
      this.ElOption_backgroundItem.options_in = val.myChartList;
    }
  },
  methods: {
    CetDialog_pagedialog_openTrigger_out(val) {
      this.$emit("openTrigger_out", val);
    },
    CetDialog_pagedialog_closeTrigger_out(val) {
      this.$emit("closeTrigger_out", val);
    },
    CetButton_preserve_statusTrigger_out(val) {
      this.$emit("save", this.form.chartid);
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    CetButton_cancel_statusTrigger_out(val) {
      this.CetDialog_pagedialog.closeTrigger_in = this._.cloneDeep(val);
    },
    // backgroundItem输出,方法名要带_out后缀
    ElSelect_backgroundItem_change_out() {},

    no() {}
  },
  created: function () {}
};
</script>
<style lang="scss" scoped></style>
