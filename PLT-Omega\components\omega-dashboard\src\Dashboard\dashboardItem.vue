<template>
  <div :class="{ fullheight: autoHeight }" ref="container">
    <div v-show="!showChartPanel" :class="{ fullheight: autoHeight }">
      <div class="tool-bar text-T1">
        <div>
          <span class="db-name text-T1">{{ dashboard.name }}</span>
          <span class="text-T3">{{ dashboard.description }}</span>
        </div>
        <div v-show="mode === 'edit'">
          <el-button type="primary" size="mini" @click="handleBackGroundItem">
            设置底图组件
          </el-button>
          <el-button type="primary" size="mini" @click="editBackGroundItem">
            编辑底图组件
          </el-button>
          <el-button type="primary" size="mini" @click="cancelBackGroundItem">
            取消底图组件
          </el-button>
          <!-- <el-button type="primary" size="mini" @click="handleShare">分享</el-button> -->
          <el-button type="primary" size="mini" @click="handleLinkChart">
            我的图表
          </el-button>
        </div>
      </div>
      <div v-if="bgItemId > 0" class="background-item">
        <preView
          v-if="getChartItem(bgItemId).content.componentType === 'predefined'"
          :key="bgItemId"
          :ref="`chartInstance${bgItemId}`"
          :chartType="getChartItem(bgItemId).content.chartType"
          :content="JSON.stringify(getChartItem(bgItemId).content)"
          :chartStyle="{
            height: `100%`
          }"
        ></preView>
        <visualize-panel
          v-else
          :key="bgItemId"
          :ref="`chartInstance${bgItemId}`"
          :data="results[bgItemId] ? results[bgItemId] : []"
          :schema="getChartItem(bgItemId).content.allSelected"
          :chart-type.sync="getChartItem(bgItemId).content.chartType"
          :is-edit-mode="false"
          :chart-style="{
            height: `100%`
          }"
        />
      </div>
      <grid-layout
        v-if="charts.length !== 0"
        v-loading="loading"
        :layout.sync="layout"
        :col-num="60"
        :row-height="gridRowHeight"
        :is-draggable="mode === 'edit'"
        :is-resizable="mode === 'edit'"
        :is-mirrored="false"
        :vertical-compact="false"
        :pane-container="false"
        :prevent-collision="false"
        :margin="[gridMarginTB, gridMarginLR]"
        :use-css-transforms="false"
        style="min-height: 500px"
        @layout-updated="handleLayoutChange"
      >
        <grid-item
          v-for="item in layout || []"
          :key="item.i"
          :x="item.x"
          :y="item.y"
          :w="item.w"
          :h="item.h"
          :i="item.i"
          @resized="handleResize"
          dragAllowFrom=".operation-bar"
        >
          <el-card
            v-loading="chartLoading[item.i]"
            class="visualize-card"
            :class="{
              'no-header': getChartItem(item.i).content.hideHeader === true,
              'no-padding': getChartItem(item.i).content.noPadding === true,
              'edit-card':
                getChartItem(item.i).content.hideHeader === true &&
                mode === 'edit'
            }"
            body-style="padding: 10px;"
          >
            <div slot="header" class="operation-bar">
              <div>
                <span>{{ getChartItem(item.i).chart_name }}</span>
              </div>
              <div>
                <i
                  v-show="getJumpPath(getChartItem(item.i))"
                  class="el-icon-share text-ZS"
                  @click="handleJumpto(getChartItem(item.i))"
                />
                <i
                  v-show="mode === 'edit'"
                  class="el-icon-edit text-ZS"
                  @click="handleEdit(getChartItem(item.i))"
                />
                <i
                  v-show="mode === 'edit'"
                  class="el-icon-delete text-ZS"
                  @click="handleDelete(getChartItem(item.i))"
                />
                <el-tooltip
                  v-show="getChartItem(item.i).description"
                  :content="getChartItem(item.i).description"
                  class="item"
                  effect="dark"
                  placement="top-end"
                >
                  <i class="el-icon-info text-ZS" style="cursor: pointer" />
                </el-tooltip>
              </div>
            </div>
            <preView
              v-if="getChartItem(item.i).content.componentType === 'predefined'"
              :key="item.i"
              :ref="`chartInstance${item.i}`"
              :chartType="getChartItem(item.i).content.chartType"
              :content="JSON.stringify(getChartItem(item.i).content)"
              :chartStyle="{
                height: getChartHeight(item)
              }"
            ></preView>
            <visualize-panel
              v-else
              :key="item.i"
              :ref="`chartInstance${item.i}`"
              :data="results[item.i] ? results[item.i] : []"
              :schema="getChartItem(item.i).content.allSelected"
              :chart-type.sync="getChartItem(item.i).content.chartType"
              :is-edit-mode="false"
              :chart-style="{
                height: getChartHeight(item)
              }"
            />
          </el-card>
        </grid-item>
      </grid-layout>

      <el-dialog title="我的图表" :visible.sync="showChartList">
        <el-button type="primary" size="mini" @click="createChart">
          新建
        </el-button>
        <el-table :data="myChartList" :max-height="500">
          <el-table-column type="index"></el-table-column>
          <el-table-column label="名称" width="200" prop="chart_name" />
          <el-table-column label="描述" prop="description" />
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                :disabled="isExisted(scope.row)"
                @click="linkChart(scope.row)"
              >
                添加
              </el-button>
              <el-button
                size="mini"
                type="warning"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                size="mini"
                type="danger"
                @click="deleteChart(scope.row)"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button
            type="primary"
            plain
            size="small"
            @click="showChartList = false"
          >
            取消
          </el-button>
        </span>
      </el-dialog>
      <SetBackground
        v-bind="SetBackground"
        v-on="SetBackground.event"
      ></SetBackground>
    </div>
    <ChartPanel
      v-if="showChartPanel"
      :currentChartId="currentChartId"
      @handleReturn="handleReturn"
      @changeCurrentChart="changeCurrentChart"
    ></ChartPanel>
  </div>
</template>
<script>
import { GridLayout, GridItem } from "vue-grid-layout";
import visualizePanel from "../ChartPanel/components/visualizePanel";
import {
  chartByDashboard,
  updateDashboard,
  addChartToDB,
  changeChartToDB,
  unMapChartDb
} from "../api/dashboard";
import { chartList, deleteChart } from "../api/chart";
import { buildQueryData } from "../utils/buildQuery";
import { queryModel } from "../api/model";
import ChartPanel from "../ChartPanel";
import preView from "../ChartPanel/preDefined/preView";
import { componentList } from "../componentList.js";
import SetBackground from "./SetBackground.vue";
function isLineOverLap(line1, line2) {
  const start1 = {
    x: line1[0][0],
    y: line1[0][1]
  };
  const end1 = {
    x: line1[1][0],
    y: line1[1][1]
  };
  const start2 = {
    x: line2[0][0],
    y: line2[0][1]
  };
  const end2 = {
    x: line2[1][0],
    y: line2[1][1]
  };
  if (start1.y === start2.y && end1.y === end2.y) {
    if (start1.x >= start2.x && start1.x <= end2.x) {
      return true;
    } else {
      return false;
    }
  } else {
    return false;
  }
}
export default {
  components: {
    GridLayout,
    GridItem,
    visualizePanel,
    ChartPanel,
    preView,
    SetBackground
  },
  props: {
    dashboard: {
      required: false,
      type: Object,
      default: () => {
        return {};
      }
    },
    mode: {
      required: false,
      type: String,
      default: "view"
    },
    autoHeight: {
      type: Boolean,
      default: false
    },
    // 是否显示标题
    viewMode: {
      type: Number
    }
  },
  data() {
    return {
      charts: [],
      results: {},
      loading: false,
      layout: [],
      myChartList: [],
      showChartList: false,
      chartLoading: {},
      currentChartId: -1, //当不显示新建或编辑图表的界面时，该值置1; 显示新建界面时，该值置0; 显示编辑界面时，该值为对应图表的id
      showChartPanel: false,
      gridMarginTB: 10,
      gridMarginLR: 10,
      gridRowHeight: 30,
      //底图组件id, 初始为-1无效值
      bgItemId: -1,
      // SetBackground弹窗页面组件
      SetBackground: {
        openTrigger_in: new Date().getTime(),
        closeTrigger_in: new Date().getTime(),
        queryId_in: 0,
        inputData_in: null,
        event: {
          openTrigger_out: this.SetBackground_openTrigger_out,
          closeTrigger_out: this.SetBackground_closeTrigger_out,
          save: this.processBackGround
        }
      }
    };
  },
  activated() {
    this.getRelatedChartList();
  },
  watch: {
    "dashboard.id": {
      immediate: true,
      handler(value) {
        if (!value) {
          this.charts = [];
          this.layout = [];
        } else {
          this.getRelatedChartList();
        }
      }
    },
    async autoHeight() {
      await this.$nextTick();
      this.gridRowHeight = this.getRowHeight();
    },
    async layout() {
      await this.$nextTick();
      this.gridRowHeight = this.getRowHeight();
    },
    async viewMode() {
      await this.$nextTick();
      this.gridRowHeight = this.getRowHeight();
    }
  },
  methods: {
    isPreDefinedComp() {},
    changeCurrentChart(val) {
      this.currentChartId = val; //修改currentChart
    },

    handleReturn() {
      this.showChartPanel = !this.showChartPanel;
      this.currentChartId = -1;
      this.$emit("handleShowDashboardList");
      this.getRelatedChartList();
    },
    createChart() {
      this.showChartList = false;
      this.$emit("handleShowDashboardList");
      this.showChartPanel = !this.showChartPanel;
      this.currentChartId = 0; //currentChartId置零，显示新建图表界面
    },
    //#region 获取dashboard关联图表并进行初始渲染和布局处理

    //获取dashboard关联图表列表
    getRelatedChartList() {
      if (!this.dashboard.id) {
        return;
      }
      this.loading = true;
      //根据dashboard的id获取关联的chart列表
      chartByDashboard(this.dashboard.id).then(resp => {
        this.loading = false;

        //获取的chart列表数据保存到charts中
        this.charts = resp.data || [];

        //获取chart的数据
        this.getChartListData();

        //获取的dashboard底图id, 并设置
        this.bgItemId = this.dashboard.content.bgId
          ? this.dashboard.content.bgId
          : -1;

        //处理chart布局
        this.processLayout();

        this.handleLayoutChange();
      });
    },
    //获取所有关联chart列表的数据
    getChartListData() {
      //遍历charts调接口获取charts的数据, 保存到this.results中
      this.charts.forEach(chart => {
        this.$set(this.results, chart.id, []);
        this.$set(this.chartLoading, chart.id, false);
        chart.content.allSelected = [];
        chart.content.allSelected = chart.content.allSelected
          .concat(chart.content.selectedCalcul)
          .concat(chart.content.selectedDimension);
        if (chart.content.componentType !== "predefined") {
          this.queryChartData(
            {
              dataSrc: chart.content.dataSrc,
              selectedCalcul: chart.content.selectedCalcul,
              selectedDimension: chart.content.selectedDimension,
              orderByStrs: chart.content.orderByStrs,
              filters: chart.content.filters,
              treeNode: chart.content.treeNode,
              limit: chart.content.limit
            },
            chart
          );
        }
      });
    },
    //获取图表数据的逻辑
    queryChartData(sqlSentence, item) {
      this.$set(this.chartLoading, item.id, true);
      let queryData = buildQueryData(sqlSentence);
      queryModel(queryData).then(resp => {
        this.$set(this.chartLoading, item.id, false);
        this.$set(this.results, item.id, resp.data);
      });
    },

    //处理关联chart列表的布局
    processLayout() {
      this.layout = [];
      const dashboardLayout =
        (this.dashboard.content && this.dashboard.content.layout) || [];
      //如果有关联chart没有布局, 则生成布局, 在新增图表关联后需要执行该逻辑
      this.charts.forEach(chart => {
        if (
          !dashboardLayout.find(layoutItem => layoutItem.id === chart.id) &&
          chart.id !== this.bgItemId
        ) {
          this.generatePosition(chart, dashboardLayout);
        }
      });
      //保存最新layout到this.layout, 过滤掉未关联chart的布局
      this.layout = dashboardLayout.filter(item => {
        return this.charts.find(chart => chart.id === item.id);
      });
    },
    //处理位置坐标计算逻辑
    handleCaculPos(layout) {
      // const layout = JSON.parse(JSON.stringify(layout))
      const bottomItems = [];
      layout.forEach(i => {
        i.yOffSet = i.y + i.h;
        i.xOffSet = i.x + i.w;
        i.bottomLine = [
          [i.x, i.yOffSet],
          [i.xOffSet, i.yOffSet]
        ];
        i.topLine = [
          [i.x, i.y],
          [i.xOffSet, i.y]
        ];
      });
      layout.forEach(i => {
        const flag = layout.every(j => {
          return !isLineOverLap(i.bottomLine, j.topLine);
        });
        if (flag) {
          bottomItems.push(i);
        }
      });
      return bottomItems;
    },
    //生成图表坐标位置
    generatePosition(chart, layout) {
      let posObj;
      if (layout.length === 0) {
        posObj = {
          id: chart.id,
          x: 0,
          y: 0,
          w: 12,
          h: 9,
          i: chart.id
        };
      } else {
        const bottomItems = this.handleCaculPos(layout);
        const highestItem = bottomItems.reduce((result, item) => {
          if (result.bottomLine[0][1] > item.bottomLine[0][1]) {
            result = item;
          }
          return result;
        }, bottomItems[0]);
        posObj = {
          id: chart.id,
          x: highestItem.x,
          y: highestItem.yOffSet,
          w: highestItem.w,
          h: 9,
          i: chart.id
        };
      }

      layout.push(posObj);
    },
    //#endregion

    //布局变化, 保存布局信息
    handleLayoutChange() {
      this.dashboard.content = this.dashboard.content || {};
      this.dashboard.content.layout = this.layout;
      updateDashboard(this.dashboard).then(() => {});
    },
    //处理大小变化逻辑
    handleResize(i) {
      this.$refs[`chartInstance${i}`][0].$children[0].$emit("resized");
    },

    //根据图表id获取图表对象
    getChartItem(id) {
      return this.charts.find(chart => chart.id === id);
    },

    //判断图表是否已经存在dashboard页面中
    isExisted(chart) {
      return this.charts.findIndex(item => item.id === chart.id) >= 0;
    },

    //#region 新增图表和dashboard关联, 删除, 编辑相关逻辑
    //处理我的图表逻辑, 获取并筛选图表列表
    handleLinkChart() {
      chartList().then(resp => {
        this.myChartList = resp.data;
        for (let i = 0; i < this.myChartList.length; ) {
          if (this.isExisted(this.myChartList[i])) {
            this.myChartList.splice(i, 1);
            continue;
          }
          i++;
        }
        this.showChartList = true;
      });
    },
    //添加图表到dashboard
    linkChart(chart) {
      const data = {
        chart_id: chart.id,
        dashboard_id: this.dashboard.id
      };
      addChartToDB(data).then(() => {
        this.showChartList = false;
        this.getRelatedChartList();
        this.$message({
          type: "success",
          message: "添加成功！"
        });
      });
    },

    // 判断是否配置了跳转路径
    getJumpPath(chart) {
      let chartType = this._.get(chart, "content.chartType");
      let comp = this._.find(componentList, ["id", chartType]);
      return this._.get(comp, "route", "");
    },
    handleJumpto(chart) {
      let path = this.getJumpPath(chart);
      if (path) this.$router.push({ path });
    },
    //编辑图表
    handleEdit(chart) {
      this.showChartList = false;
      this.$emit("handleShowDashboardList");
      this.showChartPanel = !this.showChartPanel;
      this.currentChartId = chart.id;
    },
    //取消图表和dashboard的关联
    handleDelete(chart) {
      this.$confirm(
        "该操作将从该看板中删除该图表，并不会删除原图表，确认继续?",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }
      ).then(async () => {
        const deleteChartIndex = this.layout.findIndex(
          item => item.id === chart.id
        );
        const layout = JSON.parse(JSON.stringify(this.layout));
        layout.splice(deleteChartIndex, 1);
        this.dashboard.content.layout = layout;
        const data = {
          chart_id: chart.id,
          dashboard_id: this.dashboard.id
        };

        // 改为串行调用，避免模型服务偶发性死锁
        const { code } = await updateDashboard(this.dashboard);
        if (code === 0) {
          unMapChartDb(data).then(() => {
            this.getRelatedChartList();
            this.$message({
              type: "success",
              message: "删除成功!"
            });
          });
        }

        // Promise.all([updateDashboard(this.dashboard), unMapChartDb(data)]).then(
        //   () => {
        //     this.getRelatedChartList();
        //     this.$message({
        //       type: "success",
        //       message: "删除成功!"
        //     });
        //   }
        // );
      });
    },
    //彻底删除图表
    deleteChart(chart) {
      this.$confirm(
        `确定要删除图表：${chart.chart_name}？删除后会影响所有使用该图表的看板。`,
        "提示"
      ).then(() => {
        deleteChart({ id: chart.id }).then(() => {
          this.handleLinkChart();
          this.$message({
            type: "success",
            message: "删除成功！"
          });
        });
      });
    },
    //#endregion

    //#region dashboard底图设置逻辑
    //生成选择底图组件的待选图表列表, 传递当前底图id
    handleBackGroundItem() {
      let backChartList = [];
      chartList().then(resp => {
        backChartList = resp.data;
        for (let i = 0; i < backChartList.length; ) {
          if (
            this.isExisted(backChartList[i]) &&
            this.bgItemId !== backChartList[i].id
          ) {
            backChartList.splice(i, 1);
            continue;
          }
          i++;
        }
        this.SetBackground.openTrigger_in = new Date().getTime();
        let inputData = {
          myChartList: backChartList,
          backGroundItemId: this.bgItemId
        };
        this.SetBackground.inputData_in = inputData;
      });
    },
    //如有有底图组件, 编辑底图组件
    editBackGroundItem() {
      if (!this.bgItemId || this.bgItemId < 0) {
        this.$message({
          type: "warning",
          message: "请先设置底图组件"
        });
        return;
      }
      this.handleEdit(this.getChartItem(this.bgItemId));
    },
   async cancelBackGroundItem() {
      this.dashboard.content.bgId = -1;
      let data = {
        chart_id: this.bgItemId,
        dashboard_id: this.dashboard.id
      };
      
      // 改为串行调用，避免模型服务偶发性死锁
       const { code } = await updateDashboard(this.dashboard);
        if (code === 0) {
          unMapChartDb(data).then(() => {
            this.getRelatedChartList();
            this.$message({
              type: "success",
              message: "删除成功!"
            });
          });
        }

      // Promise.all([updateDashboard(this.dashboard), unMapChartDb(data)]).then(
      //   () => {
      //     this.getRelatedChartList();
      //     this.$message({
      //       type: "success",
      //       message: "取消成功!"
      //     });
      //   }
      // );
    },
    // SetBackground弹窗页面输出
    SetBackground_openTrigger_out(val) {},
    SetBackground_closeTrigger_out(val) {},
    //选择完底图组件后处理逻辑
    processBackGround(val) {
      if (val > 0) {
        if (this.bgItemId < 1) {
          //之前没有设置底图, 直接设置即可
          this.setBGItem(val);
        } else if (this.bgItemId !== val) {
          //没有变化, 不处理
          //不相同的id再先删除之前的地图组件关联, 再设置
          this.changeBGItem(val);
        }
      }
    },
    setBGItem(bgId) {
      let data = {
        chart_id: bgId,
        dashboard_id: this.dashboard.id
      };
      this.dashboard.content = this.dashboard.content || {};
      this.dashboard.content.bgId = bgId;
      //关联图表和dashboard, 再更新content的bgId字段
      addChartToDB(data).then(() => {
        updateDashboard(this.dashboard).then(() => {
          this.getRelatedChartList();
        });
      });
    },
    //修改底图组件
    changeBGItem(bgId) {
      let data = {
        chart_id: bgId,
        old_chart_id: this.bgItemId,
        dashboard_id: this.dashboard.id
      };
      this.dashboard.content = this.dashboard.content || {};
      this.dashboard.content.bgId = bgId;
      //修改图表和dashboard的关联关系, 再更新content的bgId字段
      changeChartToDB(data).then(() => {
        updateDashboard(this.dashboard).then(() => {
          this.getRelatedChartList();
        });
      });
    },
    //#endregion
    getChartHeight(item) {
      let noHeader = this.getChartItem(item.i).content.hideHeader === true;
      let noPadding = this.getChartItem(item.i).content.noPadding === true;

      let height = item.h * 30 + 10 * (item.h - 1) - 60;
      if (noHeader) {
        height = height + 36;
      }
      if (noPadding) {
        height = height + 20;
      }

      return `${height}px`;
    },
    // 计算gridlayout的rowheight
    calcRowHeight(height, margin, layout) {
      const item = _.maxBy(layout, item => item.y + item.h);

      if (!item) {
        return 30;
      }
      const rowCount = item.y + item.h;

      const rowh = (height - margin * 2 - (rowCount - 1) * margin) / rowCount;
      return rowh;
    },
    getRowHeight() {
      if (!this.autoHeight) {
        return 30;
      }
      const containerHeight = this.$refs.container?.offsetHeight;
      if (!containerHeight) {
        return 30;
      }
      return this.calcRowHeight(
        this.viewMode == 1 ? containerHeight : containerHeight - 45,
        this.gridMarginTB,
        this.layout
      );
    },
    setRowHeight: _.debounce(async function () {
      console.log("resize----");
      await this.$nextTick();
      this.gridRowHeight = this.getRowHeight();
    }, 300)
  },
  mounted() {
    window.removeEventListener("resize", this.setRowHeight);
    window.addEventListener("resize", this.setRowHeight);
  },
  activated() {
    window.removeEventListener("resize", this.setRowHeight);
    window.addEventListener("resize", this.setRowHeight);
  },
  deactivated() {
    window.removeEventListener("resize", this.setRowHeight);
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.setRowHeight);
  }
};
</script>
<style lang="scss" scoped>
.tool-bar {
  display: flex;
  justify-content: space-between;
  border-top: none;
  height: 45px;
  line-height: 45px;
  padding: 0 10px;
  position: relative;
  .db-name {
    font-size: 1.2em;
    font-weight: 600;
    margin-left: 0;
  }
  span {
    font-size: 0.8em;
    margin-left: 10px;
  }
}
.visualize-card {
  height: 100%;
  ::v-deep .el-card__header {
    padding: 0;
    .operation-bar {
      font-size: 14px;
      display: flex;
      justify-content: space-between;
      height: 35px;
      padding: 0 10px;
      line-height: 35px;
      z-index: 9;
      i {
        margin-right: 10px;
        cursor: pointer;
      }
    }
  }
  ::v-deep .el-card__body {
    height: calc(100% - 36px);
  }
}

.no-header {
  ::v-deep .el-card__header {
    display: none;
  }
  height: calc(100% - 2px);
  ::v-deep .el-card__body {
    height: calc(100%);
  }
  ::v-deep .visualize-window {
    height: 100% !important;
  }
}

.edit-card:hover {
  ::v-deep .el-card__header {
    display: block;
  }
  ::v-deep .pane-container {
    height: calc(100% - 36px);
  }
}

.no-padding {
  height: calc(100% - 2px);
  ::v-deep .el-card__body {
    padding: 0px !important;
  }
}

.background-item {
  position: absolute;
  top: 55px;
  left: 10px;
  right: 10px;
  bottom: 10px;
}
</style>
