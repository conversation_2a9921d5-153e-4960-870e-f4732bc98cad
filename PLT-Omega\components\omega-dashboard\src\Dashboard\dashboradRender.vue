<template>
  <div :class="{ 'simple-panel': this.isSimplePanel, fullheight: autoHeight }">
    <dashboardItem
      :dashboard="dashboard"
      mode="view"
      :viewMode="mode"
      :autoHeight="aHeight"
    />
  </div>
</template>
<script>
import dashboardItem from "./dashboardItem";
const MODE = {
  Normal: 0,
  NoHeader: 1,
  AutoHeight: 1
};
export default {
  components: { dashboardItem },
  props: {
    dashboard: Object,
    mode: Number,
    autoHeight: Number
  },
  computed: {
    isSimplePanel() {
      return this.mode === MODE.NoHeader;
    },
    aHeight() {
      return this.autoHeight === MODE.AutoHeight;
    }
  }
};
</script>
<style lang="scss" scoped>
.simple-panel {
  ::v-deep .tool-bar {
    display: none;
  }
  ::v-deep .background-item {
    top: 10px;
  }
}
</style>
