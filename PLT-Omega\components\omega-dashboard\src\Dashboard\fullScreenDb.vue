<!--
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2021-12-09 14:36:45
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\Dashboard\fullScreenDb.vue
-->
<template>
  <div
    :class="{
      'simple-panel': this.viewMode === 1,
      fullheight: autoHeight,
      'transparent-comp-background': isTransparentCompBg
    }"
  >
    <dashboardItem
      :dashboard="currentDashboard"
      mode="view"
      :viewMode="viewMode"
      :autoHeight="autoHeight"
    />
  </div>
</template>
<script>
import dashboardItem from "./dashboardItem";
import { getdDashboardByName } from "../api/dashboard";

export default {
  components: { dashboardItem },
  data() {
    return {
      currentDashboard: undefined,
      viewMode: 0
    };
  },
  computed: {
    routeMode() {
      return this.$route.params.mode;
    },
    routeName() {
      return this.$route.params.name;
    },
    autoHeight() {
      return this.$route.query.autoHeight === "1";
    },
    //设置组件是否为透明背景, 该配置应用在均为预定义组件的场景比较合适, 可以通过预定义组件内部来控制背景
    isTransparentCompBg() {
      return this.$route.query.transparentCompBg === "1";
    }
  },
  watch: {
    routeMode() {
      this.setDashboard();
    },
    routeName() {
      this.setDashboard();
    },
    isTransparentCompBg() {
      this.setDashboard();
    }
  },
  created() {
    this.setDashboard();
  },

  methods: {
    setDashboard() {
      getdDashboardByName(this.$route.params.name).then(resp => {
        this.currentDashboard = resp.data[0];
      });
      let vMode = parseInt(this.$route.params.mode);
      if (this._.isNaN(vMode)) {
        vMode = 0;
      }
      this.viewMode = vMode;
    }
  }
};
</script>
<style lang="scss" scoped>
.simple-panel {
  ::v-deep .tool-bar {
    display: none;
  }
  ::v-deep .background-item {
    top: 10px;
  }
}
::v-deep * {
  box-sizing: border-box;
}
.transparent-comp-background {
  ::v-deep .visualize-card {
    background-color: transparent !important;
    border-color: transparent !important;
    box-shadow: none !important;
  }
  ::v-deep .el-card__header {
    border-color: transparent !important;
  }
}
</style>
