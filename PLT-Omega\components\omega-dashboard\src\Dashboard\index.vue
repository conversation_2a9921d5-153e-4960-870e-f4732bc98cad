<template>
  <div
    v-loading="loading"
    class="dashboard-container"
    :class="{ 'no-list-panel': !this.showDashboardList }"
  >
    <el-card
      body-style="padding: 0px;"
      class="dashboard-list"
      shadow="never"
      v-show="showDashboardList"
    >
      <div slot="header">
        <span>{{ dashboardTitle }}</span>
        <el-button
          v-show="isEdit"
          type="primary"
          size="mini"
          @click="addDashboard"
        >
          新建
        </el-button>
      </div>
      <ul class="dashboard-list-ul">
        <draggable
          v-model="dashboardList"
          :group="{ name: 'dashboard', pull: true }"
          class="draggable-wrapper"
          @change="handleOrderChange"
        >
          <li
            v-for="item in dashboardList"
            :key="item.id"
            :class="{
              'dashboard-list-item': true,
              'text-ZS': currentDashboard.id === item.id
            }"
            @click="switchDb(item)"
          >
            <span>
              <i class="el-icon-document" />
              <el-tooltip
                effect="dark"
                :content="item.name"
                placement="top"
                :open-delay="888"
              >
                <span>{{ item.name }}</span>
              </el-tooltip>
            </span>
            <div @click.stop="">
              <el-dropdown
                v-show="isEdit"
                size="mini"
                trigger="click"
                @command="handleCommand"
              >
                <span class="el-dropdown-link">
                  <i class="el-icon-more" />
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :command="{
                      type: 'edit',
                      target: item
                    }"
                  >
                    编辑
                  </el-dropdown-item>
                  <el-dropdown-item
                    :command="{
                      type: 'delete',
                      target: item
                    }"
                  >
                    删除
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>

            <!-- {{ item.description }} -->
          </li>
        </draggable>
      </ul>
    </el-card>
    <dashboardItem
      :dashboard="currentDashboard"
      :mode="mode"
      class="dashboard-wrapper"
      @handleShowDashboardList="handleShowDashboardList"
    />
    <el-dialog width="500px" :title="title" :visible.sync="editDialogVisible">
      <el-form label-width="100px;">
        <el-form-item label=" 名称：">
          <el-input
            v-model="dbObj.name"
            size="small"
            style="width: 350px"
            placeholder="请输入名称"
            :maxlength="20"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label=" 描述：">
          <el-input
            type="textarea"
            :rows="3"
            v-model="dbObj.description"
            size="small"
            style="width: 350px"
            placeholder="请输入描述"
            :maxlength="50"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain size="small" @click="handleSubmit">
          确定
        </el-button>
        <el-button
          type="primary"
          plain
          size="small"
          @click="editDialogVisible = false"
        >
          取消
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import draggable from "vuedraggable";
import dashboardItem from "./dashboardItem";
import {
  addDashboard,
  updateDashboard,
  dashboardList,
  deleteDashboard,
  dbOrder
} from "../api/dashboard";
import auth from "@omega/auth";

export default {
  components: { dashboardItem, draggable },
  props: {
    dashboardTitle: {
      type: String,
      default: "看板"
    }
  },

  data() {
    return {
      dashboardList: [],
      currentDashboard: undefined,
      editDialogVisible: false,
      dbObj: {},
      loading: false,
      isCollapse: false,
      mode: "read",
      showDashboardList: true,
      title: ""
    };
  },
  computed: {
    isEdit() {
      return this.mode === "edit";
    }
  },
  created() {

    if (auth.user.isRoot()) {
      this.mode = "edit";
    } else {
      this.mode = "read";
    }
    this.getList();
  },
  methods: {
    handleShowDashboardList() {
      this.showDashboardList = !this.showDashboardList;
      this.getList();
    },
    getList() {
      if (this.showDashboardList) {
        this.loading = true;
        dashboardList().then(resp => {
          this.loading = false;

          let rawData = resp.data;
          this.dashboardList = rawData.sort((v1, v2) => {
            if (v1.name === v2.name) {
              return 0;
            } else {
              return v1.name < v2.name ? -1 : 1;
            }
          });

          this.currentDashboard = this.dashboardList[0]; //把dashboardList中的第一项作为currentDashboard
        });
      }
    },
    switchDb(db) {
      if (db.id === this.currentDashboard.id) {
        // this.getList();
        return;
      }
      this.currentDashboard = db; //切换currentDashboard
    },
    addDashboard() {
      this.dbObj = {};
      this.title = "新建看板";
      this.editDialogVisible = true;
    },
    editDashboard(db) {
      this.dbObj = Object.assign({}, db);
      this.title = "编辑看板";
      this.editDialogVisible = true;
    },
    handleCommand(cmd) {
      cmd.type === "edit"
        ? this.editDashboard(cmd.target)
        : this.deleteDashboard(cmd.target);
      // if (cmd.type === "edit") {
      //   this.editDashboard(cmd.target);
      // } else {
      //   this.deleteDashboard(cmd.target);
      // }
    },
    handleSubmit() {
      if (!this.dbObj.name) {
        this.$message({
          type: "warning",
          message: "保存失败，请输入名称"
        });
        return;
      }
      if (this.dbObj.id) {
        updateDashboard(this.dbObj).then(resp => {
          this.getList();
          this.editDialogVisible = false;
        });
      } else {
        addDashboard(this.dbObj).then(resp => {
          this.getList();
          this.editDialogVisible = false;
        });
      }
    },
    handleOrderChange() {
      const data = {
        order: this.dashboardList.map(item => item.id)
      };
      data.userId = auth.user.getUserId();
      dbOrder(data).then(() => {
        console.log("order");
      });
    },
    deleteDashboard(db) {
      this.$confirm(`确定要删除${db.name}看板吗？`, "提示").then(() => {
        deleteDashboard({ id: db.id }).then(() => {
          this.getList();
          this.$message({
            type: "success",
            message: "删除成功！"
          });
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  // display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  ::v-deep * {
    box-sizing: border-box;
  }
  .dashboard-list {
    position: absolute;
    width: 250px;
    height: 100%;
    left: 0px;
    top: 0px;
    // min-height: 100%;
    padding: 20px 10px;
    ::v-deep .el-card__header {
      div {
        display: flex;
        justify-content: space-between;
        font-size: 14px;
        // color: #606266;
        i {
          cursor: pointer;
        }
      }
      padding: 5px 0px;
    }
    .dashboard-list-item {
      display: flex;
      justify-content: space-between;
      line-height: 35px;
      font-size: 14px;
      cursor: pointer;
      // color: #606266;

      i {
        margin-right: 10px;
        line-height: 35px;
      }
      span {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  .dashboard-wrapper {
    position: absolute;
    width: calc(100% - 250px);
    height: 100%;
    top: 0px;
    left: 250px;
  }
  .dashboard-list-ul {
    margin: 0;
    padding: 0;
  }
}

.no-list-panel {
  .dashboard-wrapper {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0px;
    left: 0px;
  }
}
</style>
