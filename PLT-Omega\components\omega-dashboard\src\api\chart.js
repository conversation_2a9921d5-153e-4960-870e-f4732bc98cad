/*
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2022-03-18 15:39:18
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\api\chart.js
 */
import { httping } from "./request.js";

import { getModelData, getProjectId } from "../utils";

export function createChart(data) {
  data = getModelData(data, "chart");

  data[0].content.projectId = getProjectId();
  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data
  });
}

export function updateChart(data) {
  data = getModelData(data, "chart");
  data[0].content.projectId = getProjectId();
  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data
  });
}

export function getChartById(id) {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    data: {
      rootID: id,
      rootLabel: "chart"
    }
  });
}

export function deleteChart(data) {
  return httping({
    url: `{{model-service}}/v1/chart`,
    method: "DELETE",
    data: [data.id]
  });
}
//根据projectId筛选chart
function filterChartByProjectID(res) {
  let fChartList = [];
  let chartList = res.data;
  let projectId = getProjectId();

  if (projectId === 0) {
    fChartList = chartList.filter(item => item.content.projectId === 0);
  } else if (projectId > 0) {
    fChartList = chartList.filter(item => item.content.projectId === projectId);
  } else {
    fChartList = chartList;
  }

  res.data = fChartList;

  return res;
}
export function chartList() {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    data: {
      rootID: 0,
      rootLabel: "chart"
    },
    transformResponse: [filterChartByProjectID] //对接口返回的数据结构进行处理
  });
}
