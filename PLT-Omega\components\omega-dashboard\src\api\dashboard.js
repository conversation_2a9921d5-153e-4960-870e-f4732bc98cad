import { httping } from "./request.js";
import { getModelData, getProjectId } from "../utils";
import auth from "@omega/auth";
import _ from "lodash";

export function addDashboard(data) {
  data = getModelData(data, "dashboard");
  data[0].content = {};
  data[0].content.projectId = getProjectId();
  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data
  });
}

export function updateDashboard(data) {
  data = getModelData(data, "dashboard");
  data[0].content.projectId = getProjectId();
  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data
  });
}

export function getdDashboardByName(name) {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    data: {
      rootLabel: "dashboard",
      rootCondition: {
        filter: {
          expressions: [
            {
              limit: name,
              operator: "EQ",
              prop: "name"
            }
          ]
        }
      }
    }
  });
}

export function deleteDashboard(data) {
  return httping({
    url: `{{model-service}}/v1/dashboard`,
    method: "DELETE",
    data: [data.id]
  });
}

function isProjectUser() {
  let custom = auth.user.getUserCustomConfig();
  if (_.isNil(custom)) {
    return true;
  }
  if (custom.userType === 4) {
    return true;
  }
  return false;
}

function processDashboardListResponse(response) {
  let res = response;
  if (!auth.user.isRoot() && isProjectUser()) {
    let data = res.data;
    let dashboardAuth = auth.user.getUserCustomConfig().dashboard;
    if (_.isNil(dashboardAuth)) {
      dashboardAuth = [];
    }

    res.data = data.filter(item => dashboardAuth.indexOf(item.id) !== -1);
  }

  return res;
}
//根据projectId筛选dashboard
function filterDashboarByProjectID(res) {
  let fDashboardList = [];
  let DashboardList = res.data;
  let projectId = getProjectId();

  if (projectId === 0) {
    fDashboardList = DashboardList.filter(item => item.content.projectId === 0);
  } else if (projectId > 0) {
    fDashboardList = DashboardList.filter(
      item => item.content.projectId === projectId
    );
  } else {
    fDashboardList = DashboardList;
  }

  res.data = fDashboardList;

  return res;
}
export function dashboardList() {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    responseType: "json",
    data: {
      rootID: 0,
      rootLabel: "dashboard"
    },
    transformResponse: [processDashboardListResponse, filterDashboarByProjectID] //对接口返回的数据结构进行处理
  });
}

export function addChartToDB(data) {
  let sendData = [
    {
      id: data.dashboard_id,
      modelLabel: "dashboard",
      chart_model: [
        {
          id: data.chart_id,
          modelLabel: "chart"
          // move_from: null
        }
      ]
    }
  ];

  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data: sendData
  });
}

export function changeChartToDB(data) {
  let sendData = [
    {
      id: data.dashboard_id,
      modelLabel: "dashboard",
      chart_model: [
        {
          id: data.chart_id,
          modelLabel: "chart",
          move_from: {
            id: data.old_chart_id,
            modelLabel: "chart"
          }
        }
      ]
    }
  ];

  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data: sendData
  });
}

export function chartByDashboard(id) {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    data: {
      rootCondition: {
        treeNode: {
          id: id,
          modelLabel: "dashboard"
        }
      },
      rootLabel: "chart"
    }
  });
}

export function dbByChart(id) {
  return httping({
    url: `{{model-service}}/v1/query`,
    method: "POST",
    data: {
      rootCondition: {
        treeNode: {
          id: id,
          modelLabel: "chart"
        }
      },
      rootLabel: "dashboard"
    }
  });
}

export function unMapChartDb(data) {
  let sendData = [
    {
      id: data.dashboard_id,
      modelLabel: "dashboard",
      chart_model: [
        {
          id: data.chart_id,
          modelLabel: "chart",
          moveout_flag: true
        }
      ]
    }
  ];

  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data: sendData
  });
}

export function dbOrder(data) {
  let sendData = {};
  sendData.userid = data.userId;
  sendData.userorder = data.order.join("|");
  sendData.modelLabel = "dashboardorder";
  return httping({
    url: "{{model-service}}/v1/write/hierachy",
    method: "POST",
    data: [sendData]
  });
}
