/*
 * @Author: your name
 * @Date: 2022-03-16 16:01:45
 * @LastEditTime: 2022-03-16 16:12:26
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \frame\src\common\cet-dashboard\index.js
 */
import dashboard from "./index.vue";
import fullScreenDb from "./Dashboard/fullScreenDb.vue";
import dashboardRender from "./Dashboard/dashboradRender.vue";
import { registerComponent } from "./componentList.js";

export default dashboard;

class OmegaModelPlugin {
  static register({ apiPrefix = {}, apiRequestInterceptor } = {}, plugin) {
    OmegaModelPlugin.apiOptions = {
      prefix: Object.assign(
        {},
        {
          "model-service": "/model"
        },
        apiPrefix
      ),
      requestInterceptor: apiRequestInterceptor
    };
  }
}

export { fullScreenDb, dashboardRender, registerComponent, OmegaModelPlugin };
