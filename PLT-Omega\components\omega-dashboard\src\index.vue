<!--
 * @Author: your name
 * @Date: 2022-03-17 14:11:52
 * @LastEditTime: 2022-03-18 15:42:53
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \Dashboard\component\index.vue
-->
<template>
  <div class="page">
    <Dashboard :dashboard-title="dashboardTitle" />
  </div>
</template>
<script>
import Dashboard from "./Dashboard/index.vue";
import store from "./ChartPanel/store";
export default {
  name: "CetDashboard",
  components: { Dashboard },
  computed: {},
  props: {
    dashboardTitle: {
      type: String
    },
    treeNodeModel: {
      type: Array
    }
  },
  created() {
    store.setTreeNodeModel(this.treeNodeModel);
  },

  data() {
    return {};
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  overflow-y: auto;
}
</style>
