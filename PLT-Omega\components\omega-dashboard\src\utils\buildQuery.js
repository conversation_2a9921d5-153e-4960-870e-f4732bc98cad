/*
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2021-03-31 18:42:35
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\utils\buildQuery.js
 */
import _ from "lodash";
export function buildQueryData({
  dataSrc,
  selectedCalcul,
  selectedDimension,
  orderByStrs,
  filters,
  treeNode,
  limit
}) {
  const queryData = {};
  queryData.rootID = 0;
  queryData.rootLabel = dataSrc;
  queryData.rootCondition = {};
  queryData.rootCondition.filter = getFilterStr(filters);
  queryData.rootCondition.orders = getOrders(orderByStrs);
  queryData.rootCondition.treeNode = treeNode;
  queryData.rootCondition.page = {
    index: 0,
    limit: limit
  };
  return queryData;
}

function getFilterStr(filters) {
  var vm = this;
  var expressions = [];

  if (!_.isArray(filters)) {
    return undefined;
  }
  for (let i = 0; i < filters.length; i++) {
    let limitValue = "";
    if (
      filters[i].operatorParamNum === 1 ||
      filters[i].operatorParamNum === 4
    ) {
      limitValue = filters[i].value.value1;
    } else if (filters[i].operatorParamNum === 2) {
      limitValue = [filters[i].value.value1, filters[i].value.value2];
    } else if (filters[i].operatorParamNum === -1) {
      limitValue = filters[i].value.arrValue;
    } else if (filters[i].operatorParamNum === 3) {
      limitValue = filters[i].value.timeValue;
    }

    if (
      limitValue !== "" &&
      !_.isNull(limitValue) &&
      !_.isUndefined(limitValue)
    ) {
      let operator = filters[i].filterOperator;
      if (operator === "TIMEBETWEEN") {
        operator = "BETWEEN";
      }
      if (operator === "NODEIDEQ") {
        operator = "EQ";
      }

      expressions.push({
        prop: filters[i].filteCol,
        operator: operator,
        limit: limitValue
      });
    }
  }

  return {
    expressions: expressions
  };
}

function getOrders(orderByStrs) {
  return orderByStrs.map((item, index) => {
    const orderObj = item.split(" ");
    return {
      orderType: orderObj[1],
      priority: index,
      propertyLabel: orderObj[0]
    };
  });
}
