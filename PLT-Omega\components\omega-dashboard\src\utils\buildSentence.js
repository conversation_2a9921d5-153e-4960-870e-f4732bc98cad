/*
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2021-03-31 18:36:05
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\utils\buildSentence.js
 */
import { dataType } from "./configs";
import moment from "moment";

export function trimColType(str) {
  let colType;
  if (str.indexOf("(") >= 0) {
    colType = dataType.find(type => type.name === str.split("(")[0]);
  } else {
    colType = dataType.find(type => type.name === str);
  }

  if (!colType) {
    colType = {
      name: str,
      needQuotation: false
    };
  }

  return colType;
}

export function buildFilterSentence(filter) {
  let filterSentence;
  let valueObj = filter.value;
  if (trimColType(filter.colType).needQuotation) {
    valueObj = addQuotation(filter.value);
  }
  if (filter.operatorParamNum === 1 || filter.operatorParamNum === 4) {
    filterSentence = `${filter.filteCol} ${filter.filterOperator} ${valueObj.value1}`;
  } else if (filter.operatorParamNum === 2) {
    filterSentence = `${filter.filteCol} ${filter.filterOperator} ${valueObj.value1} and ${valueObj.value2}`;
  } else if (filter.operatorParamNum === 3) {
    filterSentence = `${filter.filteCol} ${filter.filterOperator} ${moment(
      valueObj.timeValue[0]
    ).format("YYYY-MM-DD HH:mm:ss")} and ${moment(valueObj.timeValue[1]).format(
      "YYYY-MM-DD HH:mm:ss"
    )}`;
  } else {
    filterSentence = `${filter.filteCol} ${
      filter.filterOperator
    } ('${valueObj.arrValue.join(",")}')`;
  }
  return filterSentence;
  function addQuotation(valueObj) {
    return {
      value1: `'${valueObj.value1}'`,
      value2: `'${valueObj.value2}'`,
      arrValue: valueObj.arrValue.map(value => `'${value}'`)
    };
  }
}
