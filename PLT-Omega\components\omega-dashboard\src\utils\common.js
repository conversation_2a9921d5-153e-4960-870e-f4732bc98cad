/*
 * @Author: your name
 * @Date: 2021-01-08 15:45:38
 * @LastEditTime: 2021-01-11 11:21:52
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\utils\common.js
 */

import _ from "lodash";
import moment from "moment";

//自定义格式化表格列数据的函数, 函数名不能重名, 配置column的formatter为函数名即可
const InvalidValue = [null, -2147483648, "NaN", "Infinity", 2147483648];
function get(object, path, defaultValue = "--") {
  const val = _.get(object, path, defaultValue);
  if (InvalidValue.includes(val)) {
    return defaultValue;
  }
  return val;
}

const toFixed2 = function(value, precision) {
  precision = precision || 0;
  var pow = Math.pow(10, precision);
  return (Math.round(value * pow) / pow).toFixed(precision);
};

export default {
  get,
  formatNumberWithPrecision: function(value, precision) {
    if (_.isNumber(precision)) {
      if (!_.isNumber(value)) {
        //先转换成数字
        value = parseFloat(value);
      }
      if (isNaN(value)) {
        //如果为空直接返回空
        return null;
      }
      value = toFixed2(value, precision); //不为空的话就保留小数位
    }

    return value;
  }
};
