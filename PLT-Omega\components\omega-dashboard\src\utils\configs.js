/*
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2021-03-30 14:59:13
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\utils\configs.js
 */
export const sqlFunc = {
  sum: "合计",
  avg: "平均",
  max: "最大值",
  min: "最小值",
  count: "计数",
  none: "无"
};

export const filterOperator = [
  { operator: "EQ", name: "等于", paramNum: 1 },
  { operator: "GT", name: "大于", paramNum: 1 },
  { operator: "LT", name: "小于", paramNum: 1 },
  { operator: "GE", name: "大于等于", paramNum: 1 },
  { operator: "LE", name: "小于等于", paramNum: 1 },
  { operator: "NE", name: "不等于", paramNum: 1 },
  { operator: "BETWEEN", name: "区间", paramNum: 2 },
  { operator: "TIMEBETWEEN", name: "区间(时间)", paramNum: 3 },
  { operator: "IN", name: "包含", paramNum: -1 },
  { operator: "LIKE", name: "模糊匹配", paramNum: 1 },
  { operator: "NODEIDEQ", name: "关联节点ID", paramNum: 4 }
];

export const dataType = [
  {
    name: "tinyint",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "smallint",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "mediumint",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "int",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "int8",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "bigint",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "float",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "double",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "real",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  {
    name: "decimal",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  },
  { name: "timestamp", needQuotation: false, availableFunc: ["count", "none"] },
  { name: "date", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "time", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "datetime", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "year", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "char", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "varchar", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "tinytext", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "text", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "mediumtext", needQuotation: true, availableFunc: ["count", "none"] },
  { name: "longtext", needQuotation: true, availableFunc: ["count", "none"] },
  {
    name: "integer",
    needQuotation: false,
    availableFunc: ["sum", "avg", "max", "min", "count", "none"]
  }
];
