<template>
  <div ref="chart" :style="chartStyle" />
</template>
<script>
import * as echarts from "echarts";
import { labelFormatter } from "./chartUtils";
import Common from "../utils/common.js";
// require("echarts/theme/macarons");

export default {
  props: {
    data: {
      required: true,
      default: () => {}
    },
    schema: {
      type: Array,
      required: true
    },
    chartOpt: {
      type: Object,
      required: false
    },
    chartStyle: {
      require: false,
      type: Object,
      default: () => {
        return {
          height: "420px"
        };
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        this.renderChart(data);
      }
    },
    schema: {
      deep: true,
      handler: function () {
        this.renderChart(this.data);
      }
    }
  },
  mounted() {
    this.renderChart(this.data);
    this.$on("resized", this.handleResize);
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    validateData(data) {
      if (!Array.isArray(data)) {
        this.$message({
          message: "柱状图的数据格式必须为数组，请检查你的数据格式"
        });
      }
    },
    renderChart(data) {
      if (!this.$refs.chart) return;
      let legend = [];
      let xAxisData = [];
      const seriesObj = {};
      if (this.schema.filter(schema => schema.asxAxis).length === 2) {
        const dimensions = this.schema.filter(schema => schema.asxAxis);
        const xAxisName = dimensions[0].alias;
        const legendName = dimensions[1].alias;
        const valueName = this.schema.find(schema => !schema.asxAxis).alias;
        xAxisData = this.data.map(item => {
          legend.push(item[legendName]);
          return item[xAxisName];
        });
        xAxisData = Array.from(new Set(xAxisData));
        legend = Array.from(new Set(legend));
        legend = legend.map((legendItem, index) => {
          const seriesData = xAxisData.map(xAxisValue => {
            const item = data.find(
              dataItem =>
                dataItem[legendName] === legendItem &&
                dataItem[xAxisName] === xAxisValue
            );
            if (item) {
              return item[valueName];
            } else {
              return "-";
            }
          });
          legendItem += "";
          seriesObj[legendItem] = {
            name: legendItem,
            data: seriesData,
            type: "bar"
          };
          return legendItem;
        });
      } else {
        this.schema.forEach((schema, index) => {
          legend.push(schema.alias || schema.name);
          if (!schema.asxAxis) {
            seriesObj[schema.name] = {
              name: schema.alias || schema.name,
              data: [],
              // showSymbol: false,
              type: "bar"
            };
          }
          data.forEach(item => {
            if (schema.asxAxis) {
              xAxisData.push(item[schema.name]);
            } else {
              if (schema.Type === "float") {
                item[schema.name] = Common.formatNumberWithPrecision(
                  item[schema.name],
                  2
                );
              }
              seriesObj[schema.name].data.push(item[schema.name]);
            }
          });
        });
      }

      const option = {
        legend: {
          bottom: 0,
          type: "scroll",
          data: legend
        },
        // color: ['#4097ff'],
        toolbox: {
          show: true,
          top: -5,
          itemSize: 12,
          feature: {
            saveAsImage: {
              show: true
            },
            magicType: {
              type: ["line", "bar"]
            },
            restore: {
              show: true
            },
            dataZoom: {
              show: true
            }
          }
        },
        grid: {
          top: "20px",
          left: "45px",
          right: "0",
          bottom: "45px"
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow"
          }
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#95a4bd"
          },
          axisLine: {
            lineStyle: {
              color: "#95a4bd"
            }
          },
          splitArea: {
            show: true,
            interval: 0
          },
          data: xAxisData
        },
        yAxis: {
          axisLabel: {
            show: true,
            color: "#95a4bd",
            formatter: labelFormatter
          },
          axisLine: {
            lineStyle: {
              color: "#95a4bd"
            }
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        series: Object.values(seriesObj)
      };
      setTimeout(() => {
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chart);
        }
        this.chart.clear();
        this.chart.setOption(option);
        if (this.chartOpt) {
          this.chart.setOption(this.chartOpt);
        }
      }, 0);
    }
  }
};
</script>
