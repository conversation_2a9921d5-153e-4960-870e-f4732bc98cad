<!--
 * @Author: your name
 * @Date: 2020-12-25 15:30:12
 * @LastEditTime: 2022-03-17 11:41:23
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\common\cet-dashboard\widgets\DataTable.vue
-->
<template>
  <el-table
    :data="data"
    :style="chartStyle"
    :height="chartStyle.height"
    highlight-current-row
    border
    :header-cell-style="{ padding: '5px 0' }"
    :cell-style="{ padding: '5px 0' }"
  >
    <el-table-column
      v-for="item in schema"
      :key="item.Column"
      :prop="item.Column"
      :label="item.alias"
      align="center"
      :formatter="columnFormatter"
    />
  </el-table>
</template>
<script>
import _ from "lodash";
import Common from "../utils/common.js";
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    },
    schema: {
      type: Array,
      default: () => []
    },
    chartStyle: {
      require: false,
      type: Object,
      default: () => {
        return {
          height: "420px"
        };
      }
    }
  },
  methods: {
    columnFormatter(row, column, cellValue) {
      const schema = _.find(this.schema, { Column: column.property });
      if (schema.Type === "float") {
        return Common.formatNumberWithPrecision(cellValue, 2);
      } else {
        return cellValue;
      }
    }
  }
};
</script>
