<template>
  <div ref="chart" :style="chartStyle" />
</template>
<script>
import * as echarts from "echarts";
import { labelFormatter } from "./chartUtils";
import Common from "../utils/common.js";
// require("echarts/theme/macarons");

export default {
  props: {
    widgetMeta: {
      required: false,
      type: Object,
      default: () => {}
    },
    data: {
      required: true,
      default: () => {}
    },
    schema: {
      type: Array,
      required: true
    },
    chartOpt: {
      type: Object,
      required: false
    },
    chartStyle: {
      require: false,
      type: Object,
      default: () => {
        return {
          height: "420px"
        };
      }
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    data: {
      deep: true,
      handler: function (data) {
        this.renderChart(data);
      }
    },
    schema: {
      deep: true,
      handler: function () {
        this.renderChart(this.data);
      }
    }
  },
  mounted() {
    this.renderChart(this.data);
    this.$on("resized", this.handleResize);
    window.addEventListener("resize", this.handleResize);
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
    window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    handleResize() {
      if (this.chart) {
        this.chart.resize();
      }
    },
    validateData(data) {
      if (!Array.isArray(data)) {
        this.$message({
          message: "线图的数据格式必须为数组，请检查你的数据格式"
        });
      }
    },
    renderChart(data) {
      if (!this.$refs.chart) return;
      const legend = [];
      const xAxisData = [];
      const seriesObj = {};
      this.schema.forEach((schema, index) => {
        legend.push(schema.alias || schema.name);
        if (!schema.asxAxis) {
          seriesObj[schema.name] = {
            name: schema.alias || schema.name,
            data: [],
            // showSymbol: false,
            type: "line"
          };
        }
        data.forEach(item => {
          if (schema.asxAxis) {
            xAxisData.push(item[schema.name]);
          } else {
            if (schema.Type === "float") {
              item[schema.name] = Common.formatNumberWithPrecision(
                item[schema.name],
                2
              );
            }
            seriesObj[schema.name].data.push(item[schema.name]);
          }
        });
      });
      const option = {
        legend: {
          bottom: 0,
          type: "scroll",
          data: legend
        },
        // color: ['#4097ff'],
        toolbox: {
          show: true,
          itemSize: 12,
          top: -5,
          feature: {
            saveAsImage: {
              show: true
            },
            magicType: {
              type: ["line", "bar"]
            },
            restore: {
              show: true
            },
            dataZoom: {
              show: true
            }
          }
        },
        grid: {
          // show: true,
          top: "20px",
          left: "45px",
          right: "0",
          bottom: "45px"
        },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross"
          }
        },
        xAxis: {
          type: "category",
          axisLabel: {
            color: "#95a4bd"
          },
          axisLine: {
            lineStyle: {
              color: "#95a4bd"
            }
          },
          data: xAxisData
        },
        yAxis: {
          axisLabel: {
            show: true,
            color: "#95a4bd",
            formatter: labelFormatter
          },
          axisLine: {
            lineStyle: {
              color: "#95a4bd"
            }
          },
          splitLine: {
            lineStyle: {
              type: "dashed"
            }
          }
        },
        series: Object.values(seriesObj)
      };
      setTimeout(() => {
        if (!this.chart) {
          this.chart = echarts.init(this.$refs.chart);
        }
        this.chart.clear();
        this.chart.setOption(option);
        if (this.chartOpt) {
          this.chart.setOption(this.chartOpt);
        }
      }, 0);
    }
  }
};
</script>
