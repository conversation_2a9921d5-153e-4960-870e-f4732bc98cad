/*
 * @Author: your name
 * @Date: 2022-04-07 16:51:20
 * @LastEditTime: 2022-04-14 09:58:35
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\components\omega-dashboard\vue.config.js
 */

const config = {
  css: {
    extract: false
  },
  configureWebpack: config => {
    return {
      externals: {
        "@omega/http": "commonjs2 @omega/http",
        "@omega/auth": "commonjs2 @omega/auth",
        "@omega/icon": "commonjs2 @omega/icon",
        "element-ui": "commonjs2 element-ui",
        "cet-common": "commonjs2 cet-common",
        "cet-chart": "commonjs2 cet-chart",
        echarts: "commonjs2 echarts",
        lodash: "commonjs2 lodash",
        moment: "commonjs2 moment",
        "core-js": "commonjs2 core-js",
        vuedraggable: "commonjs2 vuedraggable",
        "vue-grid-layout": "commonjs2 vue-grid-layout",
        xlsx: "commonjs2 xlsx",
        "file-saver": "commonjs2 file-saver"
      }
    };
  }
};

module.exports = config;
