<template>
  <div id="cetmap"></div>
</template>

<script>
// import echarts from "echarts";
import * as echarts from "echarts";
// 引入百度地图扩展
import "echarts/extension/bmap/bmap.js";
// 引入百度地图的js sdk
import loadBMap from "../config/loadBMap.js";
// 引入百度地图库
import loadBMapLib from "./loadBMapLib.js";
// 主题
import { themeConfig } from "../config/const";

// 获取当前暗、亮皮肤值 current
export function getTheme(name) {
  const theme = _.get(themeConfig, `theme.${name}`, "light");
  let mapStyle = { styleJson: theme };
  return mapStyle;
}
export default {
  name: "CetMap",
  props: {
    ak: {
      type: String,
      default: "NUi5LUp0Ls477BWawHL8TyjKlY6EudEI",
    },
    inputData_in: Array,
    options: Object, //配置项
    customMark_in: {
      type: Boolean,
      default: false,
    }, //customMark为true，添加地图选点功能
    districts_in: {
      type: Array,
      default() {
        return [];
      },
    },
    districtsBorder_in: {
      type: Object,
      default() {
        return { strokeWeight: 1, strokeColor: "#aaaaaa" };
      },
    },
    bounds_in: Object,
    configMapControls: Function,
  },
  components: {},
  data() {
    return {
      center: { lng: 0, lat: 0 },
      address: "", //选取点的名称，
      mapOptions: {
        bmap: {
          // 百度地图中心经纬度
          center: [116.404, 39.915], //当前视角中心位置的坐标
          layoutCenter: ["50%", "55%"], //'50%'位置居中 定义地图中心在屏幕中的位置
          // 百度地图缩放
          zoom: 5,
          // 是否开启拖拽缩放，可以只设置 'scale' 或者 'move'
          roam: true,
          // 百度地图的自定义样式，见 http://developer.baidu.com/map/jsdevelop-11.htm
          mapStyle: {},
        },
      },
    };
  },
  watch: {
    districts_in: {
      deep: true,
      handler: function (val, oldVal) {
        const vm = this;
        if (val.length > 0) {
          const bmap = vm.getBmap();
          vm.setAdministrativeColor(bmap, val);
        }
      },
    },
    options: {
      deep: true,
      handler: function (val, oldVal) {
        const vm = this;
        if (!this.myChart && val) {
          this.init();
        } else {
          this.myChart.setOption(val, val !== oldVal);
        }
      },
    },
  },
  mounted() {
    const vm = this;
    // 打开地图的页面，才会去加载js
    loadBMap(vm.ak).then(() => {
      vm.init();
      if (!_.has(vm.options, "bmap.center")) return vm.setCenter();
    });
  },
  methods: {
    init() {
      const vm = this;
      // 打开地图的页面，才会去加载js
      loadBMap(vm.ak).then(() => {
        initEcharts();
        if (!_.has(vm.options, "bmap.center")) return vm.setCenter();
      });

      /**
       * @description: 初始化echarts及bmap配置等
       * @param {*}
       * @return {*}
       */
      function initEcharts() {
        const myChart = echarts.init(document.getElementById("cetmap"));
        let arr = _.merge({}, vm.mapOptions, vm.options);
        myChart.setOption(arr);
        vm.myChart = myChart;
        // 通过echarts实例获取地图实例的方法
        const bmap = vm.getBmap();
        // 开启鼠标滚轮缩放
        bmap.enableScrollWheelZoom();
        // 添加地图选点
        if (vm.customMark_in) vm.addCustomMark(bmap);
        // 判断是否添加控件
        if (typeof vm.configMapControls === "function") vm.addControls(bmap);
        // 设置限制显示区域
        if (!_.isEmpty(vm.bounds_in)) vm.setDisplayRange(bmap);
        // 设置行政区域颜色、描边
        if (_.isArray(vm.districts_in)) {
          vm.setAdministrativeColor(bmap, vm.districts_in);
        }
      }
    },
    getBmap() {
      // 通过echarts实例获取地图实例的方法
      let bmap = this.myChart.getModel().getComponent("bmap").getBMap();
      return bmap;
    },
    //设置中心点坐标为当前市
    setCenter() {
      const vm = this;
      //获取浏览器所在的城市坐标
      const geolocation = new BMap.Geolocation();
      /**
       * @description: 获取浏览器当前位置信息
       * @param {*} function
       * @return {*}
       */
      geolocation.getCurrentPosition(function (r) {
        //通过Geolocation类的getStatus()可以判断是否成功定位。
        if (this.getStatus() == BMAP_STATUS_SUCCESS) {
          let city = r.address.city.substring(0, r.address.city.length - 1);
          // r.address.city.Substring(0, r.address.city.Length - 1);
          // vm.InsertAddress(city, r.point.lng, r.point.lat); //r.point.lng
          let option = {
            bmap: {
              // 百度地图中心经纬度
              center: [r.point.lng, r.point.lat],
            },
          };
          vm.myChart.setOption(option);
        } else {
          console.log("failed 你的位置未知" + this.getStatus());
        }
      });
    },
    /**
     * @description: 着色并高亮显示点击块
     * @param {*} map bmap实例
     * @param {*} val 行政区数据[["黑龙江", "#F09ABD"]]
     * @return {*}
     */
    setAdministrativeColor(map, val) {
      const vm = this;
      // map.clearOverlays(); //清除地图覆盖物

      function getBoundary(provItem) {
        const bdary = new BMap.Boundary();
        bdary.get(provItem[0], function (rs) {
          //获取行政区域
          let count = rs.boundaries.length; //行政区域的点有多少个
          if (count === 0) {
            console.log("未能获取当前输入行政区域");
            return;
          }
          // 填充色、边框色，边框大小
          let obj = {
            fillColor: provItem[1], //填充颜色
            strokeColor:
              provItem.length > 2
                ? provItem[2]
                : vm.districtsBorder_in.strokeColor,
            strokeWeight:
              provItem.length > 3
                ? provItem[2]
                : vm.districtsBorder_in.strokeWeight,
          };

          // let pointArray = [];
          for (let i = 0; i < count; i++) {
            let ply = new BMap.Polygon(rs.boundaries[i], obj); //建立多边形覆盖物
            map.addOverlay(ply); //添加覆盖物
            // pointArray = pointArray.concat(ply.getPath());
          }
        });
      }
      // map.setViewport(ply.getPath()); //调整视野
      setTimeout(function () {
        val.forEach(function (item) {
          getBoundary(item);
        });
      }, 20);
    },
    /**
     * @description: 添加地图选点功能
     * @param {*} map
     * @return {*}
     */
    addCustomMark(map) {
      const vm = this,
        geoc = new BMap.Geocoder(); //创建一个地址解析器的实例

      //给地图添加监听事件，点击地图添加一个标记点
      map.addEventListener("click", function (e) {
        map.clearOverlays(); //先清除原有的点
        if (vm.mark) {
          //如果存在添加的点，则移除；不存在，就添加。目的是只能在地图上添加一个点
          map.removeOverlay(vm.mark);
        }
        vm.mark = new BMap.Marker(new BMap.Point(e.point.lng, e.point.lat)); // 创建标注
        map.addOverlay(vm.mark); //给地图增加标注

        geoc.getLocation(e.point, function (rs) {
          vm.address = rs.address;
          //监听customPoint事件，返回所选点的经纬度和地址
          vm.$emit("customPoint_out", [e.point, vm.address]);
        });

        //设置标记点可拖拽
        vm.mark.enableDragging();
        //给点添加监听事件，拖拽后执行程序
        vm.mark.addEventListener("dragend", function (e) {
          geoc.getLocation(e.point, function (rs) {
            vm.address = rs.address;
            vm.$emit("customPoint_out", [e.point, vm.address]);
          });
        });
      });
    },
    /**
     * @description:添加控件,百度API 2.0
     * @param {*} map
     * @return {*}
     */
    addControls(map) {
      let arr = this.configMapControls(BMap);
      arr.forEach((element) => {
        let obj = element.position ? element.position : null;
        map.addControl(new BMap[element.name](obj)); // 缩放控件
      });
      // map.addControl(new BMap.ZoomControl()); // 比例尺
    },
    /**
     * @description:限制显示范围
     * @param {*} map
     * @return {*}
     */
    setDisplayRange(map) {
      loadBMapLib().then(() => {
        let sw = this.bounds_in.sw;
        let ne = this.bounds_in.ne;
        let b = new BMap.Bounds(
          new BMap.Point(sw[0], sw[1]),
          new BMap.Point(ne[0], ne[1])
        ); // 范围 左下角，右上角的点位置
        try {
          // js中尽然还有try catch方法，可以避免bug引起的错误
          BMapLib.AreaRestriction.setBounds(map, b); // map对象，b需要限定的区域
        } catch (e) {
          // 捕获错误异常
          console.log(e);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#cetmap {
  width: 100%;
  height: 100%;
}
</style>
<style>
/* 隐藏地图bmap左下角logo  */
.anchorBL.BMap_cpyCtrl,
.anchorBL > a {
  display: none;
}
</style>
