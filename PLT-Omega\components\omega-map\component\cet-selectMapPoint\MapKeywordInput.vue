<template>
  <div class="wrapper">
    <input type="text" ref="input" placeholder="请输入地点" />
    <div
      ref="result"
      style="
        border: 1px solid #c0c0c0;
        width: 150px;
        height: auto;
        display: none;
      "
    ></div>
  </div>
</template>

<script>
export default {
  name: "MapKeywordInput",
  props: {
    map: {
      type: Object
    },
    // 是否允许拖动打点
    allowDrag: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {};
  },
  watch: {
    map(val) {
      if (val) {
        this.initAutoComplete();
      }
    }
  },
  methods: {
    initAutoComplete() {
      const map = this.map;

      const ac = new BMap.Autocomplete({
        input: this.$refs.input,
        location: this.map
      });

      ac.addEventListener("onhighlight", e => {
        // 鼠标放在下拉列表上的事件
        let str = "";
        let _value = e.fromitem.value;
        let value = "";
        if (e.fromitem.index > -1) {
          value =
            _value.province +
            _value.city +
            _value.district +
            _value.street +
            _value.business;
        }
        str =
          "FromItem<br />index = " +
          e.fromitem.index +
          "<br />value = " +
          value;

        value = "";
        if (e.toitem.index > -1) {
          _value = e.toitem.value;
          value =
            _value.province +
            _value.city +
            _value.district +
            _value.street +
            _value.business;
        }
        str +=
          "<br />ToItem<br />index = " +
          e.toitem.index +
          "<br />value = " +
          value;
        this.$refs.result.innerHTML = str;
      });

      let myValue;
      ac.addEventListener("onconfirm", e => {
        // 鼠标点击下拉列表后的事件
        let _value = e.item.value;
        myValue =
          _value.province +
          _value.city +
          _value.district +
          _value.street +
          _value.business;
        this.$refs.result.innerHTML =
          "onconfirm<br />index = " +
          e.item.index +
          "<br />myValue = " +
          myValue;

        setPlace();
      });

      const setPlace = () => {
        map.clearOverlays(); // 清除地图上所有覆盖物

        const myFun = () => {
          let pp = local.getResults().getPoi(0).point; // 获取第一个智能搜索的结果
          map.centerAndZoom(pp, 18);
          const marker = new BMap.Marker(pp);
          if (this.allowDrag) {
            marker.enableDragging();
            marker.addEventListener("dragend", e => {
              this.decodeAndEmit(e.point);
            });
          }
          map.addOverlay(marker); // 添加标注
          this.decodeAndEmit(pp);
        };

        let local = new BMap.LocalSearch(map, {
          // 智能搜索
          onSearchComplete: myFun
        });

        local.search(myValue);
      };
    },
    /**
     * @param {BMap.Point} point
     */
    decodeAndEmit(point) {
      const vm = this;
      const decoder = new BMap.Geocoder();
      decoder.getLocation(point, function (rs) {
        vm.address = rs.address;

        vm.$emit("select-point", { point, address: rs.address });
        console.log(point, rs.address);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wrapper {
  position: relative;
}
</style>

<style>
/* hack 这个是百度地图插到 body 元素最后的 */
.tangram-suggestion-main {
  z-index: 30000;
}
</style>
