<template>
  <div>
    <el-input
      :style="`width: ${width}px`"
      ref="input"
      @focus="openDialog"
      :value="coordinatesText"
      placeholder="请选择"
      suffix-icon="el-icon-location-outline"
    ></el-input>
    <Dialog
      :autoLocate="autoLocate"
      @close="handleClose"
      v-model="show"
      :theme="theme"
      :initialCoordinates="coordinates"
      @confirm="setCoordinates"
    />
  </div>
</template>

<script>
import Dialog from "./SelectMapPointDialog.vue";

export default {
  name: "CetSelectMapPoint",
  components: { Dialog },
  props: {
    width: {
      type: [Number, String],
      default: 200,
    },
    value: {
      type: Array,
      default: () => [],
    },
    autoLocate: {
      type: Boolean,
      default: true,
    },
    theme: {
      type: String,
      default: "light",
    },
  },
  data() {
    return {
      coordinates: [...this.value],
      dialogVisible: false,
      show: false,
    };
  },
  computed: {
    coordinatesText() {
      if (this.coordinates.length) {
        const [lng, lat] = this.coordinates;

        return `${this.toFixed2(lng, 4)}, ${this.toFixed2(lat, 4)}`;
      }
      return "";
    },
  },
  methods: {
    openDialog() {
      this.show = true;
    },
    setCoordinates(val) {
      this.$emit("input", [...val]);
      this.coordinates = val;
    },
    handleClose() {
      this.$refs.input.blur();
    },
    /**
     * 精确数据到指定位数
     *
     * @param {number} num 要精确的数据
     * @param {number} precision 精确的位数
     */
    toFixed2(num, precision) {
      precision = precision || 0;
      var pow = Math.pow(10, precision);
      return (Math.round(num * pow) / pow).toFixed(precision);
    },
  },
};
</script>

<style lang="scss" scoped>
.mapContainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  z-index: -1;
}
</style>
