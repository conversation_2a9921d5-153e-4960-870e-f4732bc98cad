<template>
  <el-dialog
    :visible.sync="dialogVisible"
    v-on="$listeners"
    width="50%"
    @open="loadMap"
    title="请选择点"
  >
    <div class="content">
      <div ref="container" class="mapContainer"></div>
      <MapKeywordInput
        :map="map"
        class="auto-complete"
        @select-point="handleAutoSelect"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button
        v-if="showPolygon"
        type="primary"
        style="float: left"
        plain
        @click="togglePlygonEdit"
      >
        {{ `${editPolygon ? "关闭" : "开启"}范围编辑` }}
      </el-button>

      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </div>
  </el-dialog>
</template>

<script>
import loadBMap from "../config/loadBMap.js";
import MapKeywordInput from "./MapKeywordInput.vue";
import { isPointInPolygon } from "./isPointInPolygon.js";
// 主题
import { themeConfig } from "../config/const";

const BMap_AK = "NUi5LUp0Ls477BWawHL8TyjKlY6EudEI";

export default {
  name: "CetSelectMapPointDialog",
  components: { MapKeywordInput },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    autoLocate: {
      type: Boolean,
      default: true,
    },
    initialCoordinates: {
      type: Array,
      default: () => [],
    },
    initialPolygonPath: {
      type: Array,
      default: () => [],
    },
    zoom: {
      type: Number,
      default: 14,
    },
    showSearch: {
      type: Boolean,
      default: true,
    },
    showPolygon: {
      type: Boolean,
      default: false,
    },
    polygonStep: {
      type: Number,
      default: 0.02,
    },
    theme: {
      type: String,
      default: "light",
    },
  },
  data() {
    return {
      /** @type {BMap.Map} */
      map: null,
      /** @type {BMap.Marker} */
      markerOverlay: null,
      /** @type {BMap.Polygon} */
      polygonOverlay: null,
      address: "",
      coordinates: [],
      editPolygon: false,
    };
  },

  watch: {
    coordinates(val) {
      this.$emit("change", [...val]);
      if (this.map && val.length) {
        setTimeout(() => {
          let isIn = false;
          if (this.polygonOverlay) {
            isIn = isPointInPolygon(
              new BMap.Point(...val),
              this.polygonOverlay.getPath()
            );
          }
          // 超出了边界，则画一个默认的矩形框的 范围
          this.showPolygon && !isIn && this.drawPolygon(val, this.map);
        }, 100);
      }
    },
  },
  computed: {
    /** @returns {boolean} */
    dialogVisible: {
      /** @returns {boolean} */
      get() {
        return this.value;
      },
      /**
       * @param {boolean} val
       */
      set(val) {
        this.$emit("input", val);
      },
    },
  },

  methods: {
    resetData() {
      this.coordinates = [];
      this.editPolygon = false;
    },
    handleCancel() {
      this.dialogVisible = false;
      this.map && this.map.clearOverlays();
      this.resetData();
    },
    handleConfirm() {
      this.dialogVisible = false;
      this.$emit(
        "confirm",
        [...this.coordinates],
        this.polygonOverlay &&
          this.polygonOverlay.getPath().map(({ lng, lat }) => [lng, lat])
      );
      this.map && this.map.clearOverlays();
      this.resetData();
    },
    doInitMark(point) {
      const marker = new BMap.Marker(point);
      this.markerOverlay = marker;
      marker.enableDragging();
      marker.addEventListener("dragend", (e) => {
        console.log("init drag end ");
        this.decodeAndEmit(e.point);
      });
      this.map.addOverlay(marker);
      if (this.showPolygon) {
        if (this.initialPolygonPath && this.initialPolygonPath.length) {
          this.drawPolygon(
            this.initialPolygonPath.map((c) => new BMap.Point(...c)),
            this.map,
            false
          );
        } else {
          // 画默认的矩形范围
          this.drawPolygon(this.coordinates, this.map);
        }
      }
    },
    async loadMap() {
      /** @type {BMap.Map} */
      let mapInstance = this.map;
      const zoom = this.zoom || 13;
      if (this.initialCoordinates) {
        this.coordinates = [...this.initialCoordinates];
      }

      if (mapInstance) {
        // mapInstance has already been created
        if (this.initialCoordinates && this.initialCoordinates.length) {
          setTimeout(() => {
            const point = new BMap.Point(...this.initialCoordinates);
            mapInstance.centerAndZoom(point, zoom);
            mapInstance.removeOverlay(this.markerOverlay);
            this.doInitMark(point);
          }, 100);
        } else {
          console.log("未提供初始的坐标信息");
        }
      } else {
        await loadBMap(BMap_AK);
        const map = new BMap.Map(this.$refs.container, {
          enableMapClick: false,
        });
        // 主题设置
        map.setMapStyle(this.getTheme(this.theme));

        map.enableScrollWheelZoom();
        mapInstance = map;
        this.map = map;

        let point;
        if (this.initialCoordinates?.length) {
          point = new BMap.Point(...this.initialCoordinates);
        } else if (this.autoLocate) {
          try {
            point = await this.getCurrentLocation();
          } catch (e) {
            this.$message.error("无法获取当前的定位信息");
            point = new BMap.Point(116.404, 39.915);
          }
        }

        point && mapInstance.centerAndZoom(point, zoom);
        point && this.doInitMark(point);
        // this.showPolygon && this.drawPolygon(this.coordinates, this.map);

        this.listenMapClick(mapInstance);
      }
    },
    /**
     * @param {BMap.Point} point
     */
    decodeAndEmit(point) {
      const vm = this;
      const decoder = new BMap.Geocoder();
      decoder.getLocation(point, function (rs) {
        vm.address = rs.address;

        vm.coordinates = [point.lng, point.lat];
        vm.address = rs.address;

        vm.$emit("select-point", { point, address: rs.address });
        console.log(point, rs.address);
      });
    },
    /**
     * 监听点击事件，地图打点
     * @param {BMap.Map} map
     */
    listenMapClick(map) {
      const vm = this;

      map.addEventListener("click", (e) => {
        if (this.editPolygon) {
          return;
        }
        // map.clearOverlays();
        map.removeOverlay(this.markerOverlay);

        vm.markerOverlay = new BMap.Marker(
          new BMap.Point(e.point.lng, e.point.lat)
        );
        map.addOverlay(vm.markerOverlay);

        vm.decodeAndEmit(e.point);

        /** @type {BMap.Marker} */
        const marker = vm.markerOverlay;
        marker.enableDragging();

        marker.addEventListener("dragend", (e) => {
          vm.decodeAndEmit(e.point);
        });
      });
    },

    getCurrentLocation() {
      const geolocation = new BMap.Geolocation();

      return new Promise((resolve, reject) => {
        geolocation.getCurrentPosition((r) => {
          if (geolocation.getStatus() === BMAP_STATUS_SUCCESS) {
            resolve(r.point);
          } else {
            console.warn(`无法获取当前位置信息：${geolocation.getStatus()}`);
            reject("无法获取当前位置信息：" + geolocation.getStatus());
          }
        });
      });
    },
    openDialog() {
      this.dialogVisible = true;
    },
    /**
     * @param {object} param
     * @param {BMap.Point} param.point
     * @param {String} param.address
     */
    handleAutoSelect({ point, address }) {
      this.$emit("select-point", { point, address });
      this.coordinates = [point.lng, point.lat];
      this.address = address;
    },
    /**
     * 画一个矩形
     * @param {[number, number] | BMap.Point[]} points 中心点经纬度坐标
     * @param {BMap.Map} map
     */
    drawPolygon(centerOrPath, map, byCenter = true) {
      let arr = centerOrPath;
      if (byCenter) {
        const [x, y] = centerOrPath;
        const step = this.polygonStep;
        const a = [x - step, y + step],
          b = [x + step, y + step],
          c = [x + step, y - step],
          d = [x - step, y - step];
        arr = [a, b, c, d].map((p) => new BMap.Point(...p));
      }
      map.removeOverlay(this.polygonOverlay);
      const polygon = new BMap.Polygon(arr, {
        strokeColor: "blue",
        strokeWeight: 2,
        strokeOpacity: 0.5,
      });
      console.log(polygon);
      map.addOverlay(polygon);
      if (this.editPolygon) {
        polygon.enableEditing();
      }
      this.polygonOverlay = polygon;
    },
    togglePlygonEdit() {
      this.editPolygon = !this.editPolygon;
      if (this.editPolygon) {
        this.polygonOverlay.enableEditing();
        this.markerOverlay.disableDragging();
      } else {
        this.polygonOverlay.disableEditing();
        this.markerOverlay.enableDragging();
      }
    },
    getTheme(name) {
      const theme = _.get(themeConfig, `theme.${name}`, "light");
      let mapStyle = { styleJson: theme };
      return mapStyle;
    },
  },
  async mounted() {},
  created() {},
};
</script>

<style lang="scss" scoped>
.content {
  position: relative;
  margin: -20px auto;
  .mapContainer {
    position: relative;
    height: 50vh;
  }
  .auto-complete {
    position: absolute;
    top: 8px;
    left: 8px;
  }
}
</style>
