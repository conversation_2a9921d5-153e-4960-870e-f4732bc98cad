// 整个包的入口

// 定义 install 方法，接受 Vue 作为参数，如果使用 use 注册插件，则所有组件都将被注册
// 基础组件
import CetMap from "./cet-map/index.vue";
import CetSelectMapPoint from "./cet-selectMapPoint/SelectMapPoint.vue";
import CetSelectMapPointDialog from "./cet-selectMapPoint/SelectMapPointDialog.vue";
import { getTheme } from "./cet-map/index.vue";

const components = [CetMap, CetSelectMapPoint, CetSelectMapPointDialog];

const install = function (Vue) {
  // 全局注册所有组件
  components.forEach((item) => {
    Vue.component(item.name, item);
  });
};

/* istanbul ignore if */
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}
export default {
  install,
  getTheme,
};
