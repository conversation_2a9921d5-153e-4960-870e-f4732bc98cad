[{"featureType": "estatelabel", "elementType": "labels.text.fill", "stylers": {"color": "#82b9f8ff"}}, {"featureType": "restaurantlabel", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "restaurantlabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "lifeservicelabel", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "lifeservicelabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "transportationlabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "transportationlabel", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "financelabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "financelabel", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "land", "elementType": "geometry", "stylers": {"color": "#fafafaff"}}, {"featureType": "building", "elementType": "geometry.topfill", "stylers": {"color": "#e0e3e5ff"}}, {"featureType": "building", "elementType": "geometry.sidefill", "stylers": {"color": "#e0e3e5ff"}}, {"featureType": "building", "elementType": "geometry.stroke", "stylers": {"color": "#b9a79700"}}, {"featureType": "estatelabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "estatelabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "estatelabel", "elementType": "labels.text", "stylers": {"fontsize": "28"}}, {"featureType": "manmade", "elementType": "labels.text.fill", "stylers": {"color": "#3a725bff"}}, {"featureType": "manmade", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff"}}, {"featureType": "manmade", "elementType": "labels.text", "stylers": {"fontsize": "36"}}, {"featureType": "manmade", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "green", "elementType": "geometry", "stylers": {"color": "#f0f4fcff"}}, {"featureType": "education", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff"}}, {"featureType": "medical", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff"}}, {"featureType": "scenicspots", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff"}}, {"featureType": "entertainment", "elementType": "geometry", "stylers": {"visibility": "off"}}, {"featureType": "estate", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff"}}, {"featureType": "shopping", "elementType": "geometry", "stylers": {"visibility": "off", "color": "#ecececff"}}, {"featureType": "transportation", "elementType": "geometry", "stylers": {"color": "#f1f1f1ff", "visibility": "on"}}, {"featureType": "transportation", "elementType": "labels.text.fill", "stylers": {"color": "#006cffff"}}, {"featureType": "transportation", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "transportation", "elementType": "labels.text", "stylers": {"fontsize": "36"}}, {"featureType": "medical", "elementType": "labels.text.fill", "stylers": {"color": "#024975ff"}}, {"featureType": "medical", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "medical", "elementType": "labels.text", "stylers": {"fontsize": "36"}}, {"featureType": "education", "elementType": "labels.text.fill", "stylers": {"color": "#024975ff"}}, {"featureType": "education", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "education", "elementType": "labels.text", "stylers": {"fontsize": "36"}}, {"featureType": "carservicelabel", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "carservicelabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "shoppinglabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "hotellabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "governmentlabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "companylabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "businesstowerlabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "entertainmentlabel", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "entertainmentlabel", "elementType": "labels.icon", "stylers": {"visibility": "on"}}, {"featureType": "medicallabel", "elementType": "labels.icon", "stylers": {"visibility": "on"}}, {"featureType": "educationlabel", "elementType": "labels.icon", "stylers": {"visibility": "on"}}, {"featureType": "scenicspotslabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "airportlabel", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "airportlabel", "elementType": "labels.text", "stylers": {"fontsize": "36"}}, {"featureType": "airportlabel", "elementType": "labels.text.fill", "stylers": {"color": "#2f448aff"}}, {"featureType": "airportlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "scenicspotslabel", "elementType": "labels.text", "stylers": {"fontsize": "28"}}, {"featureType": "scenicspotslabel", "elementType": "labels.text.fill", "stylers": {"color": "#4a4a4aff"}}, {"featureType": "scenicspotslabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "educationlabel", "elementType": "labels.text.fill", "stylers": {"color": "#4a90e2ff"}}, {"featureType": "educationlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "educationlabel", "elementType": "labels.text", "stylers": {"fontsize": "26"}}, {"featureType": "medicallabel", "elementType": "labels.text.fill", "stylers": {"color": "#4a90e2ff"}}, {"featureType": "medicallabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "medicallabel", "elementType": "labels.text", "stylers": {"fontsize": "24"}}, {"featureType": "businesstowerlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "businesstowerlabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "businesstowerlabel", "elementType": "labels.text", "stylers": {"fontsize": "24"}}, {"featureType": "companylabel", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "hotellabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "hotellabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "hotellabel", "elementType": "labels.text", "stylers": {"fontsize": "24"}}, {"featureType": "shoppinglabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "shoppinglabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ebe1d800"}}, {"featureType": "transportationlabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "transportationlabel", "elementType": "labels.text", "stylers": {"fontsize": "24"}}, {"featureType": "scenicspots", "elementType": "labels.text.fill", "stylers": {"color": "#024975ff"}}, {"featureType": "scenicspots", "elementType": "labels.text.stroke", "stylers": {"color": "#b6997f00"}}, {"featureType": "scenicspots", "elementType": "labels.text", "stylers": {"fontsize": 36}}, {"featureType": "governmentlabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "scenicspotslabel", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "district", "elementType": "labels.text.fill", "stylers": {"color": "#6e6a63ff"}}, {"featureType": "district", "elementType": "labels.text.stroke", "stylers": {"color": "#7e868f00", "weight": 1}}, {"featureType": "town", "elementType": "labels.text.stroke", "stylers": {"color": "#72533a00", "weight": "3"}}, {"featureType": "town", "elementType": "labels.text.fill", "stylers": {"color": "#6e6a63ff"}}, {"featureType": "village", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00", "weight": "2.5"}}, {"featureType": "village", "elementType": "labels.text.fill", "stylers": {"color": "#6e6a63ff", "weight": "40"}}, {"featureType": "village", "elementType": "labels.text", "stylers": {"fontsize": "20"}}, {"featureType": "highway", "elementType": "geometry.fill", "stylers": {"color": "#efefefff"}}, {"featureType": "highway", "elementType": "geometry.stroke", "stylers": {"color": "#b9b9b9ff"}}, {"featureType": "highway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "highway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "nationalway", "elementType": "geometry.fill", "stylers": {"color": "#efefefff"}}, {"featureType": "nationalway", "elementType": "geometry.stroke", "stylers": {"color": "#b9b9b9ff"}}, {"featureType": "nationalway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "nationalway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "provincialway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "provincialway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "provincialway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "provincialway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "subway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "manmade", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "water", "elementType": "geometry", "stylers": {"color": "#a8c9ffff"}}, {"featureType": "water", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "water", "elementType": "labels.text.fill", "stylers": {"color": "#2f448aff"}}, {"featureType": "subwaystation", "elementType": "geometry", "stylers": {"visibility": "on", "color": "#fdfdfdf2"}}, {"featureType": "transportation", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "cityhighway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "cityhighway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "cityhighway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "cityhighway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "arterial", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "arterial", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "arterial", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "arterial", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "tertiaryway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "tertiaryway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "tertiaryway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "tertiaryway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "fourlevelway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "fourlevelway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "fourlevelway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "fourlevelway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "local", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "local", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "local", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "local", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "local", "elementType": "labels.text", "stylers": {"fontsize": 20}}, {"featureType": "scenicspotsway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "scenicspotsway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "scenicspotsway", "elementType": "geometry", "stylers": {"weight": 2}}, {"featureType": "local", "elementType": "geometry", "stylers": {"weight": 2}}, {"featureType": "universityway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "universityway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "universityway", "elementType": "geometry", "stylers": {"weight": 2}}, {"featureType": "vacationway", "elementType": "geometry", "stylers": {"weight": 2}}, {"featureType": "vacationway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "vacationway", "elementType": "geometry.fill", "stylers": {"color": "#f4f3f1ff"}}, {"featureType": "subway", "elementType": "geometry.stroke", "stylers": {"color": "#ead7b4ff"}}, {"featureType": "subway", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "subway", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffffff"}}, {"featureType": "poilabel", "elementType": "labels.text.fill", "stylers": {"color": "#2f448aff"}}, {"featureType": "poilabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "entertainmentlabel", "elementType": "labels.text.fill", "stylers": {"color": "#4a90e2ff"}}, {"featureType": "entertainmentlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "companylabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "companylabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "governmentlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "restaurantlabel", "elementType": "labels.text.fill", "stylers": {"color": "#90867eff"}}, {"featureType": "restaurantlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "hotellabel", "elementType": "labels", "stylers": {"visibility": "on"}}, {"featureType": "transportationlabel", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "INTERNATIONALIZEICONICON", "elementType": "labels", "stylers": {"visibility": "off"}}, {"featureType": "INTERNATIONALIZEICONICON", "elementType": "labels.icon", "stylers": {"visibility": "off"}}, {"featureType": "districtlabel", "elementType": "labels.text.fill", "stylers": {"color": "#333333ff"}}, {"featureType": "country", "elementType": "labels.text.fill", "stylers": {"color": "#000000ff"}}, {"featureType": "country", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "city", "elementType": "labels.text.fill", "stylers": {"color": "#3e6374ff"}}, {"featureType": "city", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "island", "elementType": "labels.text.fill", "stylers": {"color": "#252525ff"}}, {"featureType": "island", "elementType": "labels.text", "stylers": {"fontsize": 36}}, {"featureType": "island", "elementType": "labels.text.stroke", "stylers": {"color": "#ffffff00"}}, {"featureType": "railway", "elementType": "geometry", "stylers": {"visibility": "off"}}, {"featureType": "city", "elementType": "labels.text", "stylers": {"fontsize": 24}}]