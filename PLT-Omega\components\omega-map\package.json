{"name": "@omega/map", "version": "0.1.6", "private": false, "description": "地图组件", "main": "lib/omega-map.umd.min.js", "module": "component/index.js", "author": "ZYX", "scripts": {"dev": "vue-cli-service serve", "build:lib": "vue-cli-service build --target lib --name omega-map --dest lib --formats umd-min --report component/index.js", "lint": "vue-cli-service lint", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "lib": "vue-cli-service build --target lib component/index.js"}, "peerDependencies": {"core-js": "^3.36.0", "echarts": "^5.3.1", "element-ui": "^2.15.6", "lodash": "^4.17.21", "moment": "^2.29.1", "vue": "^2.6.14"}, "dependencies": {"echarts": "^5.3.1", "vue-template-compiler": "^2.6.14"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "axios": "^0.26.0", "babel-eslint": "^10.1.0", "core-js": "^3.6.5", "element-ui": "^2.11.1", "extract-loader": "^5.1.0", "file-loader": "^6.0.0", "hard-source-webpack-plugin": "^0.13.1", "jquery": "^1.12.4", "lodash": "^4.17.21", "lowdb": "^2.1.0", "moment": "^2.29.1", "sass": "^1.26.5", "sass-loader": "^9.0.3", "vue": "^2.6.14", "vue-router": "^3.4.3", "vuex": "^3.5.1"}}