<template>
  <div id="app">
    <!-- <img alt="Vue logo" src="./assets/logo.png" /> -->
    <!-- <HelloWorld msg="Welcome to Your Vue.js App" /> -->
    <el-tabs
      v-model="activeName"
      @tab-click="handleClick"
      style="height：400px"
    >
      <el-tab-pane label="地图" name="first">
        <MapDemo></MapDemo>
      </el-tab-pane>
      <el-tab-pane label="地图选点 弹窗" name="second">
        <SelectMapPointDemo></SelectMapPointDemo>
      </el-tab-pane>
      <el-tab-pane label="地图选点 表单控件" name="third">
        <SelectPointInputDemo></SelectPointInputDemo>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MapDemo from "./projects/map.vue";
import SelectMapPointDemo from "./projects/selectMapPointDemo.vue";
import SelectPointInputDemo from "./projects/selectPointInputDemo.vue";
export default {
  name: "App",
  components: { MapDemo, SelectMapPointDemo, SelectPointInputDemo },
  data() {
    return {
      activeName: "first",
    };
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
  },
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 20px;
  height: 600px;
}
</style>
