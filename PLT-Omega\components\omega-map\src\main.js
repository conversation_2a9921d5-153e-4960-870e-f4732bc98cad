import Vue from "vue";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

import lodash from "lodash";

import App from "./App.vue";
import OmegaMap from "../component/index";
// import OmegaMap from "@omega/map";
// import OmegaMap from '../lib/omega-map.umd.min.js'

Vue.use(ElementUI);
Vue.use(OmegaMap);

Vue.config.productionTip = false;
// 工具库
Vue.prototype._ = lodash;

new Vue({
  render: (h) => h(App),
}).$mount("#app");

let vm = new Vue(OmegaMap)
console.log(vm)