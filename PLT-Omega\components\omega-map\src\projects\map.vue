<template>
  <div class="page">
    <CetMap
      style="height: calc(100% - 40px)"
      v-bind="CetMap_A"
      v-on="CetMap_A.event"
    ></CetMap>
  </div>
</template>

<script>
import map from "../../component/index";

export default {
  name: "MapDemo",
  components: {},
  data() {
    return {
      CetMap_A: {
        customMark_in: false,
        districts_in: [],
        configMapControls(Bmap) {
          return [
            {
              name: "NavigationControl",
              position: {
                offset: new Bmap.Size(10, 30),
              },
            },
            {
              name: "ScaleControl",
              position: {
                anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
              },
            },
          ];
        },
        districtsBorder_in: {
          //行政区边框
          //配置边框色、大小，如需每色块边框等不同则在districts_in每一项中加配置
          strokeColor: "#aaaaaa",
          strokeWeight: 1,
        },
        bounds_in: {
          // sw: [116.027143, 39.772348],
          // ne: [116.832025, 40.126349]
        },
        options: {
          bmap: {
            // center: [104.114129, 37.550339],
            // zoom: 5,
            // roam: true,
            mapStyle: map.getTheme("dark"),
            // mapStyle: {
            //   style: "midnight",
            // },
            // center: [116.404, 39.915],
            // zoom: 5
            // zoom: 10 116.65569, 39.78261
            // mapStyle: {
            //   style: "hardedge"
            // }
          },
          tooltip: {
            type: "item",
          },
          series: [],
        },
        event: {
          customPoint_out: this.CetMap_A_customPoint_out,
        },
      },
      // management组件
      CetButton_management: {
        visible_in: true,
        disable_in: false,
        title: "批量显示点",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_management_statusTrigger_out,
        },
      },
      // pathDrawing组件
      CetButton_pathDrawing: {
        visible_in: true,
        disable_in: false,
        title: "路径绘制",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_pathDrawing_statusTrigger_out,
        },
      },
      // districtBackground组件
      CetButton_districtBackground: {
        visible_in: true,
        disable_in: false,
        title: "行政区背景&色块",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out:
            this.CetButton_districtBackground_statusTrigger_out,
        },
      },
      icon: "path://M35.5 40.5c0-22.16 17.84-40 40-40s40 17.84 40 40c0 1.6939-.1042 3.3626-.3067 5H35.8067c-.2025-1.6374-.3067-3.3061-.3067-5zm90.9621-2.6663c-.62-1.4856-.9621-3.1182-.9621-4.8337 0-6.925 5.575-12.5 12.5-12.5s12.5 5.575 12.5 12.5a12.685 12.685 0 0 1-.1529 1.9691l.9537.5506-15.6454 27.0986-.1554-.0897V65.5h-28.7285c-7.318 9.1548-18.587 15-31.2715 15s-23.9535-5.8452-31.2715-15H15.5v-2.8059l-.0937.0437-8.8727-19.0274C2.912 41.5258.5 37.5549.5 33c0-6.925 5.575-12.5 12.5-12.5S25.5 26.075 25.5 33c0 .9035-.0949 1.784-.2753 2.6321L29.8262 45.5h92.2098z",
    };
  },
  watch: {},
  methods: {
    CetMap_A_customPoint_out(val) {
      console.log(
        "🚀 ~ file: index.vue ~ line 100 ~ CetMap_A_customPoint_out ~ val",
        val
      );
    },
    // management输出
    CetButton_management_statusTrigger_out(val) {
      // 批量打点
      var data = [
        { name: "福田总部", value: [114.03392, 22.540907] },
        { name: "深圳北区", value: [114.269113, 22.597795] },
        { name: "武汉研发", value: [114.458176, 30.425081] },
      ];
      this.CetMap_A.options.series = [
        {
          // name: "空气质量",
          type: "effectScatter", //带有涟漪特效动画的散点
          coordinateSystem: "bmap", //该系列使用的坐标系 bmap
          data: data, //点数据
        },
      ];
    },
    // pathDrawing输出
    CetButton_pathDrawing_statusTrigger_out(val) {
      // 多个线段、路径绘制
      let coordr = [
        {
          name: "张三",
          value: [
            [114.2592716217, 30.5616327403],
            [114.26728, 30.549565],
            [114.2530059814, 30.5378686275],
            [114.254722, 30.550605],
          ],
        },
        {
          name: "李四",
          value: [
            [114.03392, 22.540907],
            [114.269113, 22.597795],
          ],
        },
      ];
      var series2 = [];
      let arr = [
        {
          type: "scatter", // 坐标点数据
          coordinateSystem: "bmap",
          zlevel: 2,
          rippleEffect: {
            brushType: "stroke",
          },
          label: {
            normal: {
              show: true,
              position: "right",
              formatter: "{b}",
            },
          },
          symbolSize: 20,
          showEffectOn: "render",
          itemStyle: {
            normal: {
              color: "#2EC7C9",
            },
          },
        },
        {
          type: "lines",
          coordinateSystem: "bmap", // 线连接，  只需要坐标，为 起点和终点
          zlevel: 2,
          polyline: true, //是否多线段
          effect: {
            show: true,
            period: 6,
            // color: "#a10000",
            trailLength: 0,
            symbol: this.icon,
            symbolSize: [20, 12],
          },
          lineStyle: {
            normal: {
              color: "#5AB1EF",
              width: 1,
              opacity: 0.4,
              curveness: 0,
            },
          },
        },
      ];
      coordr.forEach((item, i) => {
        let obj = _.cloneDeep(arr);
        obj[0].data = [
          {
            name: item.name,
            value: item.value[0],
          },
          {
            name: item.name,
            value: item.value[item.value.length - 1],
          },
        ];
        obj[0].name = item.name;
        obj[1].data = [
          {
            coords: item.value,
          },
        ];
        series2.push(obj[0], obj[1]);
      });
      this.CetMap_A.options.series = series2;
    },
    // districtBackground输出
    CetButton_districtBackground_statusTrigger_out(val) {
      let provList = [
        // ["江苏", "#D8EDDA"],
        // ["上海", "#B9B4C8"],
        // ["浙江", "#FCF502"],
        // ["广东", "#FCF502"],
        // ["四川", "#FAC300"],
        // ["贵州", "#01933F"],
        // ["重庆", "#B9B4C8"],
        // ["湖南", "#F09ABD"],
        // ["江西", "#01933F"],
        ["湖北", "#f5f5f5", "#03a9f4"],
      ];
      this.CetMap_A.districts_in = provList;
    },
  },
};
</script>

<style scoped >
.page {
  width: 100%;
  height: 700px;
  position: relative;
}
</style>
