<template>
  <!--  直接作为弹窗使用 -->

  <div style="margin-bottom: 16px">
    <el-button @click="handleClick">选择地图上的点</el-button>
    <CetSelectMapPointDialog
      v-model="show"
      :theme="theme"
      :showPolygon="showPolygon"
      :initialCoordinates="initialCoordinates"
      @confirm="handlePointSelect"
    />
    <el-button @click="toggleShowPolygon">{{ btnText }}</el-button>
  </div>
</template>

<script>
export default {
  components: {},
  data() {
    return {
      show: false,
      showPolygon: false,
      initialCoordinates: [114.458176, 30.425081],
      theme: "dark",
    };
  },
  computed: {
    btnText() {
      return this.showPolygon ? "关闭选择范围" : "开启选择范围";
    },
  },
  methods: {
    toggleShowPolygon() {
      this.showPolygon = !this.showPolygon;
    },
    handleClick() {
      this.show = true;
    },
    handlePointSelect(coordinates, path) {
      if (!coordinates.length) {
        this.$message.warning("没有选择哦");
      } else {
        const [lng, lat] = coordinates;
        let msg = `经度：${lng} 纬度：${lat}`;
        if (this.showPolygon && path) {
          msg += `；多边行路径为：${JSON.stringify(path)}`;
        }
        console.log(path);
        this.$alert(msg, "提示");
      }
    },
  },
};
</script>