# scalescreen

## 使用

  - 按需引入该组件即可

  ```js
    import scaleScreen from '@omega/scalescreen'
  ```

## 开发

  - 安装依赖

  ```js
    npm install --save @omega/scalescreen
  ```

  - 引入: 按需引入即可，在需要开发大屏的页面引入该组件

  ```js
    import scaleScreen from '@omega/scalescreen'
  ```

  - 大屏开发

    - 组件内提供一个插槽，在插槽内添加大屏的具体内容即可，由于大屏组件已经固定了长宽，可通过入参控制，所以具体内容通过百分比或像素开发都可以。
    - 新大屏开发时建议使用像素，UI设计时，保证整体像素的合理分配，开发时直接使用UI给出的像素大小，降低开发复杂度。
    - 已开发完成的大屏，可以直接在最外层div外套一层组件即可，无需考虑之前是通过百分比还是像素方式开发。

  ```html
    <scaleScreen>
      <div>
        具体内容
      </div>
    </scaleScreen>
  ```

  - 组件参数: 
  
    <table>
    <thead>
    <tr>
    <th>属性</th>
    <th>说明</th>
    <th>类型</th>
    <th>默认值</th>
    </tr>
    </thead>
    <tbody>
    <tr>
    <td>selfAdaption</td>
    <td>是否进行自适应</td>
    <td>Boolean</td>
    <td>true</td>
    </tr>
    <tr>
    <td>width</td>
    <td>大屏宽度</td>
    <td><code>Number</code> or <code>String</code></td>
    <td>1920</td>
    </tr>
    <tr>
    <td>height</td>
    <td>大屏高度</td>
    <td><code>Number</code> or <code>String</code></td>
    <td>1080</td>
    </tr>
    <tr>
    <td>autoScale</td>
    <td>自适应配置，配置为boolean类型时，为启动或者关闭自适应，配置为对象时，若x为true，x轴产生边距，y为true时，y轴产生边距，启用fullScreen时此配置失效</td>
    <td>Boolean or {x:boolean,y:boolean}</td>
    <td>true</td>
    </tr>
    <tr>
    <td>delay</td>
    <td>窗口变化防抖延迟时间</td>
    <td>Number</td>
    <td>500</td>
    </tr>
    <tr>
    <td>fullScreen</td>
    <td>全屏自适应，启用此配置项时会存在拉伸效果，同时autoScale失效，非必要情况下不建议开启</td>
    <td>Boolean</td>
    <td>false</td>
    </tr>
    <td>wrapperStyle</td>
    <td>修改自适应区域样式，符合Vue双向绑定style标准格式</td>
    <td>Object</td>
    <td>{}</td>
    </tr>
    </tbody>
    </table>
