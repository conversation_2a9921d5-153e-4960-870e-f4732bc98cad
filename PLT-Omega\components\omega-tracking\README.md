# 简易的埋点组件

## 功能描述

获取一些 web 使用中的行为，使用 gif 发送对应的信息

- 默认自带的埋点，适配于 5.x 框架
- 支持自定义埋点，需要引用方法后，在业务代码中调用

## 默认埋点使用

### 第一步

安装组件，在项目中`src/omege/extend`中新建`track.js`文件，文件内容：

```bash
pnpm install @omega/tracking
```

```js
import omegaApp from "@omega/app";
import { OmegaTrackPlugin } from "@omega/tracking";

omegaApp.plugin.register(OmegaTrackPlugin, {
  initPm: true,
  serverPath: "/",
  initEl: true,
  initFu: true,
  initUi: true,
  projectName: "EEM"
});
```

### 第二步

在`src/omega/index.js`中引入`track.js`文件：

```js
import "./extend/admin.js"; //如有使用admin，在admin后引入
import "./extend/track.js";
```

### 配置说明

参数均为可选项  
`projectName`(Number|String) :项目名称的标识，可以用于标识正在记录的项目,默认值为 `""`

`initPm`(Boolean) :是否启用界面功能访问单次记录，功能使用时长记录（某些边缘情况无法准确记录，例如直接关闭浏览器程序）,默认值为 `false`

`initEl`(Boolean) :是否启用 web 错误日志记录，默认值为 `false`

`initFu`(Boolean) :是否启用 web 性能监控（目前采用 load 结束时间记录首屏加载时间），默认值为 `false`

浏览器访问的时间图
<img src="http://*************:8090/@omega/tracking@1.0.1/assets/timestamp-diagram.svg" alt="donate">

`initUi`(Boolean) :是否启用访问者账户记录，默认值为`false`

`serverPath`(String) :gif 请求的前缀,默认值为空字符串 `""`，可根据项目情况修改，例如`"/track-service"`。因为 gif 的请求是支持任意跨域的，所以也可以修改为完整的请求地址，例如`"https://www.example.com/track-service"`或者`http://10.12.137.105:8098`

## 自定义埋点

在业务中自定义使用埋点，注意由于信息安全因素，传递的 params 对象中不要使用表意明显的参数名，例如`userId`，`userName`等，否则可能会造成用户隐私泄露。关于更多参数名，[在这里提到。](#index1)

### 第一步

安装组件，在项目中引入：

```bash
pnpm install @omega/tracking
```

```js
//GifSender是一个类,sendInfo是一个直接使用的方法
import { GifSender, sendInfo } from "@omega/tracking";
```

### 第二步

示例代码

```js
//实例一个GifSender的方法，参数为gif请求的前缀以及项目标识付，例如"/track-service"，"PQ"
const gifSender = new GifSender("/track-service","PQ");
//方法的参数为埋点的参数对象
gifSender({
    trackType:"z",
    aid:"555",
    uu:"parmas"
});
...
//直接使用sendInfo方法，第一个参数为埋点的参数对象
//第二个参数为gif请求的前缀，例如"/track-service"
sendInfo({
    projectName: 'PQ',
    trackType:"z",
    aid:"555",
    uu:"parmas",
    lf:Json.stringify({
        bt:"map"
    }),
},"/track-service")

//注意埋点参数只能是单层级的json对象，所以当其中某个属性多层级时，需要将其变成json字符串
```

## 参数说明

<a id="index1"></a>

### 参数组成

参数由埋点类型，上传的埋点信息组成。即无论是默认埋点还是自定义埋点，参数都由`trackType`和其他`params`组成一个对象。

```ts
interface Params {
  trackType: string;
  [key: string]: string | number | boolean;
}
```

_注意，目前未对内容参数进行校验，但不符合条件的参数无法正常上传。当传递非对象或空对象时，不会调用上传。对象的属性不能重复_

### 埋点类型

埋点类型是字符串，由`trackType`属性指定，目前支持以下类型：

- `a`：记录功能访问次数
- `b`：记录功能访问时长
- `c`：记录 web 错误日志
- `d`：记录首页的加载时间
- `e`：记录访问者的账户信息

后续会按`a~z`顺序，依次添加埋点类型。

### 参数属性简易加密

参数属性中，所有属性都会被加密，加密方式为简单的一一对应，不会调用复杂转换
目前的对应关系：

```json
{
  "projectName": "ep", //项目名称标识
  "trackType": "qid", //记录的类型
  "pageurl": "rsv", //功能路径
  "longtime": "long", //功能停留时间
  "name": "tid", //功能名称
  "trackTime": "r", //日志时间
  "message": "g", //错误信息
  "url": "p", //错误位置
  "line": "l", //错误行号
  "loadEventEnd": "le", //首屏加载时间，单位ms
  "userName": "un" //访问者的账户名称
}
```

后续较为通用的属性会加入组件中的默认转换，对于不在默认转换中的属性，需要先自行转换再传入组件

## 接收埋点信息

### 使用 nginx

nginx 具有拦截请求并获取信息的功能，所以可以利用 nginx 来接收埋点信息，对于埋点请求统一返回 1px\*1px 的 gif 图片。
可以将获取的信息写入一个独立的日志文件

缺点：

- 需要自己处理日志文件,无法自动统计
- 获取的参数无法自动解析，例如中文或者 json 字符串，需要后续解析日志文件

```nginx
http{
    ...
limit_conn_zone $binary_remote_addr zone=two:1m;
limit_req_zone  $binary_remote_addr  zone=one:1m  rate=1r/s;//限制请求频率
创建一个名为auto_report的日志格式
log_format auto_report '
        "trackTime": "$time_iso8601",'
        '"projectName": "$arg_ep",'
        '"trackType": "$arg_qid", '
        '"pageurl": "$arg_rsv", '
        '"longtime": "$arg_long", '
        '"name": "$arg_tid", '
        '"trackTime": "$arg_r", '
        '"message": "$arg_g"'
        '"url": "$arg_p"'
        '"line": "$arg_l"'
        '"loadEventEnd": "$arg_le"'
        '"userName": "$arg_un"'
        ',
    ';
...
server {
    ...
    location /track-service/v.gif {
            limit_conn   two  1;//限制ip同时请求数
            limit_req zone=one burst=5 nodelay;
            log_subrequest on;
            #日志的输出路径及格式
            access_log logs\auto_report.log auto_report;
            #返回一个空的文件
            add_header Expires "Fri, 01 Jan 1980 00:00:00 GMT";
            add_header Pragma "no-cache";
            add_header Cache-Control "no-cache, max-age=0, must-revalidate";
            #返回一个1×1的空gif图片
            empty_gif;
        }
    ...
}
...
}
```

### 使用后端统计程序

这里以 nodejs 为例，省略了较多初始化的代码，贴出具体接口的部分

```js
router.get("/v.gif", function (req, res) {
  console.log(req.query); //这里可以正常获取所有的参数
  res.cacheControl = "no-cache"; //设置无缓存
  res.sendFile(__dirname + "/" + "1.gif"); //将一张准备好的gif图片发送给前端
});
```

<script type="text/javascript" src="http://*************:8090/@omega/tracking@1.0.1/assets/clipboard.js"></script>

<script>
window.onload = function () {
    setTimeout(() => {
    const preNode = document.querySelectorAll("pre");
    preNode.forEach((item, i) => {
      let CopyBtn = "<button class='codecopy-btn' id='copyText' style='position: absolute; margin-top: -5px; right: 30px'  data-clipboard-action='copy' data-clipboard-target='#code_" +
        i +
        "'" +
        ">一键复制</button>";
      if (item.firstElementChild.localName == "code") {
        item.firstElementChild.id = "code_" + i;
        let html = CopyBtn + item.innerHTML;
        item.innerHTML = html;
      }
    })

    const clipboard = new ClipboardJS('.codecopy-btn')
    }, 3e3)
    
}
</script>
