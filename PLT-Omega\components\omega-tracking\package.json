{"name": "@omega/tracking", "version": "1.0.2", "description": "简易埋点组件", "main": "lib", "module": "lib", "publishConfig": {"main": "lib"}, "files": ["lib", "CHANGELOG.md", "assets"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "rollup -c"}, "keywords": [], "author": "chengkai", "license": "MIT", "peerDependencies": {"vue": "^2.6.0", "@omega/auth": "^1.7.10"}, "devDependencies": {"vue": "^2.6.0", "@babel/cli": "^7.23.4", "@rollup/plugin-node-resolve": "^15.2.3", "@rollup/plugin-babel": "^6.0.4", "rollup": "^4.9.2", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6"}}