import Vue from "vue";
class ErrorLog {
  constructor({ send }) {
    this.send = send;
  }
  init() {
    this.gloalError();
    this.promiseError();
    this.vueError();
  }
  gloalError() {
    window.addEventListener("error",err=>{
      this.send({
        trackType:"c",
        message:err.message,
        trackTime:Date.now(),
        url:err.filename,
        line:err.lineno
      })
    },true)
  }
  promiseError() {
    window.addEventListener("unhandledrejection", event => {
      this.send({
        trackType:"c",
        message:event.reason.message,
        trackTime:Date.now(),
        url:event.reason.stack.split("\n")[1].split(":")[1].split('"'),
        line:event.reason.stack.split("\n")[1].split(":")[2].split('"')
      })
    });
  }
  vueError() {
    Vue.config.errorHandler = (err, vm, info) => {
      this.send({
        trackType: "c",
        message: err.name + ":" + err.message,
        trackTime: Date.now(),
        url: vm.$options.name,
        line: err.stack.split("\n")[1].split(":")[2].split('"')
      });
      if (process.env.NODE_ENV !== "production") {
        console.log(vm, "vm");
        console.log(info, "info");
      }
    };
  }
  send(params) {}
}

export default ErrorLog;
