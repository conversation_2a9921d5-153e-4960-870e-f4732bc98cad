//To do 记录访问者的账户信息
import omegaAuth from "@omega/auth";

class UserInfo {
  constructor({ router, send }) {
    this.router = router;
    this.send = send;
  }
  init() {
    this.getUser(this.router);
  }
  getUser(router) {
    router.afterEach((to, from) => {
      if (from.path === "/login" && to.path !== "/login") {
        this.send({
          trackType: "e",
          userName: omegaAuth.user.getUserName()
        });
      }
    });
  }
  send(params) {}
}

export default UserInfo;
