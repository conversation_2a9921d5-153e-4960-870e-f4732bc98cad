import utils from "../utils/index.js";

//功能访问的基础记录
class ViewPath {
  constructor({ conf, router, send }) {
    this.conf = conf;
    this.router = router;
    this.send = send
  }
  //初始化
  init() {
    this.viewCount(this.router, this.conf);
    this.viewLongTime(this.router, this.conf);
    this.viewBlindActive(this.conf);
  }
  //利用vuerouter的进出记录上个功能的时间
  viewLongTime(router, conf) {
    router.afterEach((to, from) => {
      if (to.path === "/" || to.path === "/login") return;
      if (from.path !== "/" && from.path !== "/login") {
        this.send({
          pageurl: from.path,
          trackType: "b",
          name: utils.getBreadcrumb(from.path, conf.getNavmenu()),
          longtime: Date.now() - window.omegaTrackTime,
        });
      }
      window.omegaTrackTime = Date.now();
    });
  }
  //利用vuerouter记录功能访问次数
  viewCount(router, conf) {
    router.afterEach((to, from) => {
      if (to.path === "/" || to.path === "/login") return;
      window.omegaTrackPath = to.path;
      this.send({
        pageurl: to.path,
        trackType: "a",
        name: utils.getBreadcrumb(to.path, conf.getNavmenu()),
      });
    });
  }
  //在对应web界面隐藏时，记录功能访问时间
  viewBlindActive(conf) {
    function state(e) {
      if (document.hidden) {
        this.send({
          pageurl: window.omegaTrackPath,
          trackType: "b",
          name: utils.getBreadcrumb(window.omegaTrackPath, this.getNavmenu()),
          longtime: Date.now() - window.omegaTrackTime
        });
      } else {
        window.omegaTrackTime = Date.now();
      }
    }
    document.addEventListener(
      "visibilitychange",
      state.bind({ getNavmenu: conf.getNavmenu, send: this.send })
    );
  }
  send(params) {}
}

export default ViewPath;
