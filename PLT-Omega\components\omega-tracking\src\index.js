import ViewPath from "./Event/viewPath.js";
import ErrorLog from "./Event/error.js";
import FURPS from "./Event/furps.js";
import send from "./send/index.js";
import UserInfo from "./Event/userInfo.js";
export class OmegaTrackPlugin {
  constructor(
    { conf, router },
    {
      initPm = false,
      serverPath = "",
      initEl = false,
      initFu = false,
      initUi = false,
      projectName = ""
    }
  ) {
    this.conf = conf;
    this.router = router;
    this.initPm = initPm; //是否启动默认的访问埋点
    this.initEl = initEl; //是否启动默认的错误log埋点
    this.initFu = initFu; //是否启动默认的性能埋点
    this.initUi = initUi; //是否启动访问者账户记录埋点
    this.projectName = projectName; //项目名称标识;
    this.sendFuc = new send.GifSender(serverPath, projectName);
  }
  beforeAppBoot() {
    if (this.initPm) {
      const Pm = new ViewPath({
        conf: this.conf,
        router: this.router,
        send: this.sendFuc
      });
      Pm.init();
    }
    if (this.initEl) {
      const El = new ErrorLog({
        send: this.sendFuc
      });
      El.init();
    }
    if (this.initFu) {
      const Fu = new FURPS({
        send: this.sendFuc
      });
      Fu.init();
    }
    if (this.initUi) {
      const Ui = new UserInfo({
        router: this.router,
        send: this.sendFuc
      });
      Ui.init();
    }
  }
}

export const GifSender = send.GifSender;
export const sendInfo = send.sendInfo;
export default {
  ...send,
  OmegaTrackPlugin
};
