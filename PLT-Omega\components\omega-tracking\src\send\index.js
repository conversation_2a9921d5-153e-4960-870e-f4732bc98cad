import utils from "../utils/index.js";

//使用gif发送信息
export function sendInfo(params, path = "") {
  if (utils.isObjectEmpty(params)) return;
  const Img = new Image();
  //to do Tips
  //使用的gif名称
  Img.src = path + "/v.gif?" + utils.parse(params);
}

export function sendBeacon(params,path="") {
  if (utils.isObjectEmpty(params)) return;
  //to do Tips
  //原生方法埋点
  navigator.sendBeacon(path, params);
}

//初始化使用固定路径gif
export class GifSender {
  constructor(servePath, projectName) {
    return this.initSend(servePath, projectName);
  }
  initSend(servePath, projectName) {
    return function (params) {
      if (utils.isObjectEmpty(params)) return;
      params.projectName = projectName;
      //to do tips
      //参数传递用户的信息
      const Img = new Image();
      //to do Tips
      //使用的gif名称
      Img.src = servePath + "/v.gif?" + utils.parse(params);
    };
  }
}

export default {
  GifSender,
  sendInfo
  // ...
};
