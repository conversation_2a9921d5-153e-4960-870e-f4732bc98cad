import passwordList from "./passwordList.js";

//将属性根据对应关系转换成表意不明显的字段
function transformKey(key) {
  return passwordList[key]?passwordList[key]:key;
}

//将对象转换成对应的query字符串
function parse(params) {
  const arr = [];
  for (let key in params) {
    arr.push(transformKey(key) + "=" + params[key]);
  }
  return arr.join("&");
}

//判断是否为非空对象
function isObjectEmpty(obj) {
  if (typeof obj !== "object" || obj === null || Array.isArray(obj)) {
    return true;
  } else if (Object.keys(obj).length !== 0) {
    return false;
  } else {
    return true;
  }
}

//根据路径查找功能的全称--面包屑层级文字
function getBreadcrumb(path, navmenu) {
  function _findNode(arr, value) {
    let fin = [],
      node;
    node = arr.find(ele => ele.location === value);
    if (node) {
      fin.push(node);
      return fin;
    } else {
      arr.forEach(ele => {
        if (ele.subMenuList) {
          node = _findNode(ele.subMenuList, value);
          if (node.length) {
            fin = node;
            fin.unshift(ele);
          }
        }
      });
      return fin;
    }
  }
  return _findNode(navmenu, path)
    .map(ele => ele.label)
    .join("/");
}

export default {
  parse,
  isObjectEmpty,
  getBreadcrumb
};
