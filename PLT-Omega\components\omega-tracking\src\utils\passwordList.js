export default {
  projectName: "ep", //项目名称标识
  trackType: "qid", //记录的类型
  pageurl: "rsv", //功能路径
  longtime: "long", //功能停留时间
  name: "tid", //功能名称
  trackTime: "r", //日志时间
  message: "g", //错误信息
  url: "p", //错误位置
  line: "l", //错误行号
  loadEventEnd:"le", //首屏加载时间
  userName: "un" //访问者的账户名称
};
//To do 设置一个类似权限验证简单key

//记录类型的枚举
let trackType = {
  a: "记录功能访问次数",
  b: "记录功能停留时间",
  c: "记录web错误日志",
  d: "记录首屏加载时间",
  e: "记录访问者的账户信息"
};