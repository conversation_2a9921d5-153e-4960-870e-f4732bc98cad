# @omega/trend

## 1.2.13

- chore:增加表格模式中表头里unit为空的兼容显示

## 1.2.12

- chore:兼容min和max相等的情况

相关工作项: #23226

## 1.2.11

- chore: 更新corejs版本至3.36.0

## 1.2.10

- feat: 恢复支持自定义曲线line的颜色序列

## 1.2.9

- fixed: 修复限值超出当前Y轴范围时不显示的情况.

## 1.2.8

- perf: 优化自适应设置 Y 轴最大值最小值逻辑, 改善曲线波动极小情况下曲线展示起伏不明显的情况

## 1.2.7

- feat: 添加 unit 单位的容错

## 1.2.6

- fixed: 差值功能主要用于能耗值, 电量等累加值的差值展示, 从而能够展示时段内的耗电量, 因此需要将差值展示在前一个时刻上.

## 1.2.5

- feat:添加打点显示 tooltip 的格式化函数参数

## 1.2.4

- 4086246: fixed:修复趋势曲线保留小数的显示功能，表格头部显示数据单位

## 1.2.3

- feat: 添加 splitLine 分割线配置

## 1.2.2

- 29bc7929: '更改趋势曲线最小视图配置'

- 950e19bd: fix: 修复趋势曲线无显示接口数据问题

## 1.2.0

- feat: 趋势曲线增加 responseStatus 输出

## 1.1.4

- feat: esmodule 入口添加
- Updated dependencies
  - cet-chart@1.2.3
  - @omega/http@1.2.3
  - @omega/i18n@1.1.5

## 1.1.3

- fix: 调整依赖版本为外部任意版本
- Updated dependencies
  - cet-chart@1.2.1

## 1.1.2

- fix: 开发环境下语法报错

## 1.1.1

- fix: 趋势曲线中存在点位展示的数值与坐标轴不对应的问题

## 1.1.0

- feat: 新增导出图片颜色适配当前皮肤功能配置 withBackgroudColor

## 1.0.9

- Updated dependencies
  - @omega/http@1.1.5

## 1.0.8

- Updated dependencies
  - @omega/http@1.1.4

## 1.0.7

- feat: 国际化适配

## 1.0.6

- 3a4a9e1: [2022-04-15]
  fix(trend): 调整对 http 包引用方式, 兼容最新 http 包对外 api
