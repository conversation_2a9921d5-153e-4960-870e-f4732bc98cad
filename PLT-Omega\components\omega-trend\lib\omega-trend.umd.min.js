(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=e():"function"===typeof define&&define.amd?define([],e):"object"===typeof exports?exports["omega-trend"]=e():t["omega-trend"]=e()})("undefined"!==typeof self?self:this,(function(){return function(){var t={2661:function(t,e,n){var r=n(2484),a=n(7892),i=n(8863),o=r.TypeError;t.exports=function(t){if(a(t))return t;throw o(i(t)+" is not a function")}},9092:function(t,e,n){var r=n(4148),a=n(2060),i=n(3800),o=r("unscopables"),s=Array.prototype;void 0==s[o]&&i.f(s,o,{configurable:!0,value:a(null)}),t.exports=function(t){s[o][t]=!0}},3933:function(t,e,n){var r=n(2484),a=n(6528),i=r.String,o=r.TypeError;t.exports=function(t){if(a(t))return t;throw o(i(t)+" is not an object")}},4578:function(t,e,n){var r=n(4774),a=n(4156),i=n(8517),o=function(t){return function(e,n,o){var s,u=r(e),l=i(u),c=a(o,l);if(t&&n!=n){while(l>c)if(s=u[c++],s!=s)return!0}else for(;l>c;c++)if((t||c in u)&&u[c]===n)return t||c||0;return!t&&-1}};t.exports={includes:o(!0),indexOf:o(!1)}},780:function(t,e,n){var r=n(4044),a=r({}.toString),i=r("".slice);t.exports=function(t){return i(a(t),8,-1)}},6241:function(t,e,n){var r=n(2603),a=n(6408),i=n(7501),o=n(3800);t.exports=function(t,e,n){for(var s=a(e),u=o.f,l=i.f,c=0;c<s.length;c++){var f=s[c];r(t,f)||n&&r(n,f)||u(t,f,l(e,f))}}},606:function(t,e,n){var r=n(7565),a=n(3800),i=n(2039);t.exports=r?function(t,e,n){return a.f(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},2039:function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},9899:function(t,e,n){var r=n(2484),a=n(7892),i=n(606),o=n(4825),s=n(2424);t.exports=function(t,e,n,u){var l=!!u&&!!u.unsafe,c=!!u&&!!u.enumerable,f=!!u&&!!u.noTargetGet,d=u&&void 0!==u.name?u.name:e;return a(n)&&o(n,d,u),t===r?(c?t[e]=n:s(e,n),t):(l?!f&&t[e]&&(c=!0):delete t[e],c?t[e]=n:i(t,e,n),t)}},7565:function(t,e,n){var r=n(2116);t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},3101:function(t,e,n){var r=n(2484),a=n(6528),i=r.document,o=a(i)&&a(i.createElement);t.exports=function(t){return o?i.createElement(t):{}}},2489:function(t,e,n){var r=n(1110);t.exports=r("navigator","userAgent")||""},5552:function(t,e,n){var r,a,i=n(2484),o=n(2489),s=i.process,u=i.Deno,l=s&&s.versions||u&&u.version,c=l&&l.v8;c&&(r=c.split("."),a=r[0]>0&&r[0]<4?1:+(r[0]+r[1])),!a&&o&&(r=o.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=o.match(/Chrome\/(\d+)/),r&&(a=+r[1]))),t.exports=a},3473:function(t){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},8668:function(t,e,n){var r=n(2484),a=n(7501).f,i=n(606),o=n(9899),s=n(2424),u=n(6241),l=n(4112);t.exports=function(t,e){var n,c,f,d,p,h,m=t.target,g=t.global,v=t.stat;if(c=g?r:v?r[m]||s(m,{}):(r[m]||{}).prototype,c)for(f in e){if(p=e[f],t.noTargetGet?(h=a(c,f),d=h&&h.value):d=c[f],n=l(g?f:m+(v?".":"#")+f,t.forced),!n&&void 0!==d){if(typeof p==typeof d)continue;u(p,d)}(t.sham||d&&d.sham)&&i(p,"sham",!0),o(c,f,p,t)}}},2116:function(t){t.exports=function(t){try{return!!t()}catch(e){return!0}}},5771:function(t,e,n){var r=n(2116);t.exports=!r((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},6724:function(t,e,n){var r=n(5771),a=Function.prototype.call;t.exports=r?a.bind(a):function(){return a.apply(a,arguments)}},3279:function(t,e,n){var r=n(7565),a=n(2603),i=Function.prototype,o=r&&Object.getOwnPropertyDescriptor,s=a(i,"name"),u=s&&"something"===function(){}.name,l=s&&(!r||r&&o(i,"name").configurable);t.exports={EXISTS:s,PROPER:u,CONFIGURABLE:l}},4044:function(t,e,n){var r=n(5771),a=Function.prototype,i=a.bind,o=a.call,s=r&&i.bind(o,o);t.exports=r?function(t){return t&&s(t)}:function(t){return t&&function(){return o.apply(t,arguments)}}},1110:function(t,e,n){var r=n(2484),a=n(7892),i=function(t){return a(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?i(r[t]):r[t]&&r[t][e]}},190:function(t,e,n){var r=n(2661);t.exports=function(t,e){var n=t[e];return null==n?void 0:r(n)}},2484:function(t,e,n){var r=function(t){return t&&t.Math==Math&&t};t.exports=r("object"==typeof globalThis&&globalThis)||r("object"==typeof window&&window)||r("object"==typeof self&&self)||r("object"==typeof n.g&&n.g)||function(){return this}()||Function("return this")()},2603:function(t,e,n){var r=n(4044),a=n(9477),i=r({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(a(t),e)}},291:function(t){t.exports={}},5559:function(t,e,n){var r=n(1110);t.exports=r("document","documentElement")},9079:function(t,e,n){var r=n(7565),a=n(2116),i=n(3101);t.exports=!r&&!a((function(){return 7!=Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},9491:function(t,e,n){var r=n(2484),a=n(4044),i=n(2116),o=n(780),s=r.Object,u=a("".split);t.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(t){return"String"==o(t)?u(t,""):s(t)}:s},5486:function(t,e,n){var r=n(4044),a=n(7892),i=n(7653),o=r(Function.toString);a(i.inspectSource)||(i.inspectSource=function(t){return o(t)}),t.exports=i.inspectSource},1570:function(t,e,n){var r,a,i,o=n(5547),s=n(2484),u=n(4044),l=n(6528),c=n(606),f=n(2603),d=n(7653),p=n(4363),h=n(291),m="Object already initialized",g=s.TypeError,v=s.WeakMap,y=function(t){return i(t)?a(t):r(t,{})},b=function(t){return function(e){var n;if(!l(e)||(n=a(e)).type!==t)throw g("Incompatible receiver, "+t+" required");return n}};if(o||d.state){var w=d.state||(d.state=new v),x=u(w.get),S=u(w.has),T=u(w.set);r=function(t,e){if(S(w,t))throw new g(m);return e.facade=t,T(w,t,e),e},a=function(t){return x(w,t)||{}},i=function(t){return S(w,t)}}else{var _=p("state");h[_]=!0,r=function(t,e){if(f(t,_))throw new g(m);return e.facade=t,c(t,_,e),e},a=function(t){return f(t,_)?t[_]:{}},i=function(t){return f(t,_)}}t.exports={set:r,get:a,has:i,enforce:y,getterFor:b}},7892:function(t){t.exports=function(t){return"function"==typeof t}},4112:function(t,e,n){var r=n(2116),a=n(7892),i=/#|\.prototype\./,o=function(t,e){var n=u[s(t)];return n==c||n!=l&&(a(e)?r(e):!!e)},s=o.normalize=function(t){return String(t).replace(i,".").toLowerCase()},u=o.data={},l=o.NATIVE="N",c=o.POLYFILL="P";t.exports=o},6528:function(t,e,n){var r=n(7892);t.exports=function(t){return"object"==typeof t?null!==t:r(t)}},9889:function(t){t.exports=!1},3601:function(t,e,n){var r=n(2484),a=n(1110),i=n(7892),o=n(4076),s=n(2306),u=r.Object;t.exports=s?function(t){return"symbol"==typeof t}:function(t){var e=a("Symbol");return i(e)&&o(e.prototype,u(t))}},8517:function(t,e,n){var r=n(83);t.exports=function(t){return r(t.length)}},4825:function(t,e,n){var r=n(2116),a=n(7892),i=n(2603),o=n(7565),s=n(3279).CONFIGURABLE,u=n(5486),l=n(1570),c=l.enforce,f=l.get,d=Object.defineProperty,p=o&&!r((function(){return 8!==d((function(){}),"length",{value:8}).length})),h=String(String).split("String"),m=t.exports=function(t,e,n){if("Symbol("===String(e).slice(0,7)&&(e="["+String(e).replace(/^Symbol\(([^)]*)\)/,"$1")+"]"),n&&n.getter&&(e="get "+e),n&&n.setter&&(e="set "+e),(!i(t,"name")||s&&t.name!==e)&&d(t,"name",{value:e,configurable:!0}),p&&n&&i(n,"arity")&&t.length!==n.arity&&d(t,"length",{value:n.arity}),n&&i(n,"constructor")&&n.constructor){if(o)try{d(t,"prototype",{writable:!1})}catch(a){}}else t.prototype=void 0;var r=c(t);return i(r,"source")||(r.source=h.join("string"==typeof e?e:"")),t};Function.prototype.toString=m((function(){return a(this)&&f(this).source||u(this)}),"toString")},930:function(t,e,n){var r=n(5552),a=n(2116);t.exports=!!Object.getOwnPropertySymbols&&!a((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},5547:function(t,e,n){var r=n(2484),a=n(7892),i=n(5486),o=r.WeakMap;t.exports=a(o)&&/native code/.test(i(o))},2060:function(t,e,n){var r,a=n(3933),i=n(920),o=n(3473),s=n(291),u=n(5559),l=n(3101),c=n(4363),f=">",d="<",p="prototype",h="script",m=c("IE_PROTO"),g=function(){},v=function(t){return d+h+f+t+d+"/"+h+f},y=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},b=function(){var t,e=l("iframe"),n="java"+h+":";return e.style.display="none",u.appendChild(e),e.src=String(n),t=e.contentWindow.document,t.open(),t.write(v("document.F=Object")),t.close(),t.F},w=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}w="undefined"!=typeof document?document.domain&&r?y(r):b():y(r);var t=o.length;while(t--)delete w[p][o[t]];return w()};s[m]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(g[p]=a(t),n=new g,g[p]=null,n[m]=t):n=w(),void 0===e?n:i.f(n,e)}},920:function(t,e,n){var r=n(7565),a=n(8656),i=n(3800),o=n(3933),s=n(4774),u=n(3382);e.f=r&&!a?Object.defineProperties:function(t,e){o(t);var n,r=s(e),a=u(e),l=a.length,c=0;while(l>c)i.f(t,n=a[c++],r[n]);return t}},3800:function(t,e,n){var r=n(2484),a=n(7565),i=n(9079),o=n(8656),s=n(3933),u=n(6727),l=r.TypeError,c=Object.defineProperty,f=Object.getOwnPropertyDescriptor,d="enumerable",p="configurable",h="writable";e.f=a?o?function(t,e,n){if(s(t),e=u(e),s(n),"function"===typeof t&&"prototype"===e&&"value"in n&&h in n&&!n[h]){var r=f(t,e);r&&r[h]&&(t[e]=n.value,n={configurable:p in n?n[p]:r[p],enumerable:d in n?n[d]:r[d],writable:!1})}return c(t,e,n)}:c:function(t,e,n){if(s(t),e=u(e),s(n),i)try{return c(t,e,n)}catch(r){}if("get"in n||"set"in n)throw l("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},7501:function(t,e,n){var r=n(7565),a=n(6724),i=n(3873),o=n(2039),s=n(4774),u=n(6727),l=n(2603),c=n(9079),f=Object.getOwnPropertyDescriptor;e.f=r?f:function(t,e){if(t=s(t),e=u(e),c)try{return f(t,e)}catch(n){}if(l(t,e))return o(!a(i.f,t,e),t[e])}},9619:function(t,e,n){var r=n(2491),a=n(3473),i=a.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,i)}},5004:function(t,e){e.f=Object.getOwnPropertySymbols},4076:function(t,e,n){var r=n(4044);t.exports=r({}.isPrototypeOf)},2491:function(t,e,n){var r=n(4044),a=n(2603),i=n(4774),o=n(4578).indexOf,s=n(291),u=r([].push);t.exports=function(t,e){var n,r=i(t),l=0,c=[];for(n in r)!a(s,n)&&a(r,n)&&u(c,n);while(e.length>l)a(r,n=e[l++])&&(~o(c,n)||u(c,n));return c}},3382:function(t,e,n){var r=n(2491),a=n(3473);t.exports=Object.keys||function(t){return r(t,a)}},3873:function(t,e){"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,a=r&&!n.call({1:2},1);e.f=a?function(t){var e=r(this,t);return!!e&&e.enumerable}:n},7563:function(t,e,n){var r=n(2484),a=n(6724),i=n(7892),o=n(6528),s=r.TypeError;t.exports=function(t,e){var n,r;if("string"===e&&i(n=t.toString)&&!o(r=a(n,t)))return r;if(i(n=t.valueOf)&&!o(r=a(n,t)))return r;if("string"!==e&&i(n=t.toString)&&!o(r=a(n,t)))return r;throw s("Can't convert object to primitive value")}},6408:function(t,e,n){var r=n(1110),a=n(4044),i=n(9619),o=n(5004),s=n(3933),u=a([].concat);t.exports=r("Reflect","ownKeys")||function(t){var e=i.f(s(t)),n=o.f;return n?u(e,n(t)):e}},8787:function(t,e,n){var r=n(2484),a=r.TypeError;t.exports=function(t){if(void 0==t)throw a("Can't call method on "+t);return t}},2424:function(t,e,n){var r=n(2484),a=Object.defineProperty;t.exports=function(t,e){try{a(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},4363:function(t,e,n){var r=n(3018),a=n(2471),i=r("keys");t.exports=function(t){return i[t]||(i[t]=a(t))}},7653:function(t,e,n){var r=n(2484),a=n(2424),i="__core-js_shared__",o=r[i]||a(i,{});t.exports=o},3018:function(t,e,n){var r=n(9889),a=n(7653);(t.exports=function(t,e){return a[t]||(a[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.22.5",mode:r?"pure":"global",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.22.5/LICENSE",source:"https://github.com/zloirock/core-js"})},4156:function(t,e,n){var r=n(7066),a=Math.max,i=Math.min;t.exports=function(t,e){var n=r(t);return n<0?a(n+e,0):i(n,e)}},4774:function(t,e,n){var r=n(9491),a=n(8787);t.exports=function(t){return r(a(t))}},7066:function(t){var e=Math.ceil,n=Math.floor;t.exports=function(t){var r=+t;return r!==r||0===r?0:(r>0?n:e)(r)}},83:function(t,e,n){var r=n(7066),a=Math.min;t.exports=function(t){return t>0?a(r(t),9007199254740991):0}},9477:function(t,e,n){var r=n(2484),a=n(8787),i=r.Object;t.exports=function(t){return i(a(t))}},9811:function(t,e,n){var r=n(2484),a=n(6724),i=n(6528),o=n(3601),s=n(190),u=n(7563),l=n(4148),c=r.TypeError,f=l("toPrimitive");t.exports=function(t,e){if(!i(t)||o(t))return t;var n,r=s(t,f);if(r){if(void 0===e&&(e="default"),n=a(r,t,e),!i(n)||o(n))return n;throw c("Can't convert object to primitive value")}return void 0===e&&(e="number"),u(t,e)}},6727:function(t,e,n){var r=n(9811),a=n(3601);t.exports=function(t){var e=r(t,"string");return a(e)?e:e+""}},8863:function(t,e,n){var r=n(2484),a=r.String;t.exports=function(t){try{return a(t)}catch(e){return"Object"}}},2471:function(t,e,n){var r=n(4044),a=0,i=Math.random(),o=r(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+o(++a+i,36)}},2306:function(t,e,n){var r=n(930);t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},8656:function(t,e,n){var r=n(7565),a=n(2116);t.exports=r&&a((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},4148:function(t,e,n){var r=n(2484),a=n(3018),i=n(2603),o=n(2471),s=n(930),u=n(2306),l=a("wks"),c=r.Symbol,f=c&&c["for"],d=u?c:c&&c.withoutSetter||o;t.exports=function(t){if(!i(l,t)||!s&&"string"!=typeof l[t]){var e="Symbol."+t;s&&i(c,t)?l[t]=c[t]:l[t]=u&&f?f(e):d(e)}return l[t]}},7256:function(t,e,n){"use strict";var r=n(8668),a=n(4578).includes,i=n(2116),o=n(9092),s=i((function(){return!Array(1).includes()}));r({target:"Array",proto:!0,forced:s},{includes:function(t){return a(this,t,arguments.length>1?arguments[1]:void 0)}}),o("includes")},7584:function(t,e,n){"use strict";n.r(e);var r=n(3431),a=n.n(r),i=n(6577),o=n.n(i),s=o()(a());s.push([t.id,".omega-trend[data-v-95227668]{width:100%;height:100%;box-sizing:border-box;border-radius:6px;padding:8px}.trend-class[data-v-95227668]{text-align:right}.trend-class .el-select[data-v-95227668],.trend-class>div[data-v-95227668],.trend-class>label[data-v-95227668],.trend-table[data-v-95227668]{display:inline-block}.trend-table[data-v-95227668]{height:100%;width:100%}.trend-table p[data-v-95227668]{text-align:center}.trend-table>div[data-v-95227668]{display:inline-block;height:100%;padding-right:12px;min-width:312px;box-sizing:border-box}.trend-chart[data-v-95227668]{width:100%;height:100%}.hide-chart[data-v-95227668]{position:absolute;left:-10000px;top:-10000px;width:calc(100% - 80px);height:100%}",""]),e["default"]=s},6577:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var n="",r="undefined"!==typeof e[5];return e[4]&&(n+="@supports (".concat(e[4],") {")),e[2]&&(n+="@media ".concat(e[2]," {")),r&&(n+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),n+=t(e),r&&(n+="}"),e[2]&&(n+="}"),e[4]&&(n+="}"),n})).join("")},e.i=function(t,n,r,a,i){"string"===typeof t&&(t=[[null,t,void 0]]);var o={};if(r)for(var s=0;s<this.length;s++){var u=this[s][0];null!=u&&(o[u]=!0)}for(var l=0;l<t.length;l++){var c=[].concat(t[l]);r&&o[c[0]]||("undefined"!==typeof i&&("undefined"===typeof c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),n&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=n):c[2]=n),a&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=a):c[4]="".concat(a)),e.push(c))}},e}},3431:function(t){"use strict";t.exports=function(t){return t[1]}},6136:function(t,e,n){var r=n(7584);r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.id,r,""]]),r.locals&&(t.exports=r.locals);var a=n(7325).Z;a("e0e10702",r,!0,{sourceMap:!1,shadowMode:!1})},7325:function(t,e,n){"use strict";function r(t,e){for(var n=[],r={},a=0;a<e.length;a++){var i=e[a],o=i[0],s=i[1],u=i[2],l=i[3],c={id:t+":"+a,css:s,media:u,sourceMap:l};r[o]?r[o].parts.push(c):n.push(r[o]={id:o,parts:[c]})}return n}n.d(e,{Z:function(){return h}});var a="undefined"!==typeof document;if("undefined"!==typeof DEBUG&&DEBUG&&!a)throw new Error("vue-style-loader cannot be used in a non-browser environment. Use { target: 'node' } in your Webpack config to indicate a server-rendering environment.");var i={},o=a&&(document.head||document.getElementsByTagName("head")[0]),s=null,u=0,l=!1,c=function(){},f=null,d="data-vue-ssr-id",p="undefined"!==typeof navigator&&/msie [6-9]\b/.test(navigator.userAgent.toLowerCase());function h(t,e,n,a){l=n,f=a||{};var o=r(t,e);return m(o),function(e){for(var n=[],a=0;a<o.length;a++){var s=o[a],u=i[s.id];u.refs--,n.push(u)}e?(o=r(t,e),m(o)):o=[];for(a=0;a<n.length;a++){u=n[a];if(0===u.refs){for(var l=0;l<u.parts.length;l++)u.parts[l]();delete i[u.id]}}}}function m(t){for(var e=0;e<t.length;e++){var n=t[e],r=i[n.id];if(r){r.refs++;for(var a=0;a<r.parts.length;a++)r.parts[a](n.parts[a]);for(;a<n.parts.length;a++)r.parts.push(v(n.parts[a]));r.parts.length>n.parts.length&&(r.parts.length=n.parts.length)}else{var o=[];for(a=0;a<n.parts.length;a++)o.push(v(n.parts[a]));i[n.id]={id:n.id,refs:1,parts:o}}}}function g(){var t=document.createElement("style");return t.type="text/css",o.appendChild(t),t}function v(t){var e,n,r=document.querySelector("style["+d+'~="'+t.id+'"]');if(r){if(l)return c;r.parentNode.removeChild(r)}if(p){var a=u++;r=s||(s=g()),e=b.bind(null,r,a,!1),n=b.bind(null,r,a,!0)}else r=g(),e=w.bind(null,r),n=function(){r.parentNode.removeChild(r)};return e(t),function(r){if(r){if(r.css===t.css&&r.media===t.media&&r.sourceMap===t.sourceMap)return;e(t=r)}else n()}}var y=function(){var t=[];return function(e,n){return t[e]=n,t.filter(Boolean).join("\n")}}();function b(t,e,n,r){var a=n?"":r.css;if(t.styleSheet)t.styleSheet.cssText=y(e,a);else{var i=document.createTextNode(a),o=t.childNodes;o[e]&&t.removeChild(o[e]),o.length?t.insertBefore(i,o[e]):t.appendChild(i)}}function w(t,e){var n=e.css,r=e.media,a=e.sourceMap;if(r&&t.setAttribute("media",r),f.ssrId&&t.setAttribute(d,e.id),a&&(n+="\n/*# sourceURL="+a.sources[0]+" */",n+="\n/*# sourceMappingURL=data:application/json;base64,"+btoa(unescape(encodeURIComponent(JSON.stringify(a))))+" */"),t.styleSheet)t.styleSheet.cssText=n;else{while(t.firstChild)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}}},e={};function n(r){var a=e[r];if(void 0!==a)return a.exports;var i=e[r]={id:r,exports:{}};return t[r](i,i.exports,n),i.exports}!function(){n.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return n.d(e,{a:e}),e}}(),function(){n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"===typeof window)return window}}()}(),function(){n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}(),function(){n.p=""}();var r={};return function(){"use strict";if(n.r(r),n.d(r,{default:function(){return N}}),"undefined"!==typeof window){var t=window.document.currentScript,e=t&&t.src.match(/(.+\/)[^/]+\.js(\?.*)?$/);e&&(n.p=e[1])}var a=function(){var t=this,e=t._self._c;return e("div",{staticClass:"omega-trend bg-BG1"},[e("el-container",{staticStyle:{height:"100%"}},[e("el-header",{staticClass:"trend-class",attrs:{height:t.isNoHeaderButton?"0px":"30px"}},[e("div",{staticStyle:{"padding-bottom":"5px"}},[t.showLimitButton?e("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.showTable,expression:"!showTable"}],on:{change:t.limitHandler}},[t._v(" "+t._s(t.limitText.buttonText)+" ")]):t._e(),t.showExtremButton?e("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.showTable,expression:"!showTable"}],on:{change:t.extremValueHandler}},[t._v(" "+t._s(t.i18n("最值"))+" ")]):t._e(),t.showDiffButton?e("el-checkbox",{on:{change:t.diffHandler}},[t._v(" "+t._s(t.i18n("差值"))+" ")]):t._e(),t.showAverageButton?e("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.showTable,expression:"!showTable"}],on:{change:t.averageHandler}},[t._v(" "+t._s(t.i18n("平均值"))+" ")]):t._e(),t.showPointButton?e("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.showTable,expression:"!showTable"}],on:{change:t.pointHandler}},[t._v(" "+t._s(t.i18n("打点显示"))+" ")]):t._e(),t.showRawMarkButton?e("el-checkbox",{directives:[{name:"show",rawName:"v-show",value:!t.showTable,expression:"!showTable"}],on:{change:t.rawMarkHandler}},[t._v(" "+t._s(t.i18n("原始标记"))+" ")]):t._e()],1),t.showTableButton?e("div",{staticStyle:{"padding-left":"20px"}},[t._v(" "+t._s(t.i18n("展示类型:"))+" "),e("el-select",{staticStyle:{width:"120px"},attrs:{size:"mini"},model:{value:t.showTableSelect,callback:function(e){t.showTableSelect=e},expression:"showTableSelect"}},t._l(t.options,(function(t){return e("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1):t._e()]),e("el-main",{style:t.mainStyle},[e("el-container",{staticStyle:{height:"100%"}},[e("div",{staticClass:"trend-chart",class:t.showTable?"hide-chart":"trend-chart"},[e("CetChart",t._b({ref:"chart",attrs:{inputData_in:t.CetChart_trend.inputData_in},on:{finished:t.chartFinish,click:t.clickHandler}},"CetChart",t.CetChart_trend.config,!1))],1),t.showTable?e("div",{staticClass:"trend-table"},t._l(t.tableData,(function(n,r){return e("div",{key:r,style:t.tableDivStyle},[e("el-table",{ref:"cetTable",refInFor:!0,staticStyle:{height:"100%",width:"100%"},attrs:{data:n.data,"tooltip-effect":"light",border:"",height:"true"}},[t._l(n.header,(function(t,n){return[e("el-table-column",{key:n,attrs:{label:t.label,prop:t.prop,"header-align":"center",width:t.width,"show-overflow-tooltip":!0}})]}))],2)],1)})),0):t._e()])],1)],1)],1)},i=[],o=(n(7256),require("lodash")),s=n.n(o);const u=[null,-2147483648,"NaN","Infinity",2147483648];function l(t,e,n="--"){const r=s().get(t,e,n);return u.includes(r)?n:r}const c=function(t,e){e=e||0;var n=Math.pow(10,e);return(Math.round(t*n)/n).toFixed(e)};var f={get:l,formatNumberWithPrecision:function(t,e){if(s().isNumber(e)){if(s().isNumber(t)||(t=parseFloat(t)),isNaN(t))return null;t=c(t,e)}return t}},d=require("moment"),p=n.n(d),h=require("@omega/http"),m=require("cet-chart"),g=n.n(m),v=require("@omega/i18n"),y=n.n(v),b=JSON.parse('{"最值":"Max And Min ","差值":"D-value","平均值":"AVG","打点显示":"Dot display","原始标记":"Original mark","展示类型:":"Display Type :","限值":"Limit","上限":"Upper limit","下限":"Lower limit","曲线":"Curve","表格":"Table","最大值":"Max","最小值":"Min","时间":"Time"}');const w=y().init({scope:"@omega/trend",map:{en:b}});var x={components:{CetChart:g()},name:"OmegaTrend",props:{params_in:{type:[Array,Object]},queryTime_in:{type:Object},interval_in:{type:Number,default:0},title_in:{type:String},scatter_in:{type:[Number,Array]},queryMode:{type:String},queryTrigger_in:{type:[Number,Array,Object,String]},clearTrigger_in:{type:Number},dataConfig:{type:Object},showLegend:{type:Boolean,default:!0},showExtremButton:{type:Boolean,default:!0},showDiffButton:{type:Boolean,default:!0},showLimitButton:{type:Boolean,default:!1},showAverageButton:{type:Boolean,default:!0},showPointButton:{type:Boolean,default:!0},showRawMarkButton:{type:Boolean,default:!1},showTableButton:{type:Boolean,default:!0},precision:{type:Number},splitNumber:{type:Number,default:5},color:{type:Array},limitText:{type:Object,default:()=>({buttonText:w("限值"),upperLimitText:w("上限"),lowerLimitText:w("下限")})},exportImgName_in:{type:String},withBackgroudColor:{type:Boolean,default:!0},viewSize:{type:String,default:""},splitLine:{type:Boolean,default:!0},scatter_tooltip:Function},data(){let t=this;return{trendData:[],originalData:[],originalTrendData:[],diffData:[],showMaxAndMinStatus:!1,showLimitStatus:!1,showAverageStatus:!1,showPointStatus:!1,showRawMarkStatus:!1,showDiffStatus:!1,showTableSelect:!1,CetChart_trend:{inputData_in:{},config:{options:{color:this.color||[],legend:{show:t.showLegend,type:"scroll",top:20,left:100},tooltip:{trigger:"axis",appendToBody:!0,formatter:function(e){let n=t.trendData;for(var r=p()(e[0].axisValue).format("MM-DD HH:mm")+"<br>",a=0;a<e.length;a++){var i=e[a]["seriesName"];if("trendScatter"===i){t.scatter_tooltip&&(r=t.scatter_tooltip(e[0].data[2],e));continue}let u=s().find(n,{name:i}),l="";u&&!s().isEmpty(s().get(u.param,"unit"))&&(l=u.param.unit);var o=e[a]["value"][1];o=s().isNumber(o)&&!s().isNaN(o)?o.toFixed(t.precision||2):"--",r+=i+" ："+o+l+"<br>"}return r}},title:{left:"left",text:this.title_in},grid:{left:8,right:8,top:100,containLabel:!0},toolbox:{top:40,right:30,feature:{dataZoom:{yAxisIndex:"none"},saveAsImage:{name:this.exportImgName_in}}},xAxis:{type:"time",axisLine:{onZero:!1}},yAxis:{boundaryGap:[0,"10%"],splitLine:{show:!0}},dataZoom:[{type:"inside",start:0,end:100,minValueSpan:36e5},{start:0,end:100,handleIcon:"M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",handleSize:"80%",handleStyle:{color:"#fff",shadowBlur:3,shadowColor:"rgba(0, 0, 0, 0.6)",shadowOffsetX:2,shadowOffsetY:2},left:150,right:150}],series:[]},manualUpdate:!0}},showTable:!1,renderColumns:[],tableData:[],options:[{value:!1,label:w("曲线")},{value:!0,label:w("表格")}],distributionChart:"",tableDivStyle:{width:"100%"},extremValue:[],optionsSize:{legend:{top:5},grid:{top:70,bottom:50},toolbox:{top:20},dataZoom:[{},{bottom:10,height:20}]}}},watch:{params_in:{handler:function(){this.paramsChange()}},queryTime_in:{deep:!0,handler:function(t){this.paramsChange()}},title_in:{immediate:!0,handler:function(t){this.$refs.chart&&this.$refs.chart.mergeOptions({title:{text:t}})}},exportImgName_in:{immediate:!0,handler:function(t){this.$refs.chart&&this.$refs.chart.mergeOptions({toolbox:{feature:{saveAsImage:{name:t}}}})}},trendData:{deep:!0,handler:function(t){let e=this,n=e.trendData.length;e.tableDivStyle.width=n&&1!==n?100/n+"%":"100%"}},queryTrigger_in(){this.getChartData()},clearTrigger_in(){this.clearLines()},showTableSelect(t){let e=this;e.showTable=t,!1===t&&setTimeout((()=>{e.updateChart(),e.setTrendStatus()}),0)},scatter_in(t){const e=this;if(t&&s().isArray(t)){let n=t.map((t=>[t.x,0,t])),r=[{name:"trendScatter",data:n,type:"scatter"}];e.$refs.chart.mergeOptions({series:r})}}},updated(){},computed:{isNoHeaderButton(){return!this.showExtremButton&&!this.showPointButton&&!this.showAverageButton&&!this.showTableButton&&!this.showLimitButton&&!this.showRawMarkButton&&!this.showDiffButton},mainStyle(){return this.isNoHeaderButton?{padding:"0px 20px",overflowX:"auto",whiteSpace:"nowrap",height:"100%"}:{padding:"0px 20px",overflowX:"auto",whiteSpace:"nowrap",height:"calc(100% - 30px)"}}},methods:{paramsChange(){"diff"===this.queryMode&&this.getChartData()},getQueryTime(){let t=this,e={};return s().isEmpty(t.queryTime_in)||!t.queryTime_in?null:(e={timeType:t.queryTime_in.timeType?t.queryTime_in.timeType:1,startTime:null,endTime:null},s().isArray(t.queryTime_in.time)&&(e.startTime=p()(t.queryTime_in.time[0]).format("YYYY-MM-DD HH:mm:ss"),e.endTime=p()(t.queryTime_in.time[1]).format("YYYY-MM-DD HH:mm:ss")),e)},getParams(){let t=this;return s().map(t.params_in,(function(t){return{dataId:t.dataId,dataTypeId:t.dataTypeId,deviceId:t.deviceId,logicalId:t.logicalId}}))},pointHandler(t){let e=this;e.showPointStatus=!!t,e.setPoint(e.showPointStatus)},setPoint(t){let e=this,n=e.trendData,r={series:[]};for(let a=0,i=n.length;a<i;a++){let e={showSymbol:t,name:n[a].name};r.series.push(e)}e.$refs.chart.mergeOptions(r)},rawMarkHandler(t){let e=this;e.showRawMarkStatus=!!t,e.setRawMark(e.showRawMarkStatus)},setRawMark(t){let e=this,n=e.trendData,r={series:[]};for(let a=0,i=n.length;a<i;a++)if(t){let t=e.getSingleRawMark(n[a]),i={markArea:{silent:!0,data:t},name:n[a].name};r.series.push(i)}else{let t={markArea:{silent:!0,itemStyle:{color:"rgb(230, 230, 230)",opacity:.5},data:[]},name:n[a].name};r.series.push(t)}e.$refs.chart.mergeOptions(r)},getSingleRawMark(t){const e=this;let n=[],r=[],a=0,i=0;n=e.getOriginalData(t);while(-1!==a&&i!==n.length-1)a=s().findIndex(n,{status:3},i),-1!==a&&(i=s().findIndex(n,{status:0},a),-1===i&&(i=n.length-1),r.push([]),r[r.length-1].push({xAxis:n[a].time}),r[r.length-1].push({xAxis:n[i].time}));return r},getOriginalData(t){const e=this;let n=e.originalData,r=t.param;for(let a=0,i=n.length;a<i;a++)if(n[a].deviceId===r.deviceId&&n[a].logicalId===r.logicalId&&n[a].dataTypeId===r.dataTypeId&&n[a].dataId===r.dataId)return n[a].dataList},limitHandler(t){let e=this;e.showLimitStatus=!!t,e.setMarkline()},averageHandler(t){let e=this;e.showAverageStatus=!!t,e.setMarkline()},diffHandler(t){let e=this;e.showDiffStatus=!!t,e.extremValue=[],t?e.showDiff():e.hideDiff(),e.updateChart(),e.updateTable(),e.setTrendStatus()},showDiff(){const t=this;t.trendData=t.diffData},hideDiff(){const t=this;t.trendData=t.originalTrendData},calcDiffData(){const t=this;let e=t.trendData,n=e.map((e=>{let n=s().cloneDeep(e);return n.data=e.data.map(((e,n,r)=>{let a,i=e[1];if(n===r.length-1)a=0;else{let e=r[n+1][1];a=s().isNil(i)||!s().isNumber(i)||s().isNil(e)||!s().isNumber(e)?NaN:parseFloat(f.formatNumberWithPrecision(e-i,t.precision||2))}return[e[0],a]})),n}));t.originalTrendData=e,t.diffData=n},initDiffData(){const t=this;t.calcDiffData(),t.showDiffStatus&&(t.trendData=t.diffData),t.extremValue=[],t.updateChart(),t.updateTable(),t.setTrendStatus()},extremValueHandler(t){let e=this;t?(e.showMaxAndMinStatus=!0,e.showMaxAndMin()):(e.showMaxAndMinStatus=!1,e.removeMaxAndMin())},showMaxAndMin(){let t=this,e=t.trendData,n={series:[]};for(let r=0,a=e.length;r<a;r++){let a=t.calcMaxAndMin(e[r]);s().isNil(a)||n.series.push(a)}t.$refs.chart.mergeOptions(n)},removeMaxAndMin(){let t=this,e=t.trendData,n={series:[]};for(let r=0,a=e.length;r<a;r++){let t={markPoint:{data:[]},name:e[r].name};n.series.push(t)}t.$refs.chart.mergeOptions(n)},calcMaxAndMin(t){let e,n,r,a=this,i=t.data;return i.length<1?null:(e=s().maxBy(i,(function(t){return t[1]})),n=s().minBy(i,(function(t){return t[1]})),s().isNil(e)||s().isNil(n)?null:(a.saveExtremValue(t.param.unit,e[1],n[1]),r={markPoint:{data:[{value:e[1],name:w("最大值"),coord:e,label:{position:"top"}},{value:n[1],name:w("最小值"),coord:n,label:{position:"bottom"}}],label:{formatter:"{@value}({b})",fontWeight:800}},name:t.name},r))},saveExtremValue(t,e,n){const r=this;let a=r.extremValue,i=s().find(a,{name:t});i?(i.max=e>i.max?e:i.max,i.min=n<i.min?n:i.min):a.push({name:t,max:e,min:n})},setMarkline(){let t=this,e=t.trendData,n={series:[]};for(let r=0,a=e.length;r<a;r++){let a=t.setSingleMarkline(e[r]);n.series.push(a)}t.$refs.chart.mergeOptions(n)},setSingleMarkline(t){let e,n=this;if(!n.showAverageStatus&&!n.showLimitStatus)return{markLine:{data:[]},name:t.name};if(e={markLine:{data:[],symbol:"none",label:{formatter:"{@value}({b})",position:"insideStartTop"}},name:t.name},n.showAverageStatus&&t.data.length>0){let n,r=t.data,a=r.filter((t=>s().isNumber(t[1])&&!s().isNaN(t[1])));n=s().meanBy(a,(function(t){return t[1]})),e.markLine.data.push({yAxis:n,name:w("平均值")})}if(n.showLimitStatus){let r=t.param;r.upperLimit&&e.markLine.data.push({yAxis:r.upperLimit,name:n.limitText.upperLimitText}),r.lowerLimit&&e.markLine.data.push({yAxis:r.lowerLimit,name:n.limitText.lowerLimitText}),n.saveExtremValue(r.unit,r.upperLimit,r.lowerLimit)}return e},getChartData(){const t=this;let e={endTime:t.getQueryTime().endTime,interval:t.interval_in,meterConfigs:t.getParams(),startTime:t.getQueryTime().startTime};if(e.meterConfigs.length<1)return void t.clearLines();let n={url:t.dataConfig.queryUrl,method:"POST",data:e};(0,h.httping)(n).then((function(e){0===e.code?(s().isArray(e.data)?(t.originalData=e.data,t.generateTendData(e.data),t.initDiffData()):t.clearLines(),t.$emit("responseStatus",!0)):t.$emit("responseStatus",!1)}),(()=>{t.$emit("responseStatus",!1)}))},clearLines(){const t=this;t.originalData=[],t.trendData=[],t.originalTrendData=[],t.diffData=[],t.updateChart(),t.updateTable(),t.setTrendStatus()},generateTendData(t){const e=this;e.trendData=[],e.params_in.forEach((n=>{let r=e.getSingelTrendData(n,t);e.trendData.push(r)}))},getSingelTrendData(t,e){const n=this;let r={name:"",param:{},data:[]};r.param=t,r.name=`${r.param.deviceName}-${r.param.dataName}-${r.param.dataTypeName}`;for(let a=0,i=e.length;a<i;a++)if(e[a].deviceId===t.deviceId&&e[a].logicalId===t.logicalId&&e[a].dataTypeId===t.dataTypeId&&e[a].dataId===t.dataId){r.data=s().map(e[a].dataList,(function(t){return[t.time,parseFloat(f.formatNumberWithPrecision(t.value,n.precision||2))]}));break}return r},updateChart(){let t=this,e=t.trendData,n=[],r=[],a=[];r=t.getYIndexs();for(let s=0,l=e.length;s<l;s++){let t=e[s].param,r=t.yIndex?t.yIndex:0,i={name:e[s].name,data:e[s].data,type:"line",showSymbol:!1,smooth:!0,sampling:"lttb",yAxisIndex:r};n.push(i),a.push(e[s].name)}let i=t.scatter_in,o=[];i&&s().isArray(i)&&(o=i.map((t=>[t.x,0,t]))),n.push({name:"trendScatter",data:o,type:"scatter",symbol:"pin",symbolSize:25});let u=t.CetChart_trend.config.options;if("small"===t.viewSize&&(u=s().merge({},t.CetChart_trend.config.options,t.optionsSize)),t.$refs.chart.mergeOptions(u,!0),r.length<1)t.$refs.chart.mergeOptions({series:n,legend:{data:a}});else{let e=parseInt(r.length/2)+r.length%2,i=r.length-e,o={left:50*(e-1)+20,right:20+50*i};t.$refs.chart.mergeOptions({series:n,yAxis:r,grid:o,legend:{data:a}})}},getYIndexs(){let t=this,e=t.trendData,n=[];return e.forEach((t=>{if(!s().isNil(t.param.unit)){let e=n.indexOf(t.param.unit);-1===e?(n.push(t.param.unit),t.param.yIndex=n.length-1):t.param.yIndex=e}})),n=n.map(((e,n)=>({splitLine:{show:t.splitLine},position:n%2===0?"left":"right",offset:60*parseInt(n/2),name:e,splitNumber:t.splitNumber,min(n){let r,a,i=s().find(t.extremValue,{name:e});if(i){let t=s().get(i,"max"),e=s().get(i,"min");r=s().max([t,n.max]),a=s().min([e,n.min])}else r=n.max,a=n.min;let o=t.getPow(r-a),u=2-o;return Math.floor((a-(r-a)/10)*Math.pow(10,u))/Math.pow(10,u)},max(n){let r,a,i=s().find(t.extremValue,{name:e});if(i){let t=s().get(i,"max"),e=s().get(i,"min");r=s().max([t,n.max]),a=s().min([e,n.min])}else r=n.max,a=n.min;let o=t.getPow(r-a),u=2-o;return Math.ceil((r+(r-a)/10)*Math.pow(10,u))/Math.pow(10,u)}}))),n},updateTable(){let t=this,e=t.trendData,n=[];for(let a=0,i=e.length;a<i;a++){let i={name:e[a].name,data:[],header:[{label:w("时间"),prop:"d0",width:"160"},{label:`${e[a].name} (${e[a].param.unit})`,prop:"d1"}]};for(var r=0;r<e[a].data.length;r++){let n=f.formatNumberWithPrecision(s().cloneDeep(e[a].data[r][1]),t.precision||2),o={d0:s().cloneDeep(p()(e[a].data[r][0]).format("YYYY-MM-DD HH:mm:ss")),d1:s().isNil(n)?"--":n};i.data.push(o)}n.push(i)}t.tableData=n},setTrendStatus(){let t=this;t.extremValueHandler(t.showMaxAndMinStatus),t.pointHandler(t.showPointStatus),t.rawMarkHandler(t.showRawMarkStatus),t.setMarkline()},chartFinish(){},clickHandler(t){"scatter"===t.seriesType&&"trendScatter"===t.seriesName&&this.$emit("scatterClick_out",t.data[2])},getPow(t){let e=2;for(let n=e;t<Math.pow(10,n);n--)e=n;return e},i18n:w},mounted:function(){if(this.withBackgroudColor){const t=window.getComputedStyle(this.$el).backgroundColor;this.CetChart_trend.config.options.backgroundColor=t}},activated(){}},S=x;n(6136);function T(t,e,n,r,a,i,o,s){var u,l="function"===typeof t?t.options:t;if(e&&(l.render=e,l.staticRenderFns=n,l._compiled=!0),r&&(l.functional=!0),i&&(l._scopeId="data-v-"+i),o?(u=function(t){t=t||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,t||"undefined"===typeof __VUE_SSR_CONTEXT__||(t=__VUE_SSR_CONTEXT__),a&&a.call(this,t),t&&t._registeredComponents&&t._registeredComponents.add(o)},l._ssrRegister=u):a&&(u=s?function(){a.call(this,(l.functional?this.parent:this).$root.$options.shadowRoot)}:a),u)if(l.functional){l._injectStyles=u;var c=l.render;l.render=function(t,e){return u.call(e),c(t,e)}}else{var f=l.beforeCreate;l.beforeCreate=f?[].concat(f,u):[u]}return{exports:t,options:l}}var _=T(S,a,i,!1,null,"95227668",null),D=_.exports;const M=[D],O=function(t){M.forEach((e=>{t.component(e.name,e),t.component("CetTrend",e)}))};"undefined"!==typeof window&&window.Vue&&O(window.Vue);var C=O,N=C}(),r}()}));
//# sourceMappingURL=omega-trend.umd.min.js.map