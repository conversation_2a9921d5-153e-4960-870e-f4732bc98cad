{"name": "@omega/trend", "version": "1.2.13", "private": false, "description": "趋势曲线组件", "main": "src/index.js", "module": "src/index.js", "publishConfig": {"main": "lib/omega-trend.umd.min.js"}, "author": "NS", "files": ["lib", "src", "CHANGELOG.md"], "scripts": {"build": "vue-cli-service build --target lib --name omega-trend --dest lib --formats umd-min --report src/index.js", "lint": "vue-cli-service lint", "release": "npm publish --registry http://*************:4873", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs"}, "peerDependencies": {"@omega/http": "*", "@omega/i18n": "*", "cet-chart": "^1.2.3", "core-js": "^3.36.0", "element-ui": "^2.11.1", "lodash": "^4.17.21", "moment": "^2.29.1", "vue": "^2.7.8"}, "devDependencies": {"vue": "^2.7.8", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "autoprefixer": "^9.8.8", "babel-eslint": "^10.1.0", "core-js": "^3.36.0", "extract-loader": "^5.1.0", "postcss": "^7.0.39", "sass": "^1.54.0", "sass-loader": "^13.0.2"}}