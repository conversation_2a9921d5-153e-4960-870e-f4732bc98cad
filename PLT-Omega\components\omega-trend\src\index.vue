<template>
  <div class="omega-trend bg-BG1">
    <el-container style="height: 100%">
      <el-header
        class="trend-class"
        :height="isNoHeaderButton ? '0px' : '30px'"
      >
        <div style="padding-bottom: 5px">
          <el-checkbox
            @change="limitHandler"
            v-show="!showTable"
            v-if="showLimitButton"
          >
            {{ limitText.buttonText }}
          </el-checkbox>
          <el-checkbox
            @change="extremValueHandler"
            v-show="!showTable"
            v-if="showExtremButton"
          >
            {{ i18n("最值") }}
          </el-checkbox>
          <el-checkbox @change="diffHandler" v-if="showDiffButton">
            {{ i18n("差值") }}
          </el-checkbox>
          <el-checkbox
            @change="averageHandler"
            v-show="!showTable"
            v-if="showAverageButton"
          >
            {{ i18n("平均值") }}
          </el-checkbox>
          <el-checkbox
            @change="pointHandler"
            v-show="!showTable"
            v-if="showPointButton"
          >
            {{ i18n("打点显示") }}
          </el-checkbox>
          <el-checkbox
            @change="rawMarkHandler"
            v-show="!showTable"
            v-if="showRawMarkButton"
          >
            {{ i18n("原始标记") }}
          </el-checkbox>
        </div>
        <div style="padding-left: 20px" v-if="showTableButton">
          {{ i18n("展示类型:") }}
          <el-select v-model="showTableSelect" style="width: 120px" size="mini">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </el-header>
      <el-main :style="mainStyle">
        <el-container style="height: 100%">
          <!-- <div class="trend-chart" v-show="!showTable"> -->
          <div
            :class="showTable ? 'hide-chart' : 'trend-chart'"
            class="trend-chart"
          >
            <CetChart
              ref="chart"
              :inputData_in="CetChart_trend.inputData_in"
              v-bind="CetChart_trend.config"
              @finished="chartFinish"
              @click="clickHandler"
            />
          </div>

          <div v-if="showTable" class="trend-table">
            <div
              v-for="(item, index) in tableData"
              :key="index"
              :style="tableDivStyle"
            >
              <el-table
                ref="cetTable"
                :data="item.data"
                tooltip-effect="light"
                border
                height="true"
                style="height: 100%; width: 100%"
              >
                <template v-for="(it, i) in item.header">
                  <el-table-column
                    :key="i"
                    :label="it.label"
                    :prop="it.prop"
                    header-align="center"
                    :width="it.width"
                    :show-overflow-tooltip="true"
                  ></el-table-column>
                </template>
              </el-table>
            </div>
          </div>
        </el-container>
      </el-main>
    </el-container>
  </div>
</template>
<script>
import Common from "./common.js";
import _ from "lodash";
import moment from "moment";
import { httping } from "@omega/http";
import CetChart from "cet-chart";
import { i18n } from "./local/index.js";
export default {
  components: { CetChart },
  name: "OmegaTrend",
  props: {
    //趋势曲线入参，每一条曲线包含一个入参对象，包含时间、nodeId，paramId
    params_in: {
      type: [Array, Object]
    },
    queryTime_in: {
      type: Object
    },
    //整数值, 0或者1不进行抽点, 2或者大于2的值按设置值进行抽点
    interval_in: {
      type: Number,
      default: 0
    },
    title_in: {
      type: String
    },
    scatter_in: {
      type: [Number, Array]
    },
    //查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    queryTrigger_in: {
      type: [Number, Array, Object, String]
    },
    clearTrigger_in: {
      type: Number
    },
    dataConfig: {
      type: Object
    },
    showLegend: {
      type: Boolean,
      default: true
    },
    showExtremButton: {
      type: Boolean,
      default: true
    },
    showDiffButton: {
      type: Boolean,
      default: true
    },
    showLimitButton: {
      type: Boolean,
      default: false
    },
    showAverageButton: {
      type: Boolean,
      default: true
    },
    showPointButton: {
      type: Boolean,
      default: true
    },
    showRawMarkButton: {
      type: Boolean,
      default: false
    },
    showTableButton: {
      type: Boolean,
      default: true
    },
    precision: {
      type: Number
    },
    splitNumber: {
      type: Number,
      default: 5
    },
    color: {
      type: Array
    },
    limitText: {
      type: Object,
      default: () => {
        return {
          buttonText: i18n("限值"),
          upperLimitText: i18n("上限"),
          lowerLimitText: i18n("下限")
        };
      }
    },
    exportImgName_in: {
      type: String
    },
    withBackgroudColor: {
      type: Boolean,
      default: true
    },
    viewSize: {
      type: String,
      default: ""
    },
    // 分割线配置，默认显示
    splitLine: {
      type: Boolean,
      default: true
    },
    scatter_tooltip: Function
  },
  data() {
    let vm = this;

    return {
      trendData: [],
      originalData: [],
      originalTrendData: [],
      diffData: [],
      showMaxAndMinStatus: false,
      showLimitStatus: false,
      showAverageStatus: false,
      showPointStatus: false,
      showRawMarkStatus: false,
      showDiffStatus: false,
      showTableSelect: false,
      CetChart_trend: {
        inputData_in: {},
        config: {
          options: {
            color: this.color || [],
            legend: {
              show: vm.showLegend,
              type: "scroll",
              top: 20,
              left: 100
            },
            tooltip: {
              trigger: "axis",
              appendToBody: true,
              // axisPointer: {
              //   label: {
              //     formatter: function (params) {
              //       return vm.$moment(params.value).format("MM-DD HH:mm");
              //     }
              //   }
              // }
              formatter: function (params) {
                let trendData = vm.trendData;

                var showHtm =
                  moment(params[0].axisValue).format("MM-DD HH:mm") + "<br>";
                for (var i = 0; i < params.length; i++) {
                  //名称
                  var name = params[i]["seriesName"];
                  if (name === "trendScatter") {
                    if (vm.scatter_tooltip) {
                      showHtm = vm.scatter_tooltip(params[0].data[2], params);
                    }
                    continue;
                  }
                  let series = _.find(trendData, { name: name });
                  let unit = "";
                  if (series && !_.isEmpty(_.get(series.param, "unit"))) {
                    unit = series.param.unit;
                  }

                  //值
                  var value = params[i]["value"][1];
                  value =
                    _.isNumber(value) && !_.isNaN(value)
                      ? value.toFixed(vm.precision || 2)
                      : "--";
                  showHtm += name + " ：" + value + unit + "<br>";
                }
                return showHtm;
              }
            },
            title: {
              left: "left",
              text: this.title_in
            },
            grid: {
              left: 8,
              right: 8,
              top: 100,
              containLabel: true
            },
            toolbox: {
              top: 40,
              right: 30,
              feature: {
                dataZoom: {
                  yAxisIndex: "none"
                },
                // restore: {},
                saveAsImage: {
                  name: this.exportImgName_in
                }
              }
            },
            xAxis: {
              type: "time",
              axisLine: {
                onZero: false
              }
            },
            yAxis: {
              boundaryGap: [0, "10%"],
              splitLine: {
                show: true
              }
            },
            dataZoom: [
              {
                type: "inside",
                start: 0,
                end: 100,
                minValueSpan: 60 * 60 * 1000
              },
              {
                start: 0,
                end: 100,
                handleIcon:
                  "M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z",
                handleSize: "80%",
                handleStyle: {
                  color: "#fff",
                  shadowBlur: 3,
                  shadowColor: "rgba(0, 0, 0, 0.6)",
                  shadowOffsetX: 2,
                  shadowOffsetY: 2
                },
                left: 150,
                right: 150
              }
            ],
            series: []
          },
          manualUpdate: true
        }
      },
      showTable: false,
      renderColumns: [],
      tableData: [],
      options: [
        {
          value: false,
          label: i18n("曲线")
        },
        {
          value: true,
          label: i18n("表格")
        }
      ],
      distributionChart: "",
      tableDivStyle: {
        width: "100%"
      },
      //保存各个单位的最值, 在自适应计算Y轴max和min值时, 要考虑最值点的位置; 避免最值点在Y轴范围外,格式:{max:100, min:2, name:'V'}
      extremValue: [],
      optionsSize: {
        legend: {
          top: 5
        },
        grid: {
          top: 70,
          bottom: 50
        },
        toolbox: {
          top: 20
        },
        dataZoom: [
          {},
          {
            bottom: 10,
            height: 20
          }
        ]
      }
    };
  },
  watch: {
    params_in: {
      handler: function () {
        this.paramsChange();
      }
    },
    queryTime_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    title_in: {
      immediate: true,
      handler: function (val) {
        if (this.$refs.chart) {
          this.$refs.chart.mergeOptions({ title: { text: val } });
        }
      }
    },
    exportImgName_in: {
      immediate: true,
      handler: function (val) {
        if (this.$refs.chart) {
          this.$refs.chart.mergeOptions({
            toolbox: { feature: { saveAsImage: { name: val } } }
          });
        }
      }
    },
    trendData: {
      deep: true,
      handler: function (val) {
        let vm = this;
        let dataLength = vm.trendData.length;
        if (!dataLength || dataLength === 1) {
          vm.tableDivStyle.width = "100%";
        } else {
          vm.tableDivStyle.width = `${100 / dataLength}%`;
        }
      }
    },
    queryTrigger_in() {
      this.getChartData();
    },
    clearTrigger_in() {
      this.clearLines();
    },
    showTableSelect(val) {
      let vm = this;
      vm.showTable = val;

      if (val === false) {
        setTimeout(() => {
          vm.updateChart();
          vm.setTrendStatus();
        }, 0);
      }
    },
    scatter_in(val) {
      const vm = this;
      if (val && _.isArray(val)) {
        let data = val.map(item => [item.x, 0, item]);
        let series = [
          {
            name: "trendScatter",
            data: data,
            type: "scatter"
          }
        ];
        vm.$refs.chart.mergeOptions({ series: series });
      }
    }
  },
  updated() {},
  computed: {
    isNoHeaderButton() {
      return (
        !this.showExtremButton &&
        !this.showPointButton &&
        !this.showAverageButton &&
        !this.showTableButton &&
        !this.showLimitButton &&
        !this.showRawMarkButton &&
        !this.showDiffButton
      );
    },
    mainStyle() {
      return this.isNoHeaderButton
        ? {
            padding: "0px 20px",
            overflowX: "auto",
            whiteSpace: "nowrap",
            height: "100%"
          }
        : {
            padding: "0px 20px",
            overflowX: "auto",
            whiteSpace: "nowrap",
            height: "calc(100% - 30px)"
          };
    }
  },
  methods: {
    //参数变化查询曲线数据
    paramsChange() {
      if (this.queryMode === "diff") {
        this.getChartData();
      }
    },
    //组织查询时间入参
    getQueryTime() {
      let vm = this,
        queryTime = {};
      if (_.isEmpty(vm.queryTime_in) || !vm.queryTime_in) {
        return null;
      }

      queryTime = {
        timeType: vm.queryTime_in.timeType ? vm.queryTime_in.timeType : 1,
        startTime: null,
        endTime: null
      };

      if (_.isArray(vm.queryTime_in.time)) {
        // if (_.isDate(vm.queryTime_in.time[0])) {
        queryTime.startTime = moment(vm.queryTime_in.time[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        // }
        // if (_.isDate(vm.queryTime_in.time[1])) {
        queryTime.endTime = moment(vm.queryTime_in.time[1]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        // }
      }
      // else {
      //   if (_.isDate(vm.queryTime_in.time)) {
      //     queryTime.startTime = vm.queryTime_in.time.getTime();
      //   }
      // }
      return queryTime;
    },
    //组织参数入参
    getParams() {
      let vm = this;
      return _.map(vm.params_in, function (n) {
        return {
          dataId: n.dataId,
          dataTypeId: n.dataTypeId,
          deviceId: n.deviceId,
          logicalId: n.logicalId
        };
      });
    },

    //打点显示
    pointHandler(val) {
      let vm = this;
      vm.showPointStatus = val ? true : false;

      vm.setPoint(vm.showPointStatus);
    },

    setPoint(status) {
      let vm = this,
        trendData = vm.trendData,
        options = {
          series: []
        };

      for (let i = 0, len = trendData.length; i < len; i++) {
        let single = {
          showSymbol: status,
          name: trendData[i].name
        };
        options.series.push(single);
      }
      vm.$refs.chart.mergeOptions(options);
    },
    //原始标记
    rawMarkHandler(val) {
      let vm = this;
      vm.showRawMarkStatus = val ? true : false;
      vm.setRawMark(vm.showRawMarkStatus);
    },
    //切换原始标记展示状态
    setRawMark(status) {
      let vm = this,
        trendData = vm.trendData,
        options = {
          series: []
        };

      for (let i = 0, len = trendData.length; i < len; i++) {
        if (status) {
          let data = vm.getSingleRawMark(trendData[i]);
          let single = {
            markArea: {
              silent: true,
              data: data
            },
            name: trendData[i].name
          };
          options.series.push(single);
        } else {
          let single = {
            markArea: {
              silent: true,
              itemStyle: {
                color: "rgb(230, 230, 230)",
                opacity: 0.5
              },
              data: []
            },
            name: trendData[i].name
          };
          options.series.push(single);
        }
      }
      vm.$refs.chart.mergeOptions(options);
    },
    //计算单条曲线的原始标记数据
    getSingleRawMark(trendData) {
      const vm = this;
      let data = [],
        markAreaData = [],
        startIndex = 0,
        endIndex = 0;
      data = vm.getOriginalData(trendData);
      /*   data[300].status = 3;
      data[301].status = 3;
      data[302].status = 3;
      data[303].status = 3;
      data[304].status = 3;
      data[305].status = 3;
      data[306].status = 3;
      data[307].status = 3;

      data[400].status = 3;
      data[401].status = 3;
      data[402].status = 3;
      data[403].status = 3;
      data[404].status = 3;
      data[405].status = 3;
      data[406].status = 3;
      data[407].status = 3;

      data[452].status = 3;
      data[453].status = 3;
      data[454].status = 3;
      data[455].status = 3;
      data[456].status = 3;
      data[457].status = 3; */

      //找到原始标记的status为3的序列
      while (startIndex !== -1 && endIndex !== data.length - 1) {
        startIndex = _.findIndex(data, { status: 3 }, endIndex);
        if (startIndex !== -1) {
          endIndex = _.findIndex(data, { status: 0 }, startIndex);
          if (endIndex === -1) {
            endIndex = data.length - 1;
          }
          markAreaData.push([]);
          markAreaData[markAreaData.length - 1].push({
            xAxis: data[startIndex].time
          });
          markAreaData[markAreaData.length - 1].push({
            xAxis: data[endIndex].time
          });
        }
      }
      return markAreaData;
    },
    //获取接口返回的原始数据, 原始数据中才包含status,用于判断是否为原始标记的数据点
    getOriginalData(trendData) {
      const vm = this;
      let data = vm.originalData,
        param = trendData.param;

      for (let i = 0, len = data.length; i < len; i++) {
        if (
          data[i].deviceId === param.deviceId &&
          data[i].logicalId === param.logicalId &&
          data[i].dataTypeId === param.dataTypeId &&
          data[i].dataId === param.dataId
        ) {
          return data[i].dataList;
        }
      }
    },
    //限值显示隐藏的处理
    limitHandler(val) {
      let vm = this;
      vm.showLimitStatus = val ? true : false;
      vm.setMarkline();
    },

    //处理平均值展示和隐藏逻辑
    averageHandler(val) {
      let vm = this;
      vm.showAverageStatus = val ? true : false;
      vm.setMarkline();
    },

    //差值处理逻辑
    diffHandler(val) {
      let vm = this;
      vm.showDiffStatus = val ? true : false;
      //差值和原始值是不同数据, 清空最值保存值, 重新计算
      vm.extremValue = [];
      if (val) {
        vm.showDiff();
      } else {
        vm.hideDiff();
      }
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },
    //显示差值
    showDiff() {
      const vm = this;
      vm.trendData = vm.diffData;
    },
    //隐藏差值
    hideDiff() {
      const vm = this;
      vm.trendData = vm.originalTrendData;
    },
    //计算趋势曲线差值数据
    calcDiffData() {
      const vm = this;
      let trendData = vm.trendData;
      let diffData = trendData.map(trendItem => {
        let diffItem = _.cloneDeep(trendItem);
        diffItem.data = trendItem.data.map((item, index, array) => {
          let orginalValue = item[1];
          let value;
          if (index === array.length - 1) {
            value = 0;
          } else {
            let nextValue = array[index + 1][1];
            if (_.isNil(orginalValue) || !_.isNumber(orginalValue)) {
              value = NaN;
            } else if (_.isNil(nextValue) || !_.isNumber(nextValue)) {
              value = NaN;
            } else {
              value = parseFloat(
                Common.formatNumberWithPrecision(
                  nextValue - orginalValue,
                  vm.precision || 2
                )
              );
            }
          }

          return [item[0], value];
        });
        return diffItem;
      });
      vm.originalTrendData = trendData;
      vm.diffData = diffData;
    },
    /**
     * @description: 初始化差值状态
     * @param {*}
     * @return {*}
     */
    initDiffData() {
      const vm = this;
      vm.calcDiffData();
      if (vm.showDiffStatus) {
        vm.trendData = vm.diffData;
      }

      //重新获取数据后, 清空最值保存值, 重新计算
      vm.extremValue = [];
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },

    //最值展示和隐藏逻辑
    extremValueHandler(val) {
      let vm = this;
      if (val) {
        vm.showMaxAndMinStatus = true;
        vm.showMaxAndMin();
      } else {
        vm.showMaxAndMinStatus = false;
        vm.removeMaxAndMin();
      }
    },
    showMaxAndMin() {
      let vm = this,
        trendData = vm.trendData,
        options = {
          series: []
        };

      for (let i = 0, len = trendData.length; i < len; i++) {
        let single = vm.calcMaxAndMin(trendData[i]);
        if (!_.isNil(single)) {
          options.series.push(single);
        }
      }

      vm.$refs.chart.mergeOptions(options);
    },
    removeMaxAndMin() {
      let vm = this,
        trendData = vm.trendData,
        options = {
          series: []
        };

      for (let i = 0, len = trendData.length; i < len; i++) {
        let single = {
          markPoint: { data: [] },
          name: trendData[i].name
        };
        options.series.push(single);
      }

      vm.$refs.chart.mergeOptions(options);
    },
    //计算单条曲线的最大最小值，返回echarts配置项
    calcMaxAndMin(singleTrend) {
      let vm = this,
        data = singleTrend.data,
        maxValue,
        minValue,
        single;
      if (data.length < 1) {
        return null;
      }
      maxValue = _.maxBy(data, function (a) {
        return a[1];
      });
      minValue = _.minBy(data, function (a) {
        return a[1];
      });

      if (_.isNil(maxValue) || _.isNil(minValue)) {
        return null;
      }
      vm.saveExtremValue(singleTrend.param.unit, maxValue[1], minValue[1]);

      single = {
        markPoint: {
          data: [
            {
              value: maxValue[1],
              name: i18n("最大值"),
              coord: maxValue,
              label: { position: "top" }
            },
            {
              value: minValue[1],
              name: i18n("最小值"),
              coord: minValue,
              label: { position: "bottom" }
            }
          ],
          label: {
            formatter: "{@value}({b})",
            fontWeight: 800
          }
        },
        name: singleTrend.name
      };

      return single;
    },
    saveExtremValue(name, max, min) {
      const vm = this;
      let extremValue = vm.extremValue;
      let singleExtrem = _.find(extremValue, { name: name });
      if (singleExtrem) {
        singleExtrem.max = max > singleExtrem.max ? max : singleExtrem.max;
        singleExtrem.min = min < singleExtrem.min ? min : singleExtrem.min;
      } else {
        extremValue.push({
          name,
          max,
          min
        });
      }
    },

    //设置平均线, 上下限值线等Markline的显示
    setMarkline() {
      let vm = this,
        trendData = vm.trendData,
        options = {
          series: []
        };

      //遍历每一条曲线进行处理
      for (let i = 0, len = trendData.length; i < len; i++) {
        let single = vm.setSingleMarkline(trendData[i]);
        options.series.push(single);
      }

      vm.$refs.chart.mergeOptions(options);
    },
    //设置每一条曲线的markline
    setSingleMarkline(singleData) {
      let vm = this,
        single;
      //如果不显示限值, 不显示平均线, 则清空markline显示
      if (!vm.showAverageStatus && !vm.showLimitStatus) {
        return {
          markLine: { data: [] },
          name: singleData.name
        };
      }

      single = {
        markLine: {
          data: [],
          symbol: "none",
          label: {
            formatter: "{@value}({b})",
            position: "insideStartTop"
          }
        },
        name: singleData.name
      };

      //如果显示平均线, 且数据长度大于0则计算平均值, 并增加平均值markline
      if (vm.showAverageStatus && singleData.data.length > 0) {
        let data = singleData.data,
          averageValue;
        let dataWithoutInvaid = data.filter(item => {
          return _.isNumber(item[1]) && !_.isNaN(item[1]);
        });
        averageValue = _.meanBy(dataWithoutInvaid, function (a) {
          return a[1];
        });
        single.markLine.data.push({
          yAxis: averageValue,
          name: i18n("平均值")
        });
      }

      //如果显示限值, 则增加限值的markline
      if (vm.showLimitStatus) {
        let param = singleData.param;
        if (param.upperLimit) {
          single.markLine.data.push({
            yAxis: param.upperLimit,
            name: vm.limitText.upperLimitText
          });
        }

        if (param.lowerLimit) {
          single.markLine.data.push({
            yAxis: param.lowerLimit,
            name: vm.limitText.lowerLimitText
          });
        }
       vm.saveExtremValue(param.unit,param.upperLimit,param.lowerLimit)
      }
      return single;
    },

    //查询曲线数据并处理
    getChartData() {
      const vm = this;

      let queryBody = {
        endTime: vm.getQueryTime().endTime,
        interval: vm.interval_in,
        meterConfigs: vm.getParams(),
        startTime: vm.getQueryTime().startTime
      };

      if (queryBody.meterConfigs.length < 1) {
        vm.clearLines();
        return;
      }

      let queryOption = {
        url: vm.dataConfig.queryUrl,
        method: "POST",
        data: queryBody
      };

      httping(queryOption).then(
        function (response) {
          if (response.code === 0) {
            if (_.isArray(response.data)) {
              vm.originalData = response.data;
              vm.generateTendData(response.data);
              vm.initDiffData();
            } else {
              vm.clearLines();
            }
            vm.$emit("responseStatus", true);
          } else {
            vm.$emit("responseStatus", false);
          }
        },
        () => {
          vm.$emit("responseStatus", false);
        }
      );
    },
    /**清空数据 */
    clearLines() {
      const vm = this;
      vm.originalData = [];
      vm.trendData = [];
      vm.originalTrendData = [];
      vm.diffData = [];
      vm.updateChart();
      vm.updateTable();
      vm.setTrendStatus();
    },
    //生成趋势曲线数据
    generateTendData(data) {
      const vm = this;
      vm.trendData = [];

      vm.params_in.forEach(item => {
        let single = vm.getSingelTrendData(item, data);
        vm.trendData.push(single);
      });
    },
    //获取单条曲线的数据
    getSingelTrendData(param, data) {
      const vm = this;
      let single = {
        name: "",
        param: {},
        data: []
      };
      single.param = param;
      single.name = `${single.param.deviceName}-${single.param.dataName}-${single.param.dataTypeName}`;

      for (let i = 0, len = data.length; i < len; i++) {
        if (
          data[i].deviceId === param.deviceId &&
          data[i].logicalId === param.logicalId &&
          data[i].dataTypeId === param.dataTypeId &&
          data[i].dataId === param.dataId
        ) {
          single.data = _.map(data[i].dataList, function (n) {
            return [
              n.time,
              parseFloat(
                Common.formatNumberWithPrecision(n.value, vm.precision || 2)
              )
            ];
          });
          break;
        }
      }

      return single;
    },

    //将trendData的数据展示到曲线
    updateChart() {
      let vm = this,
        trendData = vm.trendData,
        series = [],
        yIndexs = [],
        legendData = [];
      //生成Y轴序列
      yIndexs = vm.getYIndexs();

      for (let i = 0, len = trendData.length; i < len; i++) {
        let param = trendData[i].param;
        let yAxisIndex = param.yIndex ? param.yIndex : 0;

        let single = {
          name: trendData[i].name,
          data: trendData[i].data,
          type: "line",
          showSymbol: false,
          smooth: true,
          sampling: "lttb",
          yAxisIndex: yAxisIndex
        };

        series.push(single);
        legendData.push(trendData[i].name);
      }

      let scatter = vm.scatter_in,
        scatterData = [];
      if (scatter && _.isArray(scatter)) {
        scatterData = scatter.map(item => [item.x, 0, item]);
      }

      series.push({
        name: "trendScatter",
        data: scatterData,
        type: "scatter",
        symbol: "pin",
        symbolSize: 25
      });
      // 判断是否最小视图展示

      let option = vm.CetChart_trend.config.options;
      if (vm.viewSize === "small") {
        option = _.merge({}, vm.CetChart_trend.config.options, vm.optionsSize);
      }

      vm.$refs.chart.mergeOptions(option, true);

      if (yIndexs.length < 1) {
        vm.$refs.chart.mergeOptions({
          series: series,
          legend: { data: legendData }
        });
      } else {
        //根据坐标轴的数量设置grid的左右间距值
        let leftIndexNum = parseInt(yIndexs.length / 2) + (yIndexs.length % 2);
        let rightIndexNum = yIndexs.length - leftIndexNum;
        let grid = {
          left: 50 * (leftIndexNum - 1) + 20,
          right: 20 + 50 * rightIndexNum
        };
        vm.$refs.chart.mergeOptions({
          series: series,
          yAxis: yIndexs,
          grid: grid,
          legend: { data: legendData }
        });
      }
    },
    getYIndexs() {
      let vm = this,
        trendData = vm.trendData,
        yIndexs = [];
      trendData.forEach(item => {
        if (!_.isNil(item.param.unit)) {
          let index = yIndexs.indexOf(item.param.unit);
          if (index === -1) {
            yIndexs.push(item.param.unit);
            item.param.yIndex = yIndexs.length - 1;
          } else {
            item.param.yIndex = index;
          }
        }
      });
      yIndexs = yIndexs.map((item, index) => ({
        splitLine: {
          show: vm.splitLine
        },
        position: index % 2 === 0 ? "left" : "right",
        offset: 60 * parseInt(index / 2),
        name: item,
        splitNumber: vm.splitNumber,
        min(value) {
          let extrem = _.find(vm.extremValue, { name: item });
          let max;
          let min;
          if (extrem) {
            let dataMax = _.get(extrem, "max");
            let dataMin = _.get(extrem, "min");
            max = _.max([dataMax, value.max]);
            min = _.min([dataMin, value.min]);
          } else {
            max = value.max;
            min = value.min;
          }
          // 兼容max和min相等的情况
           if(max===min) return max
           
          //如果曲线最大最小值之差小于10, 允许小数点1位的上下限值, 如果小于1允许小数点2位的上下限值, 以此类推
          let logPow = vm.getPow(max - min);
          let rPow = 2 - logPow;

          return (
            Math.floor((min - (max - min) / 10) * Math.pow(10, rPow)) /
            Math.pow(10, rPow)
          );
        },
        max(value) {
          let extrem = _.find(vm.extremValue, { name: item });
          let max;
          let min;
          if (extrem) {
            let dataMax = _.get(extrem, "max");
            let dataMin = _.get(extrem, "min");
            max = _.max([dataMax, value.max]);
            min = _.min([dataMin, value.min]);
          } else {
            max = value.max;
            min = value.min;
          }
           if(max===min) return max

          let logPow = vm.getPow(max - min);
          let rPow = 2 - logPow;

          return (
            Math.ceil((max + (max - min) / 10) * Math.pow(10, rPow)) /
            Math.pow(10, rPow)
          );
        }
      }));
      return yIndexs;
    },
    //将数据更新到表格中
    updateTable() {
      let vm = this,
        trendData = vm.trendData,
        tableList = [];

      for (let i = 0, len = trendData.length; i < len; i++) {
        let unit= trendData[i].param.unit
        let obj = {
          name: trendData[i].name,
          data: [],
          header: [
            { label: i18n("时间"), prop: "d0", width: "160" },
            {
              label: `${trendData[i].name}${_.isEmpty(unit) ?'':` (${unit})`}`,
              prop: "d1"
            }
          ]
        };
        for (var j = 0; j < trendData[i].data.length; j++) {
          let value = Common.formatNumberWithPrecision(
            _.cloneDeep(trendData[i].data[j][1]),
            vm.precision || 2
          );
          let res = {
            d0: _.cloneDeep(
              moment(trendData[i].data[j][0]).format("YYYY-MM-DD HH:mm:ss")
            ),
            d1: _.isNil(value) ? "--" : value
          };
          obj.data.push(res);
        }
        tableList.push(obj);
      }
      vm.tableData = tableList;
    },
    //根据最值、平均值等勾选状态进行展示
    setTrendStatus() {
      let vm = this;
      vm.extremValueHandler(vm.showMaxAndMinStatus);
      vm.pointHandler(vm.showPointStatus);
      vm.rawMarkHandler(vm.showRawMarkStatus);
      vm.setMarkline();
    },
    chartFinish() {},
    clickHandler(params) {
      if (
        params.seriesType === "scatter" &&
        params.seriesName === "trendScatter"
      ) {
        this.$emit("scatterClick_out", params.data[2]);
      }
    },
    //获取所给的值小于10个哪个对数值, 比如0.5小于1, 返回0, 0.05小于0.1, 返回-1. 大于10的, 都返回2
    getPow(gapValue) {
      let logPow = 2;

      for (let index = logPow; gapValue < Math.pow(10, index); index--) {
        logPow = index;
      }
      return logPow;
    },
    i18n
  },
  mounted: function () {
    // 适配导出图片背景色
    if (this.withBackgroudColor) {
      const backgroundColor = window.getComputedStyle(this.$el).backgroundColor;
      this.CetChart_trend.config.options.backgroundColor = backgroundColor;
    }
  },
  activated() {}
};
</script>
<style lang="scss" scoped>
.omega-trend {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 6px;
  padding: 8px;
}
.trend-class {
  text-align: right;
  > div,
  > label {
    display: inline-block;
  }
  .el-select {
    display: inline-block;
  }
}
.trend-table {
  display: inline-block;
  height: 100%;
  width: 100%;
  p {
    text-align: center;
  }
  > div {
    display: inline-block;
    height: 100%;
    padding-right: 12px;
    min-width: 312px;
    box-sizing: border-box;
  }
}
.trend-chart {
  width: 100%;
  height: 100%;
}

.hide-chart {
  position: absolute;
  left: -10000px;
  top: -10000px;
  width: calc(100% - 80px);
  height: 100%;
}
</style>
