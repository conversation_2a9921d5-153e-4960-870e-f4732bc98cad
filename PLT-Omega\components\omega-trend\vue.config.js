/*
 * @Author: your name
 * @Date: 2022-04-07 17:13:24
 * @LastEditTime: 2022-04-14 15:19:49
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\components\omega-trend\vue.config.js
 */
const config = {
  css: {
    extract: false
  },
  configureWebpack: config => {
    return {
      externals: {
        "@omega/http": "commonjs2 @omega/http",
        "@omega/i18n": "commonjs2 @omega/i18n",
        "element-ui": "commonjs2 element-ui",
        "cet-chart": "commonjs2 cet-chart",
        lodash: "commonjs2 lodash",
        moment: "commonjs2 moment",
        "core-js": "commonjs2 core-js"
      }
    };
  }
};

module.exports = config;
