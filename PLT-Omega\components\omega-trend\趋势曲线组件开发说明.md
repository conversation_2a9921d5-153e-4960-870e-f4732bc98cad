<!--
 * @Author: your name
 * @Date: 2022-03-07 17:05:11
 * @LastEditTime: 2022-04-14 15:21:00
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-trend\README.md
-->

### 目录结构

src 为组件代码目录
lib 为打包后生成物的目录

### 组件开发和修改

组件代码在 src 中修改
在根目录运行 tpl 起来, 看趋势曲线功能进行调试即可

### 组件打包

执行命令 pnpm --filter @omega/trend build

### 组件发布

登录 npm 私库账号
版本号调整, 增加版本数字
npm run release

### 组件依赖调整

编辑 package.json 中的 peerDependencies
vue.config.js 的 externals 也要同步调整, 避免把外部依赖包打包进组件 npm 包中
