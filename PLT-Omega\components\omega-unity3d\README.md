# unity3d组件

## 使用

  - unity3d需要单独部署一个镜像，将部署后的地址(url)的路径传入组件即可

## 消息交互

  - 如果需要与3d进行消息交互，则unity3d必须按照规定的模板进行打包(模板放在template目录下)

  - 收到unity3d的消息后，组件会抛出onMessage方法

  - 使用sendMessage方法向unity3d发送消息

  - 消息格式 

  发送消息格式:
  ```js
    {
      messageId: 1721372871102, // 本次消息交互唯一标识Id, 当前时间戳
      messageType: "request", // 请求方式
      actionType: "select", // 动作类型  init: 初始化; enter: 进入; select: 选择; openDialog: 打开弹窗; cancel: 返回; filter: 筛选设备
      data: {} // 请求数据
    }
  ```

  接收消息格式:
  ```js
    {
      code: 0, // 请求成功标识, 0代表成功, 其余代表失败
      msg: "", // 请求成功或失败的提示信息
      messageId: 1721372871102, // 本次消息交互唯一标识Id, 接收到的消息中的messageId
      messageType: "response", // 请求方式
      actionType: "select", // 动作类型, 接收到的消息中actionType
      data: {} // 回应数据
    }
  ```

  - 举例 

  3d模型初始化完成后向web发送初始化成功的消息
   ```js
    {
      messageId: 1721372871102,
      messageType: "request",
      actionType: "init", 
      data: {}
    }
  ```

  web端收到初始化完成的消息后, 向3d回应token
  ```js
    {
      code: 0, 
      msg: "", 
      messageId: 1721372871102, 
      messageType: "response", 
      actionType: "init", 
      data: {
        token: "xxxxxxxxxxxxxxx"
      }
    }
  ```
