<template>
  <omega-player controls ref="play" style="height: 100%; width: 100%" :controlslist="controlslist"></omega-player>
</template>

<script>
export default {
  name: "videoPlayer",
  components: {},
  props: {
    videoInfo: {
      type: Object
    },
    controlslist: {
      type: Array,
      default: () => ["noptzcommand"]
    }
  },
  data() {
    return {};
  },
  watch: {
    videoInfo: {
      deep: true,
      immediate: true,
      handler(val, old) {
        this.$nextTick(() => {
          if (val.id) {
            this.$refs.play.loadSourceOrID(val.id);
            return;
          }
          if (val.url) {
            this.$refs.play.loadSourceOrID(val.url);
            return;
          }
        });
      }
    }
  },
  methods: {
    destroyVideo() {
      this.$refs.play.clear();
    }
  },
  deactivated() {
    this.destroyVideo();
  },
  //离开页面时，销毁播放器，防止一直请求视频流
  beforeDestroy() {
    this.destroyVideo();
  }
};
</script>

<style lang="scss" scoped></style>
