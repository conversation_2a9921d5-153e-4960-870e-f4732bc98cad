<template>
  <omega-dialog :title="videoInfo.name" width="40%" successMsg="">
    <div class="videobox">
      <video-player ref="videoPlayer" :videoInfo="videoInfo"></video-player>
    </div>
  </omega-dialog>
</template>

<script>
import videoPlayer from '../components/videoPlayer.vue';
export default {
  components: { videoPlayer },
  name: "videoDialog",
  props: {
    data: {
      type: Object
    }
  },

  data() {
    return {
      videoInfo: {
        id: null,
        url: "",
        name: ""
      }
    };
  },
  computed: {},
  methods: {},
  mounted() {
    this.videoInfo = {
      id: Number(this.data.videoID),
      url: this.data.videoUrl,
      name: this.data.videoName
    };
  }
};
</script>

<style lang="scss" scoped>
.videobox {
  height: 400px;
  padding: 16px;
}
</style>
