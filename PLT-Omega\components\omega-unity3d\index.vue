<template>
  <div class="page"></div>
</template>
<script>
import { addListener, removeListener } from "./resize-detector.js";
import { showOmegaDialog } from "@omega/widget";
import videoDialog from "./dialogs/videoDialog.vue";
export default {
  name: "OmegaUnity3D",
  components: { videoDialog },
  props: {
    url: {
      type: String
    },
    // 处理unity3d初始化完成后的方法, 不传则使用默认逻辑
    handleInit: {
      type: Function,
      default(message) {
        this.sendMessage({
          code: 0,
          msg: "操作成功",
          messageId: message.messageId,
          messageType: "response",
          actionType: message.actionType,
          data: {
            token: window.sessionStorage.getItem("omega_token")
          }
        });
      }
    },
    // 处理打开摄像头的方法
    handleVideoDialog: {
      type: Function,
      default(message) {
        showOmegaDialog(videoDialog, {
          data: message.data
        });
        this.sendMessage({
          code: 0,
          msg: "操作成功",
          messageId: message.messageId,
          messageType: "response",
          actionType: message.actionType,
          data: {}
        });
      }
    }
  },

  methods: {
    /**
     * @description: 接受unity3d消息
     */
    onMessage(evt) {
      if (evt.source !== this.iframeDOM.contentWindow) return;
      const data = evt.data;
      if (data.type !== "data") return;
      const message = data.data;

      // 如果是回应请求, 则不做后续处理, 只提示异常信息
      if (message.messageType === "response") {
        if (message.code !== 0 && message.msg)
          this.$message.warning(message.msg);
        return;
      }

      // 以下是处理unity3d向web发出的请求

      // 处理初始化请求, unity3d初始化完成后向unity3d回应token
      if (message.actionType === "init") {
        this.handleInit(message);
        return;
      }

      // 处理打开摄像头操作
      if (message.actionType === "openDialog" && message.data?.type === "camera") {
        this.handleVideoDialog(message);
        return;
      }

      this.$emit("onMessage", data.data, this.url);
    },

    /**
     * @description: 向unity3d发送消息
     */
    sendMessage(data, type = "data", url = this.url) {
      this.iframeDOM.contentWindow.postMessage(
        {
          data,
          type
        },
        url
      );
    },

    /**
     * @description: 监听画面变化
     */
    onSizeChange() {
      const dom = window.omega_admin_unity3d_cache[this.url];
      dom.style.visibility = "hidden";
      this._onSizeChange();
    },

    /**
     * @description: 监听画面变化
     */
    _onSizeChange: _.throttle(function () {
      const dom = window.omega_admin_unity3d_cache[this.url];
      const rect = this.$el.getClientRects()[0];
      dom.style.top = rect.top + "px";
      dom.style.left = rect.left + "px";
      dom.style.width = rect.width + "px";
      dom.style.height = rect.height + "px";
      dom.style.visibility = "visible";
    }, 300),

    /**
     * @description: 进入页面后, 绘制unity3d画面
     */
    visableIframe() {
      if (!window.omega_admin_unity3d_cache) {
        window.omega_admin_unity3d_cache = {};
      }

      let iframeDOM = window.omega_admin_unity3d_cache[this.url];
      if (!iframeDOM) {
        iframeDOM = document.createElement("iframe");
        iframeDOM.src = this.url;
        iframeDOM.style.position = "fixed";
        window.document.body.appendChild(iframeDOM);
        iframeDOM.onload = () => {
          this.sendMessage(null, "connect");
        };
      }
      this.iframeDOM = window.omega_admin_unity3d_cache[this.url] = iframeDOM;

      iframeDOM.style.visibility = "visible";
      iframeDOM.style.zIndex = 1;
    },

    /**
     * @description: 离开当前页面时, 将unity3d画面添加进缓存中
     */
    unvisableIframe() {
      let iframeDOM = window.omega_admin_unity3d_cache[this.url];
      iframeDOM.style.visibility = "hidden";
      iframeDOM.style.zIndex = -1;
    }
  },
  mounted() {
    addListener(this.$el, this.onSizeChange);
    this.visableIframe();
    this.onSizeChange();

    window.addEventListener("message", this.onMessage);
  },
  beforeDestroy() {
    removeListener(this.$el, this.onSizeChange);
    this.unvisableIframe();
    window.removeEventListener("message", this.onMessage);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
</style>
