unity3d 和 web 交互原理

unity 3d 暴露 `WebHandler` 中的 `OnMessage` 方法，供 web 侧调用传递信息给 unity。

web 在全局暴露 unityExternalCSemitter 对象的 emit 方法，通过在 unity中调用该全局对象的 message 方法来向 web 传递信息


web -> unity 

```js
 send(payload) {
    this._unityInstance.SendMessage(
      "WebHandler",
      "OnMessage",
      JSON.stringify(payload)
    );
  },
```

unity -> web 
```js
window.unityExternalCSemitter.emit("message", payload)
```