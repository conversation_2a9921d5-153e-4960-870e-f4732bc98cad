{
  "loaderUrl": "./Build/{{{ LOADER_FILENAME }}}",
  "unityConfig": {
    "dataUrl": "./Build/{{{ DATA_FILENAME }}}",
    "frameworkUrl": "./Build/{{{ FRAMEWORK_FILENAME }}}",
    "codeUrl": "./Build/{{{ CODE_FILENAME }}}",
#if MEMORY_FILENAME
    "memoryUrl": "./Build/{{{ MEMORY_FILENAME }}}",
#endif
#if SYMBOLS_FILENAME
    "symbolsUrl": "./Build/{{{ SYMBOLS_FILENAME }}}",
#endif
    "streamingAssetsUrl": "./StreamingAssets",
    "companyName": "{{{ COMPANY_NAME }}}",
    "productName": "{{{ PRODUCT_NAME }}}",
    "productVersion": "{{{ PRODUCT_VERSION }}}"
  }
}