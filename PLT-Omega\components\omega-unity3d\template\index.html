<!DOCTYPE html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>CET三维组态平台</title>
    <link rel="shortcut icon" href="TemplateData/img/favicon.ico">
    <link rel="stylesheet" href="TemplateData/css/style.css">

  </head>
  <body>
		<div class="webgl-content">
      <canvas id="unity-canvas"></canvas>
    </div>

    <div id="loadingBlock">
			<img class="logo" src="TemplateData/img/Logo.png"></img>
			<div id="CETTitle">
				<div>数字孪生智慧安全园区管控平台</div>
			</div>
			<div style="position: absolute; bottom: 10px; right: 10px;color:white">首次加载较慢,支持的浏览器：Chrome、Edge、Firefox
			</div>
			<div id="progressBar">
				<div class="centered">
					<div id="emptyBar"></div>
					<div id="fullBar"></div>
				</div>
			</div>

			<div id="warningBrowserBlock" style="display:none;">
				<div class="warningBrowserText">
					Your browser may not be compatible with this website. For an optimal experience, we suggest you to
					download one of this popular web browsers.
				</div>
				<div class="browserIcons">
					<a href="https://www.mozilla.org/firefox" target="_blank"><img
							src="TemplateData/img/browser-firefox.png" alt="Firefox browser"></a>
					<a href="https://www.google.com/chrome" target="_blank"><img
							src="TemplateData/img/browser-chrome.png" alt="Chrome browser"></a>
					<a href="https://www.apple.com/safari/" target="_blank"><img
							src="TemplateData/img/browser-safari.png" alt="Safari browser"></a>
				</div>
			</div>
			<div id="warningMobileBlock" style="display:none;">
				<div class="warningBrowserText">
					Please note that Unity WebGL is not currently supported on mobiles.
				</div>
			</div>

		</div>

		<script src="ExcelLibs/xlsx.full.min.js"></script>
		<script src="VideoLibs/hls.min.js"></script>
    <script>
      (function (util, unityExternal) {
        var canvas = document.querySelector("#unity-canvas");

        var resize = (function (cvs) {
          return function () {
            var height = window.innerHeight;
            var width = window.innerWidth;

            cvs.width = width;
            cvs.height = height;
          };
        })(canvas);

        resize();

        window.addEventListener("resize", resize, false);
        function start() {
          util.getJSON("./config.json").then(function(config) {
            return util
              .loadScript(config.loaderUrl)
              .then(function() {
                return window.createUnityInstance(canvas, config.unityConfig, function(progress) {
                  unityExternal.postMessage({
                    type: "progress",
                    progress: progress
                  });

                  document.getElementById("loadingBlock").style.display = "inherit";
                  document.getElementById("fullBar").style.width = (100 * progress) + "%";
                  document.getElementById("emptyBar").style.width = (100 * (1 - progress)) + "%";
                  if (progress == 1) {
                    setTimeout(function () { document.getElementById("loadingBlock").style.display = "none"; }, 3000);
                  }
                });
              })
              .then(function(unityInstance) {
                unityExternal.setUnityInstance(unityInstance);
              });
          })
        }
        start();
      })(
        {
          loadScript(src) {
            return new Promise(function (resolve, reject) {
              var script = document.createElement("script");
              var processHandler = function (cb) {
                return function() {
                  cb();
                  document.body.removeChild(script);
                }
              };
              script.src = src;
              script.onload = processHandler(resolve);
              script.onerror = processHandler(reject);

              document.body.appendChild(script);
            });
          },
          getJSON(url) {
            var xhr = new window.XMLHttpRequest();
            xhr.open("get", url, true);
            xhr.send(null);

            return new Promise(function (resolve, reject) {
              setTimeout(function () {
                if (xhr.readySate != 4) {
                  xhr.abort();
                  reject("请求超时！")
                }
              }, 4e3)
              xhr.onreadystatechange = function () {
                if (xhr.readyState == 4) {
                  let status = xhr.status
                  if ((status >= 200 && status < 300) || status == 304) {
                    try {
                      var data = JSON.parse(xhr.responseText);
                      resolve(data);
                    }
                    catch(e) {
                      reject("JSON 文件格式解析失败！");
                    }
                  } else {
                    reject(status);
                  }
                }
              }
            })
          }
        },
        (function (unityExternalCSemitter) {
          var unityExternal = {
            _unityInstance: null,
            _sourceWindow: null,
            setUnityInstance(unityInstance) {
              this._unityInstance = unityInstance;
            },
            init(sourceWin) {
              var _this = this;
              unityExternalCSemitter.on(
                "message",
                _this.onEventMessage.bind(_this)
              );

              this._sourceWindow = sourceWin;
            },
            onEventMessage(payload) {
              payload = JSON.parse(payload);
              this.postMessage({
                type: "data",
                data: payload
              });
            },
            send(payload) {
              this._unityInstance.SendMessage(
                "WebHandler",
                "OnMessage",
                JSON.stringify(payload)
              );
            },
            postMessage(data) {
              if (this._sourceWindow) {
                this._sourceWindow.postMessage(data, "*");
              }
            }
          };

          window.addEventListener("message", function (evt) {
            var data = evt.data;
            switch (data.type) {
              case "connect":
                unityExternal.init(evt.source);
                // unityExternal.postMessage({
                //   type: "ack"
                // });
                break;
              case "data":
                // 转发给 unity
                unityExternal.send(data.data);
                break;
            }
          });
          return unityExternal;
        })(
          (function () {
            return (window.unityExternalCSemitter = {
              _events: [],
              emit(type, data) {
                for (var i = 0, len = this._events.length; i < len; i++) {
                  var event = this._events[i];
                  if (event.type === type) {
                    event.fn(data);
                  }
                }
              },
              on(type, fn) {
                this._events.push({
                  type: type,
                  fn: fn
                });
              },
              off(type, fn) {
                for (var i = 0; i < this._events.length; ) {
                  var event = this._events[i];
                  if (event.type === type && event.fn === fn) {
                    this._events.splice(i, 1);
                  } else {
                    i++;
                  }
                }
              }
            });
          })()
        )
      );
    </script>
  </body>
</html>
