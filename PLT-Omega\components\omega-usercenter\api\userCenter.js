import { httping, http, projectId } from "@omega/http";
import omegaAuth from "@omega/auth";

export const UserApi = {
  async get({ id }) {
    const res = await httping({
      url: `/platform/v1/auth/user/userInfo`,
      method: "GET",
      params: {
        id,
        tenantId: projectId.get()
      }
    });
    return res.data;
  },

  async edit(data) {
    const headers = {
      tenantId: omegaAuth.user.getUserTenantId()
    };
    data.projectId = projectId.get();
    if (data.id) {
      // 更新用户
      return httping({
        url: "/platform/v1/auth/user/updateUser",
        method: "POST",
        data,
      });
    }

    // 新建用户
    return http({
      url: "/platform/v1/auth/user/addUser",
      method: "POST",
      headers,
      data,
    });
  },

  async remove({ id, name }) {
    return await httping({
      url: `/platform/v1/auth/user/deleteUser`,
      method: "GET",
      params: {
        id,
        name,
        tenantId: projectId.get()
      },
    });
  },

  // 获取用户组列表
  async getUsers() {
    const res = await httping({
      url: `/platform/v1/auth/matrix/project/auth/tree`,
      method: "GET",
      headers: {
        tenantId: projectId.get()
      }
    });
    return res.data || [];
  },


  async getUserBar() {
    const res = await httping({
      url: `/platform/v1/auth/matrix/user/bar`,
      method: "POST",
    });
    return res.data || [];
  },

  async getUserBarTree({ label, userId, treeWithSelect }) {
    const res = await httping({
      url: `/platform/v1/auth/matrix/user/bar/tree`,
      method: "POST",
      params: {
        label,
        treeWithSelect,
        userId,
        tenantId: projectId.get(),
      }
    });
    return res.data || [];
  },

  async updateUserAuthTree(data, { label, userId }) {
    const res = await httping({
      url: `/platform/v1/auth/matrix/auth/info/user/update`,
      method: "POST",
      params: {
        label,
        userId,
      },
      data
    });
    return res.data || [];
  },

  async getRoles() {
    const res = await httping({
      url: "/platform/v1/auth/role/roles",
      method: "GET",
      params: {
        tenantId: projectId.get(),
      },
    });

    return res.data || [];
  },

  async resetPassword(param) {
    return httping({
      url: "auth/v1/user/password/updateByRoot",
      method: "PUT",
      data: param,
    });
  },
};

export const UserGroupApi = {
  async get({ id }) {
    const res = await httping({
      url: "/platform/v1/auth/userGroup/usergroup/queryById",
      method: "GET",
      params: {
        userGroupId: id,
      },
    });
    return res.data || {};
  },

  async edit(data) {
    const headers = {
      tenantId: omegaAuth.user.getUserTenantId()
    };
    if (data.id) {
      data.projectId = projectId.get();
      // 编辑
      return httping({
        url: "/platform/v1/auth/userGroup/updateGroup",
        method: "POST",
        headers,
        data
      });
    } else {
      // 新建
      return httping({
        url: "/platform/v1/auth/userGroup/addGroup",
        method: "POST",
        headers,
        data
      });
    }
  },

  async remove({ name, id }) {
    return httping({
      url: "/platform/v1/auth/userGroup/deleteGroup",
      method: "DELETE",
      params: {
        name,
        id,
        tenantId: projectId.get()
      },
    });
  },

  async list() {
    const res = await httping({
      url: "/platform/v1/auth/userGroup/allGroup",
      method: "GET",
      params: {
        tenantId: projectId.get(),
      },
    });
    return res.data || [];
  }
};

export const RoleApi = {
  async list() {
    const res = await httping({
      url: "/platform/v1/auth/role/roles",
      method: "GET",
      params: {
        tenantId: projectId.get(),
      },
    });

    return res.data;
  },

  async get({ id }) {
    const res = await httping({
      url: `/platform/v1/auth/role/${id}`,
      method: "GET",
    });

    return res.data;
  },

  async edit(data) {
    const headers = {
      tenantId: omegaAuth.user.getUserTenantId()
    };
    if (data.id) {
      data.projectId = projectId.get();
      // 编辑
      return httping({
        url: `/platform/v1/auth/role/platform`,
        method: "PUT",
        headers,
        data,
      });
    } else {
      // 新建
      return httping({
        url: `/platform/v1/auth/role/platform`,
        method: "POST",
        headers,
        data,
      });
    }
  },

  async remove({ id, name }) {
    return httping({
      url: "/platform/v1/auth/role/platform/delete",
      method: "POST",
      data: {
        id,
        name,
        tenantId: projectId.get()
      },
    });
  },

  async getRoleBar() {
    const res = await httping({
      url: `/platform/v1/auth/matrix/role/bar`,
      method: "POST",
    });
    return res.data || [];
  },

  async getRoleBarTree({ label, roleId, treeWithSelect }) {
    const res = await httping({
      url: `/platform/v1/auth/matrix/role/bar/tree`,
      method: "POST",
      headers: {
        tenantId: projectId.get()
      },
      params: {
        label,
        roleId,
        treeWithSelect
      }
    });
    return res.data || [];
  },

  async updateRoleAuthTree(data, { label, roleId }) {
    const res = await httping({
      url: `/platform/v1/auth/matrix/auth/info/role/update`,
      method: "POST",
      params: {
        label,
        roleId
      },
      data
    });
    return res.data || [];
  },

  async getRoleOperatePermissions({ id }) {
    const res = await httping({
      url: `/platform/v1/auth/role/${id}/permissions`,
      method: "GET",
    });
    return res.data;
  },

  // 查询角色下所有的用户
  async getUsersByRole(roleId) {
    const res = await httping({
      url: `/platform/v1/auth/user/user/querybyrole`,
      method: "GET",
      params: {
        roleId,
        tenantId: projectId.get()
      }
    });
    return res.data || [];
  },
};
