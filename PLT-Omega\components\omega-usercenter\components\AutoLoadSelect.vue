<template>
  <el-select v-bind="$attrs" v-on="$listeners">
    <el-option
      v-for="item in data"
      :key="item[prop.key]"
      :label="item[prop.label]"
      :value="item[prop.value]"
    />
  </el-select>
</template>
<script>
/**
 * 针对 form 表单
 * 对 el-select 组件扩展
 */
export default {
  name: "AutoLoadSelect",
  props: {
    items: {
      type: Array,
      default() {
        return [];
      }
    },
    prop: {
      type: Object,
      default() {
        return {
          key: "id",
          label: "name",
          value: "id"
        };
      }
    },
    autoload: {
      type: Boolean,
      default: true
    },
    param: {
      type: Object,
      default() {
        return {};
      }
    },
    handlerData: {
      type: Function,
      default() {
        return Promise.resolve([]);
      }
    }
  },
  data() {
    return {
      data: []
    };
  },
  created() {
    // 请求接口方式
    if (this.autoload) {
      this.$watch(
        "param",
        (newVal, oldVal) => {
          this.setParam(Object.assign({}, newVal, oldVal)).load();
        },
        {
          immediate: true
        }
      );
    } else {
      // 填充数据方式
      this.$watch(
        "items",
        value => {
          this.data = value;
        },
        {
          immediate: true
        }
      );
    }
  },
  methods: {
    load() {
      const param = this.getParam();
      this.handlerData(param).then(data => {
        this.data = data;
      });
    },
    setParam(param) {
      this.sysParam = param;
      return this;
    },
    getParam() {
      return this.sysParam;
    },
    getData() {
      return this.data;
    }
  }
};
</script>
