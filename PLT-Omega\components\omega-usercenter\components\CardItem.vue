<template>
  <div class="card-item">
    <div class="card-item-label" :title="label">{{ label }}</div>
    <el-dropdown
      class="card-item-operate"
      placement="bottom"
      @command="onCommand"
    >
      <i class="el-icon-more"></i>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          v-for="btn in buttons"
          :key="btn.id"
          :command="btn.id"
          v-permission="btn.permission"
        >
          <el-link :underline="false" :type="btn.type">{{ btn.label }}</el-link>
        </el-dropdown-item>
        <el-dropdown-item v-if="isEmpty">
          {{ $T("无操作权限") }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
export default {
  name: "CardItem",
  props: {
    label: String,
    buttons: {
      type: Array,
      default() {
        return [
          // {
          //   label: "删除",
          //   id: "delete"
          // },
          // {
          //   label: "编辑",
          //   id: "edit"
          // }
        ];
      }
    }
  },
  computed: {
    isEmpty() {
      return !this.buttons.length;
    }
  },
  methods: {
    onCommand(id) {
      if (id) {
        this.$emit("command", id);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.card-item {
  flex: 1;
  position: relative;
}
.card-item-label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 90%;
}
.card-item-operate {
  position: absolute;
  right: 0;
  top: 0;
}
.el-icon-more {
  transform: rotate(90deg);
  border: 4px solid transparent;
  &:hover {
    @include font_color(F1);
  }
}
</style>
