<template>
  <div class="tree-permission">
    <header class="tree-permission-header">
      <div>
        <slot name="header" />
      </div>

      <div class="tree-permission-operates">
        <el-button type="text" @click="onToggleExpandClick">
          {{ i18n("展开/收起") }}
        </el-button>
        <el-button type="text" @click="onCheckAllClick" v-if="!isView">
          {{ i18n("全选") }}
        </el-button>
        <el-button type="text" @click="onReverseClick" v-if="!isView">
          {{ i18n("反选") }}
        </el-button>
        <el-button type="text" @click="onCancelClick" v-if="!isView">
          {{ i18n("清空") }}
        </el-button>
      </div>
    </header>
    <main>
      <cet-ztree ref="ztree" :setting="setting" />
    </main>
  </div>
</template>

<script>
import Vue from "vue";
import { i18n } from "../local/index.js";
export default {
  name: "TreePermission",
  data() {
    return {};
  },
  props: {
    isView: {
      type: <PERSON><PERSON>an,
      default: false,
      expandAll: false
    }
  },
  computed: {
    setting() {
      const data = {
        key: {
          name: "nodeName",
          children: "children",
          checked: "select"
        }
      };

      if (this.isView) {
        return {
          data
        };
      }
      return {
        data,
        check: {
          enable: true
        },
        view: {
          // showLine: true,
          addHoverDom: this.addHoverDom
        }
      };
    }
  },
  methods: {
    i18n,
    addHoverDom(treeId, treeNode) {
      if (!treeNode.isParent) {
        return;
      }
      const checkbox = this.getNodeReverseCheckbox(treeNode);
      $("#" + treeNode.tId).append(checkbox.$el);
    },
    getNodeReverseCheckbox(treeNode) {
      let reverseBtn = this.___reverseBtn;
      if (reverseBtn) {
        reverseBtn.$off();
        reverseBtn.$on("click", () => {
          this.evNodeReverseCheckboxClick(treeNode);
        });
        return reverseBtn;
      }

      reverseBtn = this.___reverseBtn = this.createNodeReverseBtn();
      reverseBtn.$mount();

      this.$on("hook:beforeDestroy", () => {
        reverseBtn.$destroy();
      });
      return reverseBtn;
    },
    createNodeReverseBtn() {
      return new Vue({
        render() {
          return (
            <el-button
              class="tree-permission-btn"
              type="text"
              nativeOnClick={this.evClick}
            >
              {$T("反选")}
            </el-button>
          );
        },
        methods: {
          evClick() {
            this.$emit("click");
          }
        }
      });
    },
    evNodeReverseCheckboxClick(treeNode) {
      _.forEach(treeNode.children, node => {
        this.$refs.ztree.checkNode(node, !node.select, true);
      });
    },
    onCheckAllClick() {
      this.$refs.ztree.checkAllNodes(true);
    },
    onReverseClick() {
      const nodes = this.$refs.ztree.getNodes();
      _.forEach(nodes, node => {
        this.$refs.ztree.checkNode(node, !node.select, true);
      });
    },
    onCancelClick() {
      this.$refs.ztree.checkAllNodes(false);
    },
    onToggleExpandClick() {
      this.expandAll = !this.expandAll;
      this.$refs.ztree.expandAll(this.expandAll);
    },
    setData(data) {
      this.$refs.ztree.setData(data);
    },
    setValue({ checkNodes }) {
      let nodeList = [];
      const ztree = this.$refs.ztree;
      const nodes = ztree.getNodes();
      nodeList = ztree.transformToArray(nodes);
      _.forEach(checkNodes, checkNode => {
        const node = _.find(nodeList, checkNode);
        if (node && !node.isParent) {
          ztree.checkNode(node, true, true);
        }
      });
    },
    getValue() {
      return {
        checkNodes: this.$refs.ztree.getCheckedNodes(true)
      };
    },
    getCheckedNodes() {
      const checknodes = this.$refs.ztree.getCheckedNodes(true);
      return checknodes.map(node => {
        return {
          nodeId: node.nodeId,
          nodeName: node.nodeName,
          nodeTye: node.nodeTye,
          select: node.select
        };
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-permission {
  height: 100%;
  & > header {
    position: relative;
    height: 40px;
  }
  & > main {
    height: calc(100% - 40px);
  }

  &::v-deep {
    li[treenode] {
      position: relative;
    }
    .tree-permission-btn {
      position: absolute;
      top: 0;
      right: 16px;
    }
  }
}

.tree-permission-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-flow: row-reverse;
}
</style>
