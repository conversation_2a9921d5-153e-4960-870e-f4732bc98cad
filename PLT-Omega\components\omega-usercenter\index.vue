<template>
  <cet-tabs class="user-manager">
    <User :label="i18n('用户')" />
    <Role :label="i18n('角色')" />
    <UserGroup :label="i18n('用户组')" />
  </cet-tabs>
</template>

<script>
import Role from "./role/Role.vue";
import User from "./user/User.vue";
import UserGroup from "./userGroup/UserGroup.vue";
import { i18n } from "./local/index.js";
export default {
  name: "UserManager",
  components: { Role, User, UserGroup },
  methods: {
    i18n
  }
};
</script>

<style lang="scss" scoped>
.user-manager {
  height: 100%;
  &::v-deep.el-tabs {
    .el-tabs__item {
      min-width: 100px;
      text-align: center;
    }
  }
}
</style>
