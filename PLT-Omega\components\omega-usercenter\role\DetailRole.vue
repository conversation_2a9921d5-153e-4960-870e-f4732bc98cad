<template>
  <el-row class="fullheight" :gutter="16" :key="roleId">
    <el-col class="fullheight" :span="10">
      <CardWrap>
        <RolePermissionTabs :roleId="roleId"></RolePermissionTabs>
      </CardWrap>
    </el-col>
    <el-col class="fullheight" :span="14">
      <CardWrap>
        <header class="header">
          {{ i18n("角色包含的用户") }}
        </header>
        <section class="container">
          <CetTable :data.sync="CetTable_Users.data" v-bind="CetTable_Users">
            <el-table-column
              v-for="(column, index) in Columns_Users"
              v-bind="column"
              :key="index"
            />
          </CetTable>
        </section>
      </CardWrap>
    </el-col>
  </el-row>
</template>

<script>
import { RoleA<PERSON> } from "../api/userCenter";
import CardWrap from "../components/CardWrap.vue";
import RolePermissionTabs from "./RolePermissionTabs.vue";
import _ from "lodash";
import { i18n } from "../local/index.js";
export default {
  name: "DetailRole",
  components: { CardWrap, RolePermissionTabs },
  props: {
    selectNode: Object
  },
  data() {
    return {
      // 操作权限树组件
      CetTree_operate: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true
      },
      // 页面权限树组件
      CetTree_page: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true
      },
      CetTable_Users: {
        dataMode: "component",
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null
      },
      Columns_Users: [
        {
          type: "index",
          prop: "id",
          minWidth: "",
          width: 80,
          label: i18n("序号"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 120,
          width: "",
          label: i18n("用户名"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "mobilePhone",
          minWidth: "",
          width: 150,
          label: i18n("移动电话"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "email",
          minWidth: 100,
          width: "",
          label: i18n("电子邮箱"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        }
      ]
    };
  },
  computed: {
    roleId() {
      return this.selectNode.id;
    }
  },
  watch: {
    selectNode(node) {
      if (!node || _.isEmpty(node)) {
        this.reset();
      } else {
        this.load(node);
      }
    }
  },
  methods: {
    i18n,
    load(node) {
      Promise.all([RoleApi.getUsersByRole(node.id)]).then(([users]) => {
        this.CetTable_Users.data = users;
      });
    },
    reset() {
      this.CetTable_Users.data = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  height: 40px;
}
.container {
  height: calc(100% - 40px);
}
</style>
