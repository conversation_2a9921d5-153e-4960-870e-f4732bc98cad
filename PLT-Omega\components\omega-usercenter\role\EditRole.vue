<template>
  <omega-dialog
    :title="title"
    :on-before-confirm="onBeforeConfirm"
    width="600px"
  >
    <el-form
      class="edit-form"
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="100px"
      label-position="left"
    >
      <el-form-item
        :label="i18n('角色名称')"
        prop="name"
        class="custom-form-item"
      >
        <el-input
          class="w260"
          clearable
          maxlength="30"
          :placeholder="i18n('请输入角色名称')"
          v-model.trim="formData.name"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>

<script>
import AutoLoadSelect from "../components/AutoLoadSelect.vue";
import { RoleApi } from "../api/userCenter";
import CardWrap from "../components/CardWrap.vue";
import { i18n } from "../local/index.js";
export default {
  name: "EditServiceUser",
  components: {
    AutoLoadSelect,
    CardWrap
  },
  props: {
    id: Number
  },
  computed: {
    isEdit() {
      return !!this.id;
    },
    title() {
      return this.isEdit ? i18n("编辑角色") : i18n("添加角色");
    }
  },
  data() {
    return {
      formData: {
        name: ""
      },
      rules: {
        name: [
          { required: true, message: i18n("角色名不能为空！"), trigger: "blur" }
        ]
      }
    };
  },
  created() {
    if (this.isEdit) {
      RoleApi.get({ id: this.id }).then(data => {
        this.originData = data;
        this.formData = {
          name: data.name
        };
      });
    }
  },
  methods: {
    i18n,
    async onBeforeConfirm() {
      const { form } = this.$refs;
      await form.validate();
      const res = await RoleApi.edit(this.getData());
      const id = res.data;
      return id;
    },

    getData() {
      const formData = this.formData;

      const _data = {
        name: formData.name
      };

      if (this.isEdit) {
        return Object.assign({}, this.originData, _data);
      }
      return Object.assign({}, _data);
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-form {
  width: 450px;
  margin: auto;
}
</style>
