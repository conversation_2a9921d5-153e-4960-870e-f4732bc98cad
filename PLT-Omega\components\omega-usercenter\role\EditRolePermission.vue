<template>
  <omega-dialog :title="title" :hasFooter="false" width="600px">
    <div class="content">
      <RolePermissionTabs
        :defaultValueOfIsEdit="true"
        :role-id="roleId"
      ></RolePermissionTabs>
    </div>
  </omega-dialog>
</template>

<script>
import RolePermissionTabs from "./RolePermissionTabs.vue";
import { i18n } from "../local/index.js";
export default {
  name: "EditRolePermission",
  components: {
    RolePermissionTabs
  },
  props: {
    roleId: Number
  },
  computed: {
    title() {
      return i18n("角色权限");
    }
  }
};
</script>
<style scoped>
.content {
  height: 600px;
}
</style>
