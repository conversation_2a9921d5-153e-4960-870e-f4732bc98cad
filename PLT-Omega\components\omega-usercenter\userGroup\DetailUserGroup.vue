<template>
  <el-row class="fullheight" :gutter="16">
    <!-- <el-col class="fullheight" :span="6">
      <div class="usergroup-info">
        <CardWrap>
          <template #header>
            {{ $T("用户组") }}
          </template>
        </CardWrap>
      </div>
      <div class="usergroup-desc">
        <CardWrap>
          <template #header>
            {{ $T("用户组简介") }}
          </template>
        </CardWrap>
      </div>
    </el-col> -->
    <el-col class="fullheight">
      <CardWrap>
        <template #header>
          {{ $T("用户列表") }}
        </template>
        <section class="usergroup-container">
          <CetTable :data.sync="CetTable_Users.data" v-bind="CetTable_Users">
            <el-table-column
              v-for="(column, index) in Columns_Users"
              v-bind="column"
              :key="index"
            />
          </CetTable>
        </section>
      </CardWrap>
    </el-col>
  </el-row>
</template>

<script>
import { UserGroupApi } from "../api/userCenter";
import CardWrap from "../components/CardWrap.vue";
import _ from "lodash";
import { i18n } from "../local/index.js";

export default {
  name: "DetailUserGroup",
  components: { CardWrap },
  props: {
    selectNode: Object
  },
  data() {
    return {
      CetTable_Users: {
        dataMode: "component",
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null
      },
      Columns_Users: [
        {
          type: "index",
          prop: "id",
          minWidth: "",
          width: 80,
          label: i18n("序号"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 120,
          width: "",
          label: i18n("用户名"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "mobilePhone",
          minWidth: "",
          width: 150,
          label: i18n("移动电话"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "email",
          minWidth: 100,
          width: "",
          label: i18n("电子邮箱"),
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        }
      ]
    };
  },
  watch: {
    selectNode(node) {
      if (!node || _.isEmpty(node)) {
        this.reset();
      } else {
        this.load(node);
      }
    }
  },
  methods: {
    load(node) {
      UserGroupApi.get({
        id: node.id
      }).then(data => {
        this.CetTable_Users.data = data.users;
      });
    },
    reset() {
      this.CetTable_Users.data = [];
    }
  }
};
</script>
<style lang="scss" scoped>
.usergroup-info {
  height: 400px;
  margin-bottom: 16px;
  box-sizing: border-box;
}
.usergroup-desc {
  height: calc(100% - 416px);
  box-sizing: border-box;
  min-height: 260px;
}
.usergroup-container {
  height: calc(100% - 60px);
  box-sizing: border-box;
}
</style>
