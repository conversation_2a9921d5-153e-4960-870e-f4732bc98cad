<template>
  <omega-dialog
    :title="title"
    :on-before-confirm="onBeforeConfirm"
    width="600px"
  >
    <el-form
      class="edit-project-user-body"
      ref="form"
      :model="formData"
      :rules="rules"
      label-width="110px"
    >
      <el-form-item :label="i18n('名称')" prop="name" class="custom-form-item">
        <el-input
          clearable
          maxlength="30"
          :placeholder="i18n('请输入用户组名')"
          v-model.trim="formData.name"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>

<script>
import { UserGroupApi } from "../api/userCenter";
import { i18n } from "../local/index.js";
export default {
  name: "EditServiceUser",
  components: {},
  props: {
    id: Number
  },
  computed: {
    isEdit() {
      return !!this.id;
    },
    title() {
      return this.isEdit ? i18n("编辑用户组") : i18n("添加用户组");
    }
  },
  data() {
    return {
      formData: {
        name: ""
      },
      rules: {
        name: [
          {
            required: true,
            message: i18n("用户组名不能为空！"),
            trigger: "blur"
          }
        ]
      }
    };
  },
  created() {
    if (this.isEdit) {
      UserGroupApi.get({ id: this.id }).then(data => {
        this.originData = data;
        this.formData = {
          name: data.name
        };
      });
    }
  },
  methods: {
    i18n,
    async onBeforeConfirm() {
      const { form } = this.$refs;
      await form.validate();
      const res = await UserGroupApi.edit(this.getData());

      return res.data;
    },
    getData() {
      const formData = this.formData;

      const _data = {
        name: formData.name,
        parentId: 0
      };

      if (this.isEdit) {
        return Object.assign({}, this.originData, _data);
      }
      return Object.assign({}, _data);
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-project-user-body {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 15px;
  & ::v-deep .el-form-item {
    margin-bottom: 32px;
  }
}
</style>
