<template>
  <TreeLayout
    :CetTree="CetTree"
    :opearateButtons="opearateButtons"
    :onCardItemCommand="onCardItemCommand"
  >
    <template #aside-footer>
      <el-button class="fullwidth" type="primary" @click="evAddClick">
        {{ i18n("+ 添加用户组") }}
      </el-button>
    </template>
    <template #container>
      <DetailUserGroup :select-node="selectNode" />
    </template>
  </TreeLayout>
</template>
<script>
import { UserGroupApi } from "../api/userCenter";
import DetailUserGroup from "./DetailUserGroup.vue";

import EditUserGroup from "./EditUserGroup.vue";
import TreeLayout from "../components/treeLayout.vue";
import { showOmegaDialog } from "@omega/widget";
import { i18n } from "../local/index.js";
export default {
  name: "UserGroupManage",
  components: {
    DetailUserGroup,
    TreeLayout
  },
  data() {
    return {
      // 用户树组件
      CetTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "userGroups"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_currentNode_out
        }
      },
      opearateButtons: [
        {
          label: i18n("编辑"),
          id: "edit",
          type: "primary"
        },
        {
          label: i18n("删除"),
          id: "delete",
          type: "danger"
        }
      ],
      selectNode: {}
    };
  },
  methods: {
    i18n,
    // 用户树组件输出
    CetTree_currentNode_out(val) {
      this.selectNode = val;
    },
    onCardItemCommand(id, node) {
      switch (id) {
        case "delete":
          this.remove(node);
          break;
        case "edit":
          this.edit(node);
          break;
        default:
          return;
      }
    },
    evAddClick() {
      const e = showOmegaDialog(EditUserGroup);
      e.on("confirm", id => {
        this.CetTree.selectNode = {
          id: id
        };
        this.refresh();
      });
    },

    edit(node) {
      const e = showOmegaDialog(EditUserGroup, {
        id: node.id
      });
      e.on("confirm", id => {
        this.CetTree.selectNode = {
          id: id
        };
        this.refresh();
      });
    },

    async remove(node) {
      await this.$confirm(i18n("确定删除该用户组？"));

      await UserGroupApi.remove({
        id: node.id,
        name: node.name
      });

      if (node.id === this.CetTree.selectNode.id) {
        this.CetTree.selectNode = {};
      }
      this.refresh();
    },

    // 刷新数据
    refresh() {
      const tree = this.CetTree;
      UserGroupApi.list().then(val => {
        const treeData = val || [];
        tree.inputData_in = treeData;

        // 默认选中第一个用户
        if (this._.isEmpty(tree.selectNode) && treeData.length) {
          const id = treeData[0] && treeData[0].id;
          if (id) {
            tree.selectNode = {
              id
            };
          }
        }
      });
    }
  },
  activated() {
    this.refresh();
  }
};
</script>
<style lang="scss" scoped>
.aside {
  box-sizing: border-box;
  position: relative;
  border-right: 1px solid #eeeeee;
  padding: 3px 30px 0 20px;

  transition: width 0.3s;
  -moz-transition: width 0.3s;
  -webkit-transition: width 0.3s;
  -o-transition: width 0.3s;
}

.usergroup-tree ::v-deep .el-tree {
  overflow: auto;
}
.padding-b10 {
  padding-bottom: 10px;
  box-sizing: border-box;
}
.footer {
  border-bottom: 1px solid #f2f2f2;
}
</style>
