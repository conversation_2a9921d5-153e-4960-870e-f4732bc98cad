<template>
  <el-row class="user-detail" :gutter="16" :key="userId">
    <el-col class="user-permission" :span="18">
      <CardWrap>
        <UserPermissionTabs :userId="userId" />
      </CardWrap>
    </el-col>
    <el-col class="user-info" :span="6">
      <div class="user-form">
        <CardWrap>
          <el-form ref="form" label-position="top">
            <el-form-item v-for="(item, index) in items" :key="index">
              <template #label>
                <omega-icon class="p2 mr10 fs18" :symbolId="item.icon" />
                <span>{{ item.label }}</span>
              </template>
              <div class="text-ellipsis">
                <el-tag v-if="item.useTag">{{ item.value }}</el-tag>
                <span v-else>{{ item.value }}</span>
              </div>
            </el-form-item>
          </el-form>
          <div class="text-right">
            <el-button type="danger" @click="evResetPasswordClick">
              {{ i18n("重置密码") }}
            </el-button>
          </div>
        </CardWrap>
      </div>
    </el-col>
  </el-row>
</template>
<script>
import ResetPassword from "./ResetPassword";
import CardWrap from "../components/CardWrap.vue";
import { UserApi, UserGroupApi } from "../api/userCenter.js";

import { showOmegaDialog } from "@omega/widget";
import UserPermissionTabs from "./UserPermissionTabs.vue";
import _ from "lodash";
import { i18n } from "../local/index.js";
export default {
  name: "DetailUser",
  components: {
    CardWrap,
    UserPermissionTabs
  },
  props: {
    selectNode: Object
  },
  watch: {
    selectNode(node) {
      if (node && !_.isEmpty(node)) {
        this.load(node);
      }
    }
  },
  data() {
    return {
      userDetail: {
        name: "",
        nicName: "",
        mobilePhone: "",
        email: "",
        roleName: "",
        relativeUserGroupName: "",
        relativeUserGroupLogo: null
      }
    };
  },
  computed: {
    items() {
      return [
        {
          icon: "user-one-lin",
          label: i18n("用户名："),
          value: this.userDetail.name || "--"
        },
        {
          icon: "user-one-lin",
          label: i18n("昵称："),
          value: this.userDetail.nicName || "--"
        },
        {
          icon: "account-lin",
          label: i18n("角色："),
          useTag: true,
          value: this.userDetail.roleName || "--"
        },
        {
          icon: "firm-sg",
          label: i18n("用户组："),
          useTag: true,
          value: this.userDetail.relativeUserGroupName || "--"
        },
        {
          icon: "phone-lin",
          label: i18n("电话："),
          value: this.userDetail.mobilePhone || "--"
        },
        {
          icon: "mailbox-lin",
          label: i18n("邮箱："),
          value: this.userDetail.email || "--"
        }
      ];
    },
    userId() {
      return this.selectNode.id;
    }
  },
  methods: {
    i18n,
    async load(node) {
      const user = await UserApi.get({
        id: node.id
      });
      const roleName = _.get(user, "roles[0].name", "--");
      this.userDetail = {
        name: user.name,
        nicName: user.nicName,
        mobilePhone: user.mobilePhone,
        email: user.email,
        roleName: roleName,
        relativeUserGroupName: "--",
        relativeUserGroupLogo: null
      };

      const userGroupId = user.relativeUserGroup[0];
      if (userGroupId) {
        const userGroup = await UserGroupApi.get({
          id: userGroupId
        });
        this.userDetail.relativeUserGroupName = userGroup.name;
      }
    },
    evResetPasswordClick() {
      showOmegaDialog(ResetPassword, {
        id: this.userDetail.id,
        userName: this.userDetail.name
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.user-detail {
  height: 100%;
}

.user-permission {
  height: 100%;
}
.user-info {
  height: 100%;
  .user-form {
    height: 100%;
  }
}
</style>
