<template>
  <omega-dialog
    :title="title"
    width="600px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form
      ref="form"
      class="user-form"
      label-width="100px"
      label-position="left"
      :model="formData"
      :rules="rules"
    >
      <el-form-item
        :label="i18n('用户名')"
        prop="name"
        class="custom-form-item"
      >
        <el-input
          clearable
          maxlength="30"
          :placeholder="i18n('请输入用户名')"
          v-model.trim="formData.name"
        />
      </el-form-item>
      <el-form-item
        :label="i18n('昵称')"
        prop="nicName"
        class="custom-form-item"
      >
        <el-input
          clearable
          maxlength="30"
          :placeholder="i18n('请输入昵称')"
          v-model.trim="formData.nicName"
        />
      </el-form-item>
      <template v-if="!isEdit">
        <el-form-item
          :label="i18n('账户密码')"
          prop="password"
          class="custom-form-item"
        >
          <el-input
            clearable
            maxlength="18"
            type="password"
            :placeholder="i18n('请输入账户密码')"
            v-model.trim="formData.password"
          />
        </el-form-item>
        <el-form-item
          :label="i18n('密码确认')"
          prop="_checkPassword"
          class="custom-form-item"
        >
          <el-input
            clearable
            maxlength="18"
            type="password"
            :placeholder="i18n('请输入确认密码')"
            v-model.trim="formData._checkPassword"
          />
        </el-form-item>
      </template>
      <el-form-item
        :label="i18n('移动电话')"
        prop="mobilePhone"
        class="custom-form-item"
      >
        <el-input
          clearable
          maxlength="30"
          :placeholder="i18n('请输入移动电话')"
          v-model.trim="formData.mobilePhone"
        />
      </el-form-item>
      <el-form-item
        :label="i18n('电子邮箱')"
        prop="email"
        class="custom-form-item"
      >
        <el-input
          clearable
          maxlength="30"
          :placeholder="i18n('请输入电子邮箱')"
          v-model.trim="formData.email"
        />
      </el-form-item>
      <el-form-item
        :label="i18n('用户组')"
        prop="userGroupId"
        class="custom-form-item"
      >
        <AutoLoadSelect
          class="fullwidth"
          filterable
          ref="userGroupSelct"
          v-bind="ElSelect_UserGroup"
          v-model="formData.userGroupId"
        />
      </el-form-item>
      <el-form-item
        :label="i18n('角色')"
        prop="roleId"
        class="custom-form-item"
      >
        <AutoLoadSelect
          class="fullwidth"
          ref="role"
          filterable
          v-bind="ElOption_Roles"
          v-model="formData.roleId"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>

<script>
import { UserApi, UserGroupApi } from "../api/userCenter";
import rule from "../utils/rule";

import AutoLoadSelect from "../components/AutoLoadSelect.vue";
import { i18n } from "../local/index.js";
export default {
  name: "EditUser",
  components: {
    AutoLoadSelect
  },
  props: {
    id: Number
  },
  computed: {
    isEdit() {
      return !!this.id;
    },
    title() {
      return this.isEdit ? i18n("编辑用户") : i18n("新建用户");
    }
  },
  data() {
    return {
      formData: {
        name: "",
        nicName: "",
        password: "",
        _checkPassword: "",
        email: "",
        mobilePhone: "",
        userGroupId: null,
        roleId: null
      },
      rules: {
        name: [
          { required: true, message: i18n("用户名不能为空！"), trigger: "blur" }
        ],
        // nicName: [
        //   { required: true, message: i18n("昵称不能为空！"), trigger: "blur" }
        // ],
        // userGroupId: [
        //   { required: true, message: i18n("请选择用户组！"), trigger: "blur" }
        // ],
        roleId: [
          { required: true, message: i18n("请选择用户角色！"), trigger: "blur" }
        ],
        password: [
          rule.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error(i18n("密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.formData.password) {
                callback(new Error(i18n("密码不一致")));
                return;
              }

              callback();
            }
          }
        ],
        mobilePhone: [
          rule.check_phone,
          { required: true, message: i18n("移动电话不能为空"), trigger: "blur" }
        ],
        email: [
          {
            type: "email",
            required: true,
            message: i18n("请输入正确的邮箱地址"),
            trigger: "blur"
          }
        ]
      },
      ElSelect_UserGroup: {
        autoload: true,
        handlerData: UserGroupApi.list
      },
      ElOption_Roles: {
        autoload: true,
        handlerData: UserApi.getRoles
      },
      originUserData: undefined,
      permissionNodesOptions: []
    };
  },
  created() {
    if (this.isEdit) {
      UserApi.get({ id: this.id }).then(data => {
        this.originUserData = data;
        this.formData = {
          name: data.name,
          nicName: data.nicName,
          email: data.email,
          mobilePhone: data.mobilePhone,
          userGroupId: data.relativeUserGroup && data.relativeUserGroup[0],
          roleId: data.roles && data.roles[0] && data.roles[0].id
        };
      });
    }
  },
  methods: {
    i18n,
    async onBeforeConfirm() {
      const { form } = this.$refs;
      await form.validate();
      const res = await UserApi.edit(this.getData());

      return res.data;
    },

    getRole(id) {
      const roles = this.$refs.role.getData();
      return this._.find(roles, { id });
    },

    getData() {
      const formData = this.formData;
      const userGroups = this.$refs.userGroupSelct.getData();
      let relativeUserGroup = null;
      if (formData.userGroupId) {
        const userGroup = _.find(userGroups, {
          id: formData.userGroupId
        });
        relativeUserGroup = [
          {
            name: userGroup.name,
            id: userGroup.id,
            tenantId: userGroup.tenantId
          }
        ];
      }
      const _data = {
        avatar: formData.avatar,
        email: formData.email,
        mobilePhone: formData.mobilePhone,
        name: formData.name,
        nicName: formData.nicName,
        roles: [this.getRole(formData.roleId)],
        relativeUserGroup
      };
      if (this.isEdit) {
        if (!this.originUserData.expiredTime) {
          this.originUserData.expiredTime = 0;
        }
        if (!this.originUserData.state) {
          this.originUserData.state = 0;
        }
        return _.assign({}, this.originUserData, _data);
      }
      return _.assign(
        {},
        {
          password: formData.password
        },
        _data
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.user-permission-tabs::v-deep.el-tabs {
  margin-left: -80px;
  & > .el-tabs__content {
    & > .el-tab-pane {
      height: 380px;
    }
  }
}
.user-form::v-deep.el-form {
  width: 350px;
  margin: auto;
  .el-form-item--small.el-form-item {
    margin-bottom: 28px;
  }
}
.custom-form-item {
  word-break: break-word;
}
</style>
