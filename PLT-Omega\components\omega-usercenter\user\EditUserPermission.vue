<template>
  <omega-dialog :title="title" :hasFooter="false" width="850px">
    <div class="content">
      <UserPermissionTabs :defaultValueOfIsEdit="true" :user-id="userId"></UserPermissionTabs>
    </div>
  </omega-dialog>
</template>

<script>
import UserPermissionTabs from "./UserPermissionTabs.vue";
import { i18n } from "../local/index.js";
export default {
  name: "EditUserPermission",
  components: {
    UserPermissionTabs
  },
  props: {
    userId: Number
  },
  computed: {
    title() {
      return $T("用户权限");
    }
  },
  methods: {
    i18n
  }
};
</script>
<style scoped>
.content {
  height: 600px;
}
</style>
