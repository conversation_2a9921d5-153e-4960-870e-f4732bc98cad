<template>
  <omega-dialog
    :title="i18n('重置密码')"
    width="600px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form
      ref="form"
      class="reset-form"
      :model="form"
      label-width="100px"
      :rules="rules"
    >
      <el-form-item class="custom-item" :label="i18n('用户名')" prop="userName">
        <el-input disabled :value="userName" />
      </el-form-item>
      <el-form-item
        class="custom-item"
        :label="i18n('新密码')"
        prop="newPassword"
      >
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="i18n('请输入新的密码')"
          v-model.trim="form.newPassword"
        />
      </el-form-item>
      <el-form-item
        class="custom-item"
        :label="i18n('新密码确认')"
        prop="_checkPassword"
      >
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="i18n('请输入确认密码')"
          v-model.trim="form._checkPassword"
        />
      </el-form-item>
      <el-form-item
        class="custom-item"
        :label="i18n('管理员密码')"
        prop="rootPassword"
      >
        <el-input
          clearable
          type="password"
          maxlength="32"
          :placeholder="i18n('请输入管理员密码（当前账号密码）')"
          v-model.trim="form.rootPassword"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>
<script>
import rule from "../utils/rule";
import { UserApi } from "../api/userCenter";
import omegaAuth from "@omega/auth";
import { i18n } from "../local/index.js";
export default {
  name: "DialogResetUserPassword",
  components: {},
  props: {
    id: Number,
    userName: String
  },
  data() {
    return {
      form: {
        rootPassword: "",
        newPassword: "",
        _checkPassword: ""
      },
      rules: {
        rootPassword: [
          {
            required: true,
            message: i18n("管理员密码不能为空"),
            trigger: "blur"
          }
        ],
        newPassword: [
          rule.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error(i18n("新密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.form.newPassword) {
                callback(new Error(i18n("密码不一致")));
                return;
              }
              callback();
            }
          }
        ]
      }
    };
  },
  methods: {
    i18n,
    getData() {
      const form = this.form;
      return {
        userId: this.id,
        userName: this.userName,
        rootName: omegaAuth.user.getUserName(),
        rootId: omegaAuth.user.getUserId(),
        newPassword: form.newPassword,
        rootPassword: form.rootPassword
      };
    },

    async onBeforeConfirm() {
      const { form } = this.$refs;

      await form.validate();

      await UserApi.resetPassword(this.getData());

      return true;
    }
  }
};
</script>
<style scoped>
.reset-form {
  width: 400px;
  margin: 0 auto;
}
.custom-item {
  word-break: break-word;
}
</style>
