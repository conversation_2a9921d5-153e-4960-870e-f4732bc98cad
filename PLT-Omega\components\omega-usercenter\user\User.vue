<template>
  <TreeLayout
    :CetTree="CetTree"
    :opearateButtons="opearateButtons"
    :onCardItemCommand="onCardItemCommand"
  >
    <template #aside-footer>
      <el-button class="fullwidth" type="primary" @click="evAddClick">
        {{ i18n("+ 添加用户") }}
      </el-button>
    </template>
    <template #container>
      <DetailUser :select-node="selectNode" />
    </template>
  </TreeLayout>
</template>
<script>
import { UserApi } from "../api/userCenter";
import EditUser from "./EditUser.vue";
import EditUserPermission from "./EditUserPermission.vue";
import DetailUser from "./DetailUser.vue";
import TreeLayout from "../components/treeLayout.vue";
import { showOmegaDialog } from "@omega/widget";
import { i18n } from "../local/index.js";
export default {
  name: "UserManage",
  components: { DetailUser, TreeLayout },
  data() {
    return {
      // 用户树组件
      CetTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_currentNode_out
        }
      },
      opearateButtons: [
        {
          label: i18n("编辑"),
          id: "edit",
          type: "primary"
        },
        {
          label: i18n("删除"),
          id: "delete",
          type: "danger"
        }
      ],
      selectNode: {}
    };
  },
  methods: {
    i18n,
    onCardItemCommand(id, node) {
      switch (id) {
        case "delete":
          this.remove(node);
          break;
        case "edit":
          this.edit(node);
          break;
        default:
          return;
      }
    },
    evAddClick() {
      const e = showOmegaDialog(EditUser);
      e.on("confirm", id => {
        this.CetTree.selectNode = {
          id: id
        };

        const e = showOmegaDialog(EditUserPermission, {
          userId: id
        });
        e.on("cancel", () => {
          this.refresh();
        });
      });
    },
    // 用户树组件输出
    CetTree_currentNode_out(val) {
      this.selectNode = val;
    },

    edit(node) {
      const e = showOmegaDialog(EditUser, {
        id: node.id
      });
      e.on("confirm", id => {
        this.CetTree.selectNode = {
          id: id
        };
        this.refresh();
      });
    },

    async remove(node) {
      await this.$confirm(i18n("确定删除该用户？"));

      await UserApi.remove({
        id: node.id,
        name: node.name
      });

      if (node.id === this.CetTree.selectNode.id) {
        this.CetTree.selectNode = {};
      }
      this.refresh();
    },
    // 刷新数据
    refresh() {
      const tree = this.CetTree;
      UserApi.getUsers().then(user => {
        const treeData = user || [];
        tree.inputData_in = treeData;
        // 默认选中第一个用户
        if (this._.isEmpty(tree.selectNode) && treeData.length) {
          const [user] = treeData;
          if (user) {
            tree.selectNode = {
              id: user.id
            };
          }
        }
      });
    }
  },
  activated() {
    this.refresh();
  }
};
</script>
<style lang="scss" scoped>
.aside {
  box-sizing: border-box;
  position: relative;
  border-right: 1px solid #eeeeee;
  padding: 3px 30px 0 20px;

  transition: width 0.3s;
  -moz-transition: width 0.3s;
  -webkit-transition: width 0.3s;
  -o-transition: width 0.3s;
}

.usergroup-tree ::v-deep .el-tree {
  overflow: auto;
}
.padding-b10 {
  padding-bottom: 10px;
  box-sizing: border-box;
}
.footer {
  border-bottom: 1px solid #f2f2f2;
}
.title {
  padding: 14px 20px 0;
  border: 0;
  border-bottom: 1px solid #f2f2f2;
  overflow: hidden;
  position: relative;
}
.title .icon {
  display: inline-block;
  width: 30px;
  height: 30px;
  // background: #fff url("../assets/icon-user.svg") no-repeat center center;
  border-radius: 50px;
}
</style>
