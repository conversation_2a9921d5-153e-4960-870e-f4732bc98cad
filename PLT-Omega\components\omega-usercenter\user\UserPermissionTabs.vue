<template>
  <el-tabs
    class="user-permission-tabs"
    tab-position="top"
    type="border-card"
    v-model="activeTab"
  >
    <el-tab-pane
      v-for="option in permissionTabs"
      :key="option.barLabel"
      :label="option.barName"
      :name="option.barLabel"
      :lazy="true"
    >
      <TreePermissionLoad
        :user-id="userId"
        :label="option.barLabel"
        :operationType="option.operationType"
        :defaultValueOfIsEdit="defaultValueOfIsEdit"
      ></TreePermissionLoad>
    </el-tab-pane>
  </el-tabs>
</template>
<script>
import { UserApi } from "../api/userCenter.js";
import TreePermissionLoad from "./components/TreePermissionLoad.vue";
export default {
  name: "UserPermissionTabs",
  components: { TreePermissionLoad },
  props: {
    userId: Number,
    defaultValueOfIsEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: null,
      permissionTabs: []
    };
  },
  mounted() {
    this.initPermissionTabs();
  },
  methods: {
    initPermissionTabs() {
      if (this.userId) {
        UserApi.getUserBar().then(data => {
          this.permissionTabs = data;
          this.activeTab = data[0]?.barLabel;
        });
      }
    }
  }
};
</script>
<style scoped>
.user-permission-tabs::v-deep.el-tabs {
  height: 100%;
  & > .el-tabs__header.is-top {
    margin: 0 0 5px;
  }
  & > .el-tabs__content {
    height: calc(100% - 45px);
    & > .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
