<template>
  <label>
    <span class="mr8">{{ $T("匹配模式") }}</span>
    <el-radio-group v-bind="$attrs" v-on="$listeners" :disabled="isView">
      <el-radio :label="0">{{ $T("白名单") }}</el-radio>
      <el-radio :label="1">{{ $T("黑名单") }}</el-radio>
    </el-radio-group>
  </label>
</template>

<script>
export default {
  name: "BlackList",
  props: {
    isView: {
      type: Boolean,
      default: false
    }
  }
};
</script>
