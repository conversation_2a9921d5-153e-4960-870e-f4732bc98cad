<template>
  <TreePermission ref="tree" :is-view="!isEdit">
    <template #header>
      <el-button type="text" @click="onEdit" v-show="!isEdit" v-if="!readonly">
        {{ i18n("编辑") }}
      </el-button>
      <el-button type="text" @click="onCancel" v-show="isEdit">
        {{ i18n("取消编辑") }}
      </el-button>
      <el-button type="text" @click="onSave" v-show="isEdit">
        {{ i18n("保存") }}
      </el-button>
    </template>
  </TreePermission>
</template>
<script>
import TreePermission from "../../components/TreePermission.vue";
import { UserApi } from "../../api/userCenter.js";
import { i18n } from "../../local/index.js";
export default {
  name: "TreePermissionLoad",
  components: {
    TreePermission
  },
  props: {
    label: String,
    userId: Number,
    operationType: String,
    defaultValueOfIsEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEdit: this.defaultValueOfIsEdit,
      isEditing: false
    };
  },
  computed: {
    readonly() {
      return this.operationType === "readonly";
    }
  },
  mounted() {
    if (this.isEdit) {
      this.loadData({ withSelect: true });
    } else {
      this.loadData({ withSelect: false });
    }
  },
  methods: {
    i18n,
    async loadData({ withSelect }) {
      const data = await UserApi.getUserBarTree({
        label: this.label,
        userId: this.userId,
        treeWithSelect: withSelect
      });

      this.$refs.tree.setData(data);
    },
    onEdit() {
      this.isEdit = true;
      this.loadData({ withSelect: true });
    },
    onCancel() {
      this.isEdit = false;
      this.loadData({ withSelect: false });
    },
    async onSave() {
      const data = this.$refs.tree.getCheckedNodes();
      await UserApi.updateUserAuthTree(data, {
        label: this.label,
        userId: this.userId
      });
      this.onCancel();
    }
  }
};
</script>
