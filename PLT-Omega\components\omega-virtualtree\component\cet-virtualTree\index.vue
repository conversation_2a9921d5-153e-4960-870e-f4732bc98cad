<template>
  <div class="cet-virtual-tree"></div>
</template>
<script>
// vue3+elementPlus+vite 打包生成物
import createTreeV2 from "./lib/CetVirtualTree.umd.js";

const treeInstance = Symbol("virtualTree");

const EVENTS = [
  "onNodeClick",
  "onNodeContextmenu",
  "onCheckChange",
  "onCheck",
  "onCurrentChange",
  "onNodeExpand",
  "onNodeCollapse"
];
export default {
  name: "CetVirtualTree",
  props: {
    attribute: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  watch: {
    // "attribute.data": function (value) {
    //   console.log("🚀 ~ file: index.vue ~ line 34 ~ value", value);
    //   // this.init();
    //   // let d = this.attribute.data;
    //   // this[treeInstance] = createTreeV2(this.$el, { data: d });
    //   // this[treeInstance].set(this.attribute.data, value);
    //   this[treeInstance].setData(this.attribute.data);
    //   // this[treeInstance].setCheckedKeys(["1-0"]);
    //   this.$nextTick(() => {
    //     this[treeInstance].setCheckedKeys(["Big-01-0", "Big-01-1"]);
    //   });
    // }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      let event = this.forwardingEvents();

      let props = Object.assign(this.attribute, event);

      // 创建并挂载，返回实例
      this[treeInstance] = createTreeV2(this.$el, props);
      // 外部通过事件获取实例调用方法等
      this.$emit("onCreate", this[treeInstance]);
    },
    // elementplus事件转发,方法直接通过 this[CetTreeV2]获取
    forwardingEvents() {
      const vm = this;
      let obj = {
        // onCheck:this.onCheck
      };
      EVENTS.forEach(eventName => {
        vm[eventName] = (...args) => {
          vm.$emit(eventName, ...args);
        };
        obj[eventName] = vm[eventName];
      });
      return obj;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  // color: var(--color);
  /* font-size: inherit; */
}
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}
</style>
