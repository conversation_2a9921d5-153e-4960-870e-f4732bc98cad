{"name": "@omega/virtualtree", "version": "1.0.2", "private": false, "description": "虚拟树组件", "main": "lib/omega-virtualtree.umd.min.js", "module": "component/index.js", "author": "ZYX", "scripts": {"dev": "vue-cli-service serve", "build:lib": "vue-cli-service build --target lib --name omega-virtualtree --dest lib --formats umd-min --report component/index.js", "lint": "vue-cli-service lint", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "lib": "vue-cli-service build --target lib component/index.js"}, "peerDependencies": {"element-ui": "^2.15.6", "vue": "^2.6.14"}, "dependencies": {"core-js": "^3.36.0", "vue-template-compiler": "^2.6.14"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "axios": "^0.26.0", "babel-eslint": "^10.1.0", "element-ui": "^2.11.1", "extract-loader": "^5.1.0", "file-loader": "^6.0.0", "hard-source-webpack-plugin": "^0.13.1", "lodash": "^4.17.21", "lowdb": "^2.1.0", "sass": "^1.26.5", "sass-loader": "^9.0.3", "vue": "^2.6.14", "vue-router": "^3.4.3", "vuex": "^3.5.1"}}