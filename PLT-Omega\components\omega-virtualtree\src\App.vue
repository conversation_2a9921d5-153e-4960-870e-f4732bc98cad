<template>
  <div id="app">
    <!-- <img alt="Vue logo" src="./assets/logo.png" /> -->
    <!-- <HelloWorld msg="Welcome to Your Vue.js App" /> -->
    <el-tabs
      v-model="activeName"
      @tab-click="handleClick"
      style="height: 600px"
    >
      <el-tab-pane label="地图" name="first">
        <el-input v-model="filterText" placeholder="Filter keyword" />
        <CetVirtualTree
          v-bind="CetVirtualTree_obj"
          v-on="CetVirtualTree_obj.event"
        ></CetVirtualTree>
      </el-tab-pane>
      <el-tab-pane label="地图选点 弹窗" name="second"></el-tab-pane>
      <el-tab-pane label="地图选点 表单控件" name="third"></el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
// import virtualTree from "./projects/virtualTree/index.vue";
let list = [
  {
    label: "Level one 1",
    name: "Level one 1",
    children: [
      {
        label: "Level two 1-1",
        name: "Level two 1-1",
        children: [
          {
            label: "Level three 1-1-1",
            name: "Level three 1-1-1"
          }
        ]
      }
    ]
  },
  {
    label: "Level one 2",
    name: "Level one 2",
    children: [
      {
        label: "Level two 2-1",
        name: "Level two 2-1",
        children: [
          {
            label: "Level three 2-1-1",
            name: "Level three 2-1-1"
          }
        ]
      },
      {
        label: "Level two 2-2",
        name: "Level two 2-2",

        children: [
          {
            label: "Level three 2-2-1",
            name: "Level three 2-2-1"
          }
        ]
      }
    ]
  },
  {
    label: "Level one 3",
    name: "Level one 3",

    children: [
      {
        label: "Level two 3-1",
        name: "Level two 3-1",
        children: [
          {
            label: "Level three 3-1-1",
            name: "Level three 3-1-1"
          }
        ]
      },
      {
        label: "Level two 3-2",
        name: "Level two 3-2",
        children: [
          {
            label: "Level three 3-2-1",
            name: "Level three 3-2-1"
          }
        ]
      }
    ]
  }
];
export default {
  name: "App",
  components: {
    // virtualTree
  },
  data() {
    return {
      activeName: "first",
      filterText: "",
      CetVirtualTree_obj: {
        attribute: {
          data: list,
          props: {
            value: "name",
            label: "name",
            children: "children"
          },
          nodeKey: "name",
          defaultCheckedKeys: [],
          showCheckbox: true,
          // checkStrictly: true,
          height: 400,
          filterMethod: this.filterNode
        },
        event: {
          onCheck: this.onCheck,
          onCreate: this.onCreate
        }
      },
      CetTreeV2: ""
    };
  },
  watch: {
    filterText(val) {
      this.CetTreeV2.filter(val);
    }
  },
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.includes(value);
    },
    onCreate(event) {
      this.CetTreeV2 = event;
    },
    onCheck(tab, event) {
      console.log(
        "🚀 ~ file: App.vue ~ line 219 ~ onCheck ~ tab, event",
        tab,
        event
      );
    }
  }
};
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-align: center;
  color: #2c3e50;
  margin-top: 20px;
  height: 600px;
}
</style>
