<template>
  <div class="cet-virtual-tree"></div>
</template>
<script>
import createTreeV2 from "./lib/CetVirtualTree.umd.js";

const treeInstance = Symbol();

const EVENTS = [
  "onNodeClick",
  "onNodeContextmenu",
  "onCheckChange",
  "onCheck",
  "onCurrentChange",
  "onNodeExpand",
  "onNodeCollapse"
];
export default {
  name: "CetTreeV2",
  props: {
    propstree: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {};
  },
  mounted() {
    let event = this.forwardingEvents();

    let props = Object.assign(this.propstree, event);
    // 创建并挂载，返回实例
    this[treeInstance] = createTreeV2(this.$el, props);
    // 外部通过事件获取实例调用方法等
    this.$emit("onCreate", this[treeInstance]);

    // 你这样只能找一个 单独的事件订阅来搞。在props里面把对应的树的事件给定义上，通过外部的事件订阅来触发
    // 或者传一个方法进去，利用callback回调通知外层组件
  },
  methods: {
    // elementplus事件转发,方法直接通过 this[CetTreeV2]获取
    forwardingEvents() {
      const vm = this;
      let obj = {
        // onCheck:this.onCheck
      };
      EVENTS.forEach(eventName => {
        vm[eventName] = (...args) => {
          vm.$emit(eventName, ...args);
        };
        obj[eventName] = vm[eventName];
      });
      return obj;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-icon {
  --color: inherit;
  height: 1em;
  width: 1em;
  line-height: 1em;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  fill: currentColor;
  // color: var(--color);
  /* font-size: inherit; */
}
::v-deep .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}
</style>
