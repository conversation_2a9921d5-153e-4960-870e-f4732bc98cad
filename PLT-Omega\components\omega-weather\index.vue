<template>
  <div class="weather-card">
    <span class="city">{{ address.city }}</span>
    <span class="icon-img" :class="weatherIconClass"></span>
    <span class="weather-text">{{ weatherInfo.description || "--" }}</span>
    <span class="temperature">{{ weatherInfo.temperature || "--" }}℃</span>
    <span class="wind">
      {{ weatherInfo.wind || "--" }}
      <span class="unit">米/秒</span>
    </span>
    <span class="humidity">{{ weatherInfo.humidity || "--" }}%</span>
  </div>
</template>

<script>
// HELP no tree-shaking why ???
// import {get} from 'lodash'
import get from "lodash/get";

export default {
  name: "WeatherCard",
  data() {
    return {
      address: {
        nation: "",
        province: "",
        city: "",
        district: "",
        adcode: ""
      },
      weatherInfo: {
        description: "",
        icon: "",
        temperature: "",
        wind: "",
        humidity: ""
      }
    };
  },
  computed: {
    weatherIconClass() {
      return `eem-icon-${this.weatherInfo.icon}`;
    }
  },

  methods: {
    // 获取当前位置经纬度
    // 腾讯位置服务 https://lbs.qq.com/service/webService/webServiceGuide/webServiceIp
    async getWeatherInfo() {
      $.ajax({
        url: "https://apis.map.qq.com/ws/location/v1/ip?&key=F35BZ-7GHK3-O5Q3L-3EAPP-ZDKWQ-C5BWS&output=jsonp",
        type: "get",
        dataType: "jsonp",
        jsonp: "callback",
        success: data => {
          if (data.status === 0) {
            var lat = get(data, ["result", "location", "lat"], null),
              lng = get(data, ["result", "location", "lng"], null);

            this.address = get(data, "result.ad_info", {});

            var url = `http://api.openweathermap.org/data/2.5/weather?lat=${lat}&lon=${lng}&appid=c9bab64583e3ee085e48f6f0186cf3e6&lang=zh_cn&&units=metric&output=jsonp`;
            $.get({
              url: url,
              type: "get",
              dataType: "jsonp",
              jsonp: "callback",
              success: data => {
                if (data.cod === 200) {
                  const w = get(data, "weather[0]", {});

                  this.weatherInfo = {
                    icon: w.icon,
                    description: w.description,
                    temperature: get(data, "main.temp", null),
                    wind: get(data, "wind.speed", null),
                    humidity: get(data, "main.humidity", null)
                  };
                }
              }
            });
          }
        }
      });
    }
  },
  created() {
    this.getWeatherInfo();
  }
};
</script>

<style lang="scss" scoped>
.weather-card {
  display: flex;
  height: 32px;
  line-height: 32px;
  // @include font_color(T5);
  color: #fff;
  span:not(:first-child) {
    margin-left: 16px;
  }
  .icon-img {
    display: block;
    width: 32px;
    height: 32px;
    border-radius: 32px;
  }
  /*中按钮列表*/
  $mediumbuttonList: "01d", "01n", "02d", "02n", "03d", "03n", "04d", "04n",
    "09d", "09n", "10d", "10n", "11d", "11n", "13d", "13n", "50d", "50n";

  @each $member in $mediumbuttonList {
    .eem-icon-#{$member} {
      background: url("./icons/#{$member}.png") no-repeat center transparent;
      background-size: cover;
    }
  }
}
</style>
