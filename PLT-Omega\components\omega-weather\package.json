{"name": "@omega/weather", "version": "1.0.2", "description": "", "main": "dist/index.umd.min.js", "module": "./index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "build": "vue-cli-service build --target lib --name index --formats umd-min --dest dist ./index.js"}, "keywords": [], "author": "", "license": "ISC", "peerDependencies": {"jquery": "^1.12.4", "vue": "^2.6.14", "lodash": "^4.17.21"}, "devDependencies": {"jquery": "^1.12.4", "vue": "^2.6.14", "lodash": "^4.17.21", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "vue-template-compiler": "^2.6.14"}}