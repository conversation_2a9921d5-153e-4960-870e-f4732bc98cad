/*
 * @Author: your name
 * @Date: 2021-07-27 15:33:15
 * @LastEditTime: 2021-07-30 15:53:00
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\.eslintrc.js
 */
/**
 * @fileoverview http://eslint.org/docs/user-guide/configuring
 */
module.exports = {
  parser: "vue-eslint-parser",
  parserOptions: {
    parser: "@babel/eslint-parser",
    sourceType: "module"
  },
  env: {
    browser: true,
    jquery: true,
    commonjs: true,
    node: true,
    es2021: true
  },
  globals: {
    Vue: "readonly",
    $T: "readonly",
    mhjs: "readonly",
    _: "readonly",
    BMap: "readonly",
    BMapLib: "readonly",
    BMAP_STATUS_SUCCESS: "readonly",
    BMAP_ANCHOR_BOTTOM_RIGHT: "readonly",
    BMAP_NAVIGATION_CONTROL_ZOOM: "readonly"
  },
  // https://eslint.vuejs.org/rules/#priority-b-strongly-recommended-improving-readability-for-vue-js-2-x
  // https://eslint.bootcss.com/docs/rules/
  extends: ["plugin:vue/essential", "eslint:recommended", "prettier"],
  // add your custom rules here
  // "off" 或 0 - 关闭规则
  // "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出)
  // "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
  rules: {
    semi: 0,
    quotes: 0,
    "space-before-function-paren": 0,
    // allow paren-less arrow functions
    "arrow-parens": 0,
    // allow async-await
    "generator-star-spacing": 0,
    // allow debugger during development
    "no-debugger": process.env.NODE_ENV === "production" ? 2 : 1,

    "spaced-comment": 0,

    // allow duplicated imports
    "import/no-duplicates": "off",

    // 关闭vue对prop字段短横线校验
    "vue/attribute-hyphenation": [
      "off",
      "never",
      {
        ignore: []
      }
    ],
    // 已定义, 未使用的变量规则降级为警告.
    "no-unused-vars": [
      "warn",
      { vars: "all", args: "after-used", ignoreRestSiblings: false }
    ]
  }
};
