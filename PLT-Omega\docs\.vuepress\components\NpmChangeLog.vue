<template>
  <div v-html="content"></div>
</template>
<script>
import { http } from "./utils/http.js";
import MarkdownIt from "markdown-it";

const md = new MarkdownIt();

export default {
  name: "change-log",
  props: {
    packageName: {
      type: String,
      require: true
    },
    mdFileName: {
      type: String,
      default: "CHANGELOG.md"
    }
  },
  data() {
    return {
      content: "",
      version: "latest"
    };
  },
  mounted() {
    this.load();
  },
  methods: {
    load() {
      http
        .get(`/unpkg/${this.packageName}@${this.version}/${this.mdFileName}`)
        .then(res => {
          this.content = md.render(res);
        });
    }
  }
};
</script>
