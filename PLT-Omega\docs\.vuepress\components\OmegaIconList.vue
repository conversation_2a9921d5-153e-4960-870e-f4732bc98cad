<template>
  <div class="omega-icon-list">
    <div class="omega-icon-list-group" v-for="group of list" :key="group.id">
      <h4 class="omega-icon-list-groupname">
        {{ group.name }}
      </h4>
      <div class="omega-icon-list-items">
        <div
          class="omega-icon-list-item"
          v-for="item of group.children"
          :key="item.id"
        >
          <div class="omega-icon-list-item-icon" v-html="item.resource"></div>
          <div class="omega-icon-list-item-text">{{ item.name_zh }}</div>
          <div class="omega-icon-list-item-text" :title="item.name">
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { http } from "./utils/http.js";

export default {
  name: "OmegaIconList",
  data() {
    return {
      list: []
    };
  },
  mounted() {
    http.get("/unpkg/@omega/icon@latest/temp/details.json").then(data => {
      this.list = data;
    });
  }
};
</script>
<style lang="scss">
.omega-icon-list-items {
  display: flex;
  flex-wrap: wrap;
}
.omega-icon-list-item {
  margin: 8px;
  width: 90px;
  height: 115px;
  text-align: center;
}
.omega-icon-list-item-icon {
  width: 32px;
  height: 32px;
  margin: 8px auto;
}
.omega-icon-list-item-text {
  // text-align: center;
  overflow: hidden;
  text-overflow: hidden;
}
</style>
