const proxy = require("express-http-proxy");

module.exports = {
  title: "Omega Frame",
  description: "omega 文档",
  themeConfig: {
    smoothScroll: true,
    lastUpdated: "上次更新",
    displayAllHeaders: true,
    nav: [
      { text: "指南", link: "/guide/基础-介绍.md" },
      { text: "API 参考", link: "/api/总文档.md" },
      { text: "组件", link: "/component/基础组件-总体说明.md" },
      { text: "图标库", link: "/icon/图标列表.md" },
      { text: "组件开发", link: "/developer/组件开发指南.md" },
      {
        text: "Issue",
        link: "https://cetsoft-svr1/Platforms/PLT-%E9%9C%80%E6%B1%82%E5%BA%93/_boards/board/t/%E5%B9%B3%E5%8F%B0/%E7%94%A8%E6%88%B7%E6%83%85%E6%99%AF/"
      },
      { text: "变更日志", link: "/changelog/omega-layout.md" }
    ],
    sidebar: {
      "/guide/": [
        {
          title: "基础",
          collapsable: false,
          children: [
            "基础-介绍.md",
            "基础-核心模块.md",
            "基础-扩展模块.md",
            "基础-工具模块.md"
          ]
        },
        {
          title: "深入",
          collapsable: false,
          children: [
            "深入-标准化.md",
            "深入-接入换肤.md",
            "深入-扩展皮肤.md",
            "深入-插件机制.md"
          ]
        },
        {
          title: "项目迁移",
          collapsable: false,
          children: ["项目升级说明.md"]
        }
      ],
      "/component/": [
        {
          title: "基础组件",
          collapsable: true,
          children: [
            "基础组件-总体说明.md",
            "基础组件-通用配置和入参说明.md",
            "基础组件-CetInterface组件说明.md",
            "基础组件-CetTable组件说明.md",
            "基础组件-CetTree组件说明.md",
            "基础组件-CetGiantTree组件说明.md",
            "基础组件-CetForm组件说明.md",
            "基础组件-CetSimpleSelect组件说明.md"
          ]
        },
        {
          title: "业务组件",
          collapsable: true,
          children: [
            "业务组件-CetChart.md",
            "业务组件-dashboard.md",
            "业务组件-trend.md",
            "业务组件-map.md"
          ]
        }
      ],
      "/icon/": [
        {
          title: "图标库",
          collapsable: false,
          children: [
            "说明.md",
            "闲话.md",
            ["图标列表.md", "图标列表"],
            "扩展svg.md"
          ]
        }
      ],
      "/developer/": [
        {
          title: "组件开发",
          collapsable: true,
          children: ["组件开发指南.md"]
        }
      ],
      "/changelog/": [
        {
          title: "变更日志",
          collapsable: false,
          children: [
            {
              title: "核心模块",
              collapsable: false,
              children: [
                ["omega-layout.md", "@omega/layout"],
                ["omega-auth.md", "@omega/auth"],
                ["omega-app.md", "@omega/app"],
                ["omega-http.md", "@omega/http"],
                ["omega-i18n.md", "@omega/i18n"],
                ["omega-theme.md", "@omega/theme"],
                ["omega-widget.md", "@omega/widget"]
              ]
            },
            {
              title: "扩展模块",
              collapsable: false,
              children: [["omega-admin.md", "@omega/admin"]]
            },
            {
              title: "组件",
              collapsable: false,
              children: [
                ["omega-dashboard.md", "@omega/dashboard"],
                ["omega-trend.md", "@omega/trend"],
                ["cet-common.md", "cet-common"],
                ["cet-chart.md", "cet-chart"]
              ]
            }
          ]
        }
      ]
    }
  },
  plugins: [
    {
      name: "omega-docs-proxy",
      // 自定义的代理插件
      // 使用示例
      // 以 yapi 为例
      // yapi Get 请求 mock地址为 http://192.168.131.47:3000/mock/11/bff/v1/project/list
      // axios 使用时： axios.get("/yapi/mock/11/bff/v1/project/list")
      beforeDevServer(app, server) {
        const proxys = new Map([
          ["/unpkg", "http://10.12.135.149:8090"],
          ["/yapi", "http://10.12.135.149:3000"],
          ["/omega-icon", "http://10.12.135.149:7878"]
        ]);

        proxys.forEach((value, key) => {
          app.use(
            key,
            proxy(value, {
              proxyReqPathResolver(req) {
                const url = req.path;
                return url;
              },
              userResDecorator(proxyRes, proxyResData, userReq, userRes) {
                if (userRes.statusCode === 301 || userRes.statusCode === 302) {
                  const location = userRes.getHeader("location");
                  userRes.setHeader("location", key + location);
                }
                return proxyResData;
              }
            })
          );
        });
      }
    }
  ]
};
