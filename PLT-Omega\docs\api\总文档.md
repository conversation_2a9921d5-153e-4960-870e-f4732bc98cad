---
sidebar: auto
---

# Api

为方便表示所有模块对外的对象统一用驼峰命名的缩写，以@omega/layout 为例，缩写为 omegaLayout

## @omega/layout

### toggleSideBarCollapse

- `toggleSideBarCollapse(isSidebarCollapse: <PERSON>ole<PERSON>)`

切换侧边栏收起展示

## @omega/auth

### login

- `login({userName, password})`

登录，主要为登录界面调用

### logout

退出当前用户

### checkPermission

- `checkPermission(permission: String)`

检查用户是否拥有某项页面权限或者操作权限

### isRootUser

- `isRootUser()`

判断当前登录用户是否为 root 用户

### user

用户访问信息工具类的实例

```js
class User {
  isRoot() {
    return util.isRoot(this._user);
  }

  getUserId() {
    return this._user.id;
  }

  getUserName() {
    return this._user.name;
  }

  getUserCustomConfig() {
    const customConfig = this._user.customConfig;
    if (this._usercustomConfig) {
      return this._usercustomConfig;
    }

    return (this._usercustomConfig =
      (customConfig && JSON.parse(customConfig)) || {});
  }

  getUserTenantId() {
    return this._user.tenantId;
  }

  getUserModelNodes() {
    return this._user.modelNodes;
  }

  getRelativeUserGroup() {
    return this._user.relativeUserGroup || [];
  }

  getRole() {
    return (this._user.roles && this._user.roles[0]) || {};
  }

  getRoleId() {
    const role = this.getRole();
    return role.id;
  }

  getRoleCustomConfig() {
    const customConfig = this.getRole().customConfig;
    return (customConfig && JSON.parse(customConfig)) || {};
  }

  getRolePageNodes() {
    return this.getRole().pageNodes ?? [];
  }

  getRoleAuths() {
    return this.getRole().auths;
  }

  isSameUser(id) {
    return this.getUserId() === id;
  }
}
```

## @omega/http

### HttpBase

- http 基础类

### http

- 不带 loading 效果的 `json` 数据请求

### httping

- 带 loading 效果的 `json` 数据请求

:::danger
`http`和`httping` 默认`responseType:"json"`, 如果你请求的数据是文本格式，需要将`responseType"`设置为 `responseType: "text"`（e.g.pecreport 报表接口返回的为 `html`文本）。
:::

### download

- 以二进制的方式下载文件的方法封装（`method: "POST", responseType: "blob"`）

## @omega/theme

### theme

- 获取/设置当前皮肤

```js
// 获取
let theme = omegaI18n.theme;
...

// 设置(会更新localstorage并刷新界面)
omegaI18n.theme = "dark";
```

## @omega/i18n

### locale

- 获取/设置当前语言

```js
// 获取
let locale = omegaI18n.locale;
...

// 设置(会更新localstorage并刷新界面)
omegaI18n.locale = "en";
```
