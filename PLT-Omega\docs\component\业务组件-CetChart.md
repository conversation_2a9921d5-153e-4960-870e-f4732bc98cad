# CetChart 组件

CetChart 封装了 echarts 图表，通过 vue 数据驱动的方式进行使用，实现数据展示功能；

CetInterface 组件进行模型接口数据查询获取，两者结合可以完成常用的图表展示功能

如果不适用模型接口数据，需要自定义接口调用的，可以单独通过代码实现接口调用

## 调用组件

参见框架代码中 src\components\chartdemo\ChartDemo.vue 文件中 CetChart 组件的使用方式，可以通过代码模板进行快捷编写

```vue
<template>
  <div class="page">
    <CetChart v-bind="CetChart_test"></CetChart>
  </div>
</template>

<script>
export default {
  data() {
    return {
      CetChart_test: {
        inputData_in: [],
        options: {
          legend: {},
          tooltip: {},
          //初始值设置方法如下
          // dataset: {
          //   source: [
          //     { product: "Matcha Latte", "2015": 43.3, "2016": 85.8, "2017": 93.7 },
          //     { product: "Milk Tea", "2015": 83.1, "2016": 73.4, "2017": 55.1 },
          //     { product: "Cheese Cocoa", "2015": 86.4, "2016": 65.2, "2017": 82.5 },
          //     { product: "Walnut Brownie", "2015": 72.4, "2016": 53.9, "2017": 39.1 }
          //   ]
          // },
          xAxis: { type: "category" },
          yAxis: {},
          series: [
            { type: "bar", encode: { x: "product", y: "2015" } },
            { type: "bar", encode: { x: "product", y: "2016" } },
            { type: "bar", encode: { x: "product", y: "2017" } }
          ]
        }
      }
    };
  },
  activated: function () {
    setTimeout(() => {
      this.CetChart_test.inputData_in = [
        { product: "Matcha Latte", 2015: 43.3, 2016: 85.8, 2017: 93.7 },
        { product: "Milk Tea", 2015: 83.1, 2016: 73.4, 2017: 55.1 },
        { product: "Cheese Cocoa", 2015: 86.4, 2016: 65.2, 2017: 82.5 },
        { product: "Walnut Brownie", 2015: 72.4, 2016: 53.9, 2017: 39.1 }
      ];
    }, 2000);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
```

以下为对 echarts 图表的封装属性和方，更多详细信息请参考 [ECharts 的 API 文档](https://www.echartsjs.com/zh/api.html#echarts)。

## Props _（均为响应式）_

- `inputData_in`
  用来设置 echarts 实例的数据，格式为数据集格式，可以参考 echarts 官网教程关于数据集部分章节

- `initOptions`

  用来初始化 ECharts 实例。

- `theme`

  当前 ECharts 实例使用的主题。

- `options`

  ECharts 实例的数据。修改这个 prop 会触发 ECharts 实例的 `setOption` 方法。

  如果直接修改 `options` 绑定的数据而对象引用保持不变，`setOption` 方法调用时将带有参数 `notMerge: false`。否则，如果为 `options` 绑定一个新的对象，`setOption` 方法调用时则将带有参数 `notMerge: true`。

  例如，如果有如下模板：

  ```html
  <v-chart :options="data" />
  ```

  那么：

  ```
  this.data = newObject // setOption(this.options, true)
  this.data.title.text = 'Trends' // setOption(this.options, false)
  ```

- `group`

  实例的分组，会自动绑定到 ECharts 组件的同名属性上。

- `autoresize` （默认值：`false`）

  这个 prop 用来指定 ECharts 实例在组件根元素尺寸变化时是否需要自动进行重绘。

- `manual-update` （默认值：`false`）

  在性能敏感（数据量很大）的场景下，我们最好对于 `options` prop 绕过 Vue 的响应式系统。当将 `manual-update` prop 指定为 `true` 且不传入 `options` prop 时，数据将不会被监听。然后，你需要用 `ref` 获取组件实例以后手动调用 `mergeOptions` 方法来更新图表。

## 计算属性

- `width` **[只读]**

  用来获取 ECharts 实例的当前宽度。

- `height` **[只读]**

  用来获取 ECharts 实例的当前高度。

- `computedOptions` **[只读]**

  用来读取 ECharts 更新内部 `options` 后的实际数据。

## 方法

- `mergeOptions`（底层调用了 ECharts 实例的 `setOption` 方法）

  _提供了一个更贴切的名称来描述 `setOption` 方法的实际行为。_

- `appendData`
- `resize`
- `dispatchAction`
- `showLoading`
- `hideLoading`
- `convertToPixel`
- `convertFromPixel`
- `containPixel`
- `getDataURL`
- `getConnectedDataURL`
- `clear`
- `dispose`

## 静态方法

- `connect`
- `disconnect`
- `registerMap`
- `registerTheme`
- `graphic.clipPointsByRect`
- `graphic.clipRectByRect`

## 事件

事件根据需要手动绑定事件，这里模板上不默认绑定事件。

Vue-ECharts 支持如下事件：

- `legendselectchanged`
- `legendselected`
- `legendunselected`
- `legendscroll`
- `datazoom`
- `datarangeselected`
- `timelinechanged`
- `timelineplaychanged`
- `restore`
- `dataviewchanged`
- `magictypechanged`
- `geoselectchanged`
- `geoselected`
- `geounselected`
- `pieselectchanged`
- `pieselected`
- `pieunselected`
- `mapselectchanged`
- `mapselected`
- `mapunselected`
- `axisareaselected`
- `focusnodeadjacency`
- `unfocusnodeadjacency`
- `brush`
- `brushselected`
- `rendered`
- `finished`
- 鼠标事件
  - `click`
  - `dblclick`
  - `mouseover`
  - `mouseout`
  - `mousemove`
  - `mousedown`
  - `mouseup`
  - `globalout`
  - `contextmenu`
