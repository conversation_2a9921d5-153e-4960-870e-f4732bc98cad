# Dashboard 组件

Dashboard 功能是提供给用户, 对系统总览信息进行自定义展示的功能, 可以通过灵活的自定义设置, 达到根据自己的需要,展示系统中相关数据的需求.

# 如何在项目中使用

## 升级到 npm 包版本 dashboard 注意要点

### 安装@omega/dashboard

`npm install @omega/dashboard`

### 在 src\main.js 中注册预定义组件

```javascript
//注册dashboard预定义组件
import { registerComponent } from "@omega/dashboard";
import componentList from "@/plugins/dashboard/componentList";
registerComponent(componentList);
```

### 引入 dashboard 的方式改为引入包

```javascript
//使用
<CetDashboard dashboard-title="看板" :tree-node-model="treeNodeModel" />
//引入
import CetDashboard from "@omega/dashboard";

//全屏模式下使用
<fullScreenDb />

import { fullScreenDb } from "@omega/dashboard";

```

### projectId 需要存入 localstorage

在能源管理云平台等解决方案中, 是平台/项目的结构, 通过 projectId 来区别当前是哪个项目, 项目之间要做数据隔离, dashboard 也要进行数据隔离

为了跨 npm 包进行数据共享, 将 projectId 规范化, 约定存储在 localstorage 的 projectId 字段中

项目开发中需要在 projectId 变化时, 将其存储在 localstorage 中, 这样 Dashboard 组件才能根据 projectId 获取指定项目的 dashboard 页面

### 对外依赖 http auth common 包

Dashboard 组件对外依赖 http auth common 等基础包, 基础包不可缺失

## 预定义组件

预定义组件需要进行额外的预定义组件的开发工作, 定制性更高, 能满足的场景也更多, 应用人员配置起来更简单.

### 预定义组件配置

参见如下配置说明

```javascript
export default [
  {
    id: "pue",
    name: "PUE",
    viewCmp: () => import(`./pue/view`),
    cfgCmp: () => import(`./pue/config`),
    paltform: true, //用于平台和项目区分的字段
    tag: "电能质量" //增加tag配置项, 相同的tag会作为一个分组展示, 只支持一层分组
  },
  {
    id: "test",
    name: "测试",
    viewCmp: () => import(`./test/view`),
    cfgCmp: () => import(`./test/config`),
    route: "trenddemo", //指定跳转到其他路由的配置
    tag: "能耗管理"
  }
];
```
