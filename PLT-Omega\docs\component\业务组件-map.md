# 地图组件

## 功能说明

cetmap 地图组件基于 echarts，以及 bmap 百度地图 api 实现

## 代码示例

```javascript
// template模板
	<CetMap
      style="height: calc(100% - 40px)"
      v-bind="CetMap_A"
      v-on="CetMap_A.event"
    ></CetMap>

// data配置
  CetMap_A: {
        customMark_in: false, //地图选点
        districts_in: [  //高亮行政区
          ["湖北", "#f5f5f5", "#03a9f4"],
          ["重庆", "#B9B4C8", "#F09ABD"]
        ],
        configMapControls(Bmap) {//地图控件
          return [
            {
              name: "NavigationControl",
              position: {
                offset: new Bmap.Size(10, 30)
              }
            },
            {
              name: "ScaleControl",
              position: {
                anchor: BMAP_ANCHOR_BOTTOM_RIGHT
              }
            }
          ];
        },
        districtsBorder_in: {
          //行政区边框
          //配置边框色、大小，如需每色块边框等不同则在districts_in每一项中加配置
          strokeColor: "#aaaaaa",
          strokeWeight: 1
        },
        bounds_in: { //限制显示区域
          // sw: [116.027143, 39.772348],
          // ne: [116.832025, 40.126349]
        },
        options: {
          bmap: {
            // center: [104.114129, 37.550339], //中心点
            // zoom: 5, //显示层级
            // roam: true,
            mapStyle: { //皮肤配置
              style: "midnight"
              // styleJson: [
              //   {
              //     featureType: "water",
              //     elementType: "all",
              //     stylers: {
              //       color: "#044161"
              //     }
              //   },
              // ]
            }
          },
          tooltip: {
            type: "item"
          },
          series: []
        },
        event: {
          customPoint_out: this.CetMap_A_customPoint_out
        }
      }
	 // methods
	 CetMap_A_customPoint_out(val) {}//输出选点的经纬度、地点名称
```

## 地图打点

在地图批量显示点，直接使用 echarts 的配置实现
如以下示例：data 为展示的数据，series 为设置点的展示类型，以及使用的坐标系

注：在使用地图组件时 coordinateSystem 必须配置为 bmap

```javascript
var data = [
  { name: "福田总部", value: [114.03392, 22.540907] },
  { name: "深圳北区", value: [114.269113, 22.597795] },
  { name: "武汉研发", value: [114.458176, 30.425081] }
];
this.CetMap_A.options.series = [
  {
    name: "中电",
    type: "effectScatter", //带有涟漪特效动画的散点
    coordinateSystem: "bmap", //该系列使用的坐标系 bmap
    data: data //点数据
  }
];
```

## 地图选点

点击、拖动取点标识，可以获取点位置经纬度信息
只需配置 customMark_in：true 即可设置为选点
customPoint_out 输出经纬度及地点名称

注：选点不能与批量显示点、路径绘制等一起组合使用

```javascript
	CetMap_A: {
		customMark_in: false,
    	event: {
    		customPoint_out: this.CetMap_A_customPoint_out
    	}
	 }
```

## 行政区域高亮描边

行政区配置背景色、边框色等，凸显配置的行政区
如以下示例：districts_in 配置突出省市，数组第一个值为行政区名称，第二个：背景填充色，第三个为边框色，第四个边框大小；
数组前 2 项必配，边框色、边框大小可通过 districtsBorder_in 统一配置，不配置则使用组件内置默认值

```
     CetMap_A: {
        customMark_in: false,
        districts_in: [["湖北", "#f5f5f5", "#03a9f4"],
        ["重庆", "#B9B4C8", "#F09ABD"]],
        districtsBorder_in: {//行政区边框
          //配置边框色、大小，如需每色块边框等不同则在districts_in每一项中加配置
          strokeColor: "#aaaaaa",
          strokeWeight: 1
       	},
		}
```

## 路径绘制

可绘制起点到终点，并进行绘制线段，可绘制多线段
如以下示例：coordr 运维人员的行走路径，configData 配置项数据，包含点、线段 2 个配置，通过循环路径数据，生成 series 完整数据

```javascript
// 多个线段、路径绘制
let coordr = [
  {
    name: "张三",
    value: [
      [114.2592716217, 30.5616327403],
      [114.26728, 30.549565],
      [114.2530059814, 30.5378686275],
      [114.254722, 30.550605]
    ]
  },
  {
    name: "李四",
    value: [
      [114.03392, 22.540907],
      [114.269113, 22.597795]
    ]
  }
];
let series = [];
let configData = [
  {
    type: "scatter", // 坐标点数据
    coordinateSystem: "bmap",
    zlevel: 2,
    rippleEffect: {
      brushType: "stroke"
    },
    label: {
      normal: {
        show: true,
        position: "right",
        formatter: "{b}"
      }
    },
    symbolSize: 20,
    showEffectOn: "render",
    itemStyle: {
      normal: {
        color: "#2EC7C9"
      }
    }
  },
  {
    type: "lines",
    coordinateSystem: "bmap", // 线连接，  只需要坐标，为 起点和终点
    zlevel: 2,
    polyline: true, //是否多线段
    effect: {
      show: true,
      period: 6,
      // color: "#a10000",
      trailLength: 0,
      symbol: this.icon,
      symbolSize: [20, 12]
    },
    lineStyle: {
      normal: {
        color: "#5AB1EF",
        width: 1,
        opacity: 0.4,
        curveness: 0
      }
    }
  }
];
//循环coordr生成series
coordr.forEach((item, i) => {
  //配置项
  let obj = _.cloneDeep(configData);
  //起点、终点经纬度
  obj[0].data = [
    {
      name: item.name,
      value: item.value[0] //起点
    },
    {
      name: item.name,
      value: item.value[item.value.length - 1] //终点
    }
  ];
  obj[0].name = item.name;
  obj[1].data = [
    //绘制线段
    {
      coords: item.value
    }
  ];
  series.push(obj[0], obj[1]);
});
//赋值
this.CetMap_A.options.series = series;
```

## 配置类

### 限制显示区域范围

限制地图显示区域，超出会自动弹回可视区，配置项 bounds_in

```javascript
范围配置 sw左下角、ne右上角
 bounds_in: {
          sw: [116.027143, 39.772348],
          ne: [116.832025, 40.126349]
        },
```

### 地图控件

配置一个函数提供给组件，组件会根据配置添加对应的地图控件，支持批量

```javascript
CetMap_A: {
    configMapControls(Bmap) {
          return [
            {
              name: "NavigationControl",//缩放控件
              position: {
                offset: new Bmap.Size(10, 30)
              }
            },
            {
              name: "ScaleControl", //比例尺
              position: {
                anchor: BMAP_ANCHOR_BOTTOM_RIGHT
              }
            }
          ];
        },
	}
```

### 换肤

地图的皮肤通过 bmap 中 mapStyle 进行配置，支持配置默认的十二种匹配，和自定义配置，自定义配置参考百度地图 api 中自定义样式
且可通过 styleJson 配置建筑物图例配置

```javascript
两种方式选其一
12种主题配置，在mapSatyle配置主题style名称即可
自定义配置，styleJson中配置
        options: {
          bmap: {
            // center: [104.114129, 37.550339],
            // zoom: 5,
            // roam: true,
            mapStyle: {
              style: "midnight" //主题配置
			  styleJson: [ //自定义配置
              //   {
              //     featureType: "water",
              //     elementType: "all",
              //     stylers: {
              //       color: "#044161"
              //     }
              //   },
              //   {
              //     featureType: "land",
              //     elementType: "all",
              //     stylers: {
              //       color: "#004981"
              //     }
              //   },
			  	...
			  	]
			  }
			}
		}
```
