# 趋势曲线组件

趋势曲线用于设备数据定时记录数据的趋势展示, 在各个项目中比较常见
对于需要趋势曲线分析, 历史曲线分析的功能, 直接采用本组件即可, 不需要进行定制化的开发

```javascript
//template模板
    <CetTrend
      :queryMode="CetTrend_test.queryMode"
	  :dataConfig="CetTrend_test.dataConfig"
	  :queryTime_in="CetTrend_test.queryTime_in"
	  :params_in="CetTrend_test.params_in"
	  :interval_in="CetTrend_test.interval_in"
	  :title_in="CetTrend_test.title_in"
	  :exportImgName_in="CetTrend_test.exportImgName_in"
	  :queryTrigger_in="CetTrend_test.queryTrigger_in"
	  :clearTrigger_in="CetTrend_test.clearTrigger_in"
	  :scatter_in="CetTrend_test.scatter_in"
	  @scatterClick_out="CetTrend_test_scatterClick_out"
      v-bind="CetTrend_test.config"
    ></CetTrend>

//data配置
     CetTrend_test: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
		  //趋势曲线查询接口配置
          queryUrl: "/devicedata/api/v1/batch/datalog/span/group",
          type: "POST"
        },
		//查询触发入参
        queryTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        // 格式为{timeType:1,time:null} time字段中一个是起始时间，两个第一个是起始时间，第二个是终止时间,初始化查询需要设置该值, 时间格式可以为momentjs可以解析的时刻
        queryTime_in: {
          timeType: 1,
          time: [new Date("2020/08/02"), new Date("2020/08/03")]
        },
		//配置趋势曲线左上角的title展示
        title_in: "趋势曲线",
		//趋势曲线的参数配置, 为一个对象数组, 有几条曲线就配置几个对象, 每个对象包含属性如下所示, 为设备数据对应的参数, 名称用于曲线的legend展示
		interval_in: 60,  //整数值, 0或者1不进行抽点, 2或者大于2的值按设置值分钟间隔进行抽点
		exportImgName_in: "测试导出图片名称", //设置图表导出图片的名称, 可以动态设置
		scatter_in: [
		{ x: 1599771600000, a: 33, fe: 34, ffe: 33 },
        { x: 1599782400000, fefg: 33, gh: 33 },
        { x: 1599787080000, bcd: 34, efg: 33 }],
		//自定义打点数据, 比如PQ里面展示暂态事件点
		//数据格式类似上面, 其中x属性为时间戳, 必填, 其他属性可以随意, 后面点击该点会返回对象数据
        params_in: [
          {
            dataId: 2,
            dataName: "A相电压",
            dataTypeId: 1,
            dataTypeName: "实时值",
            deviceId: 6,
            deviceName: "节点1",
            logicalId: 1,
			unit: "V", //曲线的单位
            upperLimit: 5, //曲线上限值
            lowerLimit: 1  //曲线下限值
          },
          {
            dataId: 1000001,
            dataName: "电流",
            dataTypeId: 1,
            dataTypeName: "实时值",
            deviceId: 6,
            deviceName: "节点1",
            logicalId: 1,
			unit: "A",
            upperLimit: 5,
            lowerLimit: 1
          },
          {
            dataId: 1,
            dataName: "频率",
            dataTypeId: 1,
            dataTypeName: "实时值",
            deviceId: 6,
            deviceName: "节点1",
            logicalId: 1,
			unit: "Hz",
            upperLimit: 5,
            lowerLimit: 1
          }
        ],
        config: {
		  //echarts的颜色序列配置, 修改各条曲线颜色
          color: ["red", "black", "green"],
		  //是否显示表格按钮
          showTableButton: true,
		  //是否显示图例
          showLegend: true,
		  //是否显示极值按钮
          showExtremButton: true,
		  //是否显示平均线按钮
          showAverageButton: true,
		  //是否显示打点按钮
          showPointButton: true,
		  //是否显示限值线按钮
		  showLimitButton: true,
          //是否显示原始标记按钮
		  showRawMarkButton: true,
		  //是否显示差值按钮
		  showDiffButton: true,
		  //设置限值展示文本
          limitText: {
            buttonText: "国标限值",
            upperLimitText: "国标上限",
            lowerLimitText: "国标下限"
          }
        }
      },

//method
	CetTrend_test_scatterClick_out(val) {
      //这里在用户点击了scatter_in指定的点后, 返回该点数据,可以基于此做一些业务处理
    }

```
