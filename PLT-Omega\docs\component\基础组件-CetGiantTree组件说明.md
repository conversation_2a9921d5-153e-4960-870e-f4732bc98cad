# CetGiantTree 组件

## 功能说明

基于 ztree 封装，将节点单选、多选、模糊搜索的功能封装到组件中，并对相关的操作通过事件可以获取到单选、勾选状态的数据 ###代码示例

```javascript
//template模板
    <CetGiantTree
		v-bind="CetGiantTree_left"
		v-on="CetGiantTree_left.event"
		:searchText_in.sync="CetGiantTree_left.searchText_in"
     ></CetGiantTree>

//data配置
      CetGiantTree_left: {
        inputData_in: [], //tree数据
        checkedNodes: [], //指定勾选的节点，单项输入{ tree_id: "linesegmentwithswitch_2" }
        selectNode: {
          //选中节点
          tree_id: "project_42"
        },
        // ztree配置，参考ztree官网
        setting: {
          check: {
            enable: true, //多选，不配置则默认单选，多选时，以下二个选项可不配置均会使用默认值
            chkStyle: "checkbox", //勾选类型
            chkboxType: { Y: "ps", N: "ps" } //父子节点关联关系
          },
          data: {
            simpleData: {
              enable: true,使用简单数据模式
              idKey: "tree_id" //唯一标识属性名
            }
            // 选配
            // key: {
            //   name: "name", //节点名称
            //   children: "children"//子节点数组属性名
            // }
          }
          // callback: { //捕获勾选取消勾选前的回调
          //   // beforeCheck: this.CetGiantTree_left_zTreeBeforeCheck
          // }
        },
        searchText_in: "", //模糊搜索字段
        event: {
          //事件
          created_out: this.CetGiantTree_left_created_out, //ztree对象，当组件无法满足需求，可直接操作ztree对象实现
          currentNode_out: this.CetGiantTree_left_currentNode_out, //输出选中节点
          checkedNodes_out: this.CetGiantTree_left_checkedNodes_out //输出tree显示节点的勾选数据（不包含半选）
        }
      },

//methods
	//勾选取消勾选前的回调
	CetGiantTree_left_zTreeBeforeCheck(treeId, treeNode) {
      return !treeNode.isParent; //当是父节点 返回false 不让选取
    },
    CetGiantTree_left_created_out(val) {
      this.treeObj = val; //存储ztree对象
    },
    CetGiantTree_left_currentNode_out(val) {},
    CetGiantTree_left_checkedNodes_out(val) {},

//内置方法
	//获取所有勾选节点（不含半选节点）
	this.$refs.CetGiantTree_left.AllCheckedNode()

	注：需根据合适的应用场景使用
	例如：在弹窗中，搜索、勾选，如果不需要即时展现给用户查看，则只需在最后保存是调用一次即可；
		如每点击一次节点需要在页面展示所有的数据，可在checkedNodes_out输出中去触发调用
```
