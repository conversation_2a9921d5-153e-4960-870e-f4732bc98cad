# CetInterface 组件

## 功能说明

```javascript
//template模板
<CetInterface
	 :data.sync="CetInterface_test.data"
	 :dynamicInput.sync="CetInterface_test.dynamicInput"
	 v-bind="CetInterface_test"
	 v-on="CetInterface_test.event"
 ></CetInterface>

//data配置
     CetInterface_test: {
        queryMode: "diff", //配置方式详见通用配置项目说明文档
        data: [], //该属性为双向绑定值, 为interface组件查询接口后获取的数据,可以直接用于展示
        dataConfig: {
          queryFunc: "", //调用的接口方法名,接口名称在api\custom.js中注册
          modelLabel: "project", //查询数据的主模型, 比如这里查项目列表, 主模型就是project,可以找项目建模人员或者项目经理了解模型的名称
          dataIndex: [], //需要的模型字段名,比如需要name, code 字段, 这里就配置'name','code' 不配置会获取所有模型字段
          modelList: [], //需要的关联模型, 比如要把项目关联的配电室查询出来, 这里就可以配置上配电室的modelLabel
		  //filters字段配置需要进行后端接口筛选的字段, name字段是自定义的, 避免prop层级较深,出现.符号不好处理,
		  // operator 有小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN" 这几种
		  //prop就是要筛选的字段的path路径,支持room_model[0].name这样的层级
          filters: [
            { name: "name_in", operator: "LIKE", prop: "projectname" },
            { name: "code_in", operator: "LIKE", prop: "code" }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: { name_in: "", code_in: "" },
        page_in: { index: 1, limit: 20 }, //{index: 1,limit: 20}
        event: {
          result_out: this.CetInterface_test_result_out,
          finishTrigger_out: this.CetInterface_test_finishTrigger_out
        }
      }
//输出方法
 CetInterface_test_finishTrigger_out(val) {},
 CetInterface_test_result_out(val) {},
```

## hideNotice 不显示遮罩框

```javascript
//首先在interface组件里设置hideNotice为true
//如果是自定义接口则需要设置将该配置放到headers中, 该配置会作为第二个入参传递给api里的函数,例子如下:

export function queryModel(data, hideNotice) {
  return fetch({
    url: `/model/${modelVersion}/query`,
    method: "POST",
    headers: { hideNotice: hideNotice },
    transformResponse: [processResponse], //对接口返回的数据结构进行处理
    timeout: 60000,
    data
  });
}
```

## 关键字搜索多个字段(table 组件同样支持)

```javascript
//比如一个关键字搜索框,需要搜索名称或者编码中包含123的工单,将prop配置为需要筛选的字段列表, tagid配置为数字, filters配置如下:

        dataConfig: {
          queryFunc: "",
          modelLabel: "pmworksheet",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "name_in", operator: "LIKE", prop: ["projectname", "code"], tagid: 1 }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
```

## 或关系搜索(table 组件同样支持)

```javascript
//比如需要搜索名称包含123, 或者编码中包含456的工单, 这种或关系筛选的配置需将两者的tagid配置为相同的数值 filters配置如下:

        dataConfig: {
          queryFunc: "",
          modelLabel: "pmworksheet",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "name_in", operator: "LIKE", prop: "projectname", tagid: 1 }
			{ name: "code_in", operator: "LIKE", prop: "code", tagid: 1 }
          ],
          orders: [],
          treeReturnEnable: false,
          hasQueryNode: false,
          hasQueryId: false
        },
```

---

## 配置项

### 通用配置项

具体说明参见组件通用配置和入参说明

| 配置项          | 含义             | 类型   |
| --------------- | ---------------- | ------ |
| queryMode       | 查询模式         | String |
| queryNode_in    | 节点查询条件     | Object |
| queryId_in      | id 查询条件      | Object |
| queryTrigger_in | 表格查询触发输入 | Number |
| dynamicInput    | 筛选输入         | Object |

### 特有配置项

| 配置项      | 含义                     | 类型     | 默认值 | 说明                                                                                         |
| ----------- | ------------------------ | -------- | ------ | -------------------------------------------------------------------------------------------- |
| data        | interface 获取的数据     | [Object] |        | 该属性为双向绑定值, 为 interface 组件查询接口后获取的数据,可以直接用于展示                   |
| hideNotice  | 调用接口时是否显示遮罩框 | Boolean  | false  | true 不显示遮罩框, false 显示遮罩框; 自定义接口需要在 api 里设置 headers                     |
| page_in     | 表格默认筛选方式         | Object   |        | 配置接口的分页查询参数, 配置例子:{ index: 1, limit: 20 } ,index 为索引, limit 为最大查询条数 |
| defaultSort | 默认筛选方式             | Object   |        | 配置默认筛选规则, 参见表格组件的该配置项                                                     |

### dataConfig 配置项

dataConfig 配置项单独说明

| 配置项           | 含义                   | 类型     | 默认值 | 说明                                                                                            |
| ---------------- | ---------------------- | -------- | ------ | ----------------------------------------------------------------------------------------------- |
| modelLabel       | 模型标识               | String   |        | 参见通用组件配置说明文档                                                                        |
| queryFunc        | 查询数据               | String   |        | 这里是 interface 组件调用的接口注册名                                                           |
| dataIndex        | 数据属性列表           | [String] |        | 参见通用组件配置说明文档                                                                        |
| modelList        | 次级模型列表           | [String] |        | 参见通用组件配置说明文档                                                                        |
| filters          | 筛选配置               | [Object] |        | 参见通用组件配置说明文档                                                                        |
| hasQueryNode     | 是否有 queryNode       | Boolean  | true   | 参见通用组件配置说明文档                                                                        |
| hasQueryId       |                        | String   |        | 参见通用组件配置说明文档                                                                        |
| treeReturnEnable | 是否按树形接口返回数据 | Boolean  | false  | 配置接口是否以树形数据返回数据,用于和树组件配合展示树形数据, modelList 对应的为树形数据的子层级 |

### event 事件

| 事件名            | 说明               | 参数   |
| ----------------- | ------------------ | ------ |
| result_out        | 输出组件获取的数据 |        |
| finishTrigger_out | 接口调用返回后输出 | 时间戳 |
| failTrigger_out   | 接口错误后输出     | 时间戳 |
| totalNum_out      | 输出数据总条数     |        |
