# CetSimpleSelect 组件

## 功能说明

基于 Elselect、 CetInterface 组件的封装, 适用于简单、常规的下拉选择，例如枚举值等

数据获取已实现，只需配置即可，

## 代码示例

```javascript
//template模板
 <CetSimpleSelect
	v-model="CetSimpleSelect_data.value"
	v-bind="CetSimpleSelect_data"
	v-on="CetSimpleSelect_data.event">
</CetSimpleSelect>

//data配置
	CetSimpleSelect_data: {
        value: "", //数据双向绑定
        option: {
          key: "id",
          value: "id",
          label: "text",
          disabled: "disabled"
        },
        interface: {
          queryMode: "diff",//查询按钮触发trigger，条件变化立即查询diff
          data: [],//数据双向绑定值, 为interface组件查询接口后获取的数据,可以直接用于展示
          dataConfig: {
            queryFunc: "queryEnum",//调用的接口方法名,接口名称在api\custom.js中注册
            modelLabel: "mu_chemicalindustry",//，查询数据的主模型
            dataIndex: [],//需要的模型字段
            modelList: [],
            filters: [],
            orders: [],
            treeReturnEnable: false,
            hasQueryNode: false,
            hasQueryId: false
          },
          queryNode_in: null,
          queryId_in: -1,
          queryTrigger_in: new Date().getTime(),
          dynamicInput: {}
        },
        event: {
          change: this.CetSimpleSelect_data_change_out //选中后输出当前
        }
      },
//输出方法
	CetSimpleSelect_data_change_out(val) {}
```

## 配置项

请参考 CetInterface、element

### event 事件

请参考 element
