# CetTable 组件

## 功能说明

CetTable 基于 el-table 组件进行封装, 配合 ElTableColumn 使用
封装了分页, 数据查询,筛选, 本地新增,本地编辑, 本地删除,批量删除（本地、接口） 导出功能.

```javascript
//template模板
<CetTable :data.sync="CetTable_right.data" :dynamicInput.sync="CetTable_right.dynamicInput" v-bind="CetTable_right" v-on="CetTable_right.event">
	<!-- <ElTableColumn v-bind="ElTableColumn_selection"></ElTableColumn> -->
	<ElTableColumn v-bind="ElTableColumn_index"></ElTableColumn>
	<ElTableColumn v-bind="ElTableColumn_name"></ElTableColumn>
	<ElTableColumn v-bind="ElTableColumn_address"></ElTableColumn>
	<ElTableColumn v-bind="ElTableColumn_createtime"></ElTableColumn>
</CetTable>

//data配置
    CetTable_right: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "backendInterface", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          exportFunc: "",
          deleteFunc: "",
          modelLabel: "pmworksheet",
          dataIndex: [],
          modelList: [],
          filters: [
            { name: "name_in", operator: "LIKE", prop: "projectname" },
            { name: "code_in", operator: "LIKE", prop: "code" },
            { name: "createtime_in", operator: "between", prop: "createtime" }
          ], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: false // 是否有queryNode入参, 没有则需要配置为false
        },
        showSummary: true,
        defaultSort: {
          prop: "projectname",
          order: "descending",
          children: [{ prop: "code", order: "descending", children: [{ prop: "createtime", order: "descending" }] }]
        },
        data: [],
        dynamicInput: {
          name_in: "",
          code_in: "",
          createtime_in: [new Date(0).getTime(), new Date().getTime()]
        },
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
		localDeleteTrigger_in: new Date().getTime(),
        batchDeletionTrigger_in: new Date().getTime(),
        localBatchDeletionTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: true,
		showPaginationUI:true,
        exportFileName: common.exportFilename,
        event: {
          record_out: this.CetTable_right_record_out,
          outputData_out: this.CetTable_right_outputData_out,
		  multiSelectData_out: this.CetTable_right_multiSelectData_out, //输出勾选的表格数据
          "select-all": this.abc
        }
      },
//输出方法
  CetTable_right_outputData_out(val) {},
  CetTable_right_record_out(val) {},
```

## 关键字搜索多个字段

参见 CetInterface 组件说明

## 或关系搜索

参见 CetInterface 组件说明

## 配置项

### 通用配置项

具体说明参见组件通用配置和入参说明

| 配置项          | 含义             | 类型   |
| --------------- | ---------------- | ------ |
| queryMode       | 查询模式         | String |
| dataMode        | 数据获取模式     | String |
| queryNode_in    | 节点查询条件     | Object |
| queryTrigger_in | 表格查询触发输入 | Number |
| dynamicInput    | 筛选输入         | Object |

### 特有配置项

| 配置项                       | 含义                              | 类型            | 默认值               | 说明                                                                                                                           |
| ---------------------------- | --------------------------------- | --------------- | -------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| data                         | 表格数据                          | Array           | []                   | 支持.sync 双向同步, component 模式下 通过赋值给 data, 表格组件展示 data 的数据; 因为双向同步,可以通过 data 取到表格的数据      |
| hideNotice                   | 调用接口时是否显示遮罩框          | Boolean         | false                | true 不显示遮罩框, false 显示遮罩框; 自定义接口需要在 api 里设置 headers                                                       |
| defaultSort                  | 表格默认筛选方式                  | Object          |                      | 配置默认筛选规则, 配置方法参考 elementui 官网文档,支持 children 属性配置, 用于多级筛选的场景,参见代码例子                      |
| exportTrigger_in             | 导出 excel                        | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行导出 excel 逻辑, 目前导出支持简单表格导出, 多级表头,合并单元格, 合计行不支持导出 |
| deleteTrigger_in             | 接口删除                          | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行删除逻辑, 通过接口执行删除当前选中的行                                           |
| localDeleteTrigger_in        | 本地删除                          | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行本地删除逻辑,删除当前选中行, 只在前端删除数据, 不调用接口                        |
| batchDeletionTrigger_in      | 接口批量删除                      | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行批量删除逻辑, 通过接口执行删除勾选的行                                           |
| localBatchDeletionTrigger_in | 本地批量删除                      | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行批量本地删除逻辑,只在前端删除勾选的行                                            |
| refreshTrigger_in            | 刷新数据                          | Number          | new Date().getTime() | 为一个时间戳数值, 入参的值发生了变化, 就会执行刷新逻辑, 刷新时不会重置分页的页码,比如第 5 页表格刷新后还是在第 5 页            |
| addData_in                   | 表格增加一行数据                  | Object          |                      | 将 addData_in 的数据添加到表格中                                                                                               |
| editData_in                  | 编辑表格中的数据                  | Object          |                      | editData_in 数据会对当前选中行的数据进行编辑                                                                                   |
| showPagination               | 是否显示分页                      | Boolean         |                      | 配合模型接口规范可以方便的实现后端分页                                                                                         |
| showPaginationUI             | 是否界面上显示分页栏,保留分页逻辑 | Boolean         |                      |                                                                                                                                |
| paginationCfg                | 分页栏自定义配置                  | Object          |                      | 配置项和 el-pagination 一致, 可以在需要对分页栏做自定义展示时使用                                                              |
| exportFileName               | 导出 excel 文件名设置             | Function/String |                      | 支持模板字符串, 支持自定义函数                                                                                                 |

### dataConfig 配置项

dataConfig 配置项单独说明

| 配置项       | 含义             | 类型     | 默认值 | 说明                            |
| ------------ | ---------------- | -------- | ------ | ------------------------------- |
| modelLabel   | 模型标识         | String   |        | 参见通用组件配置说明文档        |
| queryFunc    | 查询数据         | String   |        | 参见通用组件配置说明文档        |
| dataIndex    | 数据属性列表     | [String] |        | 参见通用组件配置说明文档        |
| modelList    | 次级模型列表     | [String] |        | 参见通用组件配置说明文档        |
| filters      | 筛选配置         | [Object] |        | 参见通用组件配置说明文档        |
| hasQueryNode | 是否有 queryNode | Boolean  | true   | 参见通用组件配置说明文档        |
| deleteFunc   | 删除数据         | String   |        | api\custom 文件中注册的接口名称 |

### event 事件

| 事件名              | 说明                     | 参数               |
| ------------------- | ------------------------ | ------------------ |
| record_out          | 输出表格当前选中行的数据 | 表格选中行对象     |
| outputData_out      | 输出表格所有数据         | 表格数据, 数组类型 |
| multiSelectData_out | 输出勾选的表格数据       | 表格数据, 数组类型 |
