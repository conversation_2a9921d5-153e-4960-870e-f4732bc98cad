# CetTree 组件

## 功能说明

基于 el-tree 封装, 将节点筛选功能封装到组件中, 并将勾选, 单选封装成通过 props 设置, 并通过事件获取树的勾选, 单选状态

数据获取需要通过 cet-interface 组件来获取, 配置示例详见下面的代码

## 代码示例

```javascript
//template模板
 <CetTree
	 :selectNode.sync="CetTree_Left.selectNode"
	 :searchText_in.sync="CetTree_Left.searchText_in"
	 :checkedNodes.sync="CetTree_Left.checkedNodes"
	 v-bind="CetTree_Left"
	 v-on="CetTree_Left.event"
 >
 </CetTree>

//data配置
     CetTree_Left: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [{ tree_id: "abc_33" }], //要包含nodeKey属性, 例:[{ tree_id: "project_67" }]
        filterNodes_in: null, //[]会清空过滤
        searchText_in: "",
        showFilter: true,
        nodeKey: "tree_id",
        props: {
          label: "code",
          children: "children"
        },
        highlightCurrent: true,
        event: {
          currentNode_out: this.CetTree_Left_currentNode_out,
          parentList_out: this.CetTree_Left_parentList_out,
          checkedNodes_out: this.CetTree_Left_checkedNodes_out,
          halfCheckNodes_out: this.CetTree_Left_halfCheckNodes_out,
          allCheckNodes_out: this.CetTree_Left_allCheckNodes_out
        }
      },
//输出方法
    CetTree_Left_currentNode_out(val) {
      this.CetTable_right.queryNode_in = this._.cloneDeep(val);
    },
    CetTree_Left_parentList_out(val) {},
    CetTree_Left_checkedNodes_out(val) {},
    CetTree_Left_halfCheckNodes_out(val) {},
    CetTree_Left_allCheckNodes_out(val) {},
```

## cet-interface 代码示例

```javascript
//template模板
 <CetInterface
	 :data.sync="CetInterface_tree.data"
	 :dynamicInput.sync="CetInterface_tree.dynamicInput"
	 v-bind="CetInterface_tree"
	 v-on="CetInterface_tree.event"
></CetInterface>

//data配置
     CetInterface_tree: {
        queryMode: "diff", //查询条件变化，立即查询
        data: [],
        dataConfig: {
          queryFunc: "",
          modelLabel: "project", //第一层级节点的modelLabel
          dataIndex: [],
          modelList: ["room"], //设置树包含的其他模型
          filters: [],
          orders: [],
          treeReturnEnable: true, //这里需要设置为true
          hasQueryNode: false,
          hasQueryId: false
        },
        queryNode_in: null,
        queryId_in: -1,
        queryTrigger_in: new Date().getTime(),
        dynamicInput: {},
        event: {
          result_out: this.CetInterface_tree_result_out,
          finishTrigger_out: this.CetInterface_tree_finishTrigger_out
        }
      },
//输出方法
    CetInterface_tree_finishTrigger_out(val) {},
    CetInterface_tree_result_out(val) {
      this.CetTree_Left.inputData_in = this._.cloneDeep(val); //将接口返回的数据传递给树组件
    },
```

## 配置项

可以使用 el-tree 里面的属性配置项

| 配置项         | 含义                                                 | 类型     | 默认值    | 说明                                                                    |
| -------------- | ---------------------------------------------------- | -------- | --------- | ----------------------------------------------------------------------- |
| inputData_in   | 树组件数据                                           | Array    | []        | 可以从外部设置节点树的展示数据                                          |
| selectNode     | 设置单选选中的树节点                                 | Object   |           | 需要包含 nodeKey 指定的属性, 例:{ tree_id: "city_53" }                  |
| checkedNodes   | 设置树勾选框勾选节点                                 | [Object] | []        | 要包含 nodeKey 属性, 例:[{ tree_id: "project_67" }]                     |
| filterNodes_in | 筛选树的节点列表                                     | [Object] | null      | 数据结构和 checkedNodes 一致, 要包含 nodeKey 数据, 用于对树节点进行筛选 |
| searchText_in  | 设置文本搜索框的值并筛选节点                         | String   |           | 设置该值可以设置树的文本筛选框的值, 并对树进行筛选                      |
| showFilter     | 是否显示树的文本筛选框                               | Boolean  | true      | 可以控制是否展示顶部的文本筛选框                                        |
| nodeKey        | 每个树节点用来作为唯一标识的属性，整棵树应该是唯一的 | String   | 'tree_id' | 设置树的唯一识别 id 属性, 模型接口默认提供了 tree_id 作为树的唯一属性   |

### event 事件

| 事件名             | 说明                               | 参数 |
| ------------------ | ---------------------------------- | ---- |
| currentNode_out    | 输出当前单选选中的节点对象         |      |
| parentList_out     | 输出单选选中节点对象及其父级节点   |      |
| checkedNodes_out   | 输出勾选的节点数组, 不包含半勾节点 |      |
| halfCheckNodes_out | 输出半勾节点数组                   |      |
| allCheckNodes_out  | 输出所有的勾选节点, 半勾节点       |      |

### Scoped Slot

| name | 说明                                                                              |
| ---- | --------------------------------------------------------------------------------- |
| —    | 自定义树节点的内容，参数为 { node, data } , 和 el-tree 中使用作用域插槽的方法一样 |
