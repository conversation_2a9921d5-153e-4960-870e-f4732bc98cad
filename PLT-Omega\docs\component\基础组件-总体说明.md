# 总体说明

## 概述

基础 UI 组件以 element-ui 为基础, 可以在其官网上了解其组件的用法, https://element.eleme.cn/#/zh-CN/component/installation

公司内部封装的组件是基于 elementui 的 UI 组件进行的高阶封装

**在开发中, 如果采用 elementui 组件更高效, 可以随时采用 elementui 进行开发**

## 封装的内容

##### 1.将 elementui 之前需要通过 ref 以方法操作组件的方式封装为完全的通过数据驱动的方式进行校验

比如 elementui 的树设定选中行的实现方式:

```javascript
setCheckedKeys() {
	this.$refs.tree.setCheckedKeys([3]);
},
```

封装后的选中树的指定节点:

```javascript
CetTree_Left.selectNode = { tree_id: "floor_35" };
```

通过赋值的方式, 去指定要选中的节点即可

##### 2.将模型接口查询和写入的规则进行封装, 可以直接调用模型接口, 减少重复的入参组织和接口调用工作

比如 cet-table 表格组件封装了表格通过模型接口进行数据查询, 删除, 排序, 分页等操作.

##### 3.对 elementui 的 select, input 等控件写法进行约定和规范, 便于进行可视化以及统一写法

##### 4.业务组件封装, 比如趋势曲线组件, 组态图形 CetGraph 组件, CetWaveform 波形组件, Dashboard 组件

## 基础组件列表

**接口:**

1. 接口调用组件 CetInterface

**数据展示**

1. 表格组件 CetTable 表格列组件 CetColumn
2. 树组件 CetTree
3. 大数据量树组件: CetGiantTree

**用户输入**

1. 表单组件 CetForm
2. 下拉组件 CetSelect
3. 下拉选项 CetOption
4. 文本输入框 CetInput
5. 数值输入框 CetInputnumber
6. 勾选框 CetCheckbox
7. 勾选框组 CetCheckgroup
8. 勾选框组内勾选框列表 CetChecklist
9. 单选框组 CetRadiogroup
10. 单选组内单选框列表 CetRadiolist
11. 下拉集成组件 CetSimpleSelect
12. 穿梭框组件 CetTransfer

**布局**

1. 弹窗组件 CetDialog
2. 标签页组件 CetTabs
3. 边框布局组件 CetAside

**操作**

1. 按钮组件 CetButton
2. 滚动条组件 CetScroller
3. 时间查询组件 CetDateSelect

## 组件引入,更新

采用 omega-template 项目模板即可, 会注册基础组件为全局组件.
更新时 update cet-common 包即可

## 组件使用方式

所有组件均可以通过代码模板快速使用, 这里通过一个录屏视频先直观了解一下在代码中写组件的操作方式, 以写表格组件为例:
[表格组件引入示例.exe](http://191.0.0.158:4999/server/../Public/Uploads/2021-03-04/60402a7e80d33.exe "[表格组件引入示例.exe")

代码中一个组件包含 template, data, method 三个部分, 引入时, 可以一键设置其唯一标识符, 保持这三个部分标识符一致即可.

## 组件配置

组件引入后, template 部分,不需要再进行配置, 只要放到正确的位置, 布局结构正确即可;
主要是对其 data 的内容进行配置,以及在 method 部分进行组件交互的连接.

### data 配置

自定义的配置项参见各个组件的配置项说明

这里需要注意的是, 我们的组件都是基于 elementui 进行封装的, 组件所对应的 elementui 组件有的配置项, 均可以自定义添加该配置项, 并配置相应的值

```javascript
     CetTable_right: {
        border:true, // 这里就是添加的是否带有纵向边框配置项,这里开启纵向边框显示
        showHeader:false, //这里要注意的是,elementui文档里的是show-header, 在js中可以直接写驼峰showHeader

        queryMode: "diff",
        dataMode: "backendInterface",
        dataConfig: {
        },
        ...
      },
```

### method 方法

主要用来实现页面上的各种交互, 这里同样的, 组件对应的 elementui 组件的事件, 均可以自定义添加, 添加方式为 data 里面的 event 字段添加, method 方法需要和配置的方法名对应

```javascript
     CetTable_right: {
        queryMode: "diff",
        dataMode: "backendInterface",
        dataConfig: {
        },
		event:{
		  record_out: this.CetTable_right_record_out,
          outputData_out: this.CetTable_right_outputData_out,

          "select-all": this.abc  //elementui表格组件的select-all事件,这里命名要用短横线连接,有短横线的属性名要用双引号包裹,属性名大小写不敏感, 和elementui的文档保持一致,
		}
        ...
      },

  methods: {
    abc(val) {  //添加相应的事件处理方法
      console.log("selection");
    }
	...
```
