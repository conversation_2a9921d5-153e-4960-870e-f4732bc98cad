# 通用配置和入参说明

组件之间存在较多的通用配置项, 主要是和接口数据相关的, 在这里进行统一说明, 这样在其他组件中不再进行详细说明.

## dataMode

该配置项在 cettable,cetform 中存在, 设置组件数据获取模式, 可配置项:

- backendInterface
  数据从后端接口获取

- component
  数据从其他组件传递过来

- static
  数据为配置的静态数据

## queryMode

当 dataMode 为 backendInterface, 从接口获取数据时, 需要配置 queryMode 项, 可配置项:

- trigger
  组件的入参 queryTrigger_in 发生变化时, 执行查询操作, 对应的场景是点击查询按钮后, 表格进行查询

- diff
  组件的 queryNode_in queryId_in dynamicInput 任一个查询条件发生变化后, 即执行查询
  **在 diff 模式下, queryTrigger_in 发生变化依然会执行查询**

## queryNode_in

先定义 web 框架中的 node 对象结构, 我们所有涉及到的 queryNode, treeNode 等等均是一个 node 结构:

```javascript
{
	id:34,
	modelLabel:'project'
}
```

其中 id 和 modelLabel 是必须包含的字段, node 对象中也可以包含有其他额外字段

queryNode_in 的应用场景是根据一个节点去查其他类型的数据, 比如查询一个项目下的所有设备, 这时 queryNode_in 就是指定的项目节点.

## queryId_in

查找指定 id 的数据, 为数值 number 类型; 其典型使用场景是查询表格中某一行数据的详情数据
queryId_in 和 queryNode_in 一般不会同时使用, 因为是两种不同场景下的查询方式,

- form 只有 queryId_in 入参
- table 只有 queryNode_in 入参
- interface 则是两者均有.

## queryTrigger_in

可以接收任意格式数据, 一般为一个时间戳数值, 该入参传进来值不重要, 只有入参的值发生了变化, 就会执行查询数据的逻辑

## refreshTrigger_in

和 queryTrigger_in 入参类似, 会触发组件数据刷新操作,按当前条件重新从接口获取数据

## dynamicInput

筛选条件入参, 数据格式为一个对象, 具备的属性和 dataConfig.filters 配置里面的 name 字段一一对应;
修改里面的属性值, 就相当于修改相应的筛选查询条件;
如果想要设置筛选查询条件的默认值, 可以直接设置其属性值, 这样就会将该值初始化为该筛选条件的默认值:
比如下面的配置, 就会将 name_in 这个筛选条件 默认值设置为"中电", 一开始查询就会去筛选名称带有"中电"的数据.

```javascript
dynamicInput: {
  name_in: "中电",
  code_in: ""
},
```

## dataConfig

### dataConfig 配置项

| 配置项       | 含义                 | 类型     | 默认值 | 说明                                                                                                                         |
| ------------ | -------------------- | -------- | ------ | ---------------------------------------------------------------------------------------------------------------------------- |
| modelLabel   | 模型标识             | String   |        | 指定查询数据的模型标识, 比如查询项目列表, 其 modelLabel 为 project, 这里配置为 project 即可                                  |
| queryFunc    | 查询数据             | String   |        | 查询调用的接口方法名,接口名称在 api\custom.js 中注册                                                                         |
| dataIndex    | 数据属性列表         | [String] |        | 涉及到模型中的字段名                                                                                                         |
| modelList    | 次级模型列表         | [String] |        | 除了 modelLabel,还需要查询的次级模型列表, 比如同时查询 project 以及 building 和 floor 模型, 则 这里配置['building', 'floor'] |
| filters      | 筛选配置             | [Object] |        | 下面章节详细说明                                                                                                             |
| hasQueryNode | 入参是否有 queryNode | Boolean  |        | 用于入参有效性校验                                                                                                           |
| hasQueryId   | 入参是否有 queryId   | Boolean  |        | 用于入参有效性校验                                                                                                           |

### filters

```javascript
          filters: [
            { name: "name_in", operator: "LIKE", prop: "projectname" },
            { name: "code_in", operator: "LIKE", prop: "code" }
          ],
```

filters 字段配置需要进行后端接口筛选的字段, name 字段是自定义的, 避免 prop 层级较深,出现.符号不好处理

operator 有小于 "LT" 小于等于 "LE" 大于 "GT" 大于等于"GE" 相等 "EQ" 不等于 "NE" 像"LIKE" 间于"BETWEEN" 包含 "IN"这几种
IN 的参数为一个数组, 确定筛选范围
BETWEEN 参数为一个数组, 前闭后闭

prop 就是要筛选的字段的 path 路径,支持 keywords.name 这样的层级筛选
