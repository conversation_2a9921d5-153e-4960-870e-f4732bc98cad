# 组件开发指南

## 概述

本文档结合实例，对组件开发规范进行说明。
主要对基于[monorepo](https://cetsoft-svr1/Platforms/PLT-Matterhorn/_git/omega-repo?path=%2F&version=GBmaster&_a=history)库下的组件进行详细说明, 对于不放在 monorepo 中管理, 直接放在单独 git 库中的组件, 参照本文档管理, 进行精简即可

## 开始

1. 安装 pnpm : `npm install -g pnpm`

2. 在项目根目录执行 `pnpm install`

3. 运行模板 `pnpm dev:tpl` 跑起来看 template 文件夹 demo 效果

## 组件使用

基于私有 npm 库(http://10.12.135.149:4873/) 进行组件发布, 形式和其他第三方 npm 包一致
根据私库中组件的 readme 说明文档进行使用和安装

组件的更新记录在单独的在线文档中进行说明, 发布到钉钉群时, 会自动提取更新记录并给出更新记录的链接供查看

## 组件命名

类似@omega/xxxx
以@omega/开头

## 组件代码

### 代码库

基础组件和重要的业务组件统一放到[monorepo](https://cetsoft-svr1/Platforms/PLT-Omega/_git/PLT-Omega?path=%2F&version=GBdevelop&_a=contents)库中

这里需要了解一下 monorepo 的概念, 即把多个独立的 npm 包代码放在一个 git 库中进行管理, 这里利用了 pnpm 的 workspace 功能
可以通过其官网详细了解
不熟悉没关系,涉及到具体的操作命令, 会在本文档详细说明

### monorepo 代码库目录

- `components` 存放业务组件，和 packages 区别是 packages 中的功能包是项目所必需的, components 中的业务包并不是每个项目都需要, 项目根据需要引入, 例如 cet-chart omega-dashboard omega-trend 等

- `packages` 存放 omega 架构的必备功能包, 包含权限, 基础 UI, http 接口请求, 国际化, 主题, 框架布局等.

- `template` 为项目模板 [omega-template](https://cetsoft-svr1/Platforms/PLT-Omega/_git/PLT-OmegaTemplate) `omega-repo` 分支, 方便进行本地调试。同时方便代码模板发布（具体见下面的模板发布）

- `docs` 文档目录，基于 vuepress

### 组件编码

#### 基于 git 分支管理的流程

一个 Git 库包含 master 分支一个, develop 分支一个, feature 特性分支 N 个

master 分支和 develop 分支都不能直接提交, 需要通过平台的 pull request 方式提交合并代码请求

主线 master 分支稳定, 主要是对外发布, 由 git 库管理员负责从 develop 分支审阅和合并

feature 特性分支开发完成后, 代码合并到开发分支 develop, 由审阅者进行审阅和批准后合并, 目前可以自己提交 PR 申请, 自己合并

**创建分支**

所有的组件开发, bug 修复工作开始前, 先在 azure 平台创建一个独立分支(后续都以 dashboard 分支为例说明), 比如 dashboard 分支

通过`git checkout dashboard` 命令, 切换 dashboard 分支上进行开发, 进行正常的 git pull 和 push

**开发完成后, 要先进行合并 develop 分支 代码的工作, 在本地解决可能的冲突**
首先合并 develop 分支 代码到 dashboard 分支

`git merge origin/develop`

这里要加上 origin 指定从远程合并, 否则只是合并本地的 develop 分支代码
有冲突的话, 在本地解决冲突, 完成后, 再提交解决冲突后的代码.

这样就在 dashboard 分支上完成了和 develop 分支最新代码的同步工作

**合并分支代码到 develop 分支**

最后在 azure 平台提交 dashboard 分支合并到 develop 分支的 pull request, 这时进行合并就会比较流畅, 因为 dashboard 分支已经合并了主线最新代码, 不容易有冲突问题.

#### 创建组件

可以从已有 git 库导入, 可以带提交记录迁移
也可以复制一个已有的组件代码, 进行调整

##### 迁移已有 git 库到 omega-repo 进行代码管理

另外为方便项目迁移, 临时引入了 lerna, 借助 lerna import 迁移现有项目（如果想折腾也可以自己借助 git 迁移）。

示例:
先把目标导入 git 库文件夹改为实际需要导入的文件夹名称, 比如 Dashboard 改为 omega-dashboard, 如果一样就不改, 然后执行命令

```
lerna import F:\Azure\dashboardCmp\Dashboard --dest ./componets
```

#### 组件目录结构

推荐结构如下:

src: 组件代码目录

lib: 组件打包后目录

README.md 组件说明文档, 该文档会显示在私库中, 因此要说明组件用法和功能概述等,可以加一个开发说明文档

CHANGELOG.md 更新日志文档, 可以自动生成, 后续具体说明

vue.config.js 用于打包的配置, 后续具体说明

#### 组件 package.json 配置

组件最关键的配置是 package.json 文件, 可以参考其他组件来配置, 有几个要点:
private:false 发布的组件不能为私有

main 配置为包的入口文件

files 配置需要发布到 npm 库上的文件和文件夹

peerDependencies 外部使用组件包时需要安装的其他包, 平行依赖

dependencies 组件自己单独依赖的包

注意不要进行隐式依赖, 比如依赖一个依赖包的依赖, 要把 import 依赖的包直接配置在 dependencies 中

devDependencies 一般是组件打包时需要的依赖

#### 组件 commit 提交规范

示例：
**注意:** 提交说明末尾不要带标点, : 后面要有一个空格

```
fix(omega-theme): 修复了xxx问题

```

规范:

```
<commit-type>[(commit-scope)]: <commit-message>
<commit-type> 常见为：
  build：主要目的是修改项目构建系统(例如 glup，webpack，rollup 的配置等)的提交
  ci：主要目的是修改项目继续集成流程(例如 Travis，Jenkins，GitLab CI，Circle等)的提交
  docs：文档更新
  feat：新增功能
  fix：bug 修复
  perf：性能优化
  refactor：重构代码(既没有新增功能，也没有修复 bug)
  style：不影响程序逻辑的代码修改(修改空白字符，补全缺失的分号等)
  test：新增测试用例或是更新现有测试
  revert：回滚某个更早之前的提交
  chore：不属于以上类型的其他类型(日常事务)
[(commit-scope)] 可选，表示范围，例如：refactor(cli)，表示关于 cli 部分的代码重构。
<commit-message> 提交记录的信息，有些规范可能会要求首字母大写。

// 示例：
(注意末尾不要带标点)
fix(omega-theme): 修复了xxx问题

```

#### 跨组件变量共享

对于跨组件共享的变量, 比如指定当前是哪个项目的 projectID 值, 最好是放到 localstorage 中存储.

#### 组件样式

组件样式需要支持和外部项目同步换肤, 如果有 elementui 以外的颜色, 间距等样式设置, 采用 [tailwindcss](https://www.tailwindcss.cn/docs/text-color) 来设置组件的样式, 具体规则参考 tailwindcss 官网中的规则

扩展的颜色, 间距等变量参见 template\tailwind.config.js 文件

使用时, 可以安装 tailwindcss 插件 Tailwind CSS IntelliSense, 方便书写 tailwindcss 样式

组件样式写法参考:

```javascript
 //设置文字色为T1
 <div class="tool-bar text-T1">

//设置文字色为ZS主色
  <i
    v-show="mode === 'edit'"
    class="el-icon-edit text-ZS"
    @click="handleEdit(getChartItem(item.i))"
  />

```

## 组件调试和运行

### pnpm workspace 下的依赖方式

在 pnpm workspace 中, 对于配置在 package.json 中的依赖, 如果名称匹配, 会直接依赖 components 和 packages 中存在的包
这样可以根据最新版本的包进行调试

对于打包后的 npm 包, 包入口文件是打包后的 js 入口文件,
通过调整 package.json 文件的 main 文件为组件代码入口, 从而可以直接修改代码后调试,方便开发

然后通过 publishConfig 来配置打包后的 js 入口文件, pnpm 执行发布命令时, 会自动用 publishConfig 下的字段替换 main 的配置

例子:

```javascript
 "main": "src/index.js", //代码入口
  "publishConfig": {
    "main": "lib/omega-dashboard.umd.min.js" //打包后入口文件
  },
```

### template 目录调试方式说明

template 目录即为项目开发模板, 所有组件的 demo 调试代码都存放在这里, 按照项目中实际使用的方式, 在该目录下创建功能进行调试和测试即可

运行调试方式参见开始章节的步骤

## 组件发布

组件开发完成, 并自测通过后, 走发布的流程将组件发布到 npm 私库上

需要注意的是, 发布到私库上的 npm 包需要和 develop 主线上的代码保持一致, 不能出现 dashboard 分支上的代码发布到了私库, 但是 develop 分支上的代码是更低版本的情况

流程说明如下:

这里以在 dashboard 组件中修改一个问题后, 如何发布为例进行流程说明:

**组件开发人员**

1. 首先, 在 dashboard 分支中修改代码, 确认修改完成后修改,该组件的 package.json 文件中的 version, 一般小功能修改最后一位版本号, +1 即可.
2. 写本次修改的更新记录文件 CHANGELOG.md, 该更新记录将会在钉钉群中通知的时候展示,并通过链接展示该组件的所有历史更新记录. 更新记录示例:

   ```markdown
   # @omega/trend

   ## 1.2.4

   2023-7-20

   ### 缺陷修复

   - 4086246: fixed:修复趋势曲线保留小数的显示功能，表格头部显示数据单位

   ## 1.2.3

   2023-6-7

   ### 新功能

   - feat: 添加 splitLine 分割线配置
   ```

3. 对于需要打包的 npm 包, 执行打包命令生成打包后的内容, 具体操作详见组件打包章节, 这里执行打包命令
   `pnpm --filter @omega/dashboard build`

4. 提交代码到 dashboard 分支代码库, 然后按照 git 分支管理流程, 在 azure 平台发起 pull request 到 develop 分支;

至此, 组件开发的工作完成, 后续的**正式发布必须在 develop 分支进行**, 这样才能保证私库的程序是对应的 develop 分支代码

**develop 主线维护人员**

下面的工作应该由主线维护人员来进行操作, 做好审核, 尤其是初次开发组件的代码的人员, 应该让主线维护人员审查代码后发布包到私库, 熟悉以后可以自己切换到 develop 分支来发布包.

1. 审核合并到 develop 分支的 pull request, 检查确认代码没有问题后通过合并请求;
2. 切换到 develop 分支下, `git pull`获取最新代码.
3. 运行 `pnpm --filter @omega/dashboard publish` 完成包发布到私库

### 组件 readme 文档

发布时,在 readme 中写明组件功能和用法, 或者详细说明文档地址

## 组件打包

通过组件打包, 可以减少组件大小, 混淆组件代码, 提高环境兼容性, 这里介绍通过 vuecli 进行打包的方式

### 利用 vuecli 进行打包

1. package.json 中的 devDependencies 依赖配置, 把打包需要的库引入,例子如下:

```javascript
  "devDependencies": {
    "@vue/cli-plugin-babel": "~4.5.0",
    "@vue/cli-service": "~4.5.0",
    "autoprefixer": "^9.8.8",
    "babel-eslint": "^10.1.0",
    "core-js": "^3.6.5",
    "extract-loader": "^5.1.0",
    "postcss": "^7.0.39",
    "sass": "^1.26.5",
    "sass-loader": "^9.0.3",
    "vue-template-compiler": "^2.6.14"
  }
```

2. 配置打包命令 build, 根据自己的组件名称, 调整--name 参数配置, 代码入口统一为 src/index.js, 打包生成物路径为 lib
   例子如下:

```javascript
"scripts": {
    "build": "vue-cli-service build --target lib --name omega-dashboard --dest lib --formats umd-min --report src/index.js"
  },
```

3. 配置好打包后的组件包入口, 这里是配置在 publishConfig 中的 main 字段, 按照规范配置后, 这里调整名称即可

```javascript
  "main": "src/index.js",
  "publishConfig": {
    "main": "lib/omega-dashboard.umd.min.js"
  },
```

4. 配置 vue.config.js 配置文件, 这里通过配置实现两个功能

- 将 css 文件合并到 js 中,避免外部需要再引入一次 css 文件
- 将独立的第三方依赖通过 externals 配置排除, 避免打包后文件过大
  具体配置方法参考下例

```javascript
const config = {
  css: {
    extract: false
  },
  configureWebpack: config => {
    return {
      externals: {
        "@omega/http": "commonjs2 @omega/http",
        "@omega/auth": "commonjs2 @omega/auth",
        "@omega/icon": "commonjs2 @omega/icon",
        "element-ui": "commonjs2 element-ui",
        "cet-common": "commonjs2 cet-common",
        "cet-chart": "commonjs2 cet-chart",
        echarts: "commonjs2 echarts",
        lodash: "commonjs2 lodash",
        moment: "commonjs2 moment",
        "core-js": "commonjs2 core-js",
        vuedraggable: "commonjs2 vuedraggable",
        "vue-grid-layout": "commonjs2 vue-grid-layout",
        xlsx: "commonjs2 xlsx",
        "file-saver": "commonjs2 file-saver"
      }
    };
  }
};

module.exports = config;
```

5. 以上都配置完成后, 即可进行组件打包, 通过 pnpm 的 filter 命令, 可以在库的根目录对指定的组件包进行打包

`pnpm --filter @omega/dashboard build`

## 高级

1. 为什么选择 pnpm ?

pnpm 解决了隐式调用问题，保证依赖包被正确的依赖和引入。

不同于 vue-cli 使用的 yarn, vue3，vite 均使用 pnpm, 方便后续升级到 vue3 和 vite 实现无缝的升级。

目前 node 版本为 14.x, 内置 npm 为 6.x 版本, 但 npm 7.0 版本以上才支持 workspace。 升级本地 npm 会改变现有开发环境 势必会对旧有的维护项目产生不可预知的问题。

pnpm 文档中文支持相当友好 https://pnpm.io/zh/motivation

fix: 根目录中 package.json 依赖了 regenerator-runtime 兼容 vue-cli 的依赖问题。后续不需要了可以删除
