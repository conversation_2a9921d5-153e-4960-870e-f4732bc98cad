# 介绍

## omega 是什么

omega 是一套项目开发解决方案。基于 npm 私库之上建立起来一个具有高度的横向扩展能力和版本管理的组织架构。

## 为什么要升级

- 插件化

- 开放的生态

- 独立软件功能包

- 平滑的升级体验

- 新特性...

- 细粒度的版本管理

- 完善的发布体制

## omega 组织架构说明

对管理系统进行了细分，将我们的管理系统组成拆分成了以下

1. **应用布局（omega-layout）**

负责项目应用的主体布局。布局支持高度的可配置，包括右上角工具栏及设置列表都提供了渲染函数入口。同事其他部分也支持显示/隐藏。

2. **鉴权（omega-auth）**

负责项目鉴权功能，将权限服务进行了进一步的封装，提供了指令`v-permission`及方法`checkPermission()`来检查是否拥有操作权限和页面权限。另外对登录功能做了封装暴露了相关方法接口直接使用即可。初鉴权外还对外提供了**用户信息**的管理和访问对象。

3. **插件机制（omega-app）**

在 vue 根示例挂载之前，会去加载相关插件，支持异步等待。

4. **网络请求（omega-http）**

对 axios 进行了封装，包括具有去重功能的消息提示，loading，鉴权失败的回调。

5. **皮肤（omega-theme）**

皮肤采用的是是集中化管理，将 elementui、echarts 的多套 css 都纳入在这个包内。同事提供了 sass/tainwind 换肤相关变量

6. **国际化（omega-i18n）**

当前项目仅需要中英文，国际化采用全部引入的方式。

7. **组件库（omega-widget、cet-common）**

通用的业务组件库

8. **在线配置功能（omega-admin）**

提供了导航菜单及 logo 图片在线配置

9. **开发体验提升（omega-cli-devserver）**

提供了本地的私有代理配置，防止提交到代码库。通过合理配置可以支持多个代理环境

提供了模拟用户登录功能。告别手动登录

10. **持续集成（omega-cli-devops）**

打包环境优化体验提升，对接多个现有平台助力开发提升工作效率。
