# 工具模块

## 持续集成

简化打包配置流程，提升打包使用体验和工作效率。

::: tip
npm 私库: [@omega/cli-devops](http://*************:4873/-/web/detail/@omega/cli-devops)
:::

### 主要功能

1. Docker 镜像库管理

   1.1 版本自动化管理 - 支持自动/手动版本号管理。

   2.1 镜像仓库占用资源管理

   受镜像仓库服务器资源限制，不能无限制的存储所有版本的镜像，默认会保存 5 个最新的镜像，以往的镜像会自动清理调。不需要像以往一样担心项目镜像仓库爆了，也不需要自己去手动删除

2. 服务通知

   2.1 钉钉群消息通知

   对接了钉钉自定义机器人的群消息通知，打包成功后发布相关消息通知。消息内容包括镜像名称、docker 私库项目跳转链接，jenkins 日志链接, 代码变更记录，代码变更对比链接，另外可以通过配置@所有人或者通过手机号@指定人员（群内）。

   ![钉钉通知内容](./assets/dingding.png)

3. 代码质量平台自动对接

   3.1 开发和质量平台规则的一致性

   开发的代码质量检查和平台保持了一致，（全部借由 eslint 检查和生成报告上传至 sonar 平台）

   3.2 版本对接

   镜像库版本和 sonar 代码检查版本保持一致。可以清晰的看到历史版本代码质量情况及走势

4. Azure Devops 平台

   查看对比提交代码。

5. 代码变更记录

   5.1 代码提交记录说明

   对接 jenkins,钉钉消息中的代码变更记录，为两次成功构建之间的代码提交记录。

6. 一键下载镜像功能。

### 使用说明

该命令行工具运行环境为 jenkins。所以使用是在 jenkins 打包任务中使用。

1. 检查 `.omega-cli/dockerContext/nginx.conf` 相关服务是否都配置好了。（一般情况下仅需要在项目初始阶段配置后端业务服务。）

2. 配置`.omega-cli/build.conf.js`。 包括镜像库名称和版本号，及钉钉机器人。[钉钉自定义机器人创建详细步骤](http://191.0.0.158:4999/web/#/5?page_id=140)

3. 新建 jenkins 打包任务。选择复制`template-webframe`项目。新建成功后修改代码仓库配置即可。

### 主要命令

- omega-cli-devops init

  初始化打包需要的配置文件。文件位于项目根目录 `.omega-cli` 目录下。

  文件列表：

  - .omega-cli/dockerContext/Dockerfile - docker image 构建配置文件
  - .omega-cli/dockerContext/nginx.conf - nginx 配置文件
  - .omega-cli/build.conf.js - 自动化脚本配置文件，详细见文件配置。

* omega-cli-devops devops [uri]

启动自动化脚本打包。其中 uri 参数为是本地开发调试使用(具体见命令行解释)。

- omega-cli-devops start [--url] [--vite]

--url 跟上 uri]
--vite 如果使用的 vite 打包 （版本要求>= 0.0.7） 需要添加 --vite 参数 （即 omega-cli-devops devops --vite）

## 开发环境优化

:::tip
npm 私库: [@omega/cli-devserver](http://*************:4873/-/web/detail/@omega/cli-devserver)
:::

提升开发体验

### 主要功能

1. 模拟用户，跳过频繁的登录

   模拟网关服务，解析但不验证 token，添加 HTPP 消息头部 User-ID。 自动模拟签发 token 实现跳过登录

   > 注意: 一旦开启了模拟跳过登录，每次刷新界面都会通过注入的 js 脚本向 sessionStorage 中插入 omega_token, 如果想通过账号登录，重新配置`omegaCliDevserver.mockAuth=false`后启动，如果你知道用户 ID 的话也可以直接修改`omegaCliDevserver.mockAuthUserId`后重新启动。

2. 多个本地私有代理环境

:::warning
默认情况下，在模板中，当前目录下`var` `.env.local`目录已添加到`.gitignore`。如果不是直接使用模板需要检查下这两个是否已经添加到`.gitignore`中
:::

### 使用方式

主要配置`.env`通过环境变量来配置的，

> .env 文件为 vue-cli 提供的环境变量配置，如果需要本地私有配置需要手动创建一个`.env.local`文件

```sh
omegaCliDevserver.mockAuth=true
omegaCliDevserver.mockAuthUserId=1
omegaCliDevserver.proxyFilename="proxy.local.js"
```

在项目目录下新建一个 `var` 目录，然后新建一个代理配置文件，其文件名只要和 `omegaCliDevserver.proxyFilename` 保持一致即可生效。

文件内容格式如下：

```js
module.exports = {
  // 内容直接复制 vue.config.js-> devServer.proxy
};
```

### option 配置说明

- mockAuth {`Boolean`} - 是否开启模拟用户 token 跳过登录功能

- mockAuthUserId {`Number`} - 需要模拟的用户 ID，默认为 1（ROOT）。

- proxyFilename {`String`} - 使用的代理文件的名称，该文件格式为抛出的代理配置对象，方便本地多环境配置
