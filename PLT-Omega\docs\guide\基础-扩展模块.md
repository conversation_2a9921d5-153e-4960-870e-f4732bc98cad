# 在线配置功能

## 依赖配置

## 使用条件

- ROOT 用户登录成功后 1 分钟内可通过快捷键`Ctrl+Shift+Alt+L`唤醒(认证密码为 BFF 的`ADMIN_PASSWORD`，默认为`sA123456@`), 过时该入口关闭。

- 新增自定义页面 dashborad 依赖模型服务接口&&@omega/dashborad > 1.1.0

- 新增自定义页面图形节点依赖设备数据服务

- 新增国际化功能 `BFF >= 1.3.0` & `@omega/admin` >= 1.8.1

> 关于国际化说明：

> omega-admin 所有源数据在无外部配置的情况的默认使用的是源代码内容，包括国家化翻译在内。

## 导航配置

关于新增的自定义的菜单路由路径说明

- dashborad 路由: /omega_admin/dashboard?id="{id}&mode={mode}"

  id {String} - dashborad Id

  mode {Number} - 是否包含标题头 0:包含 1:不包含

- graph 路由: /omega_admin/graph?nodeId="{nodeId}&nodeType={nodeType}"

  nodeId {Number} - pecdraw 节点 ID
  nodeType {Number} - 节点类型

### 内置组件注册

业务测可以根据路由自己定义访问页面，也可以直接使用内置组件。

```js
import omegaApp from "@omega/app";
import { OmegaAdminPlugin } from "@omega/admin";
import { DashboradPagePlugin } from "@omega/admin/plugins/dashborad";
import { GraphPagePlugin } from "@omega/admin/plugins/graph";

omegaApp.plugin.register(OmegaAdminPlugin, {
  // apiPrefix: {
  //   "device-data-service": "/device-data"
  //   "bff-service": "/bff"
  // },
  // apiProxy: {}
});

// 注意：使用下述组件时相关依赖需要在项目中自己安装
// 注册dashborad组件
omegaApp.plugin.register(DashboradPagePlugin);
// 注册cetgraph组件
omegaApp.plugin.register(GraphPagePlugin);
```

# 开发环境如何关闭

在内部功能页面内，有开启和暂停该功能的入口按钮。或者通过代码注释或者逻辑取消该插件的注册来屏蔽相关功能

## 说明

omega-admin 依赖 BFF, 需要 omega-admin 需要将 BFF 也部署到应用中。

> docker 私库：[front-frame/bff-service](https://*************/harbor/projects/16/repositories/front-frame%2Fbff-service)

## BFF 部署说明

### docker-compose.yml 配置

```yml
bff-service:
  image: *************/front-frame/bff-service:v1.2.4
  networks:
    eureka-net:
      aliases:
        - bff-service
  restart: always
  hostname: bff-service
  ports:
    - 3005:3005

  volumes:
    # 图片&JSON配置数据持久化
    - /var/cache/CET/bff:/home/<USER>/var
  environment:
    # 前端认证密码
    ADMIN_PASSWORD: sA123456@
    # 服务启动端口
    SERVICE_PORT: 3005
  deploy:
    replicas: 1
    update_config:
      parallelism: 2
      delay: 10s
    restart_policy:
      condition: on-failure
```

### nginx 配置

```
location /bff {
    proxy_pass http://bff-service:3005;
    client_max_body_size 1000m;
    proxy_set_header Host $host;
    proxy_set_header Authorization $http_authorization;
}
```

# 开发环境如何关闭

在内部功能页面内，有开启和暂停该功能的入口按钮。或者通过代码注释或者逻辑取消该插件的注册来屏蔽相关功能
