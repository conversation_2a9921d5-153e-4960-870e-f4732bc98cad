# 核心模块

## 应用布局

:::tip
npm 私库: [@omega/layout](http://*************:4873/-/web/detail/@omega/layout)
:::

目前应用布局主要分为侧边栏布局和顶栏布局，omega-layout 将布局部分抽离成了独立的功能包，并开放了右上角工具栏区域和设置下拉列表区域的渲染入口。对于 omega-layout 提供的功能都提供了屏蔽配置方式。

---

1. 支持顶栏布局和侧边栏布局

2. 支持自定义渲染右上方工具栏/设置列表

3. 内置国际化和换肤功能封装

插件引入

```js
import omegaApp from "@omega/app";
import { OmegaLayoutPlugin } from "@omega/layout";

omegaApp.plugin.register(OmegaLayoutPlugin, {
  isShowSetting: true,
  isShowFavorites: true
  isSidebarCollapse: false,
  isShowSettingTheme: true,
  isShowSearch: true,
  renderHeaderRightTools(h) {
    return [<User />];
  },
  renderSettingItems(h) {}
});
```

:::tip

**`renderHeaderRightTools`和`renderSettingItems` 排序**

列表项是基于 `flex` 做的排版，所以可以通过设置子项 `order` 样式来排列顺序。[使用见 MDN](https://developer.mozilla.org/zh-CN/docs/Web/CSS/CSS_Flexible_Box_Layout/Ordering_Flex_Items)
:::

**插件配置说明:**

| 字段                        | 类型     | 默认值     | 必填 | 说明                                                          |
| --------------------------- | -------- | ---------- | ---- | ------------------------------------------------------------- |
| isSidebarCollapse           | Boolean  | false      | 否   | 侧边栏是否收起                                                |
| ~~layoutMode~~              | String   | "vertical" | 否   | ~~布局模式 "vertical":横向侧边栏布局 "horizontal": 顶栏布局~~ |
| ~~navStyle~~                | String   | "unite"    | 否   | ~~导航模块皮肤默认模式 unite、mix~~                           |
| isShowFavorites             | Boolean  | true       | 否   | 是否显示收藏功能                                              |
| isShowSetting               | Boolean  | true       | 否   | 是否显示设置按钮                                              |
| ~~isShowSettingLayoutMode~~ | Boolean  | true       | 否   | ~~是否显示布局模式设置~~                                      |
| ~~isShowSettingNavStyle~~   | Boolean  | true       | 否   | ~~是否显示导航样式设置~~                                      |
| isShowSettingTheme          | Boolean  | true       | 否   | 是否显示切换皮肤设置                                          |
| renderHeaderRightTools      | Function | noop       | 否   | 渲染右上角工具栏                                              |
| renderSettingItems          | Function | noop       | 否   | 渲染设置列表项                                                |
| isShowSearch                | Boolean  | true       | 否   | 是否显示搜索框                                                |

### 导航混色

扩展了 $omega_theme_extend 变量,增加了`mix`属性

> 关于 $omega_theme_extend 见后面的 @omega/theme

```scss
$omega_theme_extend: (
  "light": (
    mix: "dark",
    LOGIN_CAROUSEL_IMG: url("./assets/login_side_light.png")
  ),
  "dark": (
    LOGIN_CAROUSEL_IMG: url("./assets/login_side_dark.png")
  )
);
```

这里`mix`配置代表的是亮色皮肤下，导航部分采用暗色导航

## 路由缓存

路由支持指定页面进行缓存,配置方式为路由配置`route.meta.keepAlive`

源码

```html
<transition name="el-fade-in-linear" mode="out-in">
  <keep-alive>
    <router-view v-if="$route.meta.keepAlive" />
  </keep-alive>
</transition>
<transition name="el-fade-in-linear" mode="out-in">
  <router-view v-if="!$route.meta.keepAlive" />
</transition>
```

## 鉴权

::: tip
npm 私库: [@omega/auth](http://*************:4873/-/web/detail/@omega/auth)
:::

对基础权限服务做了一层封装，封装了权限相关的逻辑，提供了提供了鉴权需要的函数指令的封装。

> 鉴权函数: omegaAuth.checkPermission()

> 鉴权指令： v-permission=""

除此外封装了登录退出逻辑对于用户的登录和退出只需要调用即可

> 登录：omegaAuth.login({userName, password})

> 退出：omegaAuth.logout()

为方便访问用户信息，对外提供了用户访问操作类对象

> 用户操作：omgeaAuth.user

> 是否为 ROOT：omegaAuth.isRootUser()

鉴权模块以插件的方式引入

```js
import omegaApp from "@omega/app";
import { OmegaAuthPlugin } from "@omega/auth";

omegaApp.plugin.register(OmegaAuthPlugin, {
  isTokenPersist: false,
  openPermissionCheck: true,
  whiteRouteList: [],
  openHomepage: true,
  openPagePermission: true
});
```

**插件配置说明:**

| 字段               | 类型    | 默认值 | 必填 | 说明                                                             |
| ------------------ | ------- | ------ | ---- | ---------------------------------------------------------------- |
| isTokenPersist     | Boolean | false  | 否   | token 是否需要持久化存储到 localStorage                          |
| whiteRouteList     | Array   | [ ]    | 否   | 路由白名单，登录`/login` 默认会被加入不需要单独配置              |
| openHomepage       | Boolean | true   | 否   | 开启首页功能（即解析用户信息 customConfig 中用的 homepage 字段） |
| openPagePermission | Boolean | true   | 否   | 是是否开启页面权限                                               |

## 网络请求

::: tip
npm 私库: [@omega/http](http://*************:4873/-/web/detail/@omega/http)
:::

适配现有 token 存储规范，自动添加鉴权 token，将网络请求相关的请求方式进行了功能封装

- 重复错误信息进行过滤节流

- 请求全屏 loading 效果添加

插件引入

```js
import omegaApp from "@omega/app";
import { OmegaHttpPlugin } from "@omega/http";

omegaApp.plugin.register(OmegaHttpPlugin);
```

使用方式

```js
import HttpBase, { http, httping, download } from "@omega/http";
```

`http` 不带 loading 的数据请求方法（默认 `responseType："json"`）

`httping` 带 loading 的数据请求方法 （默认 `responseType："json"`）

::: warning
`http` `httping` 默认请求的解析方式为 `json` 格式（`responseType："json"`），如果加载的数据为文本类型需要手动设置`responseType："text"`
:::

`download` 二进制流的方式下载文件（`responseType: "blob"`）

`HttpBase` 类支持高度的 **自定义** 请求方式,

- 构造函数参数 `constructor(option, axiosDefaultConfig)`

| option         | 类型    | 描述                      |
| -------------- | ------- | ------------------------- |
| option.auth    | Boolean | 请求是否携带鉴权 token    |
| option.loading | Boolean | 请求是否使用 loading 效果 |
| option.silent  | Boolean | 请求是否屏蔽报错提示      |

- `axiosDefaultConfig` 为 [axiso 默认配置](http://axios-js.com/zh-cn/docs/#axios-create-config)

- HttpBase.Unauthorized (option.auth 开启的情况下有效)

注册 http 请求在鉴权 token 无效 401 的情况下的回调。默认在插件内以内置无需额外配置

## 皮肤

::: tip
npm 私库: [@omega/theme](http://*************:4873/-/web/detail/@omega/theme)
:::

换肤方案支持 sass 变量/函数方案 和 tailwind

插件使用

```js
import omegaApp from "@omega/app";
import { OmegaThemePlugin } from "@omega/theme";

omegaApp.plugin.register(OmegaThemePlugin, {
  defaultActive: "dark"
});
```

提供了`$omega_theme_extend`方便扩展当前 theme 变量或者项目需要的一些变量。

```scss
$omega_theme_extend: (
  "light": (
    LOGIN_CAROUSEL_IMG: url("./assets/login_side_light.png"),
    LOGIN_LOGO_IMG: url("./assets/login_logo_light.png")
  ),
  "dark": (
    LOGIN_CAROUSEL_IMG: url("./assets/login_side_dark.png"),
    LOGIN_LOGO_IMG: url("./assets/login_logo_dark.png")
  )
);
```

借助 vue.config.js 中`css.loaderOptions.scss.additionalData`，让所有的 scss 都能访问相关变量及函数方法，所以在所有 scss 中可以直接访问相关变量方法

## 国际化

::: tip
npm 私库: [@omega/i18n](http://*************:4873/-/web/detail/@omega/i18n)
:::

#### 换肤前提（规范）

1. `localStorage` 固定 key 为 `"omega_language"`。
2. 业务只需要中英文，暂时仅支持语言环境缩写固定为 [`"zh_cn"` , `"en"`]
3. 切换语言不支持热更新，需要重新刷新界面切换

#### 换肤模板方法说明

支持格式：

_方式一：_ `$T("总共 {0} 页 共 {1} 条", 100, 120)`

_方式二：_ `$T("总共 {page} 页 共 {strip} 条", {page: 100, strip: 120})`

- omegaI18n.locale - 获取/设置当前环境

```js
// 获取
let locale = omegaI18n.locale;
...

// 设置(会更新localstorage并刷新界面)
omegaI18n.locale = "en";
```

插件使用

```js
import omegaApp from "@omega/app";
import { OmegaI18nPlugin } from "@omega/i18n/plugin";
import en from "../config/lang/en.json";

omegaApp.plugin.register(OmegaI18nPlugin, {
  en
  // handler(local) {}
});
```

**插件配置说明:**

| 字段    | 类型     | 默认值 | 必填 | 说明                 |
| ------- | -------- | ------ | ---- | -------------------- |
| en      | Object   | {}     | 否   | 国际化英文配置       |
| handler | Function | fn     | 否   | 其他三方包国家化扩展 |

内置的国际化插件已经适配了 `elementui` 和 `momentjs`

::: warning
注意：涉及到日期格式的国家化，请使用 `momentjs`
:::
