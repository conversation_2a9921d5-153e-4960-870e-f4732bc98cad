# 扩展新皮肤

示例扩展一套国网绿色皮肤

### 1. 扩展 Elementui 皮肤

```js
import { registerElementTheme } from "@omega/theme";

registerElementTheme("green-sg", () =>
  // 其中 green-sg 为 elementui 官网上导出来的皮肤包
  import("./elementui/green-sg/theme/index.css")
);
```

### 2. 扩展基础颜色变量

> 基础颜色变量是由 UI 框架维护的一套颜色变量

```css
// src/resources/index.css
[data-theme="green-sg"] {
  /* 主色 */
  --ZS: #29b061;
  /* 辅助色1 */
  --F1: #1a7940;
  /* 辅助色2 */
  --F2: #d53b3b;
  /* 成功 */
  --Sta1: #29b061;
  /* 警告 */
  --Sta2: #fcb92c;
  /* 危险 */
  --Sta3: #f95e5a;
  /* 一般 */
  --Sta4: #2b71c3;
  /* 次要 */
  --Sta5: #7eb2ee;
  /* 状态 */
  --Sta6: #d7d7d7;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #333333;
  /* 常规 */
  --T2: #666666;
  /* 次要 */
  --T3: #989898;
  /* 占位 */
  --T4: #cccccc;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #d7d7d7;
  /* 次要 */
  --B2: #f0f0f0;
  /* >>>> 背景色 */
  --BG: #f0f0f0;
  /* 主要 */
  --BG1: #ffffff;
  /* 滑入 */
  --BG2: #e6e6e6;
  /* 点击 */
  --BG3: #d7d7d7;
  /* 选中 */
  --BG4: #eaf7ef;
  /* 滚动条 */
  --SCR: rgba(102, 102, 102, 0.3);
}
```

### 3.基础变量不够的额外的需要扩展的变量

扩展自定义的 SCSS 变量

> 基础变量不够的额外的需要扩展的变量，通过 scss 对于额外的需要扩展的变量进行扩展

```scss
// src/resource/_theme.extend.scss
$omega_theme_extend: (
  "green-sg": (
    BG5: "#11111",
    BG6: "#11111",
    ...
  )
);
```

### 4. cet-chart 皮肤扩展

```js
import { registerTheme } from "cet-chart";
import greensg from "./greensg.json";

registerTheme("green-sg", greensg);
```

### 5. 换肤按钮配置

```js
// src/omega/layout 插件配置项
{
  ...
  settingThemeList: [
    "light", // 使用内置的亮色
    ["green-sg", "国网绿", "#458614"] // 自定义的国网绿
  ],
  ...
}
```

> 上面代码中涉及到的所有 `green-sg` 为全局的皮肤 ID，不要和已有的重复即可
