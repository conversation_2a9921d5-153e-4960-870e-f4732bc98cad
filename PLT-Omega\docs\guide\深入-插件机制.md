# 插件机制

omega 所有的系统相关的成分都是以插件的方式接入进来的。这样做保证了更好的扩展性，方便我们实现对相关功能的拔插。核心模块作为必须模块是一切的基础，相关功能是不支持拔插。除核心模块其他功能模块都是可以选择性的接入。

## 启动流程图

方便我们理解我们的应用是如何一步步呈现出来的，下图是整个框架的应用启动过程。omega-app 在相对应的周期内加入特定钩子，

![生命周期图](./assets/startup-flowchart.png)

### `beforeAppBoot`

vue 应用根实例未实例，vue-router 已实例但未初始化

### `beforeAppLogin()`

登录前

### `afterAppLogin({user, isRoot})`

登录成功后，此时鉴权 token 已经完成设置。afterAppLogin 参数内包含一个 user 类实例对象方便访问 user 方法

:::tip
日常插件开发主要使用`beforeAppMount`和`afterAppLogin({user, isRoot})`，如果有特殊需求可以考虑其他两个钩子
:::

### `completeAppBoot`

应用启动结束

## 插件开发

插件对外是以一个静态类的形式开始的，对于插件而言可以接入的节点包括 6 个时间节点

1. 插件被注册

2. 实例化

3. beforeAppBoot

4. beforeAppLogin

5. afterAppLogin

6. completeAppBoot

```js
// 反馈到组件类;
class xxPlugin {
  static register(option) {
    // 插件被注册
    // omegaApp.register(xxPlugin, option)
  }

  constructor({ router, store, conf, plugin }, option) {
    // 实例化
  }

  async beforeAppBoot() {}

  async beforeAppLogin() {}

  async afterAppLogin({ user, isRoot }) {}

  async completeAppBoot() {}
}
```

参数说明

| 字段                      | 类型    | 说明                       |
| ------------------------- | ------- | -------------------------- |
| `option`                  | Object  | 插件开发人员定义的插件配置 |
| `constructorParam.router` | Object  | vue-router 实例 router     |
| `constructorParam.store`  | Object  | vuex 实例 store            |
| `constructorParam.conf`   | Object  | vue 应用配置实例           |
| `constructorParam.plugin` | Object  | 插件管理类实例             |
| `user`                    | Object  | 用户信息管理类实例         |
| `isRoot`                  | Boolean | 当前用户是否为 ROOT        |

## 示例

场景：项目的枚举值是通过后端业务接口返回存放在前端。

分析：后端业务接口是需要 token 验证的，所以需要在登录成功后添加。

代码：

```js
import CONST from "../config/const";
import Vue from "vue";
import { httping } from "@omega/http";
Vue.$const = CONST;

class ConstExtnedPlugin {
  constructor() {}

  async afterAppLogin() {
    const { data } = await httping.get("xxx/enums");
    CONST.enums = data;
  }
}

import omegaApp from "@omega/app";
omegaApp.plugin.register(ConstExtnedPlugin, {});
```

通过上述例子，我们来对比之前的代码开发方式，在之前模板代码想实现该功能势必要在登录成功的回调里面的写逻辑，侵入性较强，插件化之后功能被完美解耦出来，这样升级将互相不再影响。
