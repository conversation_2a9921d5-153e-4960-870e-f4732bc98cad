# 标准化 v1.2

全局性的具有代表性意义的一些功能数据字段被标准化，后续架构无论如何调整标准不会变。相关插件及生态内其他工具也可以根据该标准为基础来开发。

## 登录

路由路径固定为 `/login`

## 换肤

本地存储`key`为`omega_theme`

换肤缩写支持： [`"dark"` , `"light"`]

## 国际化

本地存储`key`为`omega_language`

语言缩写支持： [`"zh_cn"` , `"en"`]

## 鉴权 Token

本地存储`key`为`omega_token`

## 项目 ID (v1.1 新增)

会话存储`key`为`omega_project_id`

## 导航菜单配置

菜单配置为固定数据结构

```js
// 菜单项
{
  type: "menuItem",
  icon: "",
  label: "",
  category: "", // "project", "platform"
  location: "",
  permission: ""
}
// 菜单项分组
{
  type: "menuItemGroup",
  icon: "",
  label: "",
  subMenuList: []
}
// 子菜单
{
  type: "subMenu",
  icon: "",
  label: "",
  subMenuList: []
}
```

## 换肤功能

1. 皮肤固定存放在 `localStorage` 内, 字段名称为 `omega_theme`

2. `html` 标签添加自定义属性 `data-theme` 覆盖现有样式达到换肤效果

3. 每次换肤必须重新刷新界面

## 国际化

1. `localStorage` 固定 key 为 `"omega_language"`。

2. 业务只需要中英文，暂时仅支持语言环境缩写固定为 [`"zh_cn"` , `"en"`]

3. 切换语言不支持热更新，需要重新刷新界面切换

> 标准化管理

> 标准化作为基础约定遵从版本管理，为保证最大的兼容度标准制定要求尽可能向前兼容。当前版本为初始版本 v1.0， 后续维护人员每一个新增标准要将版本标注出来

## 变更

### v1.2 新增

菜单项新增 `category` 字段，枚举值为`project` / `platform`, 用于标识该项归属为平台还是项目