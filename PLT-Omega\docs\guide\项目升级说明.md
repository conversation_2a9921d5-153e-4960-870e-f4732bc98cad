# 项目升级指南

# 项目模板 -> omega-template

模板 Git 库地址：[omega-template](https://cetsoft-svr1/Platforms/PLT-Matterhorn/_git/omega-template)

改模板将提代之前的项目模板使用, 具体使用方式跟之前基本保持一致。

## 目录说明

api --------- api 接口

config ------ 配置文件

layout ------ 布局相关的组件

omega ------- omega 核心部件适配

projects ---- 项目目录

resources --- css/scss 样式文件

router ------ 路由配置

store ------- vuex

utils ------- 常用方法

var --------- 临时性文件

## 项目迁移

项目迁移主要是迁移 projects 目录下的一些项目模块。

### 图标(@omega/icon)

图标将整体迁移至图标库，原 icons 目录下的所有图标原则上将都迁移到新的图标库中。图标库中如果不存在，需要联系汤文琼来添加。具体图标库使用参看 omega-icon (http://10.12.135.149:4873/-/web/detail/@omega/icon)

图标库线上地址 http://192.168.131.47:7878/

### http 请求模块(@omega/http)

http 模块整体迁移至 omega/http, 提代之前 base 目录下 http 功能模块，详细使用参看 omega-http(http://10.12.135.149:4873/-/web/detail/@omega/http)

### layout 页面整体布局(@omega/layout)

layout 相关功能整体迁移至 omega/layout, 之前的 layout 目录下的功能配置方式都进行了破坏性更新，使用方式参考 omega-layout (http://10.12.135.149:4873/-/web/detail/@omega/layout)

### auth 鉴权(@omega/auth)

鉴权相关功能整体被迁移至 omega/auth, 之前的 modules/service 被完全提代。具体配置参考 omega-auth (http://10.12.135.149:4873/-/web/detail/@omega/auth)

### admin 在线配置(@omega/admin)

admin 提供了导航在线配置和 logo 及登录页面在线配置的能力。

### 开发环境优化 （@omega/cli-devserver）

cli-devserver 通过制定模拟已有的用户来跳过频繁的页面登录，提供了本地代理配置的能力

### 持续集成 (@omega/cli-devops)

提供了 jenkins 及现有相关平台的对接，docker 镜像版本的自动管理及通知。

### 换肤 （@omega/theme）

向前兼容

### 国际化 (@omega/i18n)

向前兼容，仍然为 `$T` 的全局方法。

# 破坏性更新

1. 路由取消之前所以页面缓存策略使用指定页面缓存

主要影响每个业务模块的入口文件,需要将之前的`activated`生命周期内的内容移至`mounted`

# 常见问题

1. 需要登录之后后端获取枚举值

这种情况需要手写一个插件，在`afterLogin`钩子中调用后端接口来传递， 详见插件说明
