svg symbol 支持（兼容）

### 前置条件

```
  @omega/layout >= 1.5.3
```

### 安装依赖 `svg-sprite-loader`

```

npm i -D svg-sprite-loader@6.0.11

// package.json
devDependencies: {
"svg-sprite-loader": "^6.0.11",
}

```

### 配置 webpack

```js
chainWebpack(config) {
    const path = require("path");
    function resolve(dir) {
      return path.join(__dirname, dir);
    }
    /* svgicon支持 */
    config.module.rule("svg").exclude.add(resolve("src/icons")).end();
    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/icons"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "[name]"
      })
      .end();
}
```

### 创建图标目录

在`src`目录下创建`icons`目录结构如下

---src
----icons
-----svg
======test.svg
-----index.js

其中 test.svg 为示例图标，方面后面示例演示用

`index.js`文件内容

```js
const requireAll = requireContext => requireContext.keys().map(requireContext);
const req = require.context("./svg", false, /\.svg$/);
requireAll(req);
```

### 引入 icon

main.js

```
import "./icons/index.js";
```

### navmenu 配置 icon

示例

```js
  {
    ...
    icon: "test.svg"
    ...
  }
```

### cet-icon 使用

```vue
<cet-icon iconClass="xxx" />
```
