# 说明

图标库是一套由`svg`组成的图标库

:::tip
npm 私库: [@omega/icon](http://*************:4873/-/web/detail/@omega/icon)
:::

## 引入

## 新版 > v1.3.0

```
import OmegaIcon from "@omega/icon";

Vue.use(OmegaIcon);
```

## 旧有的废弃

```js
import { OmegaIconPlugin } from "@omega/icon";

Vue.use(OmegaIconPlugin);
```

## 使用

```js
<omega-icon symbolId="home" />
```

**prop**

| 属性     | 类型   | 是否必填 | 说明                                                                                              |
| -------- | ------ | -------- | ------------------------------------------------------------------------------------------------- |
| symbolId | String | 必填     | svg symbolId，全局唯一，对应图标库下图标下的英文标识。                                            |
| size     | String | 非必填   | 默认根据当前字体大小，额外提供可选项 small\middle\large 三个可选项。分别对应大小为 24px/32px/40px |

## 协作

开发过程中原型中没有的图标直接联系 UI 团队（汤文琼）添加图标。
