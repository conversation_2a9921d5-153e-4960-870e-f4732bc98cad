{"name": "root", "private": true, "workspaces": ["template", "components/*", "packages/*", "docs"], "engines": {"node": "22.11.0", "npm": "10.9.0"}, "scripts": {"dev:tpl": "npm run -w omega-tpl dev", "update:tpl": "npm run -w omega-tpl update:omega", "dev:docs": "npm run -w omega-docs dev", "push:tpl": "git subtree push --prefix=template https://cetsoft-svr1/Platforms/PLT-Omega/_git/PLT-OmegaTemplate master", "pull:tpl": "git subtree pull --prefix=template https://cetsoft-svr1/Platforms/PLT-Omega/_git/PLT-OmegaTemplate master", "push:docs": "git subtree push --prefix=docs https://cetsoft-svr1/AzureDevOpsLab/FrontEnd/_git/omega-docs master", "pull:docs": "git subtree pull --prefix=docs https://cetsoft-svr1/AzureDevOpsLab/FrontEnd/_git/omega-docs master", "release:docs": "npm run push:docs"}, "devDependencies": {"eslint": "^8.19.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^8.7.1", "prettier": "^3.3.3"}}