# cet-common

## 1.6.5

- fix: Cet-DateSelect开放未声明的prop和事件

## 1.6.4

- fix: 国际化词条翻译规范性检查

## 1.6.3

### Minor Changes

- [注意] chore: jquery依赖版本修改为\*

## 1.6.2

- fix: Cet-table全局默认参数名称修改

## 1.6.1

- fix: Cet-table增加国际化下表头不换行自适应列宽(默认不启用)

## 1.6.0

### Minor Changes

- [注意] feat: jquery依赖版本升级, ^1.12.4 -> ^3.7.1
- feat: 将日期组件-按钮模式从el-button改为el-radio, 优化展示效果

## 1.5.1

- fix: Cet-DateSelect全局配置不生效的修复

## 1.5.0

- feat: Cet-DateSelect 新增全局前闭后开配置

  ```
  Vue.use(CetCommon, {
    api: customApi,
    CetDateSelect: {
      isClosedEnd: false
    }
  })
  ```

## 1.4.3

- fix: cet-giantTree模糊搜索不允许输入空格的修复

## 1.4.2

- fix: cet-ztree模糊搜索不允许输入空格的修复

## 1.4.1

- feat: cet-table自带的导出,没有prop的属性不参与导出

## 1.4.0

- fix: cet-table自带的导出中错误修复

## 1.3.18

- fix: cet-common去除依赖中的config包

## 1.3.17

- fix: cet-common的config依赖重新发布

## 1.3.16

- feat: cet-dateSelect所有类型均支持设置左中右对齐方式，默认左对齐

## 1.3.15

- feat: cet-tree组件节点可选择状态下，可设置选择节点是否展开expandWhenChecked,默认为true

## 1.3.14

- feat:dateSelect组件时间nextbutton按钮增加是否禁用参数

## 1.3.13

- fix: cet-gaintTree 修改不同皮肤下的勾选框背景颜色及禁用的勾选框的背景颜色，修复暗色下禁用勾选框背景色与正常勾选框背景色相同导致无法分辨勾选框是否禁用的问题

## 1.3.12

- feat: cet-gaintTree 增加customSearch配置，可从外部传入搜索节点的方法

## 1.3.11

- feat: cet-tree 节点搜索可显示搜索节点的子节点

## 1.3.10

- fix: 修复 cet-dateselect 时间控件 nextDisabledNum 报错问题

## 1.3.9

- feat: 时间选择增加可选 options，next 按钮 subtract 数字

## 1.3.8

- 2627313: 增加 doLayoutTrigger_in 参数

## 1.3.7

- [object Promise]: dateSelect 组件增加 dateTimeRangeAlign 配置, 可以控制 datetimerange 下下拉选择日期框对齐方式

## 1.3.6

- fix: 修复 cet-gainTree 勾选不显示问题

## 1.3.5

- faet: 时间组件类型选择支持按钮组布局方式

## 1.3.4

- feat: esmodule 入口添加
- Updated dependencies
  - @omega/http@1.2.3

## 1.3.3

- feat: Cet-Dialog 新增全局拖拽配置 @董兰兰

  ```
  Vue.use(CetCommon, {
    api: customApi,
    CetDialog: {
      isDraggable: false
    }
  })
  ```

## 1.3.2

- feat: cetform 查询 filter 未定义;cettable 导出数值列(包括带千分位)自动转为数字

## 1.3.1

- fix: 调整依赖版本为外部任意版本

## 1.3.0

- feat:

  1. cet-table: 增加表格是否有树形数据的配置
  2. cet-gainTree: 增加 exedit.min.js 依赖、增加 searchText_out 输出、创建自定义控件样式修改

- fix:

  1. cet-gainTree: uncheckTrigger 的时候清空选中、勾选和 checkeData

## 1.2.5

- feat: cet-table 添加接口请求完成的结果输出

## 1.2.4

- fix: cet-ztree 选中状态异常问题修复

- fix: cet-aside 收起按钮图标恢复为箭扣:">"

## 1.2.3

- fix: cet-table 按住 shift 多选功能补充

## 1.2.2

- fix: 处理页面缓存，tooltip 触发，切换页面，导致悬停出现在其他位置

## 1.2.1

- feat: 移动 cet-weather 到业务组件 @omega/weather

## 1.2.0

- feat: 迁移了天气组件

## 1.1.0

- feat: 增加 selectMapPoint 百度地图组件

## 1.0.13

- fix: giant-tree 大批量节点选中回显卡顿问题修复

## 1.0.12

- fix: 国际化遗漏补充

## 1.0.11

- fix: table this 遗漏补充&&国际化缺失补充

## 1.0.10

- fix: 英文日期选择框宽度适配

## 1.0.9

- bdd25a7: fix: cet-table 暗色皮肤下 tooltip 样式使用亮色皮肤问题

## 1.0.8

- fix: 修复 dateSelect 国际化遗漏问题

## 1.0.7

- [2022-05-05] fix: cet-icon 不展示问题

## 1.0.6

- [2022-04-26] fix: 删除无效的 cancelToken

## 1.0.5

- [2022-04-25] 合入 common 新版本变更修改

## 1.0.4

- [2022-04-14] fix: 依赖缺失

## 2.0.0

- Updated dependencies
  - @omega/http@1.1.0

## 1.0.3

- [2022-04-13] fix: 修复 ztree 依赖

## 1.0.2

- feat: 添加国际化文本

## 1.0.1

- [2022-04-02]

  feat: 国际化功能适配

- Updated dependencies
  - @omega/http@1.0.1
