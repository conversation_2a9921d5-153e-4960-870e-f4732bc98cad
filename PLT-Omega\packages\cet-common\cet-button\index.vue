<template>
  <div class="device-Button">
    <el-button
      :size="size"
      v-show="visible_in"
      :disabled="disable_in"
      @click="handleclick"
      v-bind="$attrs"
      v-on="$listeners"
    >
      {{ title }}
    </el-button>
    <!--
      :plain="plain"
      :icon="icon"
      :circle="circle"
    -->
  </div>
</template>
<script>
export default {
  name: "CetButton",
  props: {
    //是否显示
    visible_in: {
      type: Boolean
    },
    // 是否禁用
    disable_in: {
      type: Boolean
    },
    // 名称
    title: {
      type: String
    },
    // 类型
    size: {
      type: String,
      default: "small"
    }
  },
  data() {
    return {};
  },
  methods: {
    handleclick() {
      //每点击一次进行通知
      this.$emit("statusTrigger_out", new Date().getTime());
    }
  }
};
</script>
<style scoped>
.device-Button {
  /* width: 200px; */
  display: inline-block;
}
</style>
