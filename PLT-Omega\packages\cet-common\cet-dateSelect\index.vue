<template>
  <!-- 日 周 月 季 年 时间选择控件 -->
  <div class="datetimepicker">
    <el-select
      v-model="dateType"
      @change="dateTypeChange"
      :placeholder="i18n('请选择')"
      :style="selectStyle"
      v-if="showOption && layout === 'select'"
    >
      <template v-for="item in dataTypeList">
        <el-option
          :label="i18n(item.label)"
          :value="item.value"
          :key="item.value"
          v-if="typeList.indexOf(item.key) !== -1"
        />
      </template>
    </el-select>
    <el-radio-group
      v-if="showOption && layout === 'button'"
      v-model="dateType"
      @input="dateTypeChange"
    >
      <template v-for="item in dataTypeList">
        <el-radio-button
          :key="item.value"
          :label="item.value"
          v-if="typeList.indexOf(item.key) !== -1"
        >
          {{ i18n(item.label) }}
        </el-radio-button>
      </template>
    </el-radio-group>
    <el-button
      plain
      size="small"
      v-if="showButton"
      icon="el-icon-arrow-left"
      @click="queryPrv"
    ></el-button>
    <el-date-picker
      v-if="dateType === '1'"
      key="1"
      format="yyyy-MM-dd"
      :picker-options="dayDateOption"
      :editable="false"
      :clearable="false"
      v-model="dateValue.day"
      @change="dateChange"
      type="date"
      :placeholder="i18n('选择日')"
      class="picker"
      :align="align"
      v-bind="$attrs"
      v-on="$listeners"
    />

    <el-date-picker
      v-if="dateType === '2'"
      key="2"
      v-model="dateValue.week"
      type="week"
      :format="i18n('yyyy 第 WW 周')"
      :picker-options="weekDateOption"
      :editable="false"
      :clearable="false"
      @change="dateChange"
      :placeholder="i18n('选择周')"
      class="picker"
      :align="align"
      v-bind="$attrs"
      v-on="$listeners"
    />

    <el-date-picker
      v-if="dateType === '3'"
      key="3"
      v-model="dateValue.month"
      type="month"
      format="yyyy-MM"
      :picker-options="monthDateOption"
      :editable="false"
      :clearable="false"
      @change="dateChange"
      :placeholder="i18n('选择月')"
      class="picker"
      :align="align"
      v-bind="$attrs"
      v-on="$listeners"
    />

    <season-picker
      v-if="dateType === '4'"
      :value="dateValue.season"
      @chooseSeason="seasonChange"
      class="picker"
      :align="align"
    />

    <el-date-picker
      v-if="dateType === '5'"
      key="5"
      v-model="dateValue.year"
      type="year"
      format="yyyy"
      :picker-options="yearDateOptions"
      :editable="false"
      :clearable="false"
      @change="dateChange"
      :placeholder="i18n('选择年')"
      class="picker"
      :align="align"
      v-bind="$attrs"
      v-on="$listeners"
    />

    <el-date-picker
      v-if="dateType === '6'"
      key="6"
      v-model="dateValue.daterange"
      type="daterange"
      format="yyyy-MM-dd"
      :range-separator="i18n('至')"
      :start-placeholde="i18n('开始日期')"
      :end-placeholde="i18n('结束日期')"
      :picker-options="rangeDateOptions"
      :editable="false"
      :clearable="false"
      unlink-panels
      @change="dateChange"
      :placeholder="i18n('选择日期范围')"
      class="picker-range"
      :align="align"
      v-bind="$attrs"
      v-on="$listeners"
    />
    <el-date-picker
      v-if="dateType === '7'"
      key="7"
      v-model="dateValue.datetimerange"
      type="datetimerange"
      :range-separator="i18n('至')"
      :start-placeholde="i18n('开始日期')"
      :end-placeholde="i18n('结束日期')"
      :picker-options="rangeDateOptions"
      :editable="false"
      :clearable="false"
      unlink-panels
      @change="dateChange"
      :placeholder="i18n('选择日期范围')"
      class="datetime-picker-range"
      :align="oldAlign"
      v-bind="$attrs"
      v-on="$listeners"
    />
    <el-button
      plain
      size="small"
      v-if="showButton"
      icon="el-icon-arrow-right"
      :disabled="nextDisabled && nextBtnDisabled"
      @click="queryNext"
    />
  </div>
</template>
<script>
import seasonPicker from "./seasonPicker"; // 引入季度时间组件
import _ from "lodash";
import moment from "moment";
import { i18n } from "../local/index.js";
import { defaultSettings } from "../defaultSetting.js";

export default {
  name: "CetDateSelect",
  data() {
    // 如果不喜欢，或者项目没有使用这种数据分离方式开发，那么可以像之前那样，导出一个对象，
    // 将 datetimepickerapi 文件里的所有数据放在 data 方法里面即可
    return {
      selectStyle: {
        width: "90px",
        display: "inline-block"
      },
      dateType: "3",
      dateValue: {
        day: new Date(),
        week: new Date(),
        month: new Date(),
        season: new Date(),
        year: new Date(),
        daterange: [new Date(), new Date()],
        datetimerange: [new Date(), new Date()]
      },
      momentTypeList: ["", "day", "isoWeek", "month", "quarter", "year"],
      valueList: [
        "",
        "day",
        "week",
        "month",
        "season",
        "year",
        "daterange",
        "datetimerange"
      ],
      dataTypeList: [
        {
          label: "日",
          value: "1",
          key: "day"
        },
        {
          label: "周",
          value: "2",
          key: "week"
        },
        {
          label: "月",
          value: "3",
          key: "month"
        },
        {
          label: "季",
          value: "4",
          key: "season"
        },
        {
          label: "年",
          value: "5",
          key: "year"
        },
        {
          label: "自定义",
          value: "6",
          key: "daterange"
        },
        {
          label: "自定义",
          value: "7",
          key: "datetimerange"
        }
      ]
    };
  },
  components: {
    seasonPicker
  },
  props: {
    showOption: {
      type: Boolean,
      default: true
    },
    value: {
      type: Object
    },
    typeList: {
      type: Array,
      default: () => ["day", "week", "month", "season", "year", "daterange"]
    },
    nextDisabledNum: {
      type: Number,
      default: 0,
      validator(val) {
        return val >= 0 && val % 1 === 0;
      }
    },
    nextBtnDisabled: {
      type: Boolean,
      default: true
    },
    rangeDateOptions: {
      type: Object,
      default: () => ({
        disabledDate: time => {
          return time.getTime() > Date.now();
        },
        shortcuts: [
          {
            text: i18n("最近一周"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: i18n("最近一个月"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: i18n("最近三个月"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: i18n("最近半年"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
              picker.$emit("pick", [start, end]);
            }
          },
          {
            text: i18n("最近一年"),
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
              picker.$emit("pick", [start, end]);
            }
          }
        ]
      })
    },
    dayDateOption: {
      type: Object,
      default: () => {
        return {
          firstDayOfWeek: 1,
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        };
      }
    },
    weekDateOption: {
      type: Object,
      default: () => {
        return {
          firstDayOfWeek: 1,
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        };
      }
    },
    monthDateOption: {
      type: Object,
      default: () => {
        return {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        };
      }
    },
    yearDateOptions: {
      type: Object,
      default: () => {
        return {
          disabledDate(time) {
            return time.getTime() > Date.now();
          }
        };
      }
    },
    layout: {
      type: String,
      default: "select" //select 下拉方式、button 按钮组
    },
    // TODO: dateTimeRangeAlign及oldAlign都需要逐步淘汰，统一使用align，目前作兼容处理
    //datetimerange模式下, 下拉框对齐的方式, 默认靠左对齐, 对于时间选择组件非常靠右的场景, 设置为right可以避免出现滚动条等问题.
    dateTimeRangeAlign: {
      type: String,
      default: "left"
    },
    // 对齐模式参数统一
    align: {
      type: String,
      default: "left"
    },
    isClosedEnd: {
      type: Boolean,
      default: function () {
        return defaultSettings.CetDateSelect.isClosedEnd;
      }
    }
  },
  methods: {
    /**
     * @description:日期类型切换后处理函数
     * @param {*}
     * @return {*}
     */
    queryPrv() {
      let date = this.$moment(this.dateValue[this.valueList[this.dateType]]);
      this.dateValue[this.valueList[this.dateType]] = date
        .subtract(1, this.momentTypeList[this.dateType])
        .valueOf();
      this.dateChange();
    },
    queryNext() {
      let date = this.$moment(this.dateValue[this.valueList[this.dateType]]);
      this.dateValue[this.valueList[this.dateType]] = date
        .add(1, this.momentTypeList[this.dateType])
        .valueOf();
      this.dateChange();
    },
    dateTypeChange() {
      const vm = this;
      let value = vm.handleDateChange();
      vm.$emit("dateType_out", vm.dateType, value);
    },
    /**
     * @description: 日期选择变化后调用处理函数
     * @param {*}
     * @return {*}
     */
    dateChange() {
      this.handleDateChange();
    },
    /**
     * @description:日期变化处理函数,根据值生成一个数组, 数组第一个值为起始时间,第二个为终止时间
     * @param {*}
     * @return {*}
     */
    handleDateChange() {
      const vm = this;
      let dateValue = vm.dateValue[vm.valueList[vm.dateType]];
      let type = vm.momentTypeList[vm.dateType];
      let startTime, endTime;

      if (_.isArray(dateValue)) {
        if (!dateValue[0]) {
          return;
        }
        if (vm.dateType === "6") {
          startTime = moment(dateValue[0]).startOf("day");
          endTime = moment(dateValue[1]).endOf("day");
          if (!vm.isClosedEnd) {
            endTime += 1;
          }
        } else {
          startTime = moment(dateValue[0]);
          endTime = moment(dateValue[1]);
        }
      } else {
        if (!dateValue) {
          return;
        }
        startTime = moment(dateValue).startOf(type);
        endTime = moment(dateValue).endOf(type);
        if (!vm.isClosedEnd) {
          endTime += 1;
        }
      }
      vm.$emit(
        "date_out",
        [startTime.valueOf(), endTime.valueOf()],
        vm.dateType
      );
      return [startTime.valueOf(), endTime.valueOf()];
    },
    /**
     * @description: 季度选择特殊处理
     * @param {*} val
     * @return {*}
     */
    seasonChange(val) {
      this.dateValue.season = val;
      this.handleDateChange();
    },
    i18n
  },
  created() {},
  mounted() {},
  computed: {
    nextDisabled() {
      return (
        this.$moment(this.dateValue[this.valueList[this.dateType]])
          .startOf(this.momentTypeList[this.dateType])
          .valueOf() >=
        this.$moment()
          .subtract(this.nextDisabledNum, this.momentTypeList[this.dateType])
          .startOf(this.momentTypeList[this.dateType])
          .valueOf()
      );
    },
    //是否显示前一时段, 后一时段按钮
    showButton() {
      return this.dateType !== "6" && this.dateType !== "7";
    },
    // TODO: dateTimeRangeAlign及oldAlign都需要逐步淘汰，统一使用align，目前作兼容处理
    // 外部不配置，默认值都是left，当有一个不是等于left则取这个值，如果2个参数都不等于left，则优先取align —— 朱云霞
    oldAlign() {
      if (this.align !== "left") {
        return this.align;
      } else {
        return this.dateTimeRangeAlign;
      }
    }
  },
  /**
   * @description:外部设置值发生变化后设置到组件中
   * @param {*}
   * @return {*}
   */
  watch: {
    value: {
      immediate: true,
      handler: function (val) {
        const vm = this;
        vm.dateType = val.dateType;
        if (val && val.dateType && val.value) {
          vm.dateValue[vm.valueList[vm.dateType]] = val.value;
        }
      }
    },
    typeList: {
      immediate: true,
      handler: function (val) {
        // this.dateType = this.valueList.indexOf(val[0]) + "";
        this.selectStyle.width = "100px";
        // if (
        //   val.indexOf("daterange") !== -1 ||
        //   val.indexOf("datetimerange") !== -1
        // ) {
        //   this.selectStyle.width = "100px";
        // } else {
        //   this.selectStyle.width = "100px";
        // }
      }
    }
  }
};
</script>

<style scoped lang="scss">
.datetimepicker {
  display: inline-flex;
  justify-content: space-between;
  position: relative;
  align-items: center;
  line-height: 1;
  > * {
    margin-right: 4px;
    &:last-child {
      margin-right: 0px;
    }
  }
}
.picker {
  width: 150px;
  /* display: inline-block; */
}
.picker-range {
  width: 250px;
  ::v-deep .el-range-separator {
    position: relative;
    padding: 0;
  }
}
.datetime-picker-range {
  width: 360px;
  ::v-deep .el-range-separator {
    position: relative;
    padding: 0;
  }
}
.btn-select {
  @include font_color("T2");
  @include background_color("ZS");
  @include border_color("ZS");
}
</style>
