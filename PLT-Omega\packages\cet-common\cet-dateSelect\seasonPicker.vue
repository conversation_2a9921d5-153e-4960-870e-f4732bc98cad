<template>
  <!-- 季度选择时间控件 -->
  <div class="jidudatepicker">
    <span>
      <mark
        style="
          position: fixed;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0);
          z-index: 999;
        "
        v-show="showSeason"
        @click.stop="showSeason = false"
      />
      <el-input
        :placeholder="i18n('选择季度')"
        v-model="showValue"
        style="width: 150px"
        @focus="showSeason = true"
      >
        <i slot="prefix" class="el-input__icon el-icon-date" />
      </el-input>
      <el-card
        :class="alignStyle"
        class="box-card"
        style="
          width: 322px;
          padding: 0 3px 20px;
          margin-top: 10px;
          position: absolute;
          z-index: 9999;
        "
        v-show="showSeason"
      >
        <div slot="header" class="firstBtn">
          <button
            type="button"
            :aria-label="i18n('前一年')"
            class="el-picker-panel__icon-btn el-date-picker__prev-btn el-icon-d-arrow-left"
            @click="prev"
          />
          <span role="button" class="el-date-picker__header-label">
            {{ year }} {{ i18n("年") }}
          </span>
          <button
            type="button"
            :aria-label="i18n('后一年')"
            @click="next"
            class="el-picker-panel__icon-btn el-date-picker__next-btn el-icon-d-arrow-right"
          />
        </div>
        <div class="text container">
          <!-- 如下，绑定class,disabled为禁止选择的时间的设置 -->
          <el-button
            type="text"
            size="medium"
            :class="{ colorDis: this.year > this.defaultyear }"
            :disabled="this.year > this.defaultyear"
            class="leftbutton"
            @click="selectSeason(0)"
          >
            {{ i18n("第一季度") }}
          </el-button>
          <el-button
            type="text"
            size="medium"
            :class="{
              colorDis:
                (this.year === this.defaultyear && this.month <= 3) ||
                this.year > this.defaultyear
            }"
            :disabled="
              (this.year === this.defaultyear && this.month <= 3) ||
              this.year > this.defaultyear
            "
            class="rightbutton"
            @click="selectSeason(1)"
          >
            {{ i18n("第二季度") }}
          </el-button>
        </div>
        <div class="item container" style="text-align: center">
          <el-button
            type="text"
            size="medium"
            :class="{
              colorDis:
                (this.year === this.defaultyear && this.month <= 6) ||
                this.year > this.defaultyear
            }"
            :disabled="
              (this.year === this.defaultyear && this.month <= 6) ||
              this.year > this.defaultyear
            "
            class="leftbutton"
            @click="selectSeason(2)"
          >
            {{ i18n("第三季度") }}
          </el-button>
          <el-button
            type="text"
            size="medium"
            :class="{
              colorDis:
                (this.year === this.defaultyear && this.month <= 9) ||
                this.year > this.defaultyear
            }"
            :disabled="
              (this.year === this.defaultyear && this.month <= 9) ||
              this.year > this.defaultyear
            "
            class="rightbutton"
            @click="selectSeason(3)"
          >
            {{ i18n("第四季度") }}
          </el-button>
        </div>
      </el-card>
    </span>
  </div>
</template>
<script>
import moment from "moment";
import { i18n } from "../local/index.js";
export default {
  name: "SeasonPicker",
  props: {
    value: {
      type: [Object, Number, String, Date]
    },
    align: {
      type: String,
      default: "left"
    }
  },
  data() {
    return {
      showSeason: false,
      year: new Date().getFullYear(), // input显示时间，会随着用户操作改变
      defaultyear: new Date().getFullYear(), // 当前年份，不变
      month: new Date().getMonth() + 1, // 当前月份，不变
      showValue: ""
    };
  },
  created() {},
  mounted() {
    this.setValue(this.value);
  },
  watch: {
    value(date) {
      this.setValue(date);
    }
  },
  computed: {
    alignStyle: function () {
      switch (this.align) {
        case "center":
          return "align-center";
        case "right":
          return "align-right";
        default:
          return "align-left";
      }
    }
  },
  methods: {
    prev() {
      this.year = this.year * 1 - 1;
    },
    next() {
      this.year = this.year * 1 + 1;
    },
    selectSeason(i) {
      const vm = this;
      vm.showSeason = false;
      vm.showValue = i18n("{year} 年 {season} 季度", {
        year: this.year,
        season: i + 1
      });
      const preStr = i > 2 ? "" : "0";
      vm.$emit("chooseSeason", `${vm.year}-${preStr}${i * 3 + 1}-01`);
    },
    setValue(date) {
      const vm = this;
      const momentDate = moment(date);
      vm.year = momentDate.year();

      const season = momentDate.quarter() - 1;
      vm.showValue = i18n("{year} 年 {season} 季度", {
        year: this.year,
        season: season + 1
      });
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.jidudatepicker {
  display: inline;
  .firstBtn {
    height: 30px;
    line-height: 34px;
    width: 100%;
    text-align: center;
  }
  .text {
    text-align: center;
    margin: 15px 0 10px;
  }
  .item {
    text-align: center;
  }
}
.colorDis {
  @include font_color("T3", !important);
}
.leftbutton {
  float: left;
  width: 47%;
  @include font_color("T1");
}

.rightbutton {
  float: right;
  width: 47%;
  @include font_color("T1");
}
.align-left {
  /* 左对齐不需要额外样式 */
}
.align-center {
  right: -42px;
}
.align-right {
  right: 48px;
}
</style>
<style lang="scss">
.jidudatepicker {
  .el-card__header {
    padding: 12px;
  }
}
</style>
