<template>
  <!-- <div v-if="dialogVisible"> -->
  <el-dialog
    v-dialogDrag="isDraggable"
    :visible.sync="dialogVisible"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :show-close="showClose"
    :width="width"
    :title="title"
    @opened="openDialogHandler"
    @closed="closeDialogHandler"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <slot />
    <template v-slot:title>
      <slot name="title" />
    </template>
    <template v-slot:footer>
      <slot name="footer" />
    </template>
  </el-dialog>
  <!-- </div> -->
</template>

<script>
import _ from "lodash";
import { i18n } from "../local/index.js";
import { defaultSettings } from "../defaultSetting.js";
export default {
  name: "CetDialog",
  props: {
    openTrigger_in: {
      type: Number
    },
    closeTrigger_in: {
      type: Number
    },
    width: {
      type: String,
      default: "960px"
    },
    closeOnClickModal: {
      type: Boolean,
      default: false
    },
    closeOnPressEscape: {
      type: Boolean,
      default: false
    },
    showClose: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: i18n("弹窗")
    },
    isDraggable: {
      type: Boolean,
      default: function () {
        return defaultSettings.CetDialog.isDraggable;
      }
    }
  },

  data() {
    return {
      dialogVisible: false
    };
  },
  watch: {
    //入参变化则展示弹窗
    openTrigger_in() {
      var vm = this;
      vm.dialogVisible = true;
    },
    closeTrigger_in() {
      var vm = this;
      // vm.closeDialogHandler();
      vm.dialogVisible = false;
    }
  },
  methods: {
    openDialogHandler() {
      this.$emit("openTrigger_out", new Date().getTime());
    },
    closeDialogHandler() {
      this.$emit("closeTrigger_out", new Date().getTime());
    }
  }
};
</script>
<style lang="scss" scoped></style>
