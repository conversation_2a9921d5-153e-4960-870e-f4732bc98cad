<template>
  <el-form
    :model="data"
    :rules="ruleChecking"
    ref="cetForm"
    v-bind="$attrs"
    v-on="$listeners"
    @submit.native.prevent
  >
    <slot></slot>
  </el-form>
</template>

<script>
import _ from "lodash";
import { buildQueryBody } from "../utils/generateQuery";
import { buildWriteBody } from "../utils/generateWrite";
import { api as customApi } from "../api";
import { queryModel, deleteModelInstence, writeModel } from "../baseApi/model";
import { i18n } from "../local/index.js";
export default {
  name: "CetForm",
  props: {
    //表单数据获取模式 // 数据获取模式：backendInterface后端接口 ；其他组件 component; 静态数据 static
    dataMode: {
      type: String
    },
    //表格的查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    data: {
      type: Object
    },
    //其他组件输入的数据
    inputData_in: {
      type: Object
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    //查询按钮状态输入，状态变化执行查询
    queryTrigger_in: {
      type: Number
    },
    saveTrigger_in: {
      type: Number
    },
    localSaveTrigger_in: {
      type: Number
    },
    resetTrigger_in: {
      type: Number
    },
    //校验规则
    rules: {
      type: Object
    },
    refreshAfterActived: {
      type: [Boolean],
      default: false
    }
  },
  data() {
    return {
      activatedNum: 0, //组件activated钩子触发的次数
      //需要保存的数据
      saveData: {},
      //初始值,用于保存时判断有变化的值
      initialData: {},
      ruleChecking: {},
      isSaving: false //判断是否处于保存状态
    };
  },
  watch: {
    //按钮触发查询
    queryTrigger_in() {
      var vm = this;

      //如果queryId_in为有效值，则获取弹窗数据
      if (vm.queryId_in > 0 && vm.dataMode === "backendInterface") {
        //清空data
        vm.clearData();
        vm.getComponentData();
      }
    },
    //外部输入数据
    inputData_in: {
      deep: true,
      handler: function (val) {
        var vm = this;
        if (vm.dataMode === "component") {
          if (!val && !_.isEmpty(val)) {
            return;
          }
          //清空data
          vm.clearData();

          //设置form的数据
          vm.setInputData();
        }
      }
    },

    //queryId_in变化则执行查询
    queryId_in(val) {
      var vm = this;

      //如果queryId_in为有效值，则获取弹窗数据
      if (
        vm.queryId_in > 0 &&
        vm.dataMode === "backendInterface" &&
        vm.queryMode === "diff"
      ) {
        //清空data
        vm.clearData();
        vm.getComponentData();
      }
    },
    //保存按钮触发保存操作
    saveTrigger_in() {
      this.saveComponentData();
    },

    //保存按钮触发保存操作
    localSaveTrigger_in() {
      this.localSaveComponentData();
    },
    resetTrigger_in() {
      this.clearData();
    },
    data: {
      deep: true,
      handler: function (val) {
        //输出当前值
        this.$emit("currentData_out", this._.cloneDeep(this.data));
      }
    }
  },
  created: function () {
    let vm = this,
      dataIndex = vm.dataConfig.dataIndex,
      dynamic = {};

    this.checkData();

    //根据dataIndex设置data的初始属性和值, data中有配置默认值的,初始化为默认值, 没设置的, 初始化为空字符串
    for (let i = 0; i < dataIndex.length; i++) {
      let value = _.get(vm.data, dataIndex[i]);
      value = _.isNil(value) ? "" : value;
      _.set(dynamic, dataIndex[i], value);
    }
    vm.$emit("update:data", dynamic);
  },
  mounted() {
    const vm = this;
    //diff模式下, EL挂载完成则获取一次数据
    if (vm.queryMode === "diff" && vm.dataMode === "backendInterface") {
      this.getComponentData();
    }
    //EL挂载完成后, component模式下, 执行setInput操作
    if (vm.dataMode === "component") {
      let val = vm.inputData_in;
      if (!val && !_.isEmpty(val)) {
        return;
      }

      //设置form的数据
      vm.setInputData();
    }
  },
  activated() {
    const vm = this;
    vm.activatedNum++;
    //第一次激活不执行逻辑，后续激活更新数据
    if (vm.activatedNum > 1 && vm.refreshAfterActived) {
      if (vm.dataMode === "backendInterface") {
        vm.getComponentData();
      }
    }
  },
  methods: {
    checkData() {
      var vm = this;
      //生成的表单校验规则
      var checkRules = {};
      //按表单项循环外部配置的表单规则
      for (let key in vm.rules) {
        let singleRule = vm.rules[key];
        //设置单个表单校验规则
        this.$set(checkRules, key, []);
        //根据单个表单校验规则配置的数组，生成实际的表单校验规则
        for (let i = 0; i < singleRule.length; i++) {
          var validatePass;
          switch (singleRule[i].checkType) {
            //密码输入校验类型
            case "password":
              validatePass = (rule, value, callback) => {
                //如果二次密码输入不为空，则进行密码和二次输入密码是否相等校验
                if (vm.data[singleRule[i].validatorProp] !== "") {
                  vm.$refs.cetForm.validateField(singleRule[i].validatorProp);
                }
                callback();
              };
              checkRules[key].push({
                validator: validatePass,
                trigger: "blur"
              });
              break;

            case "checkPassword":
              validatePass = (rule, value, callback) => {
                if (!value && !vm.data[singleRule[i].relationProp]) {
                  callback();
                  // callback(new Error("请再次输入密码"));
                } else if (value !== vm.data[singleRule[i].relationProp]) {
                  callback(new Error(i18n("两次输入密码不一致")));
                } else {
                  callback();
                }
              };
              checkRules[key].push({
                validator: validatePass,
                trigger: "blur"
              });
              break;

            default:
              checkRules[key].push(singleRule[i]);
              break;
          }
        }
      }

      this.ruleChecking = checkRules;
    },

    //清空弹窗的数据
    clearData() {
      var vm = this,
        recordData = {};
      if (!vm.data) {
        return;
      }

      if (vm.$refs["cetForm"]) {
        vm.$refs["cetForm"].resetFields();
      }
    },
    getQueryBody() {
      let vm = this,
        queryBody = {};

      return buildQueryBody({
        modelLabel: vm.dataConfig.modelLabel,
        modelList: vm.dataConfig.modelList,
        props: [],
        id: vm.queryId_in
      });
    },

    doQuery(queryBody) {
      let vm = this;
      //开发一个外部设置调用接口的自定义逻辑
      let queryMethod;
      let queryFunc = vm.dataConfig.queryFunc;
      if (queryFunc) {
        //如果设置了自定义接口处理函数,则调用自定义处理函数处理
        queryMethod = customApi[queryFunc];
      } else {
        //调用默认的模型api
        queryMethod = queryModel;
      }
      return queryMethod(queryBody);
    },
    //获取表单数据
    getComponentData() {
      var vm = this,
        queryBody = {};

      queryBody = vm.getQueryBody();

      //调用接口
      vm.doQuery(queryBody).then(response => {
        if (response.code === 0) {
          var resData = {};
          //兼容接口返回的数组和对象两种形式的返回数据格式
          if (_.isArray(response.data)) {
            if (response.data.length < 1) {
              return;
            }
            resData = response.data[0];
          } else {
            if (_.isNil(response.data)) {
              return;
            }
            resData = response.data;
          }

          vm.processResponseData(resData, queryBody);
        }
      });
    },

    //处理弹窗获取的返回数据
    processResponseData(resData, queryBody) {
      var vm = this,
        dataIndex = vm.dataConfig.dataIndex,
        groups = vm.dataConfig.groups,
        recordData = vm.data;

      //再将接口数据逐项保存到原始数据
      for (var i = 0; i < dataIndex.length; i++) {
        let path = dataIndex[i];

        if (_.has(resData, path)) {
          _.set(recordData, path, _.get(resData, path));
        }
      }

      //将groups配置的数据从数组转成对象，如果有多个，则取第一个
      if (groups) {
        for (let j = 0; j < groups.length; j++) {
          if (groups[j].name) {
            for (let k = 0; k < groups[j].models.length; k++) {
              if (recordData[`${groups[j].models[k]}_model`]) {
                recordData[groups[j].name] =
                  recordData[`${groups[j].models[k]}_model`];
                delete recordData[`${groups[j].models[k]}_model`];
                break;
              }
            }
          }
        }
      }

      //保存弹窗的初始值
      vm.initialData = _.cloneDeep(recordData);

      //输出弹窗获取的初始值
      vm.$emit("update:data", recordData);
    },

    //将inputData的值设置为表单的初始值
    setInputData() {
      var vm = this,
        dataIndex = vm.dataConfig.dataIndex,
        inputData = vm.inputData_in,
        recordData = _.cloneDeep(vm.data);
      if (!inputData) {
        return;
      }
      //将输入数据逐项保存到原始数据
      for (var i = 0; i < dataIndex.length; i++) {
        let path = dataIndex[i];

        if (_.has(inputData, path)) {
          _.set(recordData, path, _.cloneDeep(_.get(inputData, path)));
        }
      }

      //触发初始值输出事件
      vm.$emit("update:data", recordData);
      //保存初始值
      vm.initialData = _.cloneDeep(recordData);
    },

    localSaveComponentData() {
      var vm = this;
      if (vm.isSaving) {
        return; //如果处于保存过程中，不再接受保存请求，避免重复的请求
      }
      vm.isSaving = true; //状态置为true

      vm.$refs["cetForm"].validate((valid, object) => {
        if (valid) {
          //如果校验通过，则先输出校验通过的数据，然后输出当前毫秒数
          // vm.$emit("validateTrigger_out", new Date().getTime());

          let outData = _.cloneDeep(vm.data);

          //点击保存按钮，将待保存的数据输出，其他组件可获取该数据,这里不做针对接口的数据处理
          outData.modelLabel = vm.dataConfig.modelLabel;
          vm.$emit("saveData_out", outData);

          vm.isSaving = false;
        } else {
          vm.isSaving = false;
          vm.$emit("validateFail_out", object);
          return false;
        }
      });
    },
    saveComponentData() {
      var vm = this;
      if (vm.isSaving) {
        return; //如果处于保存过程中，不再接受保存请求，避免重复的请求
      }
      vm.isSaving = true; //状态置为true

      vm.$refs["cetForm"].validate(valid => {
        if (valid) {
          vm.doSaveComponentData();
        } else {
          vm.isSaving = false;
          return false;
        }
      });
    },

    //保存数据
    doSaveComponentData() {
      var vm = this;

      if (_.isNil(vm.data) || _.isEmpty(vm.data)) {
        return;
      }

      let writBody = buildWriteBody({
        saveData: vm.data,
        modelLabel: vm.dataConfig.modelLabel,
        initialData: vm.initialData,
        groups: vm.dataConfig.groups
      });

      //调用接口
      vm.doWrite(writBody).then(
        response => {
          if (response.code === 0) {
            vm.$message.success(
              vm.dataConfig.saveSuccessMessage
                ? vm.dataConfig.saveSuccessMessage
                : i18n("保存成功！")
            );
            vm.$emit("finishTrigger_out", new Date().getTime());
            response.data = response.data || [];
            vm.$emit("finishData_out", response.data[0]);
            vm.dialogVisible = false;
          } else {
            vm.$emit("failTrigger_out", new Date().getTime());
          }
          vm.isSaving = false;
        },
        () => {
          vm.$emit("failTrigger_out", new Date().getTime());
          vm.isSaving = false;
        }
      );
    },
    doWrite(writeBody) {
      let vm = this;
      //开发一个外部设置调用接口的自定义逻辑
      let writeMethod;
      let writeFunc = vm.dataConfig.writeFunc;
      if (writeFunc) {
        //如果设置了自定义接口处理函数,则调用自定义处理函数处理
        writeMethod = customApi[writeFunc];
      } else {
        //调用默认的模型api
        writeMethod = writeModel;
      }
      return writeMethod(writeBody);
    }
  }
};
</script>
<style lang="scss" scoped></style>
