<template>
  <div class="gianttree">
    <div class="line-bottom" v-if="this.showFilter">
      <div class="device-search">
        <slot>
          <el-input
            :id="search"
            :placeholder="i18n('输入关键字以检索')"
            v-model="filterText"
          ></el-input>
        </slot>
      </div>
    </div>
    <div class="ztree" :id="ztree" :style="{ height: height }">
      <div
        v-show="inputData_in.length"
        :id="ztreeId"
        v-bind="$attrs"
        v-on="$listeners"
      ></div>
      <div v-show="inputData_in.length < 1" class="no-data">
        {{ i18n("暂无数据") }}
      </div>
    </div>
  </div>
</template>

<script>
import "@ztree/ztree_v3/js/jquery.ztree.core.min.js";
import "@ztree/ztree_v3/js/jquery.ztree.excheck.min.js";
import "@ztree/ztree_v3/js/jquery.ztree.exhide.min.js";
import "@ztree/ztree_v3/js/jquery.ztree.exedit.min.js";
import { fuzzySearch } from "../cet-ztree/fuzzysearch";
import { i18n } from "../local/index.js";

export default {
  name: "CetGiantTree",
  props: {
    inputData_in: {
      type: Array,
      default: () => []
    },
    setting: {
      type: Object,
      require: false,
      default: () => {}
    },
    //指定勾选的节点
    checkedNodes: {
      type: Array,
      default: () => []
    },
    // 选择指定节点,就会自动展开其父节点的； 但前提是 如果你用的是异步加载模式，那么请务必保证该节点已经被加载才可以的
    selectNode: {
      type: Object,
      default: () => {}
    },
    //搜索条件
    searchText_in: {
      type: String,
      default: ""
    },
    // 是否显示搜索
    showFilter: {
      type: Boolean,
      default: true
    },
    unCheckTrigger_in: {
      type: Number
    },
    // 是否开启shift多选，默认关闭
    openShiftCheck: {
      type: Boolean,
      default: true
    },
    // 是否开启设置层级节点,objectModels需要按照查询模型规则
    openSettingLevelNode: {
      type: Object,
      default: () => {
        return {
          open: false,
          objectModels: {
            dcbase: "园区",
            building: "楼栋",
            floor: "楼层",
            room: "房间",
            battery: "设备"
          }
        };
      }
    },
    customSearch: {
      type: Function,
      default: fuzzySearch
    }
  },
  data() {
    return {
      search: "search_" + parseInt(Math.random() * 1e10),
      filterText: "",
      height: "calc(100% - 60px)",
      showNodeCount: 0,
      ztreeId: "ztree_" + parseInt(Math.random() * 1e10),
      // treeNode: {
      //   nocheck: true
      // },
      ztreeSetting: {
        // check: { //多选，不配置则默认单选
        //   enable: true
        //   chkStyle: "checkbox",
        //   chkboxType: { Y: "p", N: "s" }
        // },
        view: {
          showIcon: false, //显示节点图标
          showLine: false, //显示连接线
          expandSpeed: ""
          // nameIsHTML: true, //允许name支持html
          // selectedMulti: false,
          // txtSelectedEnable: true
        },
        callback: {
          onClick: this.zTreeOnClick,
          onCheck: this.zTreeOnCheck,
          onNodeCreated: this.onNodeCreated
        }
        // data: {
        //   simpleData: {
        //     enable: true,
        //     idKey: "tree_id" //节点的唯一性
        //   }
        // }
        // callback: {
        //   onClick: this.zTreeOnClick,
        //   onCheck: this.zTreeOnCheck,
        //   created_out: this.created_out
        // }
      },
      timeoutId: null,
      lastKeyword: "",
      ztree: "ztree",
      hideAllChecks: [] //隐藏勾选节点
    };
  },
  computed: {},
  beforeCreate() {
    this.ztreeId = "ztree_" + parseInt(Math.random() * 1e10);
    this.selectRow = {}; //历史选中值
    this.ztreeObj = null;
    this.checkeData = []; //历史勾选值
  },
  watch: {
    filterText(val) {
      this.delaySearch(val);
      if (this.searchText_in !== val) {
        this.$emit("update:searchText_in", val);
      }
    },
    searchText_in(val) {
      this.filterText = val;
    },
    checkedNodes: {
      deep: true,
      handler: function (val) {
        if (this.checkeData != val && this.inputData_in.length > 0) {
          // this.getSetCheckboxData(val, this.checkeData);
          let key = this.setting.data.simpleData.idKey;
          // 勾选数据对比是否id相同，val值在checkeData未找到，会吧数据加入数组组成新数组
          let list = _.differenceBy(val, this.checkeData, key);
          // list 有值说明有需要的勾选的参数未勾选
          // 判断设置勾选的数据与已勾选的数据是否一致
          if (list.length === 0 && this.checkeData.length === val.length) {
            return;
          } else {
            this.doCheckedNodes(val);
          }
        }
      }
    },
    inputData_in: {
      handler: function (val) {
        const vm = this;
        if (vm.ztreeObj) {
          // 销毁当前tree
          vm.ztreeObj.destroy();
        }
        // 初始化判断是否需要添加自定义控件
        if (this.openSettingLevelNode.open) {
          let obj = {
            addDiyDom: this.ztreeAddDiyDom
          };
          _.assign(vm.ztreeSetting.view, obj);
        }
        // 如果是空数组则更新tree，不输出选中、勾选数据
        if (val.length < 1) {
          vm.ztreeObj = $.fn.zTree.init(
            $("#" + vm.ztreeId),
            _.merge({}, vm.ztreeSetting, vm.setting),
            val
          );
        } else {
          this.$nextTick(() => {
            vm.setInputData();
          });
        }
      },
      immediate: true
    },

    selectNode: {
      deep: true,
      handler: function (val) {
        let key = this.setting.data.simpleData.idKey;
        // 如当前inputData为空，则不走后续输出选中节点逻辑
        if (
          !_.isEmpty(val) &&
          this.selectRow[key] !== val[key] &&
          this.inputData_in.length > 0
        ) {
          this.doSelectNode(val);
        }
      }
    },
    unCheckTrigger_in: {
      deep: true,
      handler: function () {
        this.unCheck();
      }
    }
  },
  mounted() {
    // this.$nextTick(() => {
    if (this.showFilter) {
      this.height = "calc(100% - 60px)";
    } else {
      this.height = "100%";
    }
    // });
  },
  methods: {
    zTreeOnClick(event, treeId, treeNode) {
      let key = this.setting.data.simpleData.idKey;
      if (treeNode[key] == this.selectRow[key]) {
        return;
      }
      this.selectRow = treeNode;
      this.$emit("update:selectNode", treeNode);
      this.$emit("currentNode_out", treeNode);
    },
    zTreeOnCheck(event, treeId, treeNode) {
      // 判断是否勾选
      if (!this.openShiftCheck) {
        return this.outputCheckedNodes();
      }
      // 按住shift多选
      const vm = this;
      var preClickedNode = window.preClickedNode; //定义变量
      window.preClickedNode = treeNode;
      event = window.event || event; //兼容IE

      if (!event.shiftKey || !preClickedNode) {
        return this.outputCheckedNodes();
      } // shift键
      // 起点和终点父节点是否是同一个
      if (preClickedNode.getParentNode() != treeNode.getParentNode()) {
        preClickedNode = null;
        return;
      }
      var treeObj = $.fn.zTree.getZTreeObj(treeId);
      var firstNode = treeObj.getNodeIndex(preClickedNode); //起点节点的序号
      var lastNode = treeObj.getNodeIndex(treeNode); //终点节点的序号
      var count = lastNode - firstNode;

      var nodeNew = preClickedNode;
      if (count > 0) {
        if (!_.isEmpty(preClickedNode) && preClickedNode.checked) {
          setCheck(true, count, 0, "getNextNode");
        } else {
          setCheck(false, count, 0, "getNextNode");
        }
      } else if (count < 0) {
        if (!_.isEmpty(preClickedNode) && preClickedNode.checked) {
          setCheck(true, -count, 0, "getPreNode");
        } else {
          setCheck(false, -count, 0, "getPreNode");
        }
      } else {
        if (!_.isEmpty(preClickedNode) && !preClickedNode.checked) {
          setCheck(true, -count, 1, "getPreNode");
        } else {
          setCheck(false, -count, 1, "getPreNode");
        }
      }
      window.preClickedNode = null;
      this.outputCheckedNodes();

      /**
       * @description: 设置节点的勾选
       * @param {*} state true勾选 false取消勾选
       * @param {*} count 选中顺序(起点、终点) 从上往下 count 从下往上 -count
       * @param {*} k 循环中i的值
       * @param {*} adjacentNodes 相邻节点函数名 getNextNode后一个节点  getPreNode前一个节点
       * @return {*}
       */
      function setCheck(state, count, k, adjacentNodes) {
        // 设置节点的勾选
        vm.setChildNode([preClickedNode], treeObj, state);
        for (var i = k; i < count; i++) {
          //根据参数，反向获取相邻节点
          nodeNew = nodeNew[adjacentNodes]();
          // if(!nodeNew)break;//用于排除隐患
          vm.setChildNode([nodeNew], treeObj, state);
          treeObj.checkNode(nodeNew, state, true);
          // treeObj.checkNode(menuArr[i], true, true);
        }
      }
    },
    // 递归设置节点勾选
    setChildNode(data, treeObj, state = true) {
      let menuArr = [];
      spread(data);

      function spread(menus) {
        for (var i = 0; i < menus.length; i++) {
          let menu = menus[i];
          if (!_.isEmpty(menu.children)) {
            spread(menu.children);
            // delete menu.children;
          }
          menuArr.push(menu);
          treeObj.checkNode(menu, state, true);
        }
      }
    },
    onNodeCreated(event, treeId, treeNode) {
      this.showNodeCount++;
    },
    setInputData() {
      const vm = this;
      vm.showNodeCount = 0;
      // var time1 = new Date();

      // vm.ztreeObj.expandAll(true); 设置展开所有节点
      // var time2 = new Date();

      var obj1 = _.get(vm.setting, "check.chkboxType", {});
      var obj2 = { Y: "", N: "" };
      if (_.isEqual(obj1, obj2)) {
        vm.ztree = "giantTree";
      } else {
        vm.ztree = "ztree";
      }

      // assign 只会浅拷贝，相同的属性值，取后一个，不会合并
      vm.ztreeObj = $.fn.zTree.init(
        $("#" + vm.ztreeId),
        _.merge({}, vm.ztreeSetting, vm.setting),
        vm.inputData_in
      );

      this.filterText = this.searchText_in;
      // console.log(
      //   "节点共 " +
      //     vm.inputData_in.length +
      //     " 个, 初始化生成 DOM 的节点共 " +
      //     vm.showNodeCount +
      //     " 个" +
      //     "\n\n 初始化 zTree 共耗时: " +
      //     (time2.getTime() - time1.getTime()) +
      //     " ms"
      // );
      vm.$emit("created_out", vm.ztreeObj);
      this.doSelectNode(this.selectNode);
      this.doCheckedNodes(this.checkedNodes);
    },
    unCheck() {
      const vm = this;
      let treeObj = vm.ztreeObj;
      if (!treeObj) {
        return;
      }

      this.$emit("update:selectNode", {});
      this.$emit("update:checkedNodes", []);
      this.checkeData = [];

      // 取消整颗tree选中状态
      vm.ztreeObj.cancelSelectedNode();
      // 清除当前选中行的数据，避免下次设置selectNode同上一次点击时一个值时，判断不通过
      vm.selectRow = {};
      // 取消tree勾选
      vm.ztreeObj.checkAllNodes(false);
    },
    doSelectNode(val) {
      const vm = this;
      let treeObj = vm.ztreeObj;
      let key = vm.setting.data.simpleData.idKey;
      if (!treeObj || _.isEmpty(val)) {
        return;
      }
      //获取改节点在tree的内置对象
      let node = vm.ztreeObj.getNodeByParam(key, val[key]);
      // 展开选中节点
      treeObj.expandNode(node, true);
      if (_.isEmpty(node)) return;
      //设置选中
      vm.ztreeObj.selectNode(node, false);
      // 获取当前选中的节点数据
      // var obj = vm.ztreeObj.getSelectedNodes();
      if (node[key] == this.selectRow[key]) {
        return;
      }
      this.selectRow = node;
      vm.$emit("update:selectNode", node);
      vm.$emit("currentNode_out", node);
    },
    // 设置勾选节点，数据双向绑定，设置后，勾选状态根据参数设置
    doCheckedNodes(val) {
      const vm = this;

      let treeObj = vm.ztreeObj;
      let treeAllData = treeObj.getNodes(); //获取ztree全部节点
      treeObj.checkAllNodes(false); // 全部取消节点
      // 勾选数据为空，checkeData同步，避免下一次赋值相同导致无法勾选
      if (!treeObj || _.isEmpty(val)) {
        if (_.isEmpty(val)) this.checkeData = [];
        return;
      }
      let obj = {};
      let key = vm.setting.data.simpleData.idKey;
      let children = _.get(vm.setting.data, "key.children", "children");
      val.forEach(item => (obj[item[key]] = 0));
      // let arr2 = [];

      this.setData(obj, treeAllData, treeObj, key, children);
      // var res1 = _.xor(val, arr1); //获取的选中节点且CheckedNodes中无
      // var res2 = _.xor(val, arr2);

      // res1.forEach(item => {
      //   let node = vm.ztreeObj.getNodeByParam(key, item[key]);
      //   if (node != null) {
      //     vm.ztreeObj.checkNode(node, false, true);
      //   }
      // });
      // res2.forEach(item => {
      //   var node = treeObj.getNodeByParam(key, item[key]);
      //   if (node != null) {
      //     treeObj.checkNode(node, true, true);
      //   }
      // });
      vm.outputCheckedNodes();
    },
    //递归设置勾选
    setData(obj, tree, ztreeObj, key, children) {
      tree.map(item => {
        if (item[key] in obj) {
          ztreeObj.checkNode(item, true, true);
        }
        if (item[children]) {
          this.setData(obj, item[children], ztreeObj, key, children);
        }
      });
    },
    unique(arr) {
      let key = this.setting.data.simpleData.idKey;
      const res = new Map();
      return arr.filter(arr => !res.has(arr[key]) && res.set(arr[key], 1));
    },
    // 输出选中的节点(多个)
    outputCheckedNodes() {
      const vm = this;
      let treeObj = vm.ztreeObj;
      let nodeList = treeObj.getCheckedNodes();
      // // 排除父节点
      // let nodeList = treeObj.getCheckedNodes().filter(item => !item.isParent);
      // // 合并当前显示勾选与隐藏勾选，并排除父节点tree_id相同的数据
      // let data = vm.unique(_.concat(nodeList, vm.hideAllChecks));

      // vm.AllParentNode(vm.inputData_in, data);
      // vm.chooseCheck(dataList);

      let treeNode = [];
      let checkedNodes = this.checkedNodes;
      // 父子关联时，则排除半选节点，不关联则输出data
      let type = _.get(vm.setting, "check.chkboxType", {});

      // 判断对象是否为父子不关联模式
      if (_.isEqual(type, { Y: "", N: "" })) {
        // // 合并当前显示勾选与隐藏勾选，并排除父节点tree_id相同的数据
        let data = vm.unique(_.concat(nodeList, vm.hideAllChecks));
        treeNode = data;
      } else if (_.isEmpty(type) || _.isEqual(type, { Y: "ps", N: "ps" })) {
        // 输出当前显示勾选的节点
        nodeList.forEach((item, index) => {
          //  过滤父节点,nodes[i].getCheckStatus().half===true表示是半选中
          if (!nodeList[index].getCheckStatus().half) {
            treeNode.push(item);
          }
        });
      }
      vm.checkeData = treeNode.length ? treeNode : checkedNodes;
      // 数据双向绑定，更新checkedNodes的值
      this.$emit("update:checkedNodes", treeNode);
      // 输出选中节点，不包含半选
      this.$emit("checkedNodes_out", treeNode);
    },
    delaySearch(val) {
      const vm = this;
      if (vm.timeoutId) {
        clearTimeout(vm.timeoutId);
      }
      vm.timeoutId = setTimeout(function () {
        if (vm.lastKeyword === val) {
          return;
        }
        vm.hideAllChecks = vm.customSearch(vm.ztreeId, val, null); //模糊搜索方法
        vm.lastKeyword = val;
        vm.$emit("searchText_out", val);
      }, 400);
    },
    // 获取所有勾选节点（不含半选节点）
    AllCheckedNode() {
      let key = this.setting.data.simpleData.idKey;
      let data = this.inputData_in;
      let memo = {};
      let childNode = this.filterParentNode();
      childNode.forEach(element => {
        memo[element[key]] = element[key];
      });
      let parentList = [];
      const getnode = data => {
        // 判断是否叶子结点
        if (_.isEmpty(data.children)) {
          return !!memo[data[key]];
        } else {
          let status = [],
            check = true;
          // 父节点
          data.children.forEach((item, i) => {
            // 当前父节点下，子节点所有状态
            status[i] = getnode(item);
            check = check && status[i];
            // 判断status与子节点的数组长度是否相等，相等则表示相等且为true则为勾选
            if (check && status.length === data.children.length) {
              parentList.push(data);
            }
          });
          return check;
        }
      };
      data.forEach(item => {
        getnode(item);
      });
      return _.concat(parentList, childNode);
    },
    // 可获取所有子节点
    filterParentNode() {
      const vm = this;
      let treeObj = vm.ztreeObj;
      let nodeList = treeObj.getCheckedNodes();
      let data = _.concat(vm.hideAllChecks, nodeList);
      let childNode = data.filter(item => !item.isParent);
      return childNode;
    },
    // 添加父子不关联，快捷操作选项
    ztreeAddDiyDom(treeId, treeNode) {
      // 图标插入的位置
      var IDMark_A = "_a";
      // 插入的指定dom
      var aObj = $("#" + treeNode.tId + IDMark_A);
      this.dropDownExtend(aObj, treeNode);
    },
    // 创建自定义控件组件
    dropDownExtend(aObj, treeNode) {
      let objectModelsList = this.openSettingLevelNode.objectModels;
      let key = this.setting.data.simpleData.idKey;
      let treeId = treeNode[key];
      const dropDownComponent = Vue.extend({
        template: `<el-dropdown trigger="click" @command ='handleCommand(${JSON.stringify(
          treeNode.tId
        )},$event)' id='diyBtn_space_${treeId}' class='card-item-operate' placement="bottom">
          <span class="el-dropdown-link">
            <i class='el-icon-more iconMore'></i>
          </span>
          <el-dropdown-menu slot="dropdown"  id='diyBtn_${treeId} 'class='selDemo'>
           <el-dropdown-item disabled>选中</el-dropdown-item>
              <el-dropdown-item v-for='(val,name) in objectModels' :key='name'  :command='name+"$select"' >{{ val }}</el-dropdown-item>
            <el-dropdown-item disabled divided>取消</el-dropdown-item>
            <el-dropdown-item v-for='(val,name) in objectModels' :key='name+"$cancel"'  :command='name+"$cancel"' >{{ val }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>`,
        props: {
          setTickStatus: Function, //回调
          objectModels: Object
        },
        data() {
          return {};
        },
        computed: {},
        methods: {
          handleCommand(val, a) {
            this.setTickStatus({ tId: val, type: a });
          }
        }
      });

      let modelLabel = treeNode.modelLabel;
      let obj = {};
      let flag = false;
      // 当前节点存在哪些选项
      for (const key in objectModelsList) {
        if (key === modelLabel) {
          flag = true;
          continue;
        }
        if (flag) {
          obj[key] = objectModelsList[key];
        }
      }

      //参数为空或children为空则不添加操作按钮
      if (_.isEmpty(obj) || _.isEmpty(treeNode.children)) return;

      let instance = new dropDownComponent({
        propsData: {
          // 使用propsData向子组件传参
          setTickStatus: this.setTickStatus, //传入一个回调函数
          objectModels: obj //传入一个普通变量
        }
      });
      const component = instance.$mount();
      aObj.after(component.$el);
    },
    //设置勾选状态
    setTickStatus(val) {
      // 根据ztree唯一表示tid快速获取节点json数据对象，不需要遍历节点
      const tId = val.tId;
      let arr = val.type.split("$");
      let modelLabel = arr[0]; //模型
      let state = arr[1]; //设置状态取消、选中

      var treeObj = this.ztreeObj;
      var node = treeObj.getNodeByTId(tId);
      // 根据node对象循环设置节点状态
      var nodes = treeObj.transformToArray(node);

      let flag = state === "select" ? true : false;
      // 设置勾选
      _.forEach(nodes, element => {
        if (element.modelLabel === modelLabel) {
          treeObj.checkNode(element, flag, true);
        }
      });
      // 输出选中
      this.outputCheckedNodes();
    },
    i18n
  },
  beforeDestroy() {
    this.ztreeObj.destroy();
    this.selectRow = null;
    this.ztreeObj = null;
    this.checkeData = null;
  }
};
</script>

<style lang="scss" scoped>
/* 搜索框部分的样式 */
.gianttree {
  height: 100%;
  box-sizing: content-box !important;
  ::v-deep .ztree,
  ::v-deep .ztree *,
  ::v-deep .ztree *:before,
  ::v-deep .ztree *:after {
    box-sizing: content-box !important;
  }
}
.gianttree > .ztree {
  overflow: auto;
  height: 100%;
  @include background_color("BG1");
}
.line-bottom {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  @include border_direction_color("B1", "bottom");
}
.device-search {
  margin: 8px 0px;
}
.device-search input {
  border-radius: 20px;
}

/* 样式可参考
*element官网
*vue-giant-tree https://refined-x.com/Vue-Giant-Tree/ */
.ztree {
  text-align: left;
  font-size: 14px;
  position: relative;
}
.no-data {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
}
.ztree ::v-deep li {
  list-style-type: none;
  white-space: nowrap;
  outline: none;
}
.ztree ::v-deep li:nth-child(0):hover {
  background-color: rgb(17, 48, 94);
}
.ztree ::v-deep li ul {
  position: relative;
  padding: 0 0 0 20px;
  margin: 0;
  padding-left: 18px;
}

/* 显示连接线 */
.ztree ::v-deep .line:before {
  position: absolute;
  top: 0;
  left: 18px;
  height: 100%;
  content: "";
  border-right: 1px dotted;
  @include border_direction_color("B1", "right");
}
.ztree ::v-deep .roots_docu:before,
.ztree ::v-deep .roots_docu:after,
.ztree ::v-deep .center_docu:before,
.ztree ::v-deep .bottom_docu:before,
.ztree ::v-deep .center_docu:after,
.ztree ::v-deep .bottom_docu:after {
  position: absolute;
  content: "";
  border: 0 dotted #dbdbdb;
}
.ztree ::v-deep .roots_docu:before {
  left: 10px;
  height: 50%;
  top: 50%;
  border-left-width: 1px;
}
.ztree ::v-deep .roots_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}
.ztree ::v-deep .center_docu:before {
  left: 10px;
  height: 100%;
  border-left-width: 1px;
}
// .ztree ::v-deep .center_docu:after {
//   top: 50%;
//   left: 11px;
//   width: 50%;
//   border-top-width: 1px;
// }
.ztree ::v-deep .bottom_docu:before {
  left: 10px;
  height: 50%;
  border-left-width: 1px;
}
.ztree ::v-deep .bottom_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}
.ztree ::v-deep .roots_docu:before,
.ztree ::v-deep .roots_docu:after,
.ztree ::v-deep .center_docu:before,
.ztree ::v-deep .bottom_docu:before,
.ztree ::v-deep .center_docu:after,
.ztree ::v-deep .bottom_docu:after {
  position: absolute;
  content: "";
  border: 0 dotted #dbdbdb;
}
// .ztree ::v-deep .center_docu:before {
//   left: 10px;
//   height: 100%;
//   border-left-width: 1px;
// }
// .ztree ::v-deep .center_docu:after {
//   top: 50%;
//   left: 11px;
//   width: 50%;
//   border-top-width: 1px;
// }
/* ========================= */
.ztree ::v-deep li a {
  display: inline-block;
  line-height: 22px;
  height: 22px;
  margin: 0;
  cursor: pointer;
  transition: none;
  vertical-align: middle;
  @include font_color("T2");
}
/* .ztree ::v-deep li a:hover,
.ztree ::v-deep .button:hover {
  background-color: rgb(245, 247, 250);
} */
.ztree ::v-deep .node_name {
  display: inline-block;
  padding: 0 4px;
  border-radius: 2px;
}
.ztree ::v-deep .curSelectedNode .node_name {
  @include background_color("BG4");
  @include font_color("ZS");
  // background-color: #bfdbf4;
  /* #c9e9f7 */
}
.ztree ::v-deep .curSelectedNode_Edit {
  height: 20px;
  opacity: 0.8;
  @include font_color("T2");
  border: 1px #6cc2e8 solid;
  background-color: #9dd6f0;
}
.ztree ::v-deep .tmpTargetNode_inner {
  opacity: 0.8;
  color: #fff;
  background-color: #4fcbf0;
  filter: alpha(opacity=80);
}
.ztree ::v-deep .rename {
  font-size: 12px;
  line-height: 22px;
  width: 80px;
  height: 22px;
  margin: 0;
  padding: 0;
  vertical-align: top;
  border: 0;
  background: none;
}
.ztree ::v-deep .button {
  position: relative;
  display: inline-block;
  /* line-height: 22px; */
  height: 14px;
  width: 12px;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  padding: 6px;
}

.ztree ::v-deep .button.edit {
  color: #25ae88;
}
.ztree ::v-deep .button.remove {
  color: #cb4042;
}
.ztree ::v-deep .button.chk {
  position: relative;
  width: 12px;
  height: 12px;
  margin: 0 8px 0 0;
  border: 1px solid;
  border-radius: 2px;
  @include background_color(BG1);
  @include border_color(B1);
  padding: 0;
}
.ztree ::v-deep .chk.radio_true_full,
.ztree ::v-deep .chk.radio_false_full,
.ztree ::v-deep .chk.radio_true_full_focus,
.ztree ::v-deep .chk.radio_false_full_focus,
.ztree ::v-deep .chk.radio_false_disable,
.ztree ::v-deep .chk.radio_true_disable,
.ztree ::v-deep .chk.radio_true_part,
.ztree ::v-deep .chk.radio_false_part,
.ztree ::v-deep .chk.radio_true_part_focus,
.ztree ::v-deep .chk.radio_false_part_focus {
  border-radius: 8px;
}
.ztree ::v-deep .button.checkbox_true_full:after,
.ztree ::v-deep .button.checkbox_true_full_focus:after,
.ztree ::v-deep .button.checkbox_true_disable:after,
/* 父子不关联模式下，checkbox半选需改成勾选 ，添加一个Id，去设置不同样式*/
#giantTree ::v-deep .button.checkbox_true_part:after,
#giantTree ::v-deep .button.checkbox_true_part_focus:after {
  box-sizing: content-box;
  content: "";
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(1);
  width: 3px;
  transform-origin: center center;
  border-width: 0px 1px 1px 0px;
  border-style: solid solid;
  border-color: rgb(255, 255, 255) rgb(255, 255, 255);
  border-image: initial;
  border-left: 0px;
  border-top: 0px;
  transition: transform 0.15s ease-in 0.05s;
}
.ztree ::v-deep .button.checkbox_false_full_focus {
  border-color: #ccc;
}
.ztree ::v-deep .button.checkbox_true_full,
.ztree ::v-deep .button.checkbox_true_full_focus,
.ztree ::v-deep .button.checkbox_true_part,
.ztree ::v-deep .button.checkbox_true_part_focus,
.ztree ::v-deep .button.checkbox_true_disable {
  // border-color: #39f;
  // background-color: #39f;
  @include background_color("ZS");
  @include border_color("ZS");
}
.ztree ::v-deep .button.checkbox_true_full:after,
.ztree ::v-deep .button.checkbox_true_full_focus:after,
.ztree ::v-deep .button.checkbox_true_disable:after {
  -webkit-transform: rotate(45deg) scale(1);
  transform: rotate(45deg) scale(1);
}
#ztree.ztree ::v-deep .button.checkbox_true_part::before,
#ztree.ztree ::v-deep .button.checkbox_true_part_focus::before {
  content: "";
  position: absolute;
  display: block;
  background-color: rgb(255, 255, 255);
  height: 2px;
  transform: scale(0.5);
  left: 0px;
  right: 0px;
  top: 5px;
  width: 12px;
}
.ztree ::v-deep .button.radio_true_full,
.ztree ::v-deep .chk.radio_true_full_focus,
.ztree ::v-deep .chk.radio_true_part,
.ztree ::v-deep .chk.radio_true_part_focus {
  border-color: #39f;
}
.ztree ::v-deep .button.radio_true_full:after,
.ztree ::v-deep .chk.radio_true_full_focus:after,
.ztree ::v-deep .chk.radio_true_part:after,
.ztree ::v-deep .chk.radio_true_part_focus:after {
  top: 3px;
  left: 3px;
  width: 8px;
  -webkit-transform: rotate(0deg) scale(1);
  transform: rotate(0deg) scale(1);
  border: 0;
  border-radius: 4px;
  background: #39f;
}
.ztree ::v-deep .button.checkbox_true_disable,
.ztree ::v-deep .button.checkbox_false_disable,
.ztree ::v-deep .chk.radio_false_disable,
.ztree ::v-deep .chk.radio_true_disable {
  cursor: not-allowed;
}
.ztree ::v-deep .button.checkbox_false_disable {
  background-color: #edf2fc;
}
.ztree ::v-deep .button.noline_close:before,
.ztree ::v-deep .button.noline_open:before,
.ztree ::v-deep .button.root_open:before,
.ztree ::v-deep .button.root_close:before,
.ztree ::v-deep .button.roots_open:before,
.ztree ::v-deep .button.roots_close:before,
.ztree ::v-deep .button.bottom_open:before,
.ztree ::v-deep .button.bottom_close:before,
.ztree ::v-deep .button.center_open:before,
.ztree ::v-deep .button.center_close:before {
  position: absolute;
  top: 8px;
  left: 10px;
  content: "";
  transition: -webkit-transform ease 0.3s;
  transition: transform ease 0.3s;
  transition: transform ease 0.3s, -webkit-transform ease 0.3s;
  -webkit-transform: rotateZ(0deg);
  transform: rotateZ(0deg);
  -webkit-transform-origin: 25% 50%;
  transform-origin: 25% 50%;
  border: 4px solid;
  border-color: transparent transparent transparent rgb(192, 196, 204);
}
.ztree ::v-deep .button.noline_open:before,
.ztree ::v-deep .button.root_open:before,
.ztree ::v-deep .button.roots_open:before,
.ztree ::v-deep .button.bottom_open:before,
.ztree ::v-deep .button.center_open:before {
  -webkit-transform: rotateZ(90deg);
  transform: rotateZ(90deg);
}
.ztree ::v-deep .button.ico_loading {
  margin-right: 2px;
  background: url("data:image/gif;base64,R0lGODlhEAAQAKIGAMLY8YSx5HOm4Mjc88/g9Ofw+v///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgAGACwAAAAAEAAQAAADMGi6RbUwGjKIXCAA016PgRBElAVlG/RdLOO0X9nK61W39qvqiwz5Ls/rRqrggsdkAgAh+QQFCgAGACwCAAAABwAFAAADD2hqELAmiFBIYY4MAutdCQAh+QQFCgAGACwGAAAABwAFAAADD1hU1kaDOKMYCGAGEeYFCQAh+QQFCgAGACwKAAIABQAHAAADEFhUZjSkKdZqBQG0IELDQAIAIfkEBQoABgAsCgAGAAUABwAAAxBoVlRKgyjmlAIBqCDCzUoCACH5BAUKAAYALAYACgAHAAUAAAMPaGpFtYYMAgJgLogA610JACH5BAUKAAYALAIACgAHAAUAAAMPCAHWFiI4o1ghZZJB5i0JACH5BAUKAAYALAAABgAFAAcAAAMQCAFmIaEp1motpDQySMNFAgA7")
    0 center no-repeat;
}
.ztree ::v-deep .tmpTargetzTree {
  opacity: 0.8;
  background-color: #2ea9df;
  filter: alpha(opacity=80);
}
.ztree ::v-deep .tmpzTreeMove_arrow {
  position: absolute;
  width: 18px;
  height: 18px;
  color: #4fcbf0;
}
// 更多 ... 旋转，及hover颜色配置
.ztree ::v-deep .iconMore {
  transform: rotate(90deg);
  border: 4px solid transparent;
  // ::v-deep &:hover {
  //   @include font_color(F1);
  // }
}
.ztree ::v-deep .iconMore:hover::before {
  @include font_color(F1);
}
// 之前是全局配置
::v-deep ul.ztree.zTreeDragUL {
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
  background-color: #dedede;
  border: 1px #4fcbf0 dotted;
  border-radius: 4px;
  opacity: 0.7;
}

::v-deep .zTreeMask {
  position: absolute;
  z-index: 10000;
  opacity: 0;
  background-color: #cfcfcf;
}
::v-deep .ztreeHide {
  display: none;
}
</style>
