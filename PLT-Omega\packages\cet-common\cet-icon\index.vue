<template>
  <svg class="svg-icon" aria-hidden="true" v-bind="$attrs" v-on="$listeners">
    <use :xlink:href="iconName" />
  </svg>
</template>

<script>
export default {
  name: "CetIcon",
  props: {
    iconClass: {
      type: String,
      required: true
    }
  },
  computed: {
    iconName() {
      return `#${this.iconClass}`;
    }
  }
};
</script>

<style scoped lang="scss">
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
  transition-duration: 0.3s;
  border-radius: 4px;
  box-sizing: border-box;
}

.hover {
  cursor: pointer;
  &:hover {
    @include background_color(BG2);
  }
  &:focus {
    @include background_color(BG3);
  }
}

.active {
  @include background_color(ZS);
}

.I1 {
  width: mh-get(I1);
  height: mh-get(I1);
}
.I2 {
  width: mh-get(I2);
  height: mh-get(I2);
}
.I3 {
  width: mh-get(I3);
  height: mh-get(I3);
}
.I4 {
  width: mh-get(I4);
  height: mh-get(I4);
}
.I5 {
  width: mh-get(I5);
  height: mh-get(I5);
}

$j: 0;
@while $j <= 8 {
  .p#{$j} {
    padding: #{$j}px;
  }
  $j: $j + 1;
}
</style>
