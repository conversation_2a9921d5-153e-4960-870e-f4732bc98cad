<script>
import { buildQueryBody } from "../utils/generateQuery";
import { checkQueryParams } from "../utils/checkQueryParams";
import { api as customApi } from "../api";
import { queryModel } from "../baseApi/model";

export default {
  components: {},
  name: "CetInterface",
  props: {
    //表格的查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    //双向绑定的值, interface的值会和父组件中的data同步
    data: {
      type: [Array, Object, Number, String, Boolean]
    },
    dataConfig: Object,
    //调用接口的触发器
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    //查询数据的id入参
    queryId_in: {
      type: Number,
      default: -1
    },
    queryTrigger_in: {
      type: [Number, Object, String]
    },

    //动态输入
    dynamicInput: {
      type: Object
    },
    page_in: {
      type: Object
    },
    refreshAfterActived: {
      type: Boolean,
      default: false
    },
    hideNotice: {
      type: <PERSON>olean,
      default: false
    }
  },
  render(createElement) {
    return "";
  },
  data() {
    return {
      activatedNum: 0, //组件activated钩子触发的次数
      orders: this.$attrs.defaultSort ? this.$attrs.defaultSort : null
    };
  },
  watch: {
    queryNode_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    //queryId_in变化则执行查询
    queryId_in(val) {
      this.paramsChange();
    },
    //按钮触发查询
    queryTrigger_in() {
      var vm = this;
      this.getTableData();
    },
    //条件变化直接查询
    dynamicInput: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    page_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  methods: {
    //对查询参数变化从而查询表格数据的，判断参数是否有变化，没变化的不查询
    paramsChange() {
      const vm = this;
      if (vm.queryMode === "diff") {
        const queryBody = vm.getQueryBody();
        if (vm._.isEqual(vm.oldQueryBody, queryBody)) {
          return;
        }
        vm.getTableData();
      }
    },
    //获取查询接口的入参
    getQueryBody() {
      const vm = this;

      return buildQueryBody({
        modelLabel: vm.dataConfig.modelLabel,
        modelList: vm.dataConfig.modelList,
        dataIndex: vm.dataConfig.dataIndex,
        treeReturnEnable: vm.dataConfig.treeReturnEnable,
        filters: vm.dataConfig.filters,
        dynamicInput: vm.dynamicInput,
        page: vm.page_in,
        orders: vm.orders,
        props: [],
        id: vm.queryId_in,
        treeNode: vm.queryNode_in
      });
    },
    doQuery(queryBody) {
      const vm = this;

      //开发一个外部设置调用接口的自定义逻辑
      let queryMethod;
      const queryFunc = vm.dataConfig.queryFunc;
      if (queryFunc) {
        //如果设置了自定义接口处理函数,则调用自定义处理函数处理
        queryMethod = customApi[queryFunc];
      } else {
        //调用默认的模型api
        queryMethod = queryModel;
      }
      return queryMethod(queryBody, vm.hideNotice);
    },
    // 从接口获取表格的数据
    getTableData() {
      const vm = this;
      let queryBody = {};
      let tableData = [];

      //组织body入参
      queryBody = vm.getQueryBody();

      //入参校验, 如果不是所有必要入参都是有效值, 则不执行查询
      if (
        !checkQueryParams({
          queryBody: {
            treeNode: vm.queryNode_in,
            filters: vm.dataConfig.filters,
            dynamicInput: vm.dynamicInput,
            id: vm.queryId_in
          },
          hasQueryNode: vm.dataConfig.hasQueryNode,
          hasQueryId: vm.dataConfig.hasQueryId
        })
      ) {
        console.log(
          `cet-interface queryFunc:${vm.dataConfig.queryFunc} modelLabel:${vm.dataConfig.modelLabel}入参校验未通过`,
          {
            queryBody: {
              treeNode: vm.queryNode_in,
              filters: vm.dataConfig.filters,
              dynamicInput: vm.dynamicInput,
              id: vm.queryId_in
            },
            hasQueryNode: vm.dataConfig.hasQueryNode,
            hasQueryId: vm.dataConfig.hasQueryId
          }
        );
        return;
      }

      //先保存接口入参，如果接口失败，则置vm.oldQueryBody为undefined，用于判断是否是重复的接口调用
      vm.oldQueryBody = vm._.cloneDeep(queryBody);
      //调用接口
      vm.doQuery(queryBody).then(
        response => {
          if (response.code === 0) {
            tableData = response.data;
            vm.$emit("result_out", tableData);
            vm.$emit("update:data", tableData);
            vm.$emit("totalNum_out", response.total);
          } else {
            vm.$emit("failTrigger_out", new Date().getTime());
            this.oldQueryBody = undefined;
          }
          vm.$emit("finishTrigger_out", new Date().getTime());
        },
        () => {
          vm.$emit("finishTrigger_out", new Date().getTime());
          vm.$emit("failTrigger_out", new Date().getTime());
          this.oldQueryBody = undefined;
        }
      );
    }
  },
  mounted: function () {
    const vm = this;
    //设置通过默认值获取表格数据的情况
    if (vm.queryMode === "diff") {
      // 获取到设备表格数据
      this.getTableData();
    }
  },
  activated() {
    const vm = this;
    vm.activatedNum++;
    //第一次激活不执行逻辑，后续激活更新数据
    if (vm.activatedNum > 1 && vm.refreshAfterActived) {
      vm.getTableData();
    }
  },
  created() {
    const vm = this;
    const filters = vm.dataConfig.filters;
    const dynamic = {};
    for (let i = 0; i < filters.length; i++) {
      let filterValue = vm.dynamicInput[filters[i].name];
      filterValue = vm._.isNil(filterValue) ? null : filterValue;
      vm.$set(dynamic, filters[i].name, filterValue);
      vm.$emit("update:dynamicInput", dynamic);
    }
  }
};
</script>
<style lang="scss" scoped></style>
