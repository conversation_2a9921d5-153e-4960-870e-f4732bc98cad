<template>
  <div>
    <el-input
      :style="`width: ${width}px`"
      ref="input"
      @focus="openDialog"
      :value="coordinatesText"
      placeholder="请选择"
      suffix-icon="el-icon-location-outline"
    ></el-input>
    <Dialog
      :autoLocate="autoLocate"
      @close="handleClose"
      v-model="show"
      :initialCoordinates="coordinates"
      @confirm="setCoordinates"
    />
  </div>
</template>

<script>
import { toFixed2 } from '../utils/common.js';
import Dialog from "./SelectMapPointDialog.vue";

export default {
  name: "SelectMapPoint",
  components: { Dialog },
  props: {
    width: {
      type: [Number, String],
      default: 200
    },
    value: {
      type: Array,
      default: () => []
    },
    autoLocate: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      coordinates: [...this.value],
      dialogVisible: false,
      show: false
    };
  },
  computed: {
    coordinatesText() {
      if (this.coordinates.length) {
        const [lng, lat] = this.coordinates;

        return `${toFixed2(lng, 4)}, ${toFixed2(lat, 4)}`;
      }
      return "";
    }
  },
  methods: {
    openDialog() {
      this.show = true;
    },
    setCoordinates(val) {
      this.$emit("input", [...val]);
      this.coordinates = val;
    },
    handleClose() {
      this.$refs.input.blur();
    }
  }
};
</script>

<style lang="scss" scoped>
.mapContainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0;
  z-index: -1;
}
</style>
