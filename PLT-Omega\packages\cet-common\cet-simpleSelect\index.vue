<template>
  <div>
    <ElSelect v-bind="$attrs" v-on="$listeners">
      <ElOption
        v-for="item in optionData"
        :key="item[$attrs.option.key]"
        :label="item[$attrs.option.label]"
        :value="item[$attrs.option.value]"
        :disabled="item[$attrs.option.disabled]"
      />
    </ElSelect>
    <CetInterface
      :data.sync="$attrs.interface.data"
      :dynamic-input.sync="$attrs.interface.dynamicInput"
      v-bind="$attrs.interface"
      @result_out="result_out"
      @finishTrigger_out="finishTrigger_out"
    />
  </div>
</template>

<script>
export default {
  name: "CetSimpleSelect",
  props: {},
  data() {
    return {
      optionData: []
    };
  },
  computed: {},
  created() {},
  mounted() {},
  watch: {},
  methods: {
    result_out(val) {
      this.optionData = val;
    },
    finishTrigger_out(val) {}
  },
  components: {}
};
</script>

<style scoped lang="scss"></style>
