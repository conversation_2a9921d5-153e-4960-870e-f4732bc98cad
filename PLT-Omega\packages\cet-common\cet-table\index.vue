<template>
  <el-container
    class="table-container"
    @keyup.native="shiftUp"
    @keydown.native="shiftDown"
  >
    <el-main style="padding: 0px">
      <el-table
        ref="cetTable"
        :data="tableData"
        :tooltip-effect="tooltipEffect"
        :border="border"
        :highlight-current-row="highlightCurrentRow"
        height="true"
        style="height: 100%; width: 100%"
        @current-change="handlerCurrentChange"
        @sort-change="sortChange"
        @selection-change="handleSelectionChange"
        @select="pinSelect"
        @select-all="selectAll"
        @header-dragend="doLayout"
        v-bind="$attrs"
        v-on="$listeners"
      >
        <template v-slot:default>
          <slot name="default" />
        </template>
        <template v-slot:append>
          <slot name="append" />
        </template>
      </el-table>
    </el-main>
    <el-footer
      v-show="showPaginationUI"
      v-if="showPagination"
      height="32px"
      style="padding: 0px"
    >
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentPageChange"
        :current-page.sync="currentPage"
        :page-size.sync="pageSize"
        :total="totalCount"
        :layout="pageLayout"
        :page-sizes="pageSizes"
        v-bind="paginationCfg"
      />
    </el-footer>
  </el-container>
</template>

<script>
import _ from "lodash";
import { buildQueryBody } from "../utils/generateQuery";
import { checkQueryParams } from "../utils/checkQueryParams";
import { queryModel, deleteModelInstence } from "../baseApi/model";
import { api as customApi } from "../api";
import { i18n } from "../local/index.js";
import { defaultSettings } from "../defaultSetting.js";
import useColumsHeaderDoLayout from "./useColumsHeaderDoLayout.js";

import Vue from "vue";
export default {
  components: {},
  name: "CetTable",
  props: {
    // 表格是否有树形结构数据
    isTreeData: {
      type: Boolean,
      default: false
    },
    //表格的查询模式 //查询按钮触发trigger，或者查询条件变化立即查询diff
    queryMode: {
      type: String
    },
    //表格数据获取模式 // 数据获取模式：backendInterface后端接口 ；其他组件 component; 静态数据 static
    dataMode: {
      type: String
    },
    //数据绑定配置
    dataConfig: {
      type: Object
    },
    //表格接受其他组件输入，并输出内部值变化到外部
    data: {
      type: Array
    },
    //查询节点输入
    queryNode_in: {
      type: Object
    },
    //导出操作触发
    exportTrigger_in: {
      type: Number
    },
    //查询按钮状态输入，状态变化执行查询
    queryTrigger_in: {
      type: Number
    },
    //删除按钮状态输入，状态变化执行删除
    deleteTrigger_in: {
      type: Number
    },
    // 删除按钮状态输入.状态变化执行本地删除
    localDeleteTrigger_in: {
      type: Number
    },
    // 批量删除按钮状态触发，状态变化执行删除
    batchDeletionTrigger_in: {
      type: Number
    },
    // 批量删除按钮状态触发，状态变化执行本地批删除
    localBatchDeletionTrigger_in: {
      type: Number
    },
    //取消操作后执行删除状态输入，状态变化执行删除
    // deleteWithoutConfirmTrigger_in: {
    //   type: Number
    // },
    //刷新表格数据
    refreshTrigger_in: {
      type: [Number]
    },
    //新增表格记录
    addData_in: {
      type: Object
    },
    //编辑表格记录
    editData_in: {
      type: Object
    },
    // 设置勾选的数据，如该id记录不存在，则multiSelectData_out会输出[undefined]
    multiSelectData_in: {
      type: Array
    },
    // 设置选中并高亮当前行
    selectRowData_in: {
      type: Object
    },
    //清空表格数据
    clearTrigger_in: {
      type: [Number]
    },
    doLayoutTrigger_in: {
      type: [Number]
    },
    //动态输入
    dynamicInput: {
      type: Object
    },

    showPagination: {
      type: Boolean
    },
    showPaginationUI: {
      type: Boolean,
      default: true
    },
    exportFileName: {
      type: [Function, String]
    },
    exportMultiHeader: {
      type: [Function]
    },
    //分页相关配置
    paginationCfg: {
      type: [Object],
      default: function () {
        return {};
      }
    },
    refreshAfterActived: {
      type: [Boolean],
      default: false
    },
    border: {
      type: Boolean,
      default: true
    },
    highlightCurrentRow: {
      type: Boolean,
      default: true
    },
    tooltipEffect: {
      type: String
    },
    tableKey: {
      type: String,
      default: "id"
    },
    // 开启计算表头宽度自适应
    isColumsHeaderDoLayout: {
      type: Boolean,
      default: function () {
        return defaultSettings.CetTable.isColumsHeaderDoLayout;
      }
    }
  },
  data() {
    return {
      getTableNum: 0, //如果从接口获取一次数据，则getTableNum加1，可以判断是否从接口获取过数据，获取过数据则刷新时掉接口，否则不调接口
      activatedNum: 0, //组件activated钩子触发的次数
      //表格数据
      tableData: [],
      //当前选中节点
      currentRow: {},
      //删除的行
      deleteRows: [],
      totalCount: 0,
      pageSize: this.paginationCfg.pageSize || 100,
      pageLayout:
        this.paginationCfg.layout || "total,sizes, prev, pager, next, jumper",
      pageSizes: this.paginationCfg.pageSizes || [10, 20, 50, 100],
      currentPage: 1,
      orders: this.$attrs.defaultSort ? this.$attrs.defaultSort : null,
      // 复选框勾选的行
      checkboxData: [],
      origin: -1, // 变量作为起点
      pin: false // 标记默认为false，不按住
    };
  },
  watch: {
    queryNode_in: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },

    //按钮触发查询
    queryTrigger_in() {
      var vm = this;
      vm.currentPage = 1; //分页置为第一页
      vm.getTableData();
    },
    //条件变化直接查询
    dynamicInput: {
      deep: true,
      handler: function (val) {
        this.paramsChange();
      }
    },
    tableData: {
      deep: true,
      handler: function (val) {
        this.doColumsHeaderLayout();
        // this.$emit("update:data", val); //删除数据，会触发2次输出
        // this.$emit("outputData_out", val);
      }
    },
    //导出按钮触发
    exportTrigger_in() {
      this.exportTable();
    },
    //如果是从其他组件进行数据输入，则直接赋值
    data(val) {
      this.setInputData();
    },
    //删除触发
    deleteTrigger_in() {
      this.deleteRow();
    },
    localDeleteTrigger_in() {
      this.deleteRowFromTable();
    },
    // 批量删除触发
    batchDeletionTrigger_in() {
      this.batchDeletionFromServer();
    },
    localBatchDeletionTrigger_in() {
      this.batchDeletionFromTable();
    },

    // deleteWithoutConfirmTrigger_in() {
    //   this.deleteRowWithoutConfirm();
    // },

    //在本地通过数据新增一行记录，记录无id
    addData_in(val) {
      const vm = this;
      if (_.isNil(val)) {
        return;
      }
      if (_.isEmpty(val)) {
        return;
      }

      this.tableData.push(val);
      this.setOutputData();
    },
    //在本地编辑一行记录
    editData_in(val) {
      this.editRowInTable(val);
    },
    //刷新触发
    refreshTrigger_in() {
      this.refreshTable();
    },
    //清空表格数据触发
    clearTrigger_in() {
      this.clearTable();
    },
    doLayoutTrigger_in() {
      this.doLayout();
    },
    // 设置复选框多选的参数
    multiSelectData_in(val) {
      this.setCheck();
    },
    // 设置当前选中行
    selectRowData_in(val) {
      this.setSelectedRow(val);
    },
    currentPage(val) {
      this.outPageInfo();
    },
    pageSize(val) {
      this.outPageInfo();
    },
    totalCount(val) {
      this.$emit("totalNum_out", val);
    }
  },
  computed: {
    token() {
      return this.$store.state.token;
    },
    userInfo() {
      return this.$store.state.userInfo;
    }
  },
  methods: {
    // select事件
    pinSelect(item, index) {
      const vm = this;
      const tableKey = vm.tableKey;
      this.$nextTick(() => {
        const data = vm.tableData; // 获取所以数据
        const origin = vm.origin; // 起点数 从-1开始
        // 判断点击的节点是否有父节点
        if (!index.hasParent) {
          const endIdx = index.rowIndex; // 终点数
          if (vm.pin && item.includes(data[origin])) {
            // 通过选中节点的同级的所有数据，通过步数设置区间数据的勾选
            this.setCheckbox(data, endIdx);
          } else {
            vm.origin = index.rowIndex; // 没按住记录起点
          }
        } else {
          // if (!vm.pin) {
          //   let node = item.filter(
          //     item =>
          //       item.id == index.id && item.rowIndex == index.rowParentIndex
          //   );
          //   let nodeParent = item.filter(
          //     item => item.rowIndex == index.rowParentIndex
          //   );
          //   // 判断是否 是取消勾选子节点
          //   if (_.isEmpty(node) && !_.isEmpty(nodeParent)) {
          //     vm.$refs.cetTable.toggleRowSelection(
          //       vm.tableData[index.rowParentIndex],
          //       false
          //     ); //先取消父级勾选
          //     vm.$refs.cetTable.toggleRowSelection(
          //       vm.tableData[index.rowParentIndex].children[
          //         index.rowChildIndex
          //       ],
          //       false
          //     ); //再取消当前子节点勾选
          //   }
          // }

          const endChildIdx = index.rowChildIndex;
          // rowParentIndex、hierarchy是为了判断在同一个父级下且同一层级操作
          let start = item[item.length - 1];
          let end = item[item.length - 2];
          if (
            vm.pin &&
            item.length > 1 &&
            start.rowParentIndex === end.rowParentIndex &&
            start.hierarchy === end.hierarchy
          ) {
            // 顶级父级
            let rowParent = data[index.rowParentIndex];
            let row = null; //选中节点父级
            // 获取勾选层级的父节点，通过顶级节点去循环
            // eslint-disable-next-line no-inner-declarations
            function parent(data) {
              data.children.forEach(e => {
                if (e[tableKey] == index[tableKey]) {
                  return (row = data);
                }
                if (e.children) {
                  parent(e);
                }
              });
            }
            parent(rowParent);
            this.setCheckbox(row.children, endChildIdx);
          } else {
            vm.origin = index.rowChildIndex;
          }
        }
      });
    },
    toggleRowSelection(children, type) {
      // 编辑多个子层级
      children.map(j => {
        this.$refs.cetTable.toggleRowSelection(j, type);
        if (j.children) {
          this.toggleRowSelection(j.children, type);
        }
      });
    },
    setCheckbox(data, endIdx) {
      const vm = this;
      const origin = vm.origin; // 起点数 从-1开始
      const sum = Math.abs(origin - endIdx) + 1; // 这里记录终点
      const min = Math.min(origin, endIdx); // 这里记录起点
      let i = 0;
      while (i < sum) {
        const index = min + i;
        let row = data[index];
        vm.$refs.cetTable.toggleRowSelection(row, true);
        // 选中当前行如果有子级（即当前行为树结构数据时），勾选该当前行，当前行的子级也会相应勾选
        if (row.children && row.children.length > 0 && this.isTreeData) {
          vm.toggleRowSelection(row.children, true);
        }
        i++;
      }
    },
    selectAll(selection, row) {
      if (!this.isTreeData) return;
      let vm = this;
      let data = [...selection];

      //全选按钮下，判断是否剩下的都是子级节点，如果是的话说明是取消全选操作，则取消子级勾选
      if (data.every(item => item.hasParent)) {
        data.forEach(v => {
          this.toggleRowSelection(
            vm.tableData[v.rowParentIndex].children,
            false
          );
        });
      } else {
        data.forEach(v => {
          if (v.children && v.children.length > 0) {
            vm.toggleRowSelection(v.children, true);
          }
        });
      }
    },
    //对查询参数变化从而查询表格数据的，判断参数是否有变化，没变化的不查询
    paramsChange() {
      const vm = this;
      if (vm.queryMode === "diff") {
        const queryBody = vm.getQueryBody();
        if (_.isEqual(vm.oldQueryBody, queryBody)) {
          return;
        }
        vm.currentPage = 1; //分页置为第一页
        vm.getTableData();
      }
    },

    // 从接口获取表格的数据
    getTableData() {
      const vm = this;
      let queryBody = {};

      //如果不是接口获取数据模式，不调用接口
      if (vm.dataMode !== "backendInterface") {
        return;
      }

      //组织body入参
      queryBody = vm.getQueryBody();

      //入参校验, 如果不是所有必要入参都是有效值, 则不执行查询
      if (
        !checkQueryParams({
          queryBody: {
            treeNode: vm.queryNode_in,
            filters: vm.dataConfig.filters,
            dynamicInput: vm.dynamicInput
          },
          hasQueryNode: vm.dataConfig.hasQueryNode,
          hasQueryId: false
        })
      ) {
        console.log("表格入参校验未通过", {
          queryBody: {
            treeNode: vm.queryNode_in,
            filters: vm.dataConfig.filters,
            dynamicInput: vm.dynamicInput
          },
          hasQueryNode: vm.dataConfig.hasQueryNode,
          hasQueryId: false
        });
        return;
      }
      //先保存接口入参，如果接口失败，则置vm.oldQueryBody为undefined，用于判断是否是重复的接口调用
      vm.oldQueryBody = _.cloneDeep(queryBody);

      //调用接口
      vm.doQuery(queryBody).then(
        response => {
          response.data = response.data || [];
          // 添加接口请求完成的结果输出
          this.$emit("interfaceResponse_out", response);
          if (response.code === 0 && _.isArray(response.data)) {
            this.peocessTableData(response);
          } else {
            this.oldQueryBody = undefined;
          }
        },
        () => {
          this.$emit("interfaceResponse_out", false);
          this.oldQueryBody = undefined;
        }
      );
    },

    doQuery(queryBody) {
      const vm = this;
      //开发一个外部设置调用接口的自定义逻辑
      let queryMethod;
      const queryFunc = vm.dataConfig.queryFunc;
      if (queryFunc) {
        //如果设置了自定义接口处理函数,则调用自定义处理函数处理
        queryMethod = customApi[queryFunc];
      } else {
        //调用默认的模型api
        queryMethod = queryModel;
      }
      return queryMethod(queryBody);
    },

    peocessTableData(response) {
      const vm = this;

      vm.getTableNum++; //如果从接口获取一次数据，则getTableNum加1，可以判断是否从接口获取过数据，获取过数据则刷新时掉接口，否则不调接口

      vm.tableData = response.data;
      vm.totalCount = response.total;
      // 遍历数据数组，并且赋值行索引,按住shift多选需要
      this.setChildrenIndex(vm.tableData);
      vm.selectRow(response.data);
      //重新获取数据后清空当前行、删除数据数组
      vm.deleteRows = [];
      vm.$nextTick(() => {
        vm.$refs.cetTable.doLayout();
        vm.setCheck();
      });

      vm.$emit("update:data", vm.tableData);
      vm.$emit("outputData_out", vm.tableData);
    },

    //选中指定行
    selectRow(data) {
      const vm = this;

      let addedRowID = -1;
      const tableKey = vm.tableKey;
      //说明有新增记录，需要选中新增的记录
      if (vm.tableData.length !== data.length) {
        addedRowID = vm.findAddedRowID(vm.tableData, data);
      }

      if (addedRowID !== -1) {
        //说明找到了新增记录的id
        const addedRow = _.find(vm.tableData, { id: addedRowID });
        vm.$refs.cetTable.setCurrentRow(addedRow);
      } else {
        //没有找到新增记录就选中上一次选中的记录，或第一条记录
        if (
          !_.isUndefined(vm.currentRow) &&
          vm.currentRow !== null &&
          !_.isEmpty(vm.currentRow)
        ) {
          const lastSelectedRow = _.find(vm.tableData, {
            [tableKey]: vm.currentRow[tableKey]
          });
          //上一个选中记录还存在就自动选中
          if (lastSelectedRow !== null && !_.isUndefined(lastSelectedRow)) {
            vm.$refs.cetTable.setCurrentRow(lastSelectedRow);
          } else {
            vm.selectFirstRow();
          }
        } else {
          vm.selectFirstRow();
        }
      }
    },

    //获取查询接口的入参
    getQueryBody() {
      const vm = this;
      const queryBody = {};

      const page = vm.showPagination
        ? {
            index: (vm.currentPage - 1) * vm.pageSize,
            limit: vm.pageSize
          }
        : null;

      return buildQueryBody({
        modelLabel: vm.dataConfig.modelLabel,
        modelList: vm.dataConfig.modelList,
        dataIndex: vm.dataConfig.dataIndex,
        treeNode: vm.queryNode_in,
        filters: vm.dataConfig.filters,
        dynamicInput: vm.dynamicInput,
        page: page,
        orders: vm.orders,
        props: []
      });
    },

    //寻找新增记录的id
    findAddedRowID(oldData, newData) {
      const vm = this;
      const tableKey = vm.tableKey;
      let id = -1;
      if (
        !_.isArray(oldData) ||
        !_.isArray(newData) ||
        oldData.length === newData.length
      )
        return id;
      _(newData).forEach(function (item) {
        const findRow = _.find(oldData, { [tableKey]: item[tableKey] }); //从旧的数据里面找新的记录，如果没有找到，说明是新增记录
        if (_.isUndefined(findRow)) {
          id = item[tableKey];
          return false; //终止循环
        }
      });
      return id;
    },
    // 设置选中第一行
    selectFirstRow() {
      const vm = this;
      //默认选中第一条记录
      if (vm.tableData && vm.tableData.length > 0) {
        vm.$refs.cetTable.setCurrentRow(vm.tableData[0]);
      } else {
        vm.handlerCurrentChange(null);
      }
    },
    // 设置当前选中行
    setSelectedRow(val) {
      const vm = this;
      const tableKey = vm.tableKey;
      if (vm.tableData && vm.tableData.length > 0) {
        if (_.has(val, tableKey)) {
          //判断是否在对象中存在这个属性
          const setRow = _.find(vm.tableData, { [tableKey]: val[tableKey] });
          // 在表格数据中是否有id相等的数据
          if (!_.isUndefined(setRow)) {
            // id有效则设置高亮
            vm.$refs.cetTable.setCurrentRow(setRow);
          } else {
            vm.$refs.cetTable.setCurrentRow();
          }
        }
      } else {
        vm.handlerCurrentChange(null);
      }
    },

    //其他组件直接输入表格数据的，通过inputData进行表格数据赋值
    setInputData() {
      var vm = this;
      if (vm.dataMode === "component") {
        if (!vm.data) {
          vm.tableData = _.cloneDeep([]);
        } else {
          //对输入的表格数据进行一次排序, 因为没有配置在column里面的属性无法通过配置defaultSort来进行默认排序
          vm.tableData = vm.sortByFrontEnd(_.cloneDeep(vm.data));
          // 遍历数据数组，并且赋值行索引,按住shift多选需要
          this.setChildrenIndex(vm.tableData);
        }

        //赋值时清空当前行，删除数据数组
        vm.selectRow(vm.tableData);
        vm.deleteRows = [];
        vm.$nextTick(() => {
          vm.$refs.cetTable.doLayout();
          // 赋值后，则根据multiSelectData_in 参数设置复选框勾选
          vm.setCheck();
        });
      }
    },

    // 导出设备
    exportTable() {
      const vm = this;

      if (vm.dataMode === "backendInterface" && vm.showPagination) {
        vm.getAllData();
      } else {
        vm.doExport(_.cloneDeep(vm.tableData));
      }
    },
    getAllData() {
      const vm = this;
      let queryBody = {};
      const tableKey = vm.tableKey;
      //如果不是接口获取数据模式，不调用接口
      if (vm.dataMode !== "backendInterface") {
        return;
      }

      if (
        vm.queryNode_in &&
        vm.queryNode_in[tableKey] &&
        vm.queryNode_in[tableKey] === -1
      ) {
        return;
      }

      queryBody = buildQueryBody({
        modelLabel: vm.dataConfig.modelLabel,
        modelList: vm.dataConfig.modelList,
        dataIndex: vm.dataConfig.dataIndex,
        treeNode: vm.queryNode_in,
        filters: vm.dataConfig.filters,
        dynamicInput: vm.dynamicInput,
        page: null,
        orders: vm.orders,
        props: []
      });

      //调用接口
      vm.doQuery(queryBody).then(response => {
        if (response.code === 0) {
          vm.doExport(response.data);
        }
      });
    },
    doExport(list) {
      const vm = this;
      const tHeader = [];
      const filterVal = [];
      const cols = {};
      vm.getExportCol(tHeader, filterVal, cols);
      const data = vm.formatJson(filterVal, list, cols);
      let fileName = "";
      let multiHeader = [];

      //生成导出excel标题
      if (_.isFunction(vm.exportFileName)) {
        fileName = vm.exportFileName(vm);
      } else if (vm.exportFileName) {
        var reg = new RegExp("{{(.+?)}}", "igm"); //匹配 {{}} 的内容
        fileName = vm.exportFileName.replace(reg, function (node, key) {
          if (_.get(vm, key)) {
            return _.get(vm, key);
          } else {
            return "";
          }
        });
      } else {
        fileName = i18n("表格导出");
      }

      //生成导出excel多标题
      if (vm.exportMultiHeader) {
        multiHeader = vm.exportMultiHeader(vm);
      }

      import("../vendor/Export2Excel").then(excel => {
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: fileName,
          multiHeader: multiHeader,
          autoWidth: true,
          bookType: "xlsx"
        });
      });
    },
    formatJson(filterVal, jsonData, cols) {
      const vm = this;
      return jsonData.map((v, index) =>
        filterVal.map(j => {
          v[j] = _.get(v, j);
          if (cols[j].formatter) {
            let funcName = cols[j].formatter.name;
            let res = cols[j].formatter(v, cols[j], v[j], index);

            if (funcName.includes("formatNumber")) {
              let num = parseFloat(String(res).replaceAll(",", ""));
              res = isNaN(num) ? null : num;
            }

            if (typeof res === "object") {
              const instance = new Vue({
                render() {
                  return res;
                }
              });
              const text = instance.$mount().$el.innerText;
              instance.$destroy();
              return text;
            }
            return res;
          }
          return v[j]; // vm.formatTable(v, { property: j }, v[j], 1);
        })
      );
    },
    getExportCol(tHeader, filterVal, cols) {
      const vm = this;
      const slots = vm.$slots.default;

      slots.forEach(item => {
        if (
          ["ElTableColumn", "el-table-column"].indexOf(
            _.get(item, "componentOptions.tag")
          ) !== -1
        ) {
          const data = _.get(item, "componentOptions.propsData");
          if (!data.type && data.prop) {
            tHeader.push(data.label);
            filterVal.push(data.prop);
            cols[data.prop] = data;
          }
        }
      });
    },

    //删除当前选中的行(服务器删除)
    deleteRow() {
      const vm = this;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        //设置消息提示
        vm.setMessagePrompt();
        return;
      }

      vm.$confirm(i18n("确定要删除所选项吗？"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            vm.deleteRowFromServer("single");
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: i18n("取消删除！")
            });
          }
        }
      });
    },
    //调用接口从服务器删除表格数据
    deleteRowFromServer(val) {
      const vm = this;
      let queryOption;
      let queryBody;
      let idRange;
      if (val == "single") {
        idRange = [vm.currentRow[this.tableKey]];
      } else {
        idRange = vm.checkboxData.map(item => {
          return item[this.tableKey];
        });
      }
      queryBody = {
        modelLabel: vm.dataConfig.modelLabel,
        idRange: idRange
      };
      //调用接口
      vm.doDelete(queryBody).then(response => {
        if (response.code === 0) {
          vm.$message.success(i18n("删除成功！"));
          vm.$emit("successfulOperation_out", new Date().getTime());

          //从服务器删除成功后再从本地删除该条记录
          vm.refreshTable();
        }
      });
    },
    doDelete(queryBody) {
      const vm = this;
      //开发一个外部设置调用接口的自定义逻辑
      let deleteMethod;
      const deleteFunc = vm.dataConfig.deleteFunc;
      if (deleteFunc) {
        //如果设置了自定义接口处理函数,则调用自定义处理函数处理
        deleteMethod = customApi[deleteFunc];
      } else {
        //调用默认的模型api
        deleteMethod = deleteModelInstence;
      }
      return deleteMethod(queryBody);
    },
    //从本地表格中删除数据，不调用接口
    deleteRowFromTable() {
      const vm = this;
      const tableKey = vm.tableKey;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        //设置消息提示
        vm.setMessagePrompt();
        return;
      }
      //找到当前行，并从表格数据中删除，将删除的数据打上delete_flag标签，存入deleteRows数组中
      const index = vm.tableData.indexOf(vm.currentRow);
      vm.tableData.splice(index, 1);
      if (vm.totalCount > 0) {
        vm.totalCount = vm.totalCount - 1;
      }

      vm.$emit("successfulOperation_out", new Date().getTime());

      //如果当前行有id，则存入deleteRows数组，否则直接从表格删除即可
      if (vm.currentRow[tableKey]) {
        vm.currentRow.delete_flag = true;

        vm.deleteRows.push(vm.currentRow);
      }

      //设置输出的表格数据
      vm.setOutputData();
    },
    // 批量接口删除
    batchDeletionFromServer() {
      const vm = this;
      //当前选中行无效则提示并返回
      if (_.isEmpty(vm.checkboxData)) {
        vm.setMessagePrompt();
        return;
      }

      vm.$confirm(i18n("确定要删除所选项吗？"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消"),
        cancelButtonClass: "btn-custom-cancel",
        type: "warning",
        closeOnClickModal: false,
        showClose: false,
        beforeClose: function (action, instance, done) {
          if (action == "confirm") {
            vm.deleteRowFromServer("batch");
            vm.$refs.cetTable.clearSelection(); //删除后，清除选中状态
          }
          instance.confirmButtonLoading = false;
          done();
        },
        callback: function (action) {
          if (action != "confirm") {
            vm.$message({
              type: "info",
              message: i18n("取消删除！")
            });
          }
        }
      });
    },
    // 批量本地删除
    batchDeletionFromTable() {
      const vm = this;
      const tableKey = vm.tableKey;
      if (_.isEmpty(vm.checkboxData)) {
        vm.setMessagePrompt();
        return;
      }
      //找到id相同的记录，并从表格数据中删除，将删除的数据打上delete_flag标签，存入deleteRows数组中
      // checkboxData 是勾选的数据
      const arrId = vm.checkboxData.map(item => {
        if (!_.isUndefined(item[tableKey])) {
          return item[tableKey];
        }
        vm.tableData.splice(vm.tableData.indexOf(item), 1);
      });
      vm.tableData = vm.tableData.filter(
        item => !arrId.includes(item[tableKey])
      ); // includes是否包含指定字符串 vm.tableData 是表格呈现的数据
      if (vm.totalCount > 0) {
        vm.totalCount = vm.totalCount - vm.checkboxData.length;
      }

      vm.$emit("successfulOperation_out", new Date().getTime());

      //如果当前行有id，则存入deleteRows数组，否则直接从表格删除即可
      // 保存是因为删除和当前的tableData需要输出,
      vm.checkboxData.forEach(item => {
        if (item[tableKey]) {
          var obj = _.cloneDeep(item);
          obj.delete_flag = true;
          vm.deleteRows.push(obj);
        }
      });
      vm.$refs.cetTable.clearSelection(); //删除后，清除选中状态

      //设置输出的表格数据
      vm.setOutputData();
    },

    //设置消息提示
    setMessagePrompt(val) {
      const vm = this;
      vm.$message({
        message: i18n("请先选择需要删除的行"),
        type: "warning"
      });
    },
    //在本地表格中编辑数据，不调用接口
    editRowInTable(editData) {
      const vm = this;
      const dataIndex = vm.dataConfig.dataIndex;
      //当前选中行无效则提示并返回
      if (!vm.currentRow) {
        this.$message({
          message: i18n("请先选择需要编辑的行"),
          type: "warning"
        });
        return;
      }
      //对当前行进行编辑操作，逐项赋值
      for (var i = 0; i < dataIndex.length; i++) {
        if (
          !_.isNull(editData[dataIndex[i]]) &&
          !_.isUndefined(editData[dataIndex[i]])
        ) {
          //深拷贝赋值
          vm.currentRow[dataIndex[i]] = _.cloneDeep(editData[dataIndex[i]]);
        }
      }
      //设置输出的表格数据
      vm.setOutputData();
    },
    setOutputData() {
      const vm = this;
      let outputData = [];
      // 遍历数据数组，并且赋值行索引,按住shift多选需要
      this.setChildrenIndex(vm.tableData);

      outputData = outputData.concat(vm.tableData, vm.deleteRows);
      vm.$emit("update:data", vm.tableData);
      vm.$emit("outputData_out", outputData);
      vm.selectFirstRow();
    },
    //前端进行排序，对于没有分页的表格可以用前端排序，有分页的还是要用后端排序
    sortByFrontEnd(data) {
      const vm = this;
      let defaultSort = vm.orders;
      const sortKeys = [];
      const orderTypes = [];
      if (!_.isEmpty(defaultSort)) {
        do {
          sortKeys.push(defaultSort.prop);
          orderTypes.push(defaultSort.order === "descending" ? "desc" : "asc");
          defaultSort = _.get(defaultSort, "children[0]");
        } while (defaultSort);

        return _.orderBy(data, sortKeys, orderTypes);
      }
      return data;
    },
    //当前行变化
    handlerCurrentChange(currentRow) {
      const vm = this;
      const dataIndex = vm.dataConfig.dataIndex;
      let outRecord = {};
      const tableKey = vm.tableKey;
      if (_.isNull(currentRow)) {
        for (let i = 0; i < dataIndex.length; i++) {
          outRecord[dataIndex[i]] = "";
        }
        outRecord[tableKey] = -1;
      } else {
        outRecord = currentRow;
      }
      vm.currentRow = currentRow;

      this.$emit("record_out", outRecord);
    },
    //分页大小变化
    handleSizeChange(val) {
      this.currentPage = 1;
      this.getTableData();
    },
    //分页当前页变化
    handleCurrentPageChange(val) {
      this.getTableData();
    },
    refreshTable() {
      const vm = this;
      if (vm.getTableNum > 0) {
        this.getTableData();
      }
    },
    //表格列排序条件变化
    sortChange(val) {
      const vm = this;
      if (val.column.sortable !== "custom") {
        return;
      }
      vm.orders = _.cloneDeep(val);

      this.currentPage = 1; //分页置为第一页
      vm.getTableData();
    },
    // 表格当前勾选的数据,
    handleSelectionChange(val) {
      /*如果外部入参设置的勾选无效，会返回这种结构[{id:1},undefined]，
       因此在根源直接处理，避免在后面使用checkboxData时要做判断*/
      var res = _.difference(val, [undefined]);
      // 保存多选数据
      this.checkboxData = _.cloneDeep(res);
      this.$emit("multiSelectData_out", res);
    },
    // 设置当前表格数据勾选
    setCheck() {
      const vm = this;
      const tableKey = vm.tableKey;
      const val = _.cloneDeep(this.multiSelectData_in);
      if (val) {
        val.forEach(row => {
          vm.$refs.cetTable.toggleRowSelection(
            vm.tableData.find(item => {
              return row[tableKey] == item[tableKey]; // 这里寻找的字段要唯一
            }),
            true
          );
        });
      }
      // else {
      //   vm.$refs.cetTable.clearSelection();
      // }
    },
    // 设置高亮选中某一行
    setCurrent(row) {
      this.$refs.singleTable.setCurrentRow(row);
    },
    //表格的分页信息变化后, 将其输出
    outPageInfo() {
      const pageInfo = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      };
      this.$emit("pageInfo_out", pageInfo);
    },
    clearTable() {
      const vm = this;

      vm.tableData = [];
      vm.totalCount = 0;

      vm.deleteRows = [];
      vm.$nextTick(() => {
        vm.$refs.cetTable.doLayout();
        vm.setCheck();
      });

      vm.$emit("update:data", vm.tableData);
      vm.$emit("outputData_out", vm.tableData);
    },
    // 组件区域键盘事件，获取键盘按住事件
    shiftDown(code) {
      // console.log(code); // 这个是你按住键盘打印出键盘信息，在浏览器中自行查看
      if (code.keyCode === 16 && code.shiftKey) {
        // 判断是否按住shift键，是就把pin赋值为true
        this.pin = true;
      }
    },
    // 获取键盘松开事件
    shiftUp(code) {
      if (code.keyCode === 16) {
        // 判断是否松开shift键，是就把pin赋值为false
        this.pin = false;
      }
      console.log(code, this.pin);
    },
    // 设置节点及子节点rowIndex等标识
    setChildrenIndex(arr) {
      function setChildren(children, index, sum) {
        // 编辑多个子层级
        let ind = index;
        let s = sum;
        children.forEach((v, i) => {
          // eslint-disable-next-line no-prototype-builtins
          if (!v.hasOwnProperty("rowChildIndex")) v.rowChildIndex = i; //rowChildIndex：子级的rowindex,为了shift多选时计算index
          // eslint-disable-next-line no-prototype-builtins
          if (!v.hasOwnProperty("hasParent")) v.hasParent = true; //给子节点增加hasParent，用于判断是否有父级（即是否是子节点）
          // eslint-disable-next-line no-prototype-builtins
          if (!v.hasOwnProperty("rowParentIndex")) v.rowParentIndex = index; // index; //给子节点增加rowParentIndex，用于判断shift勾选时，起始勾选是否都为同一个父级下
          // 添加层级标识
          v.hierarchy = s;
          if (v.children) {
            let k = s;
            k++;
            setChildren(v.children, ind, k);
          }
        });
      }
      // 遍历数据数组，并且赋值行索引,按住shift多选需要
      arr.forEach((item, index) => {
        let s = 0;
        item.rowIndex = index;
        // 添加层级标识
        item.hierarchy = s;
        if (item.children && item.children.length > 0) {
          s++;
          setChildren(item.children, index, s);
        }
      });
    },
    doLayout() {
      this.$nextTick(() => {
        this.$refs.cetTable.doLayout();
      });
    },
    // 处理页面缓存，tooltip触发，切换页面，导致悬停出现在其他位置
    closeTooltip() {
      let list = document.getElementsByClassName("el-tooltip__popper");
      if (list.length > 0) {
        list[list.length - 1].style.display = "none";
      }
    },
    doColumsHeaderLayout() {
      if (!this.isColumsHeaderDoLayout) {
        return;
      }

      try {
        if (this.$refs.cetTable) {
          const columns = this.$refs.cetTable.columns;
          if (Array.isArray(columns) && columns.length > 0) {
            for (let i = 0; i < columns.length; i++) {
              const item = columns[i];
              const { type, label, property } = item;
              const columnType = ["default", "tooltip", "", "text"];
              if (!!label && columnType.includes(type) && !!property) {
                const slotEl = this.$slots.default[i];
                const { register, renderHeader } = useColumsHeaderDoLayout();
                register(this.$refs.cetTable, slotEl).then(() => {
                  item.renderHeader = renderHeader.bind(this);
                  this.$nextTick(() => {
                    this.$refs.cetTable.doLayout();
                  });
                });
              }
            }
          }
        }
      } catch (error) {
        console.error("Error in doLayout:", error);
      }
    }
  },
  mounted: function () {
    const vm = this;
    //设置从data直接获取值的情况
    this.setInputData();
    //设置通过默认值获取表格数据的情况
    if (vm.queryMode === "diff") {
      // 获取到设备表格数据
      this.getTableData();
    }
  },
  activated() {
    const vm = this;
    vm.activatedNum++;
    //第一次激活不执行逻辑，后续激活更新数据
    if (vm.activatedNum > 1 && vm.refreshAfterActived) {
      vm.refreshTable();
    }
  },
  created() {
    this.$nextTick(() => {
      this.doColumsHeaderLayout();
    });

    const vm = this;
    const filters = vm.dataConfig.filters;
    const dynamic = {};
    if (!_.isArray(filters)) {
      console.log("table的filters未配置");
      return;
    }
    for (let i = 0; i < filters.length; i++) {
      let filterValue = vm.dynamicInput[filters[i].name];
      filterValue = _.isNil(filterValue) ? null : filterValue;
      vm.$set(dynamic, filters[i].name, filterValue);
      vm.$emit("update:dynamicInput", dynamic);
    }
  },
  beforeDestroy() {
    this.closeTooltip();
  }
};
</script>
<style lang="scss" scoped>
.table-container {
  height: 100%;
}
</style>
