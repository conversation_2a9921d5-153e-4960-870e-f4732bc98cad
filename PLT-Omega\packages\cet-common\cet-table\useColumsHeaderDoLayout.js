export default function useColumsHeaderDoLayout() {
  let canvasSingleton; // canvas的单例对象
  let contextSingleton; // 绘图上下文的单例对象
  let pxWidthCache; // 计算缓存
  let tableRef; // 表格实例
  let tableEL; // 表格元素
  let slotEl; // 插槽元素
  let cellEls;
  const padding = 22; // 内边距
  const tootipWidth = 14; // 提示icon
  const sortableWidth = 46; // 排序icon
  const cellClassName = `body .el-table th.el-table__cell > .cell`;

  /**
   * 注册函数，用于在表格组件中注册一个引用和插槽
   * @param {Object} ref - 表格组件的引用
   * @param {Object} slot - 插槽的引用
   * @returns {Promise} - 返回一个Promise对象，表示注册过程
   */
  const register = (ref, slot) => {
    return new Promise(resolve => {
      try {
        tableRef = ref;
        tableEL = ref.$el;
        slotEl = slot;
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  };

  /**
   * 自定义列头渲染函数
   *
   * 此函数用于渲染表格列头，可以根据列的属性和索引来自定义列头的显示内容和样式
   * 它支持处理带有插槽的列头，以及根据列的属性来调整列宽
   *
   * @param {Function} h - Vue 的渲染函数，用于创建虚拟 DOM 元素
   * @param {Object} context - 包含列信息和索引的对象
   * @returns {VueComponent} - 返回一个 Vue 组件，用于渲染列头
   */
  const renderHeader = (h, { column, $index }) => {
    const slotHeader = slotEl?.componentInstance?.$scopedSlots?.header;

    const { label = "", width = 0, minWidth = 0, sortable = false } = column;
    let px = getCachedPxWidth(label, sortable ? sortableWidth : padding);

    if (slotHeader) {
      px = sortable
        ? calculateSlotHeaderWidth($index) + 24
        : calculateSlotHeaderWidth($index);
    }

    updateColumnWidth(
      column,
      slotHeader ? px + tootipWidth : px,
      width,
      minWidth
    );

    return slotHeader
      ? h("span", slotHeader())
      : h("span", [h("span", column.label)]);
  };

  /**
   * 更新列的宽度
   * 当列的当前宽度小于给定的像素值px，并且当前宽度存在时，将列的宽度设置为px
   * 如果当前宽度大于或等于给定的像素值px，并且最小宽度小于px，则将列的最小宽度设置为px
   *
   * @param {Object} column - 列对象，包含宽度和最小宽度属性
   * @param {number} px - 像素值，用于比较和设置列的宽度或最小宽度
   * @param {number} width - 列的当前宽度，如果存在且小于px，则更新列的宽度
   * @param {number} minWidth - 列的当前最小宽度，如果小于px，则更新列的最小宽度
   */
  const updateColumnWidth = (column, px, width, minWidth) => {
    if (width < px && !!width) {
      column.width = px;
    } else if (minWidth < px) {
      column.minWidth = px;
    }
  };

  /**
   * 获取缓存的像素宽度
   *
   * 本函数旨在通过缓存机制避免对相同标签和附加宽度的重复计算
   * 它首先检查缓存中是否存在所需的宽度信息，如果不存在，则计算并存储在缓存中
   * 这样做可以显著提高性能，特别是当相同的操作需要重复执行时
   *
   * @param {string} label - 标签，用于计算宽度
   * @param {number} additionalWidth - 附加宽度，与标签一起计算总宽度
   * @returns {number} 标签和附加宽度的总像素宽度
   */
  const getCachedPxWidth = (label, additionalWidth) => {
    if (!pxWidthCache) {
      pxWidthCache = new Map();
    }
    const key = `${label}-${additionalWidth}`;
    if (!pxWidthCache.has(key)) {
      pxWidthCache.set(key, calculateTextWidth(label, additionalWidth));
    }
    return pxWidthCache.get(key);
  };

  /**
   * 获取单例的 Canvas 上下文
   * @returns {CanvasRenderingContext2D}
   */
  const getCanvasContext = () => {
    if (!canvasSingleton) {
      canvasSingleton = document.createElement("canvas");
      contextSingleton = canvasSingleton.getContext("2d");
    }
    return contextSingleton;
  };

  /**
   * 计算文本的宽度（包括边距）
   * @param {string} text - 要测量的文本
   * @param {number} [padding=22] - 文本两边的边距
   * @returns {number} - 文本的总宽度（包括边距）
   */
  const calculateTextWidth = (text, padding = 22) => {
    if (typeof text !== "string") {
      throw new TypeError('The "text" parameter must be a string.');
    }

    const targetElement = tableEL;
    if (!targetElement) {
      throw new Error(
        "Target element not found. Ensure the table element is correctly referenced."
      );
    }

    const cellEl = document.querySelector(
      `body .el-table th.el-table__cell > .cell`
    );
    if (!cellEl) {
      throw new Error(
        "Cell element not found. Please ensure the table has header cells and the selector is correct."
      );
    }

    const computedStyle = window.getComputedStyle(targetElement);
    const cellElStyle = window.getComputedStyle(cellEl);

    if (!computedStyle) {
      throw new Error(
        "Computed style not available. Ensure the element is in the DOM."
      );
    }

    const fontSize = computedStyle.fontSize;
    const cellElFontSize = cellElStyle.fontSize;

    const fontFamily = computedStyle.fontFamily;

    const context = getCanvasContext();

    context.font = `bold ${cellElFontSize || fontSize} ${fontFamily}`;
    const textWidth = Math.ceil(context.measureText(text).width);

    return textWidth + padding;
  };
  /**
   * 计算指定索引的表头单元格宽度
   * 此函数用于动态计算表格中特定列的表头宽度，以确保表头与内容列宽一致
   * 它通过分析表头单元格内的子节点（包括文本节点和其他节点）来确定宽度
   *
   * @param {number} $index - 表头单元格的索引，基于查询到的单元格列表
   * @returns {number} 表头单元格的总宽度，单位为像素
   */
  const calculateSlotHeaderWidth = $index => {
    if (!cellEls) {
      cellEls = document.querySelectorAll(cellClassName);
    }

    const cellEl = cellEls[$index];
    const children = cellEl.children[0]?.childNodes || [];

    const widths = [];
    for (let i = 0; i < children.length; i++) {
      const item = children[i];
      if (item.nodeType === Node.TEXT_NODE) {
        const px = getCachedPxWidth(item.data);
        widths.push(px);
      } else {
        widths.push(Math.ceil(item.getBoundingClientRect().width));
      }
    }

    const totalWidth = widths.reduce((acc, cur) => acc + cur, 0);

    return totalWidth;
  };

  return { register, renderHeader, tableRef };
}
