<script>
import { Tabs, TabPane } from "element-ui";
import _ from "lodash";

/**
 * 基于 elemnt-ui tabs 抽象的一个高阶组件,
 * 主要是补充 keep-alive, 在内容进行切换时
 * 在自己的业务组件中触发生命周期： activated && deactivated 来
 * 有机会处理自己的业务逻辑
 *
 *  使用：继承了所有element-ui tabs的使用用法,
 *  NOTE: 一般使用只需要配置 label 即可
 *  e.g.
 *    <CTabs>
 *      <SomeComponet1 label="某tab1" />
 *      <SomeComponet2 label="某tab2" />
 *      <SomeComponet3 label="某tab3" />
 *    </CTabs>
 */

export default {
  name: "CetTabs",
  data() {
    return {
      activeName: null
    };
  },
  render() {
    // fix: v-model 设置默认tab项，没有内容
    if (this.$attrs.value) {
      this.activeName = this.$attrs.value;
    }

    return this.$createElement(
      Tabs,
      {
        class: "cet-tabs",
        attrs: {
          ...this.$attrs,
          activeName: this.activeName
        },
        on: {
          ...this.$listeners,
          "tab-click": this.onTabClick
        }
      },
      this.createTabPanes()
    );
  },
  methods: {
    onTabClick(tabInstance) {
      this.activeName = tabInstance.name || tabInstance.index;

      this.$emit("tab-click", tabInstance);
    },
    createTabPanes() {
      // 有效的内容
      const validSlots =
        this.$slots.default &&
        this.$slots.default.filter(vnode => {
          if (!vnode.tag) {
            return false;
          } else {
            return this.checkVnodePermission(vnode);
          }
        });
      return validSlots.map((vnode, index) => {
        const attrs = (vnode.data && vnode.data.attrs) || {};
        const name = attrs.name || index.toString();
        // 未配置情况下默认选中第一个
        if (this.activeName === null && index === 0) {
          this.activeName = name;
        }

        return this.$createElement(
          TabPane,
          {
            attrs: {
              lazy: true,
              ...attrs
            }
          },
          [
            this.$createElement("keep-alive", [
              name === this.activeName ? vnode : ""
            ])
          ]
        );
      });
    },
    checkVnodePermission(vnode) {
      const directives = (vnode.data && vnode.data.directives) || [];

      const permissionDesc = _.find(directives, { name: "permission" });
      if (permissionDesc) {
        return this.$checkPermission(permissionDesc.value);
      }

      return true;
    }
  }
};
</script>

<style lang="scss" scoped>
.cet-tabs.el-tabs--top {
  height: 100%;
}
.cet-tabs.el-tabs--top::v-deep {
  & > .el-tabs__header.is-top {
    margin: 0 0 5px;
  }
  & > .el-tabs__content {
    height: calc(100% - 45px);
    & > .el-tab-pane {
      height: 100%;
    }
  }
}
</style>
