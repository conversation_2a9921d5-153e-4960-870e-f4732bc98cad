<template>
  <div class="cet-transfer">
    <div class="cet-transfer-left">
      <cet-ztree ref="cetZtree" :setting="cetZtreeSetting" :props="props" />
    </div>
    <div class="cet-transfer-center">
      <omega-icon symbolId="left-lin" />
    </div>
    <div class="cet-transfer-right">
      <div class="cet-transfer-right-header">
        <el-input
          :placeholder="i18n('输入关键字以检索')"
          v-model="searchText"
        />
      </div>
      <div class="cet-transfer-right-main">
        <div
          class="cet-transfer-item"
          @click="evRemoveItemClick(item)"
          :key="item.id"
          v-for="item in items"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import _ from "lodash";
import { i18n } from "../local/index.js";
export default {
  name: "CetTransfer",
  props: {
    handlerData: {
      type: Function,
      default() {
        return Promise.resolve([]);
      }
    },
    nodeKey: {
      type: String,
      default: "tree_id"
    },
    props: {
      type: Object,
      default() {
        return {
          label: "name",
          children: "children"
        };
      }
    },
    checkedNodes: Array
  },
  data() {
    return {
      // 设置组件唯一识别字段组件
      items: [],
      searchText: ""
    };
  },
  watch: {
    searchText(text) {
      this.filterCheckNodesList(text);
    }
  },
  created() {
    this.cetZtreeSetting = {
      check: {
        enable: true
      },
      callback: {
        onCheck: this.onZtreeCheck
      }
    };
  },
  mounted() {
    this.handlerData().then(data => {
      this.$refs.cetZtree.setData(data);
      if (this.checkedNodes) {
        this.setCheckNodes(this.checkedNodes);
      }
    });
  },
  methods: {
    onZtreeCheck(event, treeId, treeNode) {
      this.updateCheckNodesList();
    },

    updateCheckNodesList() {
      // 获取所有选中的叶子节点
      const checkNodes = this.getCheckNodes();
      const res = [];
      checkNodes.forEach(node => {
        if (node.isLastNode) {
          const paths = node.getPath();
          const label = paths.map(p => p.name).join("/");
          res.push({
            label,
            tId: node.tId
          });
        }
      });

      this.checkNodesList = this.items = res;
      // 清空搜索框
      this.searchText = "";
    },

    filterCheckNodesList: _.debounce(function (text) {
      if (text) {
        this.items = this.checkNodesList.filter(item => {
          return item.label.includes(text);
        });
      } else {
        this.items = this.checkNodesList;
      }
    }, 200),

    getCheckNodes() {
      const { cetZtree } = this.$refs;
      return cetZtree.getNodesByFilter(node => {
        return node.checked && !node.isParent;
      });
    },

    setCheckNodes(checkNodes) {
      const { cetZtree } = this.$refs;
      // 获取所有的叶子节点
      const nodes = cetZtree.getNodesByFilter(node => {
        return !node.isParent;
      });

      const nodeKey = this.nodeKey;

      nodes.forEach(node => {
        if (
          _.find(checkNodes, {
            [nodeKey]: node[nodeKey]
          })
        ) {
          cetZtree.checkNode(node, true, true);
        }
      });

      this.updateCheckNodesList();
    },

    evRemoveItemClick(item) {
      const { cetZtree } = this.$refs;
      const node = cetZtree.getNodeByTId(item.tId);
      cetZtree.checkNode(node, false, true);

      const index = _.findIndex(this.items, {
        tId: item.tId
      });
      this.items.splice(index, 1);
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.cet-transfer {
  height: 600px;
  display: flex;
  align-items: stretch;
  border: 1px solid #eee;
  box-sizing: border-box;
}
.cet-transfer-left {
  height: 100%;
  padding: 10px;
  flex: 1;
  box-sizing: border-box;
}
.cet-transfer-center {
  display: flex;
  align-items: center;
  border-right: 1px solid #eee;
  border-left: 1px solid #eee;
  padding: 10px;
}
.cet-transfer-right {
  height: 100%;
  padding: 10px;
  flex: 1;
  box-sizing: border-box;
}
.cet-transfer-right-header {
  margin: 8px 0;
  height: 40px;
}
.cet-transfer-right-main {
  border-top: 1px solid #eee;
  box-sizing: border-box;
  padding: 10px;
  font-size: 14px;
  overflow: auto;
  height: calc(100% - 60px);
}
.cet-transfer-item {
  margin-bottom: 8px;
  cursor: pointer;
  user-select: none;
  &:hover {
    text-decoration-line: line-through;
  }
}
</style>
