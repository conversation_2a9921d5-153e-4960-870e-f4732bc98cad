<template>
  <div :class="['tree', { showLine: view.showLine }]">
    <div class="line-bottom" v-if="this.showFilter">
      <el-input
        :placeholder="i18n('输入关键字以检索')"
        class="device-search"
        v-model="filterText"
      />
    </div>
    <el-tree
      :style="{ height: height }"
      :data="treeData"
      :default-expanded-keys="expandedKeys"
      :filter-node-method="showFilterChildNode ? filterChildNode : filterNode"
      @current-change="currentChange"
      @check="check"
      ref="tree"
      v-bind="$attrs"
      v-on="$listeners"
    >
      <template v-if="this.$scopedSlots.default" v-slot="slotData">
        <slot v-bind="slotData" />
      </template>
    </el-tree>
  </div>
</template>
<script>
import { i18n } from "../local/index.js";

export default {
  name: "CetTree",
  props: {
    inputData_in: {
      type: Array
    },
    // 选择指定节点
    selectNode: {
      type: Object
    },
    //指定勾选的节点
    checkedNodes: {
      type: Array
    },
    //指定过滤的节点，要包含tree_id属性
    filterNodes_in: {
      type: [String, Array]
    },
    //搜索条件
    searchText_in: {
      type: String
    },
    // 是否显示搜索
    showFilter: {
      type: Boolean
    },
    //是否显示根节点
    ShowRootNode: {
      type: Boolean
    },
    //根节点
    rootName: {
      type: Object
    },
    //用于懒加载
    nodeModelList: {
      type: Array
    },
    //懒加载
    loadFunc: {
      type: String
    },
    defaultExpandedKeys: {
      type: Array,
      default: () => []
    },
    filterNodesKey: {
      type: String,
      default: () => "filterNodesKey"
    },
    view: {
      type: Object,
      default: () => {
        return { showLine: false };
      }
    },
    // 过滤节点是否目标节点子节点
    showFilterChildNode: {
      type: Boolean,
      default:false
    },
    expandWhenChecked: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      height: "calc(100% - 60px)",
      filterText: "",
      treeData: [],
      expandedKeys: [] //默认展开的数组
    };
  },

  mounted() {
    this.$nextTick(() => {
      if (this.showFilter) {
        this.height = "calc(100% - 60px)";
      } else {
        this.height = "100%";
      }
    });
  },
  watch: {
    filterText(val) {
      if (this.searchText_in !== val) {
        this.$emit("update:searchText_in", val);
      }
      this.$refs.tree.filter(val);
    },
    filterNodes_in(val) {
      // 清楚缓存的数据
      sessionStorage.removeItem(this.filterNodesKey);
      // 重新执行过滤
      this.$refs.tree.filter(val);
    },

    searchText_in(val) {
      this.filterText = val;
    },

    inputData_in: {
      deep: true,
      immediate: true,
      handler: function (val, oldVal) {
        this.setInputData(); //使用inputData需要dataModel为false
      }
    },
    selectNode: {
      deep: true,
      handler: function () {
        this.doSelectNode();
      }
    },
    checkedNodes: {
      deep: true,
      handler: function () {
        this.doCheckedNodes();
      }
    },
    defaultExpandedKeys: {
      immediate: true,
      handler(val) {
        this.expandedKeys = val;
      }
    }
  },
  methods: {
    //筛选节点树, 支持文本和节点, 节点要包含nodeKey
    filterNode(value, data, node) {
      const vm = this;
      const label = this._.get(this.$attrs, "props.label") || "name";
      const nodeLabel = this._.get(data, label) || "";

      const nodeKey = this.$attrs.nodeKey;
      const filterNode = {};
      filterNode[nodeKey] = data[nodeKey];
      //文本和数组都满足才显示，返回true，只要一项不满足，返回false
      if (
        vm.filterText &&
        nodeLabel.toLowerCase().indexOf(vm.filterText.toLowerCase()) === -1
      ) {
        return false;
      }

      // filterArray 包含filterNodes_in以及它的所有父节点组成的列表
      var filterArray = JSON.parse(sessionStorage.getItem(this.filterNodesKey));
      // _.isEmpty({ 'a': 1 })  => false
      if (_.isEmpty(filterArray) && !_.isEmpty(vm.filterNodes_in)) {
        const parentNode = [];
        const tree = this.$refs.tree;
        let filterNodes = [];
        // 获取每个节点的所有父节点信息
        vm.filterNodes_in.forEach((item, index) => {
          const treenode = tree.getNode(item[nodeKey]);
          //检查在树组件数据中存在才输出该节点以及父节点
          if (treenode && this.existNode(treenode)) {
            parentNode.push(this.getParentNode(treenode));
          }
        });
        filterNodes = _.unionBy(_.flattenDeep(parentNode), nodeKey); //递归转换成一维数组,并去重
        // 缓存数据，减少内存占用
        sessionStorage.setItem(
          this.filterNodesKey,
          JSON.stringify(filterNodes)
        );
        // 更新filterArray的数据
        filterArray = JSON.parse(sessionStorage.getItem(this.filterNodesKey));
      }

      if (
        vm._.isArray(vm.filterNodes_in) &&
        vm._.findIndex(filterArray, filterNode) === -1
      ) {
        return false;
      }
      return true;
    },
    //筛选节点树，显示节点及其子节点
    filterChildNode(value, data, node) {
      if (!value) return true;
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(label => label.indexOf(value) !== -1);
    },
    setInputData() {
      this.treeData = this.inputData_in;
      if (this.treeData.length == 0) return;

      this.$nextTick(() => {
        this.doSelectNode();
        this.doCheckedNodes();
        this.$refs.tree.filter();
      });
    },
    doSelectNode() {
      // 动态响应selectnode改变后，做相应的配置
      const vm = this;
      const nodeKey = this.$attrs.nodeKey;
      const tree = vm.$refs.tree;
      if (!tree || !vm.selectNode || vm._.isEmpty(vm.selectNode)) {
        return;
      }
      const selectNode = vm.selectNode;

      vm.expandedKeys = [selectNode[nodeKey]];
      tree.setCurrentKey(selectNode[nodeKey]);
      vm.outputCurrentNode(selectNode[nodeKey]);
    },
    //输出当前选中节点
    outputCurrentNode(currentKey) {
      const tree = this.$refs.tree;
      const treenode = tree.getNode(currentKey);
      //检查在树组件数据中存在才输出该节点
      if (treenode && this.existNode(treenode)) {
        this.outputParentList(treenode);
        this.$emit("currentNode_out", treenode.data);
      }
    },
    //用于检查在树组件的数据中是否存在treenode这个节点
    existNode(treenode) {
      const vm = this;
      const lazy = this._.get(this.$attrs, "lazy");
      if (lazy) {
        return true;
      }
      const children = this._.get(this.$attrs, "props.children") || "children";
      const nodeKey = this.$attrs.nodeKey;
      return vm.checkExistNode(treenode, vm.treeData, children, nodeKey);
    },
    checkExistNode(treenode, nodeList, children, nodeKey) {
      const vm = this;
      for (let i = 0, len = nodeList.length; i < len; i++) {
        if (nodeList[i][nodeKey] === treenode.data[nodeKey]) {
          return true;
        }
        if (nodeList[i][children]) {
          const result = vm.checkExistNode(
            treenode,
            nodeList[i][children],
            children,
            nodeKey
          );
          if (result) {
            return true;
          }
        }
      }
      return false;
    },
    //输出选中节点父节点列表
    outputParentList(treenode) {
      var arr = [];
      arr = this.getParentNode(treenode);
      this.$emit("parentList_out", arr);
    },
    // 获取指定节点的父级、祖先级节点
    getParentNode(treenode) {
      var arr = [];
      var vm = this;

      while (treenode != null && treenode.parent != null) {
        //循环res.parent获取所有父节点

        const obj = {};
        //判断key是否是数组，如果是数组则新增对象不添加该属性值
        for (var key in treenode.data) {
          if (!vm._.isArray(treenode.data[key])) {
            obj[key] = treenode.data[key];
          }
        }
        arr.unshift(obj);
        treenode = treenode.parent;
      }
      return arr;
    },
    //手动勾选节点
    currentChange(data, Node) {
      //值改变输出参数 这里不需要输出node_out, selectNode会触发
      this.$emit("update:selectNode", data);
    },
    //勾选节点输入
    doCheckedNodes() {
      // 动态响应selectnode改变后，做相应的配置
      const vm = this;
      const nodeKey = this.$attrs.nodeKey;
      const tree = vm.$refs.tree;
      if (!tree || !vm._.isArray(vm.checkedNodes)) {
        return;
      }
      const checkedNodes = vm.checkedNodes;
      if (!vm._.isEmpty(checkedNodes)&&vm.expandWhenChecked) {
        vm.expandedKeys = checkedNodes.map(item => item[nodeKey]); //这里先在勾选节点时展开所有勾选节点
      }

      tree.setCheckedNodes(checkedNodes);
      vm.checkboxUpdate();

      vm.outputCheckedNodes();
    },
    checkboxUpdate() {
      //当复选框被点击的时候触发，返回勾选节点组成的数组
      //this.localTree=event.checkedNodes;
      const vm = this;
      const checkedNodes = vm.checkedNodes;
      // if (this.checkedNodes.length == 0) return;
      var line = [];
      for (var i = 0; i < this.checkedNodes.length; i++) {
        //循环勾选的数组
        var res = this.$refs.tree.getNode(this.checkedNodes[i]); //根据数组的每一项，获取父节点
        if (this._.isNil(this._.get(res, "parent"))) continue;
        var flag = true;
        for (var j = 0; j < checkedNodes.length; j++) {
          //拿数组的每一项，去跟原数据做比较，如果当前节点的父节点tree_id与原数据每一项比较，用flag做标识
          if (
            res.parent &&
            res.parent.key == checkedNodes[j][this.$attrs.nodeKey]
          ) {
            flag = false;
          }
        } //如果相等，在外层循环判断，确定是否加入空数组中
        if (flag) {
          line.push(this.checkedNodes[i]);
        }
      }
      this.$emit("checkedParentArray_out", line);
      // var halfList = this.$refs.tree.getHalfCheckedNodes(); //包含半选节点
      // this.$emit("checkedArrayWithHalf_out", [].concat(checkedNodes, halfList));
    },
    //输出勾选节点
    outputCheckedNodes() {
      const tree = this.$refs.tree;

      const checkedNodes = tree.getCheckedNodes();
      const halfCheckNodes = tree.getHalfCheckedNodes();
      const allCheckNodes = tree.getCheckedNodes(false, true);
      this.$emit("checkedNodes_out", checkedNodes);
      this.$emit("halfCheckNodes_out", halfCheckNodes);
      this.$emit("allCheckNodes_out", allCheckNodes);
    },
    check(data, checkData) {
      this.$emit("currentCheckNode_out", data);
      //值改变输出参数 这里不需要输出勾选事件
      this.$emit("update:checkedNodes", checkData.checkedNodes);
    },
    i18n
  },
  activated() {}
};
</script>
<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.line-bottom {
  border-bottom-style: solid;
  border-bottom-width: 1px;
  @include border_direction_color("B1", "bottom");
}

.device-search {
  /* width: 250px; */
  margin: 8px 0px;
}

.tree {
  height: 100%;
}
.tree > .el-tree--highlight-current {
  overflow: auto;
}
.tree .el-tree-node__label {
  font-size: 12px;
}

.device-search input {
  border-radius: 20px;
}

.tree .el-tree-node__content {
  height: 20px;
  line-height: 20px;
}

.footer-button {
  line-height: 40px;
  margin-top: 40px;
}
.showLine ::v-deep {
  .el-tree-node__children {
    position: relative;
    .el-tree-node .el-tree-node__content::before {
      content: "";
      position: absolute;
      bottom: 0px; //消除展开关闭重叠
      top: 0px;
      width: 1px;
      height: 100%;
      border: 0 dotted;
      @include border_color("B1");
      border-left-width: 1px;
      z-index: 12;
    }
  }
}
</style>
