/*
 * @Author: zyx
 * @Date: 2021-07-19 15:01:05
 */
/**
 * @param zTreeId the ztree id used to get the ztree object
 * @param searchText Input text for fuzzy search
 * @param isHighLight whether highlight the match words, default true
 * @param isExpand whether to expand the node, default false
 * @returns
 */
const fuzzySearch = (zTreeId, searchText, isHighLight, isExpand) => {
  // 通过ztreeid获取ztree对象
  var zTreeObj = $.fn.zTree.getZTreeObj(zTreeId);
  // 判断ztree对象是否存在
  if (!zTreeObj) {
    return;
  }
  // 获取节点信息绑定的key值
  var nameKey = zTreeObj.setting.data.key.name; //get the key of the node name
  isHighLight = isHighLight === false ? false : true; //默认为true，仅使用false禁用高亮显示 default true, only use false to disable highlight
  isExpand = isExpand ? true : false; //默认情况下不展开 not to expand in default

  zTreeObj.setting.view.nameIsHTML = isHighLight; //allow use html in node name for highlight use
  var metaChar = "[\\[\\]\\\\^\\$\\.\\|\\?\\*\\+\\(\\)]"; //js meta characters
  var rexMeta = new RegExp(metaChar, "gi"); // 匹配元字符的正则表达式 regular expression to match meta characters
  let hideAllChecks = [];
  function ztreeFilter(zTreeObj, _keywords, callBackFunc) {
    if (!_keywords) {
      _keywords = ""; //关键字默认为空
    }
    // 来查找匹配的节点;
    let hideNodes = [];
    let showNodes = [];
    function filterFunc(node) {
      // 将节点名称和关键字转换为小写;
      if (
        node[nameKey] &&
        node[nameKey].toLowerCase().indexOf(_keywords.toLowerCase()) != -1
      ) {
        // 高亮显示匹配字符
        if (isHighLight) {
          //highlight process
          //a new variable 'newKeywords' created to store the keywords information
          //keep the parameter '_keywords' as initial and it will be used in next node
          //process the meta characters in _keywords thus the RegExp can be co+rrectly used in str.replace
          var newKeywords = _keywords.replace(rexMeta, function (matchStr) {
            //add escape character before meta characters
            return "\\" + matchStr;
          });
          node.oldname = node[nameKey]; //store the old name
          var rexGlobal = new RegExp(newKeywords, "gi"); //'g' for global,'i' for ignore case
          //use replace(RegExp,replacement) since replace(/substr/g,replacement) cannot be used here
          node[nameKey] = node.oldname.replace(
            rexGlobal,
            function (originalText) {
              //highlight the matching words in node name
              //  style="color: whitesmoke;background-color: darkred;" 源码修改，风格样式统一
              // var highLightText = "<span>" + originalText + "</span>";
              return originalText;
            }
          );
          zTreeObj.updateNode(node); // 更新节点以使修改生效 update node for modifications take effect
        }
        var pathOfOne = node.getPath(); //获取 treeNode 节点的所有父节点（包括自己）
        if (pathOfOne && pathOfOne.length > 0) {
          //i < pathOfOne.length-1 process every node in path except self
          // 所有节点循环加入到显示的数组中
          // Object.assign(showNodes, pathOfOne);
          // console.log(
          //   "🚀 ~ file: search.js ~ line 68 ~ filterFunc ~ showNodes",
          //   showNodes
          // );
          for (var i = 0; i < pathOfOne.length; i++) {
            showNodes.push(pathOfOne[i]);
          }
        }
      } else {
        // 所有勾选放入数组中包含父节点
        //  && node.isParent === false
        if (node.checked) {
          hideAllChecks.push(node);
          // $(`#${node.tId}`).addClass("ztreeHide");
        }
        // else {
        // 隐藏不匹配的节点;
        hideNodes.push(node);
        // }
      }
    }
    if (_keywords.length == 0) {
      // 如果关键字为空，则返回true以显示所有节点
      //return true to show all nodes if the keyword is blank
      // zTreeObj.showNode(node);
      // zTreeObj.expandNode(node, isExpand);
      //  显示全部隐藏的节点;
      var nodes = zTreeObj.getNodesByParam("isHidden", true);
      zTreeObj.showNodes(nodes);
      return true;
    }
    zTreeObj.getNodesByFilter(filterFunc); //get all nodes that would be shown 获取将显示的所有节点
    zTreeObj.hideNodes(hideNodes);
    // processShowNodes(nodesShow, _keywords); //nodes should be reprocessed to show correctly
    // hideAllChecks
    zTreeObj.showNodes(showNodes);
    // for (var i = 0; i < showNodes.length; i++) {
    //   if (!showNodes[i].isParent) {
    //     //获取第一个匹配子节点的父节点
    //     // 如果第一个匹配节点是父节点？
    //     let node = showNodes[i].getParentNode();
    //     return zTreeObj.expandNode(node, true);
    //   }
    // }
    zTreeObj.expandAll(true);
  }
  // 关键字过滤
  ztreeFilter(zTreeObj, searchText);
  return hideAllChecks;
};

export { fuzzySearch };
