<template>
  <div class="cet-ztree" :class="rootAddClass">
    <div class="cet-ztree-search" v-if="hasSearch">
      <el-input class="cet-ztree-search-input" :placeholder="i18n('输入关键字以检索')" v-model="filterText" />
    </div>
    <div class="cet-ztree-main">
      <div class="ztree" />
    </div>
  </div>
</template>

<script>
import $ from "jquery";
import "@ztree/ztree_v3/js/jquery.ztree.core.min.js";
import "@ztree/ztree_v3/js/jquery.ztree.excheck.min.js";
import "@ztree/ztree_v3/js/jquery.ztree.exhide.min.js";
import _ from "lodash";

import { fuzzySearch } from "./fuzzysearch.js";
import { i18n } from "../local/index.js";

const ztreeObjSymbol = Symbol("ztreeObj");
export default {
  name: "CetZtree",
  props: {
    setting: {
      type: Object,
      default() {
        return {};
      }
    },
    hasSearch: {
      type: Boolean,
      default: true
    },
    // 数据
    data: Array,

    props: {
      type: Object,
      default() {
        return {
          name: "name",
          children: "children"
        };
      }
    }
  },
  watch: {
    filterText(text) {
      this.onSearchTextChange(text);
    },
    data(data) {
      if (data) {
        this.setData(data);
      }
    }
  },
  data() {
    return {
      rootAddClass: "cet-ztree-normal",
      filterText: ""
    };
  },
  created() {
    this.zTreeObj = null;
    // 设置部分ztee的默认配置
    this.defaultSetting = {
      data: {
        key: {
          children: this.props.children,
          name: this.props.name
        }
      },
      view: {
        showLine: false
      }
    };
    let chkboxType = _.get(this.setting, "check.chkboxType", {});
    if (_.isEqual(chkboxType, { Y: "", N: "" })) {
      this.rootAddClass = "cet-ztree-unrelated";
    }
  },
  mounted() {
    this.initProxyZtreeObj();

    if (this.data) {
      this.setData(this.data);
    }
  },
  methods: {
    setData(data) {
      this.reset();

      const uuid = _.uniqueId("zTree_");
      const $container = $(this.$el).find(".ztree").attr("id", uuid);
      const setting = _.merge({}, this.defaultSetting, this.setting);

      this[ztreeObjSymbol] = $.fn.zTree.init($container, setting, data);
    },
    reset() {
      if (this[ztreeObjSymbol]) {
        this[ztreeObjSymbol].destroy();
        this[ztreeObjSymbol] = null;
      }
    },

    onSearchTextChange: _.debounce(function (text) {
      fuzzySearch(this[ztreeObjSymbol].setting.treeId, text);
    }, 250),

    initProxyZtreeObj() {
      if (this.__hasProxyZtreeObj) {
        return;
      }
      const methods = [
        // "setting",
        "addNodes",
        "cancelSelectedNode",
        // "destroy",
        "expandAll",
        "expandNode",
        "getNodes",
        "getNodeByParam",
        "getNodeByTId",
        "getNodesByParam",
        "getNodesByParamFuzzy",
        "getNodesByFilter",
        "getNodeIndex",
        "getSelectedNodes",
        "isSelectedNode",
        "reAsyncChildNodesPromise",
        "reAsyncChildNodes",
        "refresh",
        "removeChildNodes",
        "removeNode",
        "selectNode",
        "transformTozTreeNodes",
        "transformToArray",
        "updateNode",
        "checkNode",
        "checkAllNodes",
        "getCheckedNodes",
        "getChangeCheckedNodes",
        "setChkDisabled",
        "showNodes",
        "showNode",
        "hideNodes",
        "hideNode"
      ];

      methods.forEach(method => {
        this[method] = (...argv) => {
          const ztreeObj = this[ztreeObjSymbol];
          return ztreeObj[method].apply(ztreeObj, argv);
        };
      });

      this.__hasProxyZtreeObj = true;
    },
    i18n
  },
  beforeDestroy() {
    this.reset();
  }
};
</script>

<style lang="scss" scoped>
.cet-ztree {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cet-ztree-search {
  box-sizing: border-box;
  border-bottom: 1px solid;
  @include border_direction_color(B1, bottom);

  &-input {
    margin: 10px 0px;
  }

  height: 60px;
}

.cet-ztree-main {
  flex: 1;
  overflow: auto;
  @include background_color(BG1);
}

/* 样式可参考
*element官网
*vue-giant-tree https://refined-x.com/Vue-Giant-Tree/ */
.ztree {
  text-align: left;
  font-size: 14px;
  position: relative;
}

.no-data {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #909399;
  font-size: 14px;
}

.ztree ::v-deep li {
  list-style-type: none;
  white-space: nowrap;
  outline: none;
}

.ztree ::v-deep li:nth-child(0):hover {
  background-color: rgb(17, 48, 94);
}

.ztree ::v-deep li ul {
  position: relative;
  padding: 0 0 0 20px;
  margin: 0;
  padding-left: 18px;
}

/* 显示连接线 */
.ztree ::v-deep .line:before {
  position: absolute;
  top: 0;
  left: 10px;
  height: 100%;
  content: "";
  border-right: 1px dotted #dbdbdb;
}

.ztree ::v-deep .roots_docu:before,
.ztree ::v-deep .roots_docu:after,
.ztree ::v-deep .center_docu:before,
.ztree ::v-deep .bottom_docu:before,
.ztree ::v-deep .center_docu:after,
.ztree ::v-deep .bottom_docu:after {
  position: absolute;
  content: "";
  border: 0 dotted #dbdbdb;
}

.ztree ::v-deep .roots_docu:before {
  left: 10px;
  height: 50%;
  top: 50%;
  border-left-width: 1px;
}

.ztree ::v-deep .roots_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}

.ztree ::v-deep .center_docu:before {
  left: 10px;
  height: 100%;
  border-left-width: 1px;
}

.ztree ::v-deep .center_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}

.ztree ::v-deep .bottom_docu:before {
  left: 10px;
  height: 50%;
  border-left-width: 1px;
}

.ztree ::v-deep .bottom_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}

.ztree ::v-deep .roots_docu:before,
.ztree ::v-deep .roots_docu:after,
.ztree ::v-deep .center_docu:before,
.ztree ::v-deep .bottom_docu:before,
.ztree ::v-deep .center_docu:after,
.ztree ::v-deep .bottom_docu:after {
  position: absolute;
  content: "";
  border: 0 dotted #dbdbdb;
}

.ztree ::v-deep .center_docu:before {
  left: 10px;
  height: 100%;
  border-left-width: 1px;
}

.ztree ::v-deep .center_docu:after {
  top: 50%;
  left: 11px;
  width: 50%;
  border-top-width: 1px;
}

/* ========================= */
.ztree ::v-deep li a {
  display: inline-block;
  line-height: 22px;
  height: 22px;
  margin: 0;
  cursor: pointer;
  transition: none;
  vertical-align: middle;
  @include font_color(T2);
}

/* .ztree ::v-deep li a:hover,
.ztree ::v-deep .button:hover {
  background-color: rgb(245, 247, 250);
} */
.ztree ::v-deep .node_name {
  display: inline-block;
  padding: 0 4px;
  border-radius: 2px;
}

.ztree ::v-deep .curSelectedNode .node_name {
  @include font_color(T1);
  @include background_color(BG4);
}

.ztree ::v-deep .curSelectedNode_Edit {
  height: 20px;
  opacity: 0.8;
  color: #000;
  border: 1px #6cc2e8 solid;
  background-color: #9dd6f0;
}

.ztree ::v-deep .tmpTargetNode_inner {
  opacity: 0.8;
  color: #fff;
  background-color: #4fcbf0;
  filter: alpha(opacity=80);
}

.ztree ::v-deep .rename {
  font-size: 12px;
  line-height: 22px;
  width: 80px;
  height: 22px;
  margin: 0;
  padding: 0;
  vertical-align: top;
  border: 0;
  background: none;
}

.ztree ::v-deep .button {
  position: relative;
  display: inline-block;
  /* line-height: 22px; */
  height: 14px;
  width: 12px;
  cursor: pointer;
  text-align: center;
  vertical-align: middle;
  padding: 6px;
}

.ztree ::v-deep .button.edit {
  color: #25ae88;
}

.ztree ::v-deep .button.remove {
  color: #cb4042;
}

.ztree ::v-deep .button.chk {
  position: relative;
  width: 12px;
  height: 12px;
  margin: 0 8px 0 0;
  border: 1px solid;
  @include border_color(B1);
  border-radius: 2px;
  padding: 0;
}

.ztree ::v-deep .chk.radio_true_full,
.ztree ::v-deep .chk.radio_false_full,
.ztree ::v-deep .chk.radio_true_full_focus,
.ztree ::v-deep .chk.radio_false_full_focus,
.ztree ::v-deep .chk.radio_false_disable,
.ztree ::v-deep .chk.radio_true_disable,
.ztree ::v-deep .chk.radio_true_part,
.ztree ::v-deep .chk.radio_false_part,
.ztree ::v-deep .chk.radio_true_part_focus,
.ztree ::v-deep .chk.radio_false_part_focus {
  border-radius: 8px;
}

.ztree ::v-deep .button.checkbox_true_full:after,
.ztree ::v-deep .button.checkbox_true_full_focus:after,
.ztree ::v-deep .button.checkbox_true_disable:after,
/* 父子不关联模式下，checkbox半选需改成勾选*/
.cet-ztree-unrelated ::v-deep .button.checkbox_true_part:after,
.cet-ztree-unrelated ::v-deep .button.checkbox_true_part_focus:after {
  box-sizing: content-box;
  content: "";
  height: 7px;
  left: 4px;
  position: absolute;
  top: 1px;
  transform: rotate(45deg) scaleY(1);
  width: 3px;
  transform-origin: center center;
  border-width: 0px 1px 1px 0px;
  border-style: solid solid;
  border-image: initial;
  border-left: 0px;
  border-top: 0px;
  transition: transform 0.15s ease-in 0.05s;
  @include border_color(BG1);
}

.ztree ::v-deep .button.checkbox_false_full_focus {
  border-color: #ccc;
}

.ztree ::v-deep .button.checkbox_true_full,
.ztree ::v-deep .button.checkbox_true_full_focus,
.ztree ::v-deep .button.checkbox_true_part,
.ztree ::v-deep .button.checkbox_true_part_focus,
.ztree ::v-deep .button.checkbox_true_disable {
  @include border_color(ZS);
  @include background_color(ZS);
}

.ztree ::v-deep .button.checkbox_true_full:after,
.ztree ::v-deep .button.checkbox_true_full_focus:after,
.ztree ::v-deep .button.checkbox_true_disable:after {
  -webkit-transform: rotate(45deg) scale(1);
  transform: rotate(45deg) scale(1);
}

.cet-ztree-normal ::v-deep .button.checkbox_true_part::after,
.cet-ztree-normal ::v-deep .button.checkbox_true_part_focus::after {
  content: "";
  position: absolute;
  display: block;
  background-color: rgb(255, 255, 255);
  height: 2px;
  transform: scale(0.5);
  left: 0px;
  right: 0px;
  top: 5px;
  width: 12px;
}

.ztree ::v-deep .button.radio_true_full,
.ztree ::v-deep .chk.radio_true_full_focus,
.ztree ::v-deep .chk.radio_true_part,
.ztree ::v-deep .chk.radio_true_part_focus {
  border-color: #39f;
}

.ztree ::v-deep .button.radio_true_full:after,
.ztree ::v-deep .chk.radio_true_full_focus:after,
.ztree ::v-deep .chk.radio_true_part:after,
.ztree ::v-deep .chk.radio_true_part_focus:after {
  top: 3px;
  left: 3px;
  width: 8px;
  -webkit-transform: rotate(0deg) scale(1);
  transform: rotate(0deg) scale(1);
  border: 0;
  border-radius: 4px;
  background: #39f;
}

.ztree ::v-deep .button.checkbox_true_disable,
.ztree ::v-deep .button.checkbox_false_disable,
.ztree ::v-deep .chk.radio_false_disable,
.ztree ::v-deep .chk.radio_true_disable {
  cursor: not-allowed;
}

.ztree ::v-deep .button.checkbox_false_disable {
  background-color: #f3f3f3;
}

.ztree ::v-deep .button.noline_close:before,
.ztree ::v-deep .button.noline_open:before,
.ztree ::v-deep .button.root_open:before,
.ztree ::v-deep .button.root_close:before,
.ztree ::v-deep .button.roots_open:before,
.ztree ::v-deep .button.roots_close:before,
.ztree ::v-deep .button.bottom_open:before,
.ztree ::v-deep .button.bottom_close:before,
.ztree ::v-deep .button.center_open:before,
.ztree ::v-deep .button.center_close:before {
  position: absolute;
  top: 8px;
  left: 10px;
  content: "";
  transition: -webkit-transform ease 0.3s;
  transition: transform ease 0.3s;
  transition: transform ease 0.3s, -webkit-transform ease 0.3s;
  -webkit-transform: rotateZ(0deg);
  transform: rotateZ(0deg);
  -webkit-transform-origin: 25% 50%;
  transform-origin: 25% 50%;
  border: 4px solid;
  border-color: transparent transparent transparent rgb(192, 196, 204);
}

.ztree ::v-deep .button.noline_open:before,
.ztree ::v-deep .button.root_open:before,
.ztree ::v-deep .button.roots_open:before,
.ztree ::v-deep .button.bottom_open:before,
.ztree ::v-deep .button.center_open:before {
  -webkit-transform: rotateZ(90deg);
  transform: rotateZ(90deg);
}

.ztree ::v-deep .button.ico_loading {
  margin-right: 2px;
  background: url("data:image/gif;base64,R0lGODlhEAAQAKIGAMLY8YSx5HOm4Mjc88/g9Ofw+v///wAAACH/C05FVFNDQVBFMi4wAwEAAAAh+QQFCgAGACwAAAAAEAAQAAADMGi6RbUwGjKIXCAA016PgRBElAVlG/RdLOO0X9nK61W39qvqiwz5Ls/rRqrggsdkAgAh+QQFCgAGACwCAAAABwAFAAADD2hqELAmiFBIYY4MAutdCQAh+QQFCgAGACwGAAAABwAFAAADD1hU1kaDOKMYCGAGEeYFCQAh+QQFCgAGACwKAAIABQAHAAADEFhUZjSkKdZqBQG0IELDQAIAIfkEBQoABgAsCgAGAAUABwAAAxBoVlRKgyjmlAIBqCDCzUoCACH5BAUKAAYALAYACgAHAAUAAAMPaGpFtYYMAgJgLogA610JACH5BAUKAAYALAIACgAHAAUAAAMPCAHWFiI4o1ghZZJB5i0JACH5BAUKAAYALAAABgAFAAcAAAMQCAFmIaEp1motpDQySMNFAgA7") 0 center no-repeat;
}

.ztree ::v-deep .tmpTargetzTree {
  opacity: 0.8;
  background-color: #2ea9df;
  filter: alpha(opacity=80);
}

.ztree ::v-deep .tmpzTreeMove_arrow {
  position: absolute;
  width: 18px;
  height: 18px;
  color: #4fcbf0;
}
</style>
<style>
ul.ztree.zTreeDragUL {
  margin: 0;
  padding: 0;
  position: absolute;
  overflow: hidden;
  background-color: #dedede;
  border: 1px #4fcbf0 dotted;
  border-radius: 4px;
  opacity: 0.7;
}

.zTreeMask {
  position: absolute;
  z-index: 10000;
  opacity: 0;
  background-color: #cfcfcf;
}
</style>
