export default {
  bind(el, binding, vnode, oldVnode) {
    const dialogHeaderEl = el.querySelector(".self-header");
    const dragDom = el.querySelector(".el-drag");
    const sty = dragDom.currentStyle || window.getComputedStyle(dragDom);
    const styHead =
      dialogHeaderEl.currentStyle || window.getComputedStyle(dialogHeaderEl);
    dragDom.onmouseover = e => {
      if (styHead.height === "0px" || styHead.height === "auto") {
        dialogHeaderEl.style.width = "100%";
      }
    };
    dialogHeaderEl.style.cursor = "move";
    dialogHeaderEl.onmousedown = e => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const disX = e.clientX - dialogHeaderEl.offsetLeft;
      const disY = e.clientY - dialogHeaderEl.offsetTop;

      // 获取到的值带px 正则匹配替换
      let styL, styT;
      // 注意在ie中 第一次获取到的值为auto 移动之后赋值为px
      if (sty.left.includes("auto")) {
        styL =
          +document.body.clientWidth * (+sty.left.replace(/\auto/g, "") / 100);
        styT =
          +document.body.clientHeight * (+sty.top.replace(/\auto/g, "") / 100);
      } else {
        styL = +sty.left.replace(/\px/g, "");
        styT = +sty.top.replace(/\px/g, "");
      }
      document.onmousemove = function (e) {
        // 计算移动的距离
        const l = e.clientX - disX;
        const t = e.clientY - disY;
        // 移动当前元素
        dragDom.style.left = `${l + styL}px`;
        dragDom.style.top = `${t + styT}px`;
      };
      document.onmouseup = function (e) {
        document.onmousemove = null;
        document.onmouseup = null;
      };
    };
  }
};
