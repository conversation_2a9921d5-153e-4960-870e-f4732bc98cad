import "./jqueryGlobal";
import "./directives";
import CetButton from "./cet-button/index.vue";
import CetDateSelect from "./cet-dateSelect/index.vue";
import CetDialog from "./cet-dialog/index.vue";
import CetForm from "./cet-form/index.vue";
import CetGiantTree from "./cet-giantTree/index.vue";
import CetInterface from "./cet-interface/index.vue";
import CetSimpleSelect from "./cet-simpleSelect/index.vue";
import CetTable from "./cet-table/index.vue";
import CetTree from "./cet-tree/index.vue";
import CetTabs from "./cet-tabs/index.vue";
import CetAside from "./cet-aside/index.vue";
import CetZtree from "./cet-ztree/index.vue";
import CetTransfer from "./cet-transfer/index.vue";
import CetIcon from "./cet-icon/index.vue";

import { registerApi } from "./api";
import { defaultSettings } from "./defaultSetting";

const components = [
  CetButton,
  CetDateSelect,
  CetDialog,
  CetForm,
  CetGiantTree,
  CetInterface,
  CetSimpleSelect,
  CetTable,
  CetTree,
  CetTabs,
  CetAside,
  CetZtree,
  CetTransfer,
  CetIcon
];

export {
  CetButton,
  CetDateSelect,
  CetDialog,
  CetForm,
  CetGiantTree,
  CetInterface,
  CetSimpleSelect,
  CetTable,
  CetTree,
  CetTabs,
  CetAside,
  CetZtree,
  CetTransfer,
  CetIcon
};

const excel = () => import("./vendor/Export2Excel");

export const vendor = {
  excel
  // zip
};

export default {
  install(Vue, { api, CetDialog = {}, CetDateSelect = {}, CetTable = {} }) {
    components.forEach(c => {
      Vue.component(c.name, c);
    });

    Object.assign(defaultSettings.CetDialog, CetDialog);
    Object.assign(defaultSettings.CetDateSelect, CetDateSelect);
    Object.assign(defaultSettings.CetTable, CetTable);

    registerApi(api);
  }
};
