import _ from "lodash";

function checkQueryNode(queryBody, hasQueryNode) {
  if (hasQueryNode) {
    const queryNode = queryBody.treeNode;
    if (!_.isObject(queryNode)) {
      return false;
    }
    if (queryNode.id < 0) {
      return false;
    }
  }
  return true;
}

function checkQueryId(queryBody, hasQueryId) {
  if (hasQueryId) {
    const queryId = queryBody.id;
    if (!_.isNumber(queryId)) {
      return false;
    }
    if (queryId < 0) {
      return false;
    }
  }
  return true;
}

function checkFilters(queryBody) {
  const filters = queryBody.filters;
  const dynamicInput = queryBody.dynamicInput;
  if (_.isArray(filters) && filters.length > 0) {
    for (let i = 0; i < filters.length; i++) {
      if (_.isNil(dynamicInput[filters[i].name])) {
        return false;
      }
    }
  }
  return true;
}

//生成查询数据的body
export function checkQueryParams({ queryBody, hasQueryNode, hasQueryId }) {
  if (!checkQueryNode(queryBody, hasQueryNode)) {
    return false;
  }

  if (!checkQueryId(queryBody, hasQueryId)) {
    return false;
  }

  if (!checkFilters(queryBody)) {
    return false;
  }

  return true;
}
