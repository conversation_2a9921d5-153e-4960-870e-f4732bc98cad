import _ from "lodash";

function getConditions(modelList, groups, filters) {
  const vm = this;
  let conditions = [];
  if (modelList && modelList.length > 0) {
    //构造conditions参数
    for (let i = 0; i < modelList.length; i++) {
      const expressions = _.filter(
        _.get(filters, "expressions", []),
        item => item.prop.split("$")[0] === modelList[i]
      ).map(item => ({ ...item, prop: item.prop.split("$")[1] }));
      var single = {
        filter: _.isEmpty(expressions) ? null : { expressions }, //getConditionFilterStr(modelList[i],subFilters),
        include_relations: [],
        modelLabel: modelList[i],
        props: []
      };

      conditions.push(single);
    }
  }

  if (groups && groups.length > 0) {
    for (let i = 0; i < groups.length; i++) {
      for (let j = 0; j < groups[i].models.length; j++) {
        var single = {
          filter: null,
          include_relations: [],
          modelLabel: groups[i].models[j],
          props: []
        };

        conditions.push(single);
      }
    }
  }

  conditions = conditions.length === 0 ? null : conditions;

  return conditions;
}
function getConditionFilterStr(modelLabel, filters) {
  const myFliters = filters.filter(item => item.split("$")[0] === modelLabel);
  const expressions = myFliters.map(item => {
    return (expression = getExprssion(item, item, item.split("$")[1]));
  });
}
function getQueryNode(treeNode) {
  const vm = this;
  if (_.isEmpty(treeNode) || !treeNode) {
    return null;
  }
  if (!treeNode.id) {
    return null;
  }

  const node = {
    id: treeNode.id,
    modelLabel: treeNode.modelLabel
  };

  return node;
}

function getQueryTime(queryTime) {
  const vm = this;
  if (_.isEmpty(queryTime) || !queryTime) {
    return null;
  }

  queryTime = {
    timeType: queryTime.timeType ? queryTime.timeType : 1,
    startTime: null,
    endTime: null
  };

  if (_.isArray(queryTime.time)) {
    if (_.isDate(queryTime.time[0])) {
      queryTime.startTime = queryTime.time[0].getTime();
    }
    if (_.isDate(queryTime.time[1])) {
      queryTime.endTime = queryTime.time[1].getTime();
    }
  } else {
    if (_.isDate(queryTime.time)) {
      queryTime.startTime = queryTime.time.getTime();
    }
  }
  return queryTime;
}

function getProp(prop) {
  if (_.isString(prop)) {
    const propKeyArray = prop.split(".");
    let hasModel = false;
    propKeyArray.forEach(element => {
      if (element.indexOf("_model") !== -1) {
        hasModel = true;
      }
    });
    if (hasModel === false) {
      return propKeyArray.join("$");
    }
  }
}

function getExprssion(filter, filterValue, prop) {
  const filterProp = getProp(prop);

  const exprssion = {
    prop: filterProp,
    operator: filter.operator,
    limit: filterValue
  };

  if (_.isNumber(filter.tagid)) {
    exprssion.tagid = filter.tagid;
  }
  return exprssion;
}

function getFilterStr(filters, dynamicInput) {
  const expressions = [];
  if (!_.isArray(filters)) {
    return null;
  }
  for (let i = 0; i < filters.length; i++) {
    const filter = filters[i];
    const filterValue = dynamicInput[filter.name];
    if (filterValue !== "" && !_.isNil(filterValue)) {
      //TODO 这里有一个次级模型筛选支持的逻辑暂未实现

      if (_.isArray(filter.prop)) {
        filter.prop.forEach(item => {
          const expression = getExprssion(filter, filterValue, item);
          expressions.push(expression);
        });
      } else {
        const exprssion = getExprssion(filter, filterValue, filter.prop);
        expressions.push(exprssion);
      }
    }
  }

  if (expressions.length < 1) {
    return null;
  }

  return {
    expressions: expressions
  };
}

function getOrderProp(prop) {
  let processedProp = prop;
  if (prop.indexOf("$text") !== -1) {
    processedProp = prop.substring(0, prop.length - 5);
  }
  return processedProp;
}

function generateOrder(order, orders, priority) {
  const orderType = orders.order === "descending" ? "desc" : "asc";
  order.push({
    orderType: orderType,
    priority: priority,
    propertyLabel: getOrderProp(orders.prop)
  });
  if (orders.children) {
    generateOrder(order, orders.children[0], priority + 1);
  }
}
function getOrders(orders) {
  const vm = this;
  let order;
  //非对象数据为无效的排序参数
  if (!_.isObjectLike(orders)) {
    return null;
  }
  //oder为null表示不进行排序
  if (!orders.order) {
    return null;
  }

  order = [];
  generateOrder(order, orders, 1);

  return order;
}

//生成查询数据的body
export function buildQueryBody({
  modelLabel,
  dataIndex,
  treeNode,
  modelList,
  filters = [],
  dynamicInput,
  page,
  orders,
  props,
  id,
  groups,
  treeReturnEnable
}) {
  const vm = this;
  const queryBody = {};

  queryBody.rootLabel = modelLabel;
  const rootFilters = filters.filter(item => !item.prop.includes("$"));
  const rootCondition = {};
  rootCondition.filter = getFilterStr(rootFilters, dynamicInput);
  rootCondition.include_submodel = true;
  rootCondition.treeNode = getQueryNode(treeNode);
  rootCondition.page = page;
  rootCondition.props = _.isEmpty(props) ? null : props;
  rootCondition.orders = getOrders(orders);

  for (const key in rootCondition) {
    if (_.isNil(rootCondition[key])) {
      delete rootCondition[key];
    }
  }

  queryBody.rootCondition = rootCondition;
  const subFilters = filters.filter(item => item.prop.includes("$"));
  queryBody.subLayerConditions = getConditions(
    modelList,
    groups,
    getFilterStr(subFilters, dynamicInput)
  );

  queryBody.rootID = id < 0 ? null : id;
  queryBody.treeReturnEnable = treeReturnEnable ? true : undefined;

  for (const key in queryBody) {
    if (_.isNil(queryBody[key])) {
      delete queryBody[key];
    }
  }

  return queryBody;
}
