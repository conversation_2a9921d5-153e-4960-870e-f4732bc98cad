import _ from "lodash";
//对待保存数据进行处理，将group配置的属性进行转换，
//将treenode:{id:1,modelLabel:'abc'}形式的结构转换为abc_model:{id:1,modelLabel:'abc'}这样的结构
//给treenode增加或者清除掉move_from字段
function transformGroupData(saveData, groups, initialData) {
  let modelLabel = "";
  if (!groups) return;
  for (let i = 0; i < groups.length; i++) {
    if (groups[i].name) {
      if (saveData[groups[i].name]) {
        modelLabel = saveData[groups[i].name].modelLabel;
        saveData[`${modelLabel}_model`] = [saveData[groups[i].name]];

        if (!_.isEmpty(initialData[groups[i].name])) {
          saveData[`${modelLabel}_model`][0].move_from =
            initialData[groups[i].name];
        } else {
          saveData[`${modelLabel}_model`][0].move_from = null;
        }

        delete saveData[groups[i].name];
      }
    }
  }
}

//判断是否是关联模型数据, 比如contact_model这样的就是关联模型数据, abc:[1,2,4]这样的就不是关联模型数据
function isRelationModel(saveData, initialData, key) {
  if (key.indexOf("_model") === -1) {
    return false;
  }
  if (!_.isArray(saveData[key])) {
    return false;
  }
  if (!_.isArray(initialData[key])) {
    return false;
  }

  return true;
}

function isModelObject(object) {
  if (!_.isObject(object)) {
    return false;
  }
  if (!_.has(object, "id")) {
    return false;
  }

  return true;
}

//找到被删除的数组的项，添加deleteflag
function addDeleteFlagForArray(saveData, initialData) {
  //对比原始数据和当前数据, 如果原始数据中的关联模型数据有删除的,则打上deleteFlag标签
  for (const key in saveData) {
    if (isRelationModel(saveData, initialData, key)) {
      const array = saveData[key];
      const initialArray = initialData[key];
      for (let i = 0; i < initialArray.length; i++) {
        if (
          isModelObject(initialArray[i]) &&
          _.findIndex(array, { id: initialArray[i].id }) === -1
        ) {
          array.push({
            id: initialArray[i].id,
            modelLabel: initialArray[i].modelLabel,
            delete_flag: true
          });
        }
      }
    }
  }
}

//生成写数据的body
export function buildWriteBody({ saveData, initialData, groups, modelLabel }) {
  let writeBody = {};

  writeBody = _.cloneDeep(saveData);

  addDeleteFlagForArray(writeBody, initialData);
  transformGroupData(writeBody, groups, initialData);
  writeBody.modelLabel = modelLabel;

  return writeBody;
}
