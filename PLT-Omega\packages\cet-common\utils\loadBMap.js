/*
 * @Author: zyx
 * @Date: 2021-01-14 09:39:45
 * @LastEditTime: 2021-07-15 11:17:26
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\projects\demo\mapDemo\loadBMap.js
 */
export default function loadBMap(ak) {
  return new Promise(function(resolve, reject) {
    if (typeof BMap !== "undefined") {
      resolve(BMap);
      return true;
    }
    window.onBMapCallback = function() {
      resolve(BMap);
    };
    // 这是通过script标签引入js文件，也就是CDN的方式，还可通过ajax异步加载
    let script = document.createElement("script");
    script.type = "text/javascript";
    script.src =
      "http://api.map.baidu.com/api?v=2.0&ak=" +
      ak +
      "&callback=onBMapCallback";
    script.onerror = reject;
    document.head.appendChild(script);
  });
}
