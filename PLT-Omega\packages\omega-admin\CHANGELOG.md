# @omega/admin

## 1.20.6

- fix: 国际化词条翻译规范性检查

## 1.20.6

### Patch Changes

- fix: 优化集成的soda页面未获取到完整路径时提示找不到苏打页面的问题

## 1.20.5

### Patch Changes

- fix: 集成的soda页面路径拼接协议名

## 1.20.4

### Patch Changes

- feat: 支持集成soda页面时，可以按照当前web的hostname访问soda页面

## 1.20.3

### Patch Changes

- fix: 使用iframe页面嵌入时 src 变换强制重建 iframe

## 1.20.2

### Patch Changes

- fix: 更新unity3d版本, 修改打开摄像头的参数

## 1.20.1

### Patch Changes

- fix: 修复未引用i18n导致的报错
- fix: 修改unity3d依赖包的最低版本

## 1.20.0

### Patch Changes

- feat: unity3d页面更新通讯协议及摄像头弹窗

## 1.19.0

### Patch Changes

- feat: 新增登录页的主视觉大背景图片设置

## 1.18.1

- fix: 修复其他配置模块在第一次进来时额外加载一次的问题

## 1.18.0

- feat: 新增冲突在当前窗口的忽略功能，修复部分场景下保存频繁导致的 JSON 读取报错,调整界面整体布局

## 1.17.1

- fix: 修复修改自定义菜单组后, 菜单组下所有菜单消失的问题
- fix: 退出配置页面后刷新浏览器页面, 修复退出配置页面后会在平台或项目内部展示所有菜单的问题

## 1.17.0

- feat: 菜单加载逻辑适配@omega/app@1.8.0

## 1.16.4

- fix: 修复 unity3d 页面第二次进入后, iframeDom 为 null 导致的报错问题

## 1.16.3

- fix: 修复发送 token 报错

## 1.16.2

- feat: 3d 初始化完成后给 3d 发送 token

## 1.16.1

- fix: 组件销毁时解绑全局事件

## 1.16.0

- feat: unity3d 页面添加消息交互功能

## 1.15.0

- fix: 登录后重新获取原始菜单错误引用修改

## 1.14.4

- feat: 增加可配置在登录后重新获取原始菜单

## 1.14.3

- fix: 修复 1.14.2 版本添加 localhost 动态参数后代码报错的问题

## 1.14.2

- feat: 内嵌、外链、3d 菜单页面添加 localhost 动态参数，代表当前 Web 的 ip 地址

## 1.14.1

- fix: 删除多余的融合菜单处理代码

## 1.14.0

- feat: 自定义菜单支持 unity3d 外链 iframe（支持缓存）

## 1.13.13

- fix: 修复依赖错误

## 1.13.12

- feat: 读取融合插件的菜单配置

## 1.13.11

- chore: update lang

## 1.13.10

- feat: 图片配置增加 icon 提示

## 1.13.9

- fix: sodaiframe 自动网页全屏后点击全屏报错

## 1.13.8

- fix: sodaiframe 全屏遮盖业务弹窗

## 1.13.7

- fix: 增加传递皮肤参数

## 1.13.6

- feat: soda 平台菜单支持网页自动全屏配置

## 1.13.5

- fix: 修复报错信息提示问题

## 1.13.4

- feat: 集成 soda 平台顶层窗口跳转功能
- fix: 重复打开 soda 平台新窗口

## 1.13.3

- fix: 对比提示隐藏关闭按钮

## 1.13.2

- fix: 修复 username 提示文字错误

## 1.13.1

- feat: 支持跳转链接与 iframe
-

## 1.13.0

- feat: 集成 soda 平台

## 1.12.0

- feat: 暴露 getNavmenu 方法来获取全量菜单配置

## 1.11.2

- fix: 上传图片高度溢出问题修复

## 1.11.1

- fix: 上传图片大小限制为 5M 提示信息添加

## 1.11.0

- feat: 密码输入变为密码模式&支持本地 svg-sprite-loader 加载的图标, 依赖需要图标库>=1.8.0

- Updated dependencies
  - @omega/icon@1.8.0

## 1.10.1

- feat: 应用全局 conf 状态存储新增 otherSertting

## 1.10.0

- feat: 开放在线配置存储能力帮助业务做自定义配置，增加暴露 ImageUploader 组件辅助图片上传存储

- 注意： docker 镜像版本>= *************/front-frame/bff-service:v1.4.0

## 1.9.5

- fix: 修复自定义一级及二级菜单拖放消失的问题

## 1.9.4

- feat: 暴露 OmegaAdminPlugin.unregister 属性对外来屏蔽 omega-admin 插件

## 1.9.3

- fix: 修复图片配置重复展示问题

## 1.9.2

- fix: 自定义页面无数据情况下展示提示信息 @omega/dashborad >= 1.2.0

## 1.9.1

- fix: 修复多个组态画面&dashborad 互相切换时画面未变化的问题

## 1.9.0

- 409a5de: feat: 新增 apiProxy 插件配置，支持在外部劫持 api 方法

## 1.8.1

- fix: 编辑自定义菜单组子菜单丢失及校验问题修复

## 1.8.0

- feat: 国际化支持（BFF 要求>=1.3.0, @omega/i18n >= 1.2.0）

## 1.7.0

- feat: 新增访问需要密码验证

- Updated dependencies
  - @omega/http@1.3.2

## 1.6.0

- feat: 调整自定义页面权限逻辑，统一为页面权限

## 1.5.1

- feat: 优化导航编辑拖拽区域高亮
- feat: 新增 layoutMode 插件布局配置
- fix: 工具函数 util.js 导出报错

## 1.5.0

- feat: 新增暴露 setOriginNavmenu getOriginNavmenu 接口满足外部接入
- feat: 界面布局及样式优化调整，提示信息补齐
- feat: 图片资源支持删除

## 1.4.2

- 734bd2a: fix: 新增菜单项必选菜单类型

## 1.4.1

- feat: 新增 dashborad&cetgraph 内置组件供选择使用

## 1.4.0

- feat: 支持 dashboard 页面配置 环境需要 @omega/auth>1.7.10 @omega/dashborad > 1.1.0

## 1.3.4

- feat: esmodule 入口添加
- Updated dependencies
  - @omega/http@1.2.3
  - @omega/icon@1.3.6
  - @omega/widget@1.0.2

## 1.3.3

- fix: 选择图标时，图标列表为空问题

## 1.3.2

- fix: 调整依赖版本为外部任意版本

## 1.3.1

- fix: 菜单项配置为空时卡在 loading 页面

## 1.3.0

- feat: 开放接口前缀配置及请求拦截器配置
- feat: 菜单编辑表单验证
- feat: 适配 @omega/icon 1.1.0
- Updated dependencies
  - @omega/icon@1.1.0

## 1.2.4

- feat: 新增菜单冲突更新提示及跳转

## 1.2.3

- feat: 增加资源配置开关

## 1.2.2

- feat: 新建状态下菜单类型可选

## 1.2.1

- c8f9e70: chore: 调整 graph 节点选择仅选择图性节点

## 1.2.0

- 28a8d2c: [2022-05-07] feat: 支持编辑/节点新建功能。支持权限检查

- 8e2fbbf: feat: 菜单编辑器支持 table 展示方式

## 1.1.4

- [2022-04-29]
  fix: 登录无响应问题修复
  feat: 图标增删交互方式及样式调整

## 1.1.3

- [2022-04-28] feat: pecdraw 节点为选择模式
- Updated dependencies
- Updated dependencies
  - @omega/auth@1.3.3
  - @omega/layout@1.2.2

## 1.1.2

- [2022-04-26] fix: 登录页面 logo 适配

## 1.1.1

- [2022-04-24] 插件机制适配
- Updated dependencies
- Updated dependencies

  - @omega/layout@1.1.2
  - @omega/auth@1.3.0

- Updated dependencies
  - @omega/auth@1.1.0
  - @omega/http@1.1.0
  - @omega/layout@1.1.0

## 1.0.2

- [2022-04-02]

  feat: 国际化功能适配

- Updated dependencies
  - @omega/http@1.0.1
  - @omega/layout@1.0.2
  - @omega/widget@1.0.1

## 1.0.1

- [2022-03-31]
  feat: 菜单导航编辑支持选择图标
  fix: 修复刷新时菜单内容有一瞬间渲染错乱的问题
