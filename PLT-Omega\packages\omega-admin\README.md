# 框架功能扩展：在线配置功能

## 使用条件

ROOT 用户登录成功后 1 分钟内可通过快捷键`Ctrl+Shift+Alt+L`唤醒, 过时该入口关闭。

## 导航配置

关于新增的自定义的菜单路由路径说明

- dashborad 路由: /omega_admin/dashboard?id="{id}&mode={mode}"

  id {String} - dashborad Id

  mode {Number} - 是否包含标题头 0:包含 1:不包含

- graph 路由: /omega_admin/graph?nodeId="{nodeId}&nodeType={nodeType}"

  nodeId {Number} - pecdraw 节点 ID
  nodeType {Number} - 节点类型

~~- report 路由：/omega_admin/iframe?url="{url}"~~

~~url {String} - 报表返回的 html 的接口~~

### 内置组件注册

业务测可以根据路由自己定义访问页面，也可以直接使用内置组件。

```js
import omegaApp from "@omega/app";
import { OmegaAdminPlugin } from "@omega/admin";
import { DashboradPagePlugin } from "@omega/admin/plugins/dashborad";
import { GraphPagePlugin } from "@omega/admin/plugins/graph";

omegaApp.plugin.register(OmegaAdminPlugin, {
  // apiPrefix: {
  //   "device-data-service": "/device-data"
  //   "bff-service": "/bff"
  // },
  // apiRequestInterceptor: function(config) {return config}
});

// 注意：使用下述组件时相关依赖需要在项目中自己安装
// 注册dashborad组件
omegaApp.plugin.register(DashboradPagePlugin);
// 注册cetgraph组件
omegaApp.plugin.register(GraphPagePlugin);
```

# 开发环境如何关闭

在内部功能页面内，有开启和暂停该功能的入口按钮。或者通过代码注释或者逻辑取消该插件的注册来屏蔽相关功能
