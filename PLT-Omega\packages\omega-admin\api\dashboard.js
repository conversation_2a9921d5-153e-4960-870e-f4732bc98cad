import { httping } from "../request";
import { ApiProxy } from "../apiProxy";

export default new ApiProxy({
  getDashborardList() {
    return httping({
      method: "POST",
      url: "{{model-service}}/v1/query",
      data: {
        rootID: 0,
        rootLabel: "dashboard"
      },
    }).then(res => {
      return res.data;
    });
  },

  getdDashboardById(id) {
    return httping({
      url: `/model/v1/query`,
      method: "POST",
      data: {
        rootLabel: "dashboard",
        rootID: +id
      }
    }).then((res) => {
      return res.data && res.data[0]
    });
  }
});
