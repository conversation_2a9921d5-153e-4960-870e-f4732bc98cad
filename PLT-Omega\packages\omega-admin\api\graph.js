import { httping } from "../request";
import { ApiProxy } from "../apiProxy";
import { findNodeById } from "../util";

export default new ApiProxy({
  getGraphTree() {
    return httping({
      method: "GET",
      url: "{{device-data-service}}/api/comm/v1/graph/tree"
    }).then(res => {
      return res.data;
    });
  },

  async getGraphNodeById(id) {
    const tree = await this.getGraphTree();

    const node = findNodeById(tree, id, {
      childKey: "children",
      valueKey: "id"
    });

    return node;
  },
});
