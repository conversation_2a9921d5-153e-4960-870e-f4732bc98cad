import { fetch } from "../request";

export default {
  update(setting) {
    return fetch({
      method: "POST",
      url: "{{bff-service}}/v1/image/admin/update",
      data: setting
    });
  },

  get() {
    return fetch({
      url: "{{bff-service}}/v1/image/get",
      method: "GET"
    }).then(res => {
      return res.data;
    });
  },

  disable(isDisable) {
    return fetch({
      url: "{{bff-service}}/v1/image/admin/disable",
      method: "POST",
      data: {
        disable: isDisable
      }
    });
  },

  isDisable() {
    return fetch({
      method: "GET",
      url: "{{bff-service}}/v1/image/admin/disable"
    }).then(res => res.data);
  }
};
