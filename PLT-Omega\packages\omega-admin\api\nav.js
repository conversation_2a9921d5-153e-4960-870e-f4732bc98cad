import { fetch } from "../request";

export default {
  get() {
    return fetch({
      url: "{{bff-service}}/v1/navmenu/get",
      method: "GET"
    }).then(res => {
      return res.data;
    });
  },

  getAdminNavmenu() {
    return fetch({
      url: "{{bff-service}}/v1/navmenu/admin/get",
      method: "GET"
    }).then(res => {
      return res.data;
    });
  },

  edit(navmenuSetting) {
    return fetch({
      method: "POST",
      url: "{{bff-service}}/v1/navmenu/admin/edit",
      data: navmenuSetting
    });
  },

  disable(isDisable) {
    return fetch({
      method: "POST",
      url: "{{bff-service}}/v1/navmenu/admin/disable",
      data: {
        disable: isDisable
      }
    });
  },

  isDisable() {
    return fetch({
      method: "GET",
      url: "{{bff-service}}/v1/navmenu/admin/disable"
    }).then(res => res.data);
  }
};
