import { fetch } from "../request";
import md5 from "crypto-js/md5";

export default {
  getResource() {
    return fetch({
      url: "{{bff-service}}/v1/public/setting/resource/get",
      method: "GET"
    }).then(res => {
      return res.data;
    });
  },
  getNavmenu(locale) {
    return fetch({
      url: "{{bff-service}}/v1/public/setting/navmenu/get",
      method: "GET",
      params: {
        locale
      }
    }).then(res => {
      return res.data;
    });
  },
  checkUpdate(originNavmenu) {
    return fetch({
      url: "{{bff-service}}/v1/public/setting/navmenu/checkUpdate",
      method: "POST",
      data: originNavmenu
    }).then(res => {
      return res.data;
    });
  },
  sign(password) {
    const salt = Math.random().toString(36).substr(2);
    const prefix = "omega_admin_";
    const ticket = md5(`${prefix}#${password}#${salt}`).toString();
    return fetch({
      url: "{{bff-service}}/v1/system/sign",
      method: "POST",
      data: {
        ticket,
        salt
      },
    }).then(res => {
      return res.data;
    })
  },
  updateOtherSetting(data) {
    return fetch({
      url: "{{bff-service}}/v1/otherSetting/update",
      method: "POST",
      data,
    });
  },
  getOtherSetting() {
    return fetch({
      url: "{{bff-service}}/v1/otherSetting/get",
      method: "GET",
    }).then(res => {
      return res.data;
    })
  },
};
