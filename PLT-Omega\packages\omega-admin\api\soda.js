import { fetch } from "../request";
import { ApiProxy } from "../apiProxy";
import { getToken } from "../util.js";

export default new ApiProxy({
  querySodaMenuList(tenantId = undefined) {
    return fetch({
      method: "POST",
      url: "{{bff-service}}/v1/soda/queryAppMenu",
      data: {
        tenantId,
        token: getToken()
      }
    }).then(res => {
      return res;
    });
  },

  getSodaWeb() {
    return fetch({
      method: "GET",
      url: "{{bff-service}}/v1/public/soda/web"
    }).then(res => {
      return res;
    });
  }

  // getdDashboardById(id) {
  //   return httping({
  //     url: `/model/v1/query`,
  //     method: "POST",
  //     data: {
  //       rootLabel: "dashboard",
  //       rootID: +id
  //     }
  //   }).then((res) => {
  //     return res.data && res.data[0]
  //   });
  // }
});
