import _ from "lodash";
import $ from "jquery";
class Event {
  construct({ startX, startY }) {
    // 事件类型 dragstart drag dragend
    this.type = null;
    // 原始的 jq 事件对象
    this.evt = null;
    // 初始位置
    this.startX = null;
    this.startY = null;
    // 当前位置
    this.currentX = null;
    this.currentY = null;
    // x/y 方向自拖动开始移动的距离
    this.dx = null;
    this.dy = null;
    // [一般情况下开发主要使用的字段]
    // 间隔的两次事件触发移动的距离
    this.cdx = null;
    this.cdy = null;

    // 临时存放共享dragstart drag dragend 通信用的数据
    this.data = null;
  }

  update(handler) {
    return _.assign(this, handler);
  }
}

function bind(dom, handler) {
  const $el = $(dom);
  const $document = $(document);
  $el.on("mousedown.drag", function (evt) {
    const event = new Event();

    $document.on("mousemove.drag", function (evt) {
      const dx = evt.pageX - event.startX;
      const dy = evt.pageY - event.startY;
      return handler(
        event.update({
          type: "drag",
          evt: evt,
          currentX: evt.pageX,
          currentY: evt.pageY,
          dx: dx,
          dy: dy,
          cdx: dx - event.dx,
          cdy: dy - event.dy
        })
      );
    });
    $document.on("mouseup.drag", function (evt) {
      $document.off("mousemove.drag");
      $document.off("mouseup.drag");
      return handler(
        event.update({
          type: "dragend",
          evt: evt
        })
      );
    });

    return handler(
      event.update({
        type: "dragstart",
        evt: evt,
        startX: evt.pageX,
        startY: evt.pageY,
        currentX: evt.pageX,
        currentY: evt.pageY,
        dx: 0,
        dy: 0,
        cdx: 0,
        cdy: 0
      })
    );
  });
}

function off(dom) {
  $(dom).off("modusedown.drag");
}

export default {
  bind,
  off
};
