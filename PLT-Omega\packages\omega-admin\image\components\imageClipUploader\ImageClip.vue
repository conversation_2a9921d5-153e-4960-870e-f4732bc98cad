<template>
  <div class="image-clip">
    <div class="image-clip-desk" ref="clipDesk" @wheel="evMousewheel($event)">
      <canvas class="image-clip-desk-canvas" ref="cvsDesk" />
      <div
        class="image-clip-desk-box"
        :class="{ move: isClipBoxDrag }"
        @dblclick="evClipBoxDblclick"
        :style="clipboxStyle"
      >
        <div
          class="image-clip-desk-box-view"
          v-show="isClipBoxDrag"
          ref="dragClipBox"
        />
        <div
          class="image-clip-desk-box-point"
          v-show="!isFixed"
          ref="dragPoint"
        >
          <div class="image-clip-desk-box-point-tip" v-show="isShowPosTip">
            <span>
              {{ `x: ${clipboxRect.left} ` }}
            </span>
            <span>
              {{ `y: ${clipboxRect.top}` }}
            </span>
          </div>
          <div class="box top-left" />
          <div class="box top-right" />
          <div class="box bottom-left" />
          <div class="box bottom-right" />
          <div class="point top-middle" />
          <div class="point bottom-middle" />
          <div class="point left-middle" />
          <div class="point right-middle" />
        </div>
      </div>
    </div>
    <div class="image-clip-view" :class="{ top: isShowView }">
      <canvas ref="cvsView" />
    </div>
    <div class="image-clip-desk-tip">
      <span
        class="image-clip-desk-tip-view el-icon-aim"
        title="重新定位"
        @click="evClickCenter"
      ></span>
      <span
        class="image-clip-desk-tip-view el-icon-view"
        :class="{ active: isShowView }"
        title="预览"
        @click="evViewToggle"
      ></span>
      <span>
        {{ `${i18n("宽:")}` }}
        <input
          type="number"
          :disabled="isFixed"
          class="image-clip-desk-tip-input"
          v-model.number="clipboxRect.width"
        />
      </span>
      <span>
        {{ `${i18n("高:")}` }}
        <input
          type="number"
          :disabled="isFixed"
          class="image-clip-desk-tip-input"
          v-model.number="clipboxRect.height"
        />
      </span>

      <span
        class="image-clip-desk-tip-view el-icon-close"
        title="关闭"
        @click="evClose"
      ></span>
      <span
        class="image-clip-desk-tip-view el-icon-check"
        title="保存"
        @click="evSave"
      ></span>
    </div>
  </div>
</template>

<script>
import drag from "../drag";
import { CvsRender } from "./cvsRender";
import _ from "lodash";
import { i18n } from "../../../local/index.js";

export default {
  name: "ImageClip",
  props: {
    // 是否为固定大小
    isFixed: {
      type: Boolean,
      default: false
    },
    src: {
      type: String,
      default: ""
    },
    // 剪裁盒子区域的宽高
    clipbox: {
      type: Object,
      default() {
        return {
          width: 120,
          height: 60
        };
      }
    }
  },
  data() {
    const { width, height } = this.clipbox;
    return {
      clipboxRect: {
        top: 0,
        left: 0,
        width: width,
        height: height
      },
      isShowPosTip: false,
      isClipBoxDrag: false,
      isShowView: false
    };
  },
  computed: {
    clipboxStyle() {
      return {
        top: this.clipboxRect.top + "px",
        left: this.clipboxRect.left + "px",
        width: this.clipboxRect.width + "px",
        height: this.clipboxRect.height + "px"
      };
    }
  },
  mounted() {
    drag.bind(this.$refs.clipDesk, this.evEleDragHandeler.bind(this));
    drag.bind(this.$refs.dragPoint, this.evPointDrag.bind(this));
    drag.bind(this.$refs.dragClipBox, this.evClipBoxDrag.bind(this));
    this.$on("hook:beforeDestroy", () => {
      drag.off(this.$refs.clipDesk);
      drag.off(this.$refs.dragPoint);
      drag.off(this.$refs.dragClipBox);
    });
    // 居中 clipbox
    this.centerClipbox();

    this.$watch(
      "src",
      src => {
        if (src) {
          this.setImage(this.src);
        }
      },
      {
        immediate: true
      }
    );
  },
  methods: {
    evViewToggle() {
      this.isShowView = !this.isShowView;
    },
    getFile(...argv) {
      return this.cvsRender.getFile(...argv);
    },
    evClickCenter() {
      this.centerClipbox();
      this.cvsRender.centerImage();
    },
    setImage(src) {
      const img = new Image();
      img.src = src;
      img.onload = () => {
        this.cvsRender = new CvsRender({
          container: this.$el,
          cvsDesk: this.$refs.cvsDesk,
          cvsView: this.$refs.cvsView,
          img: img,
          clipbox: this.clipbox
        });
        this.updateCvsView();
      };
    },
    evClipBoxDblclick() {
      this.isClipBoxDrag = true;
      const $document = $(document);
      const vm = this;
      $document.on("mouseup.clipboxdrag", function (evt) {
        if (evt.target !== vm.$refs.dragClipBox) {
          $document.off("mouseup.clipboxdrag");
          vm.isClipBoxDrag = false;
        }
      });
    },
    evMousewheel(evt) {
      const zoom = evt.wheelDelta / 120;
      const pos = {
        clientX: evt.clientX,
        clientY: evt.clientY
      };
      this.cvsRender.zoom(pos, zoom);
    },
    evEleDragHandeler(ev) {
      switch (ev.type) {
        case "dragstart":
          break;
        case "drag":
          this.cvsRender.update({ cdx: ev.cdx, cdy: ev.cdy });
          break;
        case "dragend":
          this.updateCvsView();
          break;
      }
    },
    evClipBoxDrag(ev) {
      switch (ev.type) {
        case "dragstart":
          this.updateView({
            isShowPosTip: true
          });
          return false;
        case "drag":
          this.updateView({
            clipboxRect: {
              left: this.clipboxRect.left + ev.cdx,
              top: this.clipboxRect.top + ev.cdy
            }
          });
          break;
        case "dragend":
          this.updateView({
            isShowPosTip: false
          });

          this.updateCvsView();
          break;
      }
    },
    evPointDrag(ev) {
      const evt = ev.evt;
      const target = evt.target;
      switch (ev.type) {
        case "dragstart":
          ev.data = {
            dir: this.handlerDir(target)
          };
          this.updateView({
            isShowPosTip: true
          });
          return false;
        case "drag":
          this.handlerClipbox(ev.data, ev);
          break;
        case "dragend":
          this.updateView({
            isShowPosTip: false
          });
          this.updateCvsView();
          break;
      }
    },
    handlerClipbox({ dir }, { cdx, cdy }) {
      let nextClipbox = {};
      const preClipbox = this.clipboxRect;
      switch (dir) {
        case "top-left":
          nextClipbox = {
            left: preClipbox.left + cdx,
            top: preClipbox.top + cdy,
            width: preClipbox.width - cdx,
            height: preClipbox.height - cdy
          };
          break;
        case "bottom-right":
          nextClipbox = {
            width: preClipbox.width + cdx,
            height: preClipbox.height + cdy
          };
          break;
        case "top-right":
          nextClipbox = {
            top: preClipbox.top + cdy,
            width: preClipbox.width + cdx,
            height: preClipbox.height - cdy
          };
          break;
        case "bottom-left":
          nextClipbox = {
            left: preClipbox.left + cdx,
            width: preClipbox.width - cdx,
            height: preClipbox.height + cdy
          };
          break;
        case "top-middle":
          nextClipbox = {
            top: preClipbox.top + cdy,
            height: preClipbox.height - cdy
          };
          break;
        case "bottom-middle":
          nextClipbox = {
            height: preClipbox.height + cdy
          };
          break;
        case "left-middle":
          nextClipbox = {
            left: preClipbox.left + cdx,
            width: preClipbox.width - cdx
          };
          break;
        case "right-middle":
          nextClipbox = {
            width: preClipbox.width + cdx
          };
          break;
      }
      this.updateView({
        clipboxRect: nextClipbox
      });
    },
    handlerDir(target) {
      if (target) {
        const dirs = [
          "top-left",
          "bottom-right",
          "top-right",
          "bottom-left",
          "top-middle",
          "bottom-middle",
          "left-middle",
          "right-middle"
        ];
        for (var dir of dirs) {
          if (_.includes(target.className, dir)) {
            return dir;
          }
        }
      }
      return null;
    },
    evClose() {
      this.$emit("close");
    },
    evSave() {
      this.getFile(file => {
        this.$emit("save", file);
      });
    },
    updateView(data) {
      const vm = this;
      this.$nextTick(function () {
        _.merge(vm, data);
      });
    },
    updateCvsView() {
      this.cvsRender.copyDeskToView(this.clipboxRect);
    },
    centerClipbox() {
      const width = this.$refs.clipDesk.offsetWidth;
      const height = this.$refs.clipDesk.offsetHeight;

      const dwidth = width - this.clipboxRect.width;
      const dheight = height - this.clipboxRect.height;

      this.clipboxRect.top = dheight / 2;
      this.clipboxRect.left = dwidth / 2;
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.image-clip {
  position: fixed;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
}

.image-clip-view {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -9999;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #a2a2a2 url(./assets/background.svg);
  background-size: 12px 12px;
  &.top {
    z-index: 1;
  }
}

.image-clip-desk {
  position: relative;
  width: 100%;
  height: 100%;
  background: #a2a2a2 url(./assets/background.svg);
  background-size: 12px 12px;
}

.image-clip-desk-box {
  position: absolute;
  border: 1px solid #000;
  outline: rgba(#000, 0.3) solid 9999px;
  &.move {
    border-style: dashed;
  }
  .point {
    position: absolute;
    width: 8px;
    height: 8px;
    border-radius: 4px;
    background: #000;
    transform: translate(-50%, -50%);
    &.top-middle {
      top: 0;
      left: 50%;
      cursor: n-resize;
    }
    &.bottom-middle {
      top: 100%;
      left: 50%;
      cursor: n-resize;
    }
    &.left-middle {
      top: 50%;
      left: 0;
      cursor: w-resize;
    }
    &.right-middle {
      top: 50%;
      left: 100%;
      cursor: w-resize;
    }
  }
  .box {
    position: absolute;
    width: 8px;
    height: 8px;
    background: #000;
    transform: translate(-50%, -50%);
    &.top-left {
      top: 0;
      left: 0;
      cursor: nw-resize;
    }
    &.top-right {
      top: 0;
      left: 100%;
      cursor: ne-resize;
    }
    &.bottom-left {
      top: 100%;
      left: 0;
      cursor: ne-resize;
    }
    &.bottom-right {
      top: 100%;
      left: 100%;
      cursor: nw-resize;
    }
  }
}

.image-clip-desk-box-view {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  cursor: all-scroll;
}

.image-clip-desk-box-point-tip {
  position: absolute;
  bottom: calc(100% + 8px);
  left: calc(100% + 8px);
  user-select: none;
  background: rgba(#000, 0.8);
  color: #fff;
  white-space: nowrap;
  padding: 5px;
}

.image-clip-desk-tip {
  position: absolute;
  z-index: 2;
  right: 0;
  top: 0;
  user-select: none;
  background: #ccc;
  color: #ffffff;
  white-space: nowrap;
  & > * {
    margin: 0 8px;
  }
  &-input {
    padding: 0;
    display: inline-block;
    max-width: 32px;
    text-align: center;
    border: none;
    outline: none;
    background: transparent;
    color: #ffffff;
    &::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }
    -moz-appearance: textfield;
  }
  &-view {
    cursor: pointer;
    &.active {
      color: #97f19c;
    }
  }
}
</style>
