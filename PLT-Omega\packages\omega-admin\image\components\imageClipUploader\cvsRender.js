/**
 * @file cvsDesk 管理器
 */
import _ from "lodash";

export class CvsRender {
  constructor({ container, cvsDesk, img, cvsView, clipbox }) {
    this.cvsDesk = cvsDesk;
    this.cvsDeskRect = null;
    this.ctxDesk = cvsDesk.getContext("2d");

    this.img = img;
    this.container = container;

    this.drawReact = {
      width: img.width,
      height: img.height,
      x: 0,
      y: 0
    };


    this.zoomRatio = 0.8;
    this.currentRadio = 1;

    this.cvsView = cvsView;
    this.ctxView = cvsView.getContext("2d");

    this.init(container, clipbox);
  }

  init(clipbox) {
    // 初始化 cvsDesk 宽高
    // 说明: 处理剪裁盒子大于实际内容区的特殊情况
    const clientWidth = this.container.clientWidth;
    const clientHeight = this.container.clientHeight;
    const range = 100;
    const width =
      clipbox.width + range > clientWidth ? clipbox.width + range : clientWidth;
    const height =
      clipbox.height + range > clientHeight
        ? clipbox.height + range
        : clientHeight;

    this.cvsDesk.width = width;
    this.cvsDesk.height = height;

    this.centerImage();

    // 获取当前cvsDesk的位置信息
    this.cvsDeskRect = this.cvsDesk.getBoundingClientRect();
  }

  centerImage() {
    const clientWidth = this.container.clientWidth;
    const clientHeight = this.container.clientHeight;
    const width = this.img.width;
    const height = this.img.height;

    const x = (clientWidth - width * this.currentRadio) / 2;
    const y = (clientHeight - height * this.currentRadio) / 2;

    this.drawReact.x = x;
    this.drawReact.y = y;

    this.update();
  }

  /**
   * 清空画布
   */
  clear() {
    this.ctxDesk.clearRect(0, 0, this.cvsDesk.width, this.cvsDesk.height);
  }

  update({ cdx = 0, cdy = 0 } = {}) {
    this.clear();

    const x = this.drawReact.x + cdx;
    const y = this.drawReact.y + cdy;

    // 更新图片位置信息
    this.drawReact.x = x;
    this.drawReact.y = y;

    this.ctxDesk.drawImage(
      this.img,
      this.drawReact.x,
      this.drawReact.y,
      this.drawReact.width,
      this.drawReact.height
    );
  }

  /**
   * @param pos
   * @prop pos.clientX 箭头相对视口的 x 坐标
   * @prop pos.clientY 箭头相对视口的 y 坐标
   * @param {Number} zoom > 0 放大、 < 0 缩小
   */
  zoom(pos, zoom) {
    this.calcDrawReact(pos, zoom);
    this.update();
  }

  calcDrawReact(pos, zoom) {
    const radio = zoom < 0 ? this.zoomRatio : 2 - this.zoomRatio;

    this.currentRadio *= radio;
    // 鼠标箭头相对于cvsDesk的坐标系
    const coordinatecvsDesk = {
      x: pos.clientX - this.cvsDeskRect.left,
      y: pos.clientY - this.cvsDeskRect.top
    };

    // 鼠标箭头相对于所画图片的坐标系
    const coordinateMirrorImg = {
      x: coordinatecvsDesk.x - this.drawReact.x,
      y: coordinatecvsDesk.y - this.drawReact.y
    };

    // 缩放/放大后鼠标箭头相对于所画图片的坐标系
    const coordinateZoomImg = {
      x: coordinateMirrorImg.x * radio,
      y: coordinateMirrorImg.y * radio
    };

    this.drawReact = {
      width: this.drawReact.width * radio,
      height: this.drawReact.height * radio,
      // 根据鼠标箭头相对与cvsDesk的坐标系和相对于图片的坐标系，求解图片相对于cvsDesk的坐标系
      x: coordinatecvsDesk.x - coordinateZoomImg.x,
      y: coordinatecvsDesk.y - coordinateZoomImg.y
    };
  }

  copyDeskToView = _.throttle(function (clipbox) {
    const _this = this;
    return new Promise(function (resolve, reject) {
      const src = _this.cvsDesk.toDataURL();
      const img = new Image();
      img.onload = function () {
        _this.cvsView.width = clipbox.width;
        _this.cvsView.height = clipbox.height;
        _this.ctxView.clearRect(
          0,
          0,
          _this.cvsView.width,
          _this.cvsView.height
        );
        _this.ctxView.drawImage(
          img,
          clipbox.left,
          clipbox.top,
          clipbox.width,
          clipbox.height,
          0,
          0,
          clipbox.width,
          clipbox.height
        );
        resolve();
      };
      img.onerror = reject;
      img.src = src;
    });
  }, 200);

  getImgSrc(...argv) {
    return this.cvsView.toDataURL(...argv);
  }

  getFile(...argv) {
    return this.cvsView.toBlob(...argv);
  }
}
