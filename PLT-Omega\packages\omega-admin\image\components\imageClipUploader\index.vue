<template>
  <div class="uploader-clip">
    <div class="uploader-clip-preview" v-show="hasUploaded">
      <el-image
        class="uploader-clip-img"
        :src="imgSrc"
        fit="contain"
        :preview-src-list="[imgSrc]"
      >
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline" />
        </div>
      </el-image>
    </div>
    <el-button type="danger" v-show="hasUploaded" @click="evDelete">
      {{ i18n("删除") }}
    </el-button>
    <el-button type="primary" @click="evClick">
      {{ btnTitle }}
    </el-button>
  </div>
</template>

<script>
import UploaderDialog from "./uploaderDialog.vue";
import { showOmegaDialog } from "@omega/widget";
import { i18n } from "../../../local/index.js";

export default {
  name: "UploaderClip",
  model: {
    prop: "imgSrc",
    event: "update"
  },
  props: {
    imgSrc: {
      type: String
    },
    // 是否需要的为固定大小的图片
    isFixed: {
      type: Boolean,
      default: false
    },
    // 默认框选区域大小
    // isFixed 为 true 时为固定这个大小
    clipbox: {
      type: Object,
      default() {
        return {
          width: 120,
          height: 60
        };
      }
    },
    maxFileSize: {
      type: Number
    },
    recommendSize: {
      type: String
    }
  },
  computed: {
    btnTitle() {
      return this.hasUploaded ? i18n("重新上传") : i18n("上传");
    },
    hasUploaded() {
      return !!this.imgSrc;
    }
  },
  methods: {
    evClick() {
      const e = showOmegaDialog(UploaderDialog, {
        imageClip: {
          isFixed: this.isFixed,
          clipbox: this.clipbox
        },
        maxFileSize: this.maxFileSize,
        recommendSize: this.recommendSize,
      });
      e.on("confirm", ({ data }) => {
        this.$emit("update", data.src);
      });
    },
    evDelete() {
      this.$emit("update", "");
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.uploader-clip {
  display: inline-block;
}
.uploader-clip-preview {
  margin-top: 10px;
}
.uploader-clip-img {
  width: 200px;
  height: 200px;
}
</style>
