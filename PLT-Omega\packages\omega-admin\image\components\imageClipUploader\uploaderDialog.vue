<template>
  <omega-dialog
    v-bind="editDialog"
    :title="i18n('图片上传')"
    :successMsg="i18n('保存成功')"
    :on-before-confirm="onBeforeConfirm"
  >
    <div class="content">
      <el-upload
        ref="uploader"
        class="uploader"
        role="el-uploader"
        action=""
        v-if="!hasImage"
        :on-change="onChange"
        :auto-upload="false"
        :multiple="false"
        accept=".jpg,.jpeg,.png,.gif,.webp,.svg"
        drag
      >
        <i class="el-icon-upload" />
        <div class="el-upload__text">
          {{ i18n("点击选择图片/拖拽图片到此处") }}
          <br />
          {{ i18n("支持的格式：.jpg .jpeg .png .gif .webp .svg") }}
          <br />
          {{ recommendSize }}
          <br />
          <b>
            {{ fileSizeWarnMessage }}
          </b>
        </div>
      </el-upload>
      <el-image
        v-else
        class="uploader-clip-img"
        :src="imgSrc"
        fit="contain"
        :preview-src-list="[imgSrc]"
      ></el-image>
      <ImageClip
        v-bind="imageClip"
        v-if="showEditor"
        :src="imgSrc"
        @close="onClipClose"
        @save="onClipSave"
      />
    </div>
    <template #footer>
      <el-button type="primary" v-show="hasImage" @click="evEdit">
        {{ i18n("编辑") }}
      </el-button>
      <el-button type="primary" v-show="hasImage" @click="evReset">
        {{ i18n("重选") }}
      </el-button>
    </template>
  </omega-dialog>
</template>

<script>
import ImageClip from "./ImageClip.vue";
import { i18n } from "../../../local/index.js";
import { fetch } from "../../../request.js";

export default {
  name: "ImageClipDialog",
  components: {
    ImageClip
  },
  props: {
    // 主要会用到的配置
    // clipbox 框选区域的宽高，也就是未来剪裁出来的图片的宽高
    imageClip: {
      type: Object,
      default() {
        return {
          isFixed: false,
          clipbox: {
            width: 120,
            height: 60
          }
        };
      }
    },
    uploadeUri: {
      type: String,
      default: "/bff/v1/image/admin/upload"
    },
    // 弹框配置
    editDialog: {
      type: Object,
      default() {
        return {
          successMsg: i18n("上传成功!")
        };
      }
    },
    maxFileSize: {
      type: Number,
      default() {
        return 5 * 1024 * 1024;
      }
    },
    recommendSize: {
      type: String
    }
  },
  data() {
    return {
      imgSrc: "",
      isEditImage: false
    };
  },
  computed: {
    fileSizeWarnMessage() {
      return i18n("图片大小不能超过") + this.maxFileSize / 1024 / 1024 + "M";
    },
    hasImage() {
      return !!this.imgSrc;
    },
    showEditor() {
      return this.hasImage && this.isEditImage;
    }
  },
  methods: {
    onChange(file) {
      if (file.size <= this.maxFileSize) {
        this._fileRaw = file.raw;
        this.imgSrc = this.getUploadObjectURL(file.raw);
      } else {
        this.$message.error(this.fileSizeWarnMessage);
        this.$refs.uploader.clearFiles();
      }
    },
    getUploadObjectURL(data) {
      if (this._imageurl) {
        window.URL.revokeObjectURL(this._imageurl);
      }
      this._imageurl = window.URL.createObjectURL(data);
      this.$on("hook:beforeDestroy", () => {
        window.URL.revokeObjectURL(this._imageurl);
      });
      return this._imageurl;
    },
    onBeforeConfirm() {
      return this.upload();
    },
    upload() {
      const formData = new FormData();
      const fileName = Date.now() + ".png";
      formData.append("file", this._fileRaw, fileName);
      return fetch.post(this.uploadeUri, formData);
    },
    evEdit() {
      this.isEditImage = true;
    },
    evReset() {
      this.imgSrc = "";
      this.file = null;
    },
    onClipClose() {
      this.isEditImage = false;
    },
    onClipSave(fileRaw) {
      this.imgSrc = this.getUploadObjectURL(fileRaw);
      this._fileRaw = fileRaw;
      this.isEditImage = false;
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.content {
  position: relative;
  min-height: 300px;
  text-align: center;
}
.uploader {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
.uploader-clip-img {
  max-height: 480px;
  max-width: 600px;
}
.el-icon-upload {
  margin-top: 20px;
}
</style>
