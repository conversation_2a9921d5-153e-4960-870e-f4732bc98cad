<template>
  <omega-dialog :title="i18n('图片资源编辑')" :fullscreen="true" :show-close="false" :successMsg="i18n('保存成功')"
    :onBeforeConfirm="onBeforeConfirm">
    <el-form class="container" :model="form" ref="form" label-position="top">
      <el-form-item v-for="(item, index) in form.items" :key="index">
        <div class="label">
          {{ item.name }}
          <el-tooltip placement="bottom">
            <div slot="content">
              <p>
                {{ i18n("支持的格式：.jpg .jpeg .png .gif .webp .svg") }}
              </p>
              <p>
                {{ recommendSize(item.clipbox.width, item.clipbox.height) }}
              </p>
              <p>{{ fileSizeWarnMessage }}</p>
            </div>
            <span class="el-icon-info"></span>
          </el-tooltip>
        </div>
        <ImageClipUploader v-model="item.url" :clipbox="item.clipbox" :maxFileSize="maxFileSize" :recommendSize="recommendSize(item.clipbox.width, item.clipbox.height)
          " />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>
<script>
import ImageClipUploader from "./components/imageClipUploader/index.vue";
import api from "./../api/image";
import { i18n } from "../local/index.js";

export default {
  name: "ImageSettingDialog",
  components: {
    ImageClipUploader
  },
  props: {
    layoutMode: {
      type: String,
      default: "vertical"
    }
  },
  data() {
    const items = [
      {
        name: "登录界面背景图",
        key: "login_background_image_url",
        desc: "",
        desc_url: "",
        clipbox: {
          width: 1000,
          height: 700
        },
        url: ""
      },
      {
        name: "登录界面主视觉图",
        key: "login_primary_image_url",
        desc: "",
        desc_url: "",
        clipbox: {
          width: 1920,
          height: 945
        },
        url: ""
      },
      {
        name: "登录界面LOGO",
        key: "login_logo_image_url",
        desc: "",
        desc_url: "",
        clipbox: {
          width: 210,
          height: 30
        },
        url: ""
      }
    ];
    if (this.layoutMode === "vertical") {
      items.push(
        ...[
          {
            name: "侧边栏布局LOGO（长）",
            key: "layout_horizontal_full_image_url",
            desc: "",
            desc_url: "",
            clipbox: {
              width: 260,
              height: 80
            },
            url: ""
          },
          {
            name: "侧边栏布局LOGO（短）",
            key: "layout_horizontal_short_image_url",
            desc: "",
            desc_url: "",
            clipbox: {
              width: 64,
              height: 80
            },
            url: ""
          }
        ]
      );
    } else if (this.layoutMode === "horizontal") {
      items.push(
        ...[
          {
            name: "顶栏布局LOGO（长）",
            key: "layout_vertical_image_url",
            desc: "",
            desc_url: "",
            clipbox: {
              width: 248,
              height: 60
            },
            url: ""
          }
        ]
      );
    }
    return {
      form: {
        items
      },
      maxFileSize: 5 * 1024 * 1024
    };
  },
  computed: {
    fileSizeWarnMessage() {
      return i18n("图片大小不能超过") + this.maxFileSize / 1024 / 1024 + "M";
    }
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      api.get().then(setting => {
        this.setData(setting);
      });
    },
    setData(data = {}) {
      this.form.items.forEach(item => {
        item.url = data[item.key] ?? "";
      });
    },
    getData() {
      let data = {};
      this.form.items.forEach(item => {
        data[item.key] = item.url;
      });
      return data;
    },
    async onBeforeConfirm() {
      const data = this.getData();
      await api.update(data);
    },
    i18n,
    recommendSize(width, height) {
      return `${i18n("推荐尺寸")}：${i18n("宽:")} ${width}${i18n(
        "高:"
      )}${height}`;
    }
  }
};
</script>
<style scoped>
.container {
  width: 800px;
  margin: auto;
}
</style>
