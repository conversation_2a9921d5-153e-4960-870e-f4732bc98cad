<template>
  <el-card class="image-setting">
    <div slot="header" class="image-setting-header">
      {{ i18n("图片资源配置") }}
      <div>
        <ToggleSetting></ToggleSetting>
        <el-button type="primary" @click="evSettingClick">
          <span>{{ i18n("编辑配置") }}</span>
        </el-button>
      </div>
    </div>
    <el-form class="image-setting-form" :model="form" label-position="top" ref="form" label-width="200px">
      <el-form-item v-for="(item, index) in form.items" :key="index" :label="item.name">
        <el-image class="uploader-clip-img" :src="item.url" fit="cover" :preview-src-list="[item.url]">
          <div slot="error" class="image-slot">
            <i class="el-icon-picture-outline" />
          </div>
        </el-image>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { showOmegaDialog } from "@omega/widget";
import EditDialog from "./edit.vue";
import ToggleSetting from "./components/toggleSetting.vue";
import api from "./../api/image";
import { i18n } from "../local/index.js";

export default {
  name: "ImageSetting",
  components: { ToggleSetting },
  props: {
    layoutMode: {
      type: String,
      default: "vertical"
    }
  },
  data() {
    let items = [
      {
        name: "登录界面背景图",
        key: "login_background_image_url",
        desc: "",
        desc_url: "",
        url: ""
      },
      {
        name: "登录界面LOGO",
        key: "login_logo_image_url",
        desc: "",
        desc_url: "",
        url: ""
      },
      {
        name: "登录界面主视觉图",
        key: "login_primary_image_url",
        desc: "",
        desc_url: "",
        url: ""
      }
    ];
    if (this.layoutMode === "vertical") {
      items.push(
        ...[
          {
            name: "侧边栏布局LOGO（长）",
            key: "layout_horizontal_full_image_url",
            desc: "",
            desc_url: "",
            url: ""
          },
          {
            name: "侧边栏布局LOGO（短）",
            key: "layout_horizontal_short_image_url",
            desc: "",
            desc_url: "",
            url: ""
          }
        ]
      );
    } else if (this.layoutMode === "horizontal") {
      items.push(
        ...[
          {
            name: "顶栏布局LOGO（长）",
            key: "layout_vertical_image_url",
            desc: "",
            desc_url: "",
            url: ""
          }
        ]
      );
    }
    return {
      form: {
        items
      }
    };
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      api.get().then(setting => {
        this.setData(setting);
      });
    },
    setData(data = {}) {
      this.form.items.forEach(item => {
        item.url = data[item.key] ?? "";
      });
    },
    evSettingClick() {
      showOmegaDialog(EditDialog, {
        layoutMode: this.layoutMode
      }).on("confirm", () => {
        this.load();
      });
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.image-setting {

  // margin: 20px;
  &::v-deep.el-card {
    box-sizing: border-box;
    height: 100%;

    .el-card__body {
      box-sizing: border-box;
      height: calc(100% - 70px);
      overflow: auto;
    }
  }
}

.image-setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
