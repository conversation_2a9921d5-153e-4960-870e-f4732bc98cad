import _ from "lodash";
import $ from "jquery";
import { MessageBox } from "element-ui";

import settingApi from "./api/setting";

import AdminMainDialog from "./main.vue";
import NavEditorDialog from "./nav/edit.vue";
import { showOmegaDialog } from "@omega/widget";
import omegaI18n from "@omega/i18n";
import OtherSetting from "./otherSetting/index.vue";
import { ApiProxy } from "./apiProxy.js";
import { conf } from "@omega/app";
import omegaAuth from "@omega/auth";
import ImageUploader from "./image/components/imageClipUploader/index.vue";

export { ImageUploader };

// 未来版本将废弃 admin 模块的 getOriginNavmenu 和 setOriginNavmenu
// 请使用 @omega/app 提供的 conf.getOriginNavmenu 和 conf.setOriginNavmenu
export function getOriginNavmenu() {
  return conf.getOriginNavmenu();;
}
export function setOriginNavmenu(navmenu) {
  return conf.setOriginNavmenu(navmenu);
}

export async function getNavmenu() {
  const locale = omegaI18n.locale;
  const navmenu = await settingApi.getNavmenu(locale);
  if (navmenu) {
    return navmenu;
  } else {
    return getOriginNavmenu();
  }
}

export class OmegaAdminPlugin {
  static apiOptions;
  static unregister = false;
  static register({
    apiProxy = {},
    apiPrefix = {},
    apiRequestInterceptor
  } = {}) {
    ApiProxy.api = apiProxy;

    this.apiOptions = {
      prefix: Object.assign(
        {},
        {
          "device-data-service": "/devicedata",
          "bff-service": "/bff",
          "model-service": "/model"
        },
        apiPrefix
      ),
      requestInterceptor: apiRequestInterceptor
    };
  }

  constructor(
    { conf, plugin },
    {
      isUseOriginNavmenu = true,
      layoutMode = "vertical",
      otherSertting = {
        component: null,
        handler: function () { }
      }
    } = {}
  ) {
    this.conf = conf;
    this.plugin = plugin;

    this.options = {
      // 废弃: 是否记录原始菜单配置
      isUseOriginNavmenu,
      layoutMode,
      otherSerttingHandler: otherSertting.handler
    };
    // HACK
    OtherSetting.components.OtherSettingComponent = otherSertting.component;
  }

  async beforeAppBoot() {
    if (OmegaAdminPlugin.unregister) {
      return;
    }
    const conf = this.conf;

    // 未来废弃
    if (this.options.isUseOriginNavmenu === false) {
      console.warn("未来废弃: 请使用 @omega/app 提供的 conf.getOriginNavmenu 和 conf.setOriginNavmenu");
    }
    if (this.options.isUseOriginNavmenu) {
      // 记录原始菜单配置
      setOriginNavmenu(_.cloneDeep(conf.getNavmenu()));
    }

    // 加载图片及菜单配置
    await this.loadSetting();
  }

  async loadSetting() {
    return Promise.all([this._loadResource(), this._loadNavmenu()]);
  }

  async _loadResource() {
    // 设置图片资源
    const setting = await settingApi.getResource();
    if (setting) {
      this.conf.setResource(setting.resource);
    }
    const otherSertting = setting.otherSetting;
    if (otherSertting) {
      this.conf.$set(this.conf.state, "otherSertting", otherSertting);
    }

    return this.options.otherSerttingHandler(setting.otherSetting);
  }

  async _loadNavmenu() {
    const locale = omegaI18n.locale;
    const navmenu = await settingApi.getNavmenu(locale);
    if (navmenu && navmenu.length) {
      this.conf.useCustomNavmenu(true);
      this.conf.setNavmenu(navmenu);
    }
  }

  async afterAppLogin() {
    if (OmegaAdminPlugin.unregister) {
      return;
    }

    const evShortcutKeyPress = evt => {
      // ctrl + shift + alt + L 触发
      if (evt.altKey && evt.ctrlKey && evt.shiftKey && evt.which === 76) {
        this._handlerInDialog(false);
      }
    };

    if (omegaAuth.user.isRoot()) {
      // 界面刷新后60s内有效
      const $document = $(document);
      $document.on("keydown", evShortcutKeyPress);
      setTimeout(() => {
        $document.off("keydown", evShortcutKeyPress);
      }, 6e4);
    }
  }
  async completeAppBoot() {
    if (omegaAuth.user.isRoot()) {
      // 延迟保证最后执行
      setTimeout(async () => {
        const hasUpdate = await settingApi.checkUpdate(getOriginNavmenu());
        if (hasUpdate && !window.sessionStorage.getItem("omega_admin_has_update_ignore")) {
          try {
            await MessageBox.confirm(
              `
            源代码菜单配置节点存在变更（ 删除/修改（ permission / location ）），
            请对比当前菜单配置和源代码菜单配置，
            手动更新当前菜单，以保证系统正常使用。

            如果不需要，请点击忽略，之后将不再当前窗口提示
          `,
              "提示",
              {
                showClose: false,
                confirmButtonText: "开始对比",
                cancelButtonText: "忽略"
              }
            );
          }
          catch (e) {
            window.sessionStorage.setItem("omega_admin_has_update_ignore", true);
            return;
          }
          await this._handlerInDialog(hasUpdate);
        }
      }, 5e3)
    }
  }
  async _handlerInDialog(hasUpdate) {
    if (this._lock) {
      return;
    }
    this._lock = true;
    const e = hasUpdate
      ? showOmegaDialog(NavEditorDialog, {
        useTableMode: true
      })
      : showOmegaDialog(AdminMainDialog, {
        layoutMode: this.options.layoutMode
      });

    // 停止等待用户操作
    await new Promise(resolve => {
      const cb = () => {
        this._lock = false;

        // this.loadSetting().then(resolve);
        window.location.reload();
      };

      e.on("cancel", cb);
      e.on("confirm", cb);
    });
  }
}
