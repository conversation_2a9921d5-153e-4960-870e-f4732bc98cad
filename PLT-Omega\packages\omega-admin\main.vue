<template>
  <omega-dialog
    class="omega-admin"
    :title="i18n('系统配置')"
    :fullscreen="true"
    :hasFooter="false"
  >
      <el-tabs class="omega-admin-content" value="nav" tab-position="left">
        <el-tab-pane :label="i18n('菜单配置')" name="nav">
          <NavSetting></NavSetting>
        </el-tab-pane>
        <el-tab-pane :label="i18n('图片配置')" name="img">
          <ImageSetting :layoutMode="layoutMode"></ImageSetting>
        </el-tab-pane>
        <el-tab-pane
          v-if="isHasOtherSetting"
          :label="i18n('其他配置')"
          name="other"
        >
          <OtherSetting :layoutMode="layoutMode"></OtherSetting>
        </el-tab-pane>
      </el-tabs>
  </omega-dialog>
</template>
<script>
import NavSetting from "./nav/index.vue";
import ImageSetting from "./image/index.vue";
import { i18n } from "./local/index.js";
import OtherSetting from "./otherSetting/index.vue";

export default {
  name: "OmegaAdminDialog",
  components: { NavSetting, ImageSetting, OtherSetting },
  data() {
    return {
      isHasOtherSetting: !!OtherSetting.components.OtherSettingComponent
    };
  },
  props: {
    layoutMode: {
      type: String,
      default: "vertical"
    },
    renderOtherSertting: Function
  },
  methods: {
    i18n
  }
};
</script>
<style scoped>
.omega-admin ::v-deep .is-fullscreen {
  overflow: hidden !important;
}

.omega-admin ::v-deep .el-dialog__body {
  height: calc(100% - 54px) !important;
  padding: 0 !important;
}
.omega-admin-content {
  height: 100%;
  &::v-deep .el-tabs__content {
    height: 100%;
  }
  &::v-deep .el-tab-pane {
    height: 100%;
    padding: 12px;
    box-sizing: border-box;
  }
}
</style>
