<script lang="jsx">
import OmegaIcon from "@omega/icon";
export default {
  name: "navPreview",
  components: {},
  props: {
    navmenu: Array
  },
  data() {
    return {};
  },
  render() {
    return (
      <el-menu class="nav-preview">{this.renderChild(this.navmenu)}</el-menu>
    );
  },
  methods: {
    renderChild(items) {
      const vnodes = [];
      _.forEach(items, item => {
        switch (item.type) {
          case "menuItem":
            vnodes.push(
              <el-menu-item>{this.renderNavItem(item)}</el-menu-item>
            );
            break;
          case "subMenu":
            vnodes.push(
              <el-submenu index="uuid">
                <span slot="title">{this.renderNavItem(item)}</span>
                {this.renderChild(item.subMenuList)}
              </el-submenu>
            );
            break;
          case "menuItemGroup":
            vnodes.push(
              <el-menu-item-group>
                <span slot="title">{this.renderNavItem(item)}</span>
                {this.renderChild(item.subMenuList)}
              </el-menu-item-group>
            );
            break;
        }
      });
      return vnodes;
    },
    renderNavItem(item) {
      return (
        <div class="nav-preview-item">
          <OmegaIcon
            class="nav-preview-item-icon"
            symbolId={item.icon}
            size="small"
          ></OmegaIcon>
          {item.label}
        </div>
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.nav-preview-item-icon {
  margin-right: 16px;
}
.nav-preview::v-deep.el-menu {
  border-right: none;
  .el-submenu__title,
  .el-menu-item {
    height: 50px;
    line-height: 50px;
    // @include padding(0 20px, !important);
  }
  .el-menu-item-group__title {
    height: 50px;
    line-height: 50px;
    // @include padding(0 20px, !important);
    .nav-preview-item-icon {
      font-size: 12px;
    }
  }
}
</style>
