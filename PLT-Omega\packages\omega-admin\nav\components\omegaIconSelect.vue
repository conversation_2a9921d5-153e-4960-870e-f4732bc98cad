<template>
  <omega-dialog :title="i18n('选择图标')" :onBeforeConfirm="onBeforeConfirm">
    <el-row>
      <el-col :span="2" v-for="iconSymbolId in list" :key="iconSymbolId">
        <div class="icon-box" @click="evClick(iconSymbolId)">
          <i
            v-if="isCheck(iconSymbolId)"
            class="icon-box-active el-icon-success"
          ></i>
          <OmegaIcon :symbolId="iconSymbolId" size="small"></OmegaIcon>
        </div>
      </el-col>
    </el-row>
  </omega-dialog>
</template>
<script>
import OmegaIcon from "@omega/icon";
import { i18n } from "../../local/index.js";

export default {
  name: "OmegaIconSelectDialog",
  components: { OmegaIcon },
  data() {
    return {
      check: "",
      loading: true,
      // activeNames: [],
      list: []
    };
  },
  created() {
    this.list = Object.keys(OmegaIcon.icons);
    this.setSpriteSvgIcons();
  },
  methods: {
    // 获取本地通过svg-sprite-loader加载的图标
    setSpriteSvgIcons() {
      const SVG_SPRITE_NODE = document.querySelector("#__SVG_SPRITE_NODE__");
      if (SVG_SPRITE_NODE) {
        const nodes = SVG_SPRITE_NODE.children;
        for (let node of nodes) {
          const symbolId = node.id;
          this.list.push(symbolId);
        }
      }
    },
    isCheck(symbolId) {
      return this.check === symbolId;
    },
    evClick(symbolId) {
      this.check = symbolId;
    },
    async onBeforeConfirm() {
      if (!this.check) {
        this.$message.info(i18n("请选择一个图标！"));
        return false;
      }
      return this.check;
    },
    i18n
  }
};
</script>
<style scoped>
.icon-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 20px;
  cursor: pointer;
  position: relative;
}
.icon-box-active {
  position: absolute;
  top: 12px;
  left: 12px;
  bottom: 0;
  right: 0;
  font-size: 12px;
  color: #48ff00;
  z-index: 1;
  padding: 8px;
}
.container {
  min-height: 400px;
}

.icon-box-desc {
  width: 100px;
}
</style>
>
