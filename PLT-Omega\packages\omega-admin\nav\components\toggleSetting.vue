<template>
  <div class="toggle-setting">
    <el-tag class="mr4" :type="tagState.type">{{ tagState.text }}</el-tag>
    <el-tooltip effect="dark" placement="bottom">
      <div slot="content">
        {{ i18n("是否启用在线菜单配置功能") }}
        <br />
        {{ i18n("暂停后将使用系统默认菜单配置") }}
      </div>

      <el-button
        :type="buttonState.type"
        icon="el-icon-question"
        @click="evClick"
      >
        {{ buttonState.text }}
      </el-button>
    </el-tooltip>
  </div>
</template>
<script>
import api from "../../api/nav";
import { i18n } from "../../local/index.js";

export default {
  name: "DisableNavSetting",
  data() {
    return {
      isDisable: false
    };
  },
  computed: {
    buttonState() {
      return this.isDisable
        ? {
            text: i18n("启用"),
            type: "success"
          }
        : {
            text: i18n("暂停"),
            type: "danger"
          };
    },
    tagState() {
      return this.isDisable
        ? {
            text: i18n("状态：已暂停"),
            type: "danger"
          }
        : {
            text: i18n("状态：已启用"),
            type: "success"
          };
    }
  },
  watch: {
    isDisable: {
      immediate: true,
      handler(val) {
        if (val === true) {
          this.$notify.warning({
            title: i18n("提示"),
            message: i18n(
              "当前导航菜单配置功能为暂停状态，该状态下使用系统默认的导航菜单配置，需要自定义的菜单配置需要开启该功能。"
            ),
            position: "bottom-right",
            duration: 0
          });
        }
      }
    }
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      api.isDisable().then(isDisable => {
        this.isDisable = isDisable;
      });
    },
    evClick() {
      let isDisable = !this.isDisable;
      api.disable(isDisable).then(() => {
        this.isDisable = isDisable;
        this.$emit("toggle", this.isDisable);
      });
    },
    i18n
  }
};
</script>
<style scoped>
.toggle-setting {
  display: inline-block;
  margin-right: 4px;
  vertical-align: middle;
}
.mr4 {
  margin-right: 4px;
}
</style>
