<template>
  <omega-dialog
    ref="dialog"
    :title="i18n('系统菜单编辑')"
    :fullscreen="true"
    :show-close="false"
    :hasFooter="false"
    :successMsg="i18n('保存成功')"
    :onBeforeConfirm="onBeforeConfirm"
  >
    <NavmenuEditor
      :useTableMode="useTableMode"
      @change="onchange"
      @save="save"
      @cancel="cancel"
    />
  </omega-dialog>
</template>

<script>
import NavmenuEditor from "./editor/index.vue";
import api from "../api/nav";
import { i18n } from "../local/index.js";

export default {
  name: "NavEditDialog",
  components: { NavmenuEditor },
  props: {
    useTableMode: Boolean
  },
  data() {
    return {
      navData: []
    };
  },
  watch: {},
  methods: {
    onchange(data) {
      this.navData = data;
    },
    async onBeforeConfirm() {
      await api.edit(this.navData);
    },
    save() {
      this.$refs.dialog.confirm();
    },
    cancel() {
      this.$refs.dialog.cancel();
    },
    i18n
  }
};
</script>
