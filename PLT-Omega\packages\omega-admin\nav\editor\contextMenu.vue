<script lang="jsx">
export default {
  name: "ContextMenu",
  data() {
    return {
      isShow: false,
      x: 0,
      y: 0
    };
  },
  computed: {
    top() {
      return this.y + this.padding + "px";
    },
    left() {
      return this.x + this.padding + "px";
    }
  },
  props: {
    items: {
      type: Array,
      default() {
        return [
          {
            label: "关闭",
            id: "close"
          }
        ];
      }
    },
    padding: {
      type: Number,
      default: 4
    }
  },
  render(h) {
    if (!this.isShow) {
      return;
    }

    const stylePos = {
      top: this.top,
      left: this.left
    };

    return (
      <ul class="editor-contextmenu" style={stylePos}>
        {this.items.map(item => {
          return (
            <li
              class="editor-contextmenu-item"
              key={item.id}
              onClick={this.evContextMenuItemClick.bind(this, item)}
            >
              <el-link underline={false} type={item.type}>
                {item.label}
              </el-link>
            </li>
          );
        })}
      </ul>
    );
  },
  methods: {
    evContextMenuItemClick(item) {
      this.$emit("contextMenuItemClick", item.id);
    },
    open({ x, y }) {
      this.x = x;
      this.y = y;

      this.isShow = true;
    },
    close() {
      this.isShow = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.editor-contextmenu {
  @include background_color(BG1);
  @include font_color(T1);
  min-width: 80px;
  z-index: 9999;
  position: fixed;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.editor-contextmenu-item {
  padding: 4px 8px;
  @include hover();
}
</style>
