<template>
  <omega-dialog
    :title="title"
    width="600px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form
      :model="form"
      ref="form"
      label-width="120px"
      :inline="false"
      :rules="rules"
    >
      <el-form-item :label="i18n('中文名称')" prop="label">
        <el-input class="w220" v-model="form.label"></el-input>
      </el-form-item>
      <el-form-item :label="i18n('英文名称')" prop="label_en">
        <el-input class="w220" v-model="form.label_en"></el-input>
      </el-form-item>
      <el-form-item :label="i18n('图标')">
        <div class="w220 icon-editor">
          <OmegaIcon
            class="dashbox"
            :symbolId="form.icon"
            size="small"
          ></OmegaIcon>

          <div>
            <el-button icon="el-icon-edit" @click="evIconAddClick"></el-button>
            <el-button
              class="mr10"
              icon="el-icon-delete"
              @click="evIconRemoveClick"
            ></el-button>
          </div>
        </div>
      </el-form-item>
      <el-form-item :label="i18n('菜单类型')">
        <el-select :disabled="isEdit" v-model="form.type">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <template v-if="isMenuItem">
        <el-form-item :label="i18n('页面类型')">
          <el-select v-model="page_type_value">
            <el-option
              v-for="item in types"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item :label="i18n('自动全屏')">
          <el-switch
            v-model="form.autoFullScreen"
            active-value="1"
            inactive-value="0"
          ></el-switch>
        </el-form-item> -->
        <component
          ref="subForm"
          v-if="isMenuItem"
          :is="componentId"
          v-model="params"
        ></component>
        <el-form-item :label="i18n('跳转地址')" prop="location" v-show="isEdit">
          <el-input type="textarea" v-model="form.location" autosize></el-input>
        </el-form-item>
        <el-form-item :label="i18n('权限')" prop="permission" v-show="isEdit">
          <el-input
            type="textarea"
            v-model="form.permission"
            autosize
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
  </omega-dialog>
</template>

<script>
import _ from "lodash";
import { i18n } from "../../../local/index.js";

import FormReport from "../form/Report.vue";
import FormGraph from "../form/Graph.vue";
import FormDashboard from "../form/Dashboard.vue";
import FormSoda from "../form/Soda.vue";
import FormLink from "../form/Link.vue";
import FormIFrame from "../form/IFrame.vue";
import FormUnity3d from "../form/Unity3d.vue";

import OmegaIcon from "@omega/icon";
import { showOmegaDialog } from "@omega/widget";

import omegaIconSelectDialog from "../../components/omegaIconSelect.vue";
import { ROUTE_PATHS } from "../../../CONST.js";

export default {
  name: "editCustomDialog",
  props: {
    data: {
      type: Object
    }
  },
  components: {
    OmegaIcon,
    FormReport,
    FormGraph,
    FormDashboard,
    FormSoda,
    FormLink,
    FormIFrame,
    FormUnity3d
  },
  data() {
    const form = {
      label: "",
      label_en: "",
      icon: "",
      type: "menuItem",
      isCustom: true,
      location: "",
      permission: "",
      autoFullScreen: "0"
    };

    const params = {};
    let page_type_value = "";

    if (this.data) {
      Object.assign(form, this.data);

      if (this.data.type === "menuItem") {
        const { pathname, search } = this.parseLocation(this.data.location);
        page_type_value = pathname;
        params.autoFullScreen = search.autoFullScreen;

        switch (page_type_value) {
          case ROUTE_PATHS.REPORT:
            // TODO
            break;
          case ROUTE_PATHS.GRAPH:
            params.id = [search.nodeType, search.nodeId].join("_");
            break;
          case ROUTE_PATHS.DASHBOARD:
            params.dashId = +search.id;
            break;
          case ROUTE_PATHS.SODA:
            params.appId = search.appId;
            params.entryId = search.entryId;
            params.type = search.type;

            break;
          case ROUTE_PATHS.LINK:
          case ROUTE_PATHS.IFRAME:
          case ROUTE_PATHS.UNITY_3D:
            params.url = search.url;
            break;
        }
      }
    }

    return {
      form,
      options: [
        {
          value: "menuItem",
          label: i18n("菜单选项（页面）")
        },
        {
          value: "menuItemGroup",
          label: i18n("菜单组（二级菜单）")
        },
        {
          value: "subMenu",
          label: i18n("子菜单（一级菜单）")
        }
      ],
      types: [
        // {
        //   value: ROUTE_PATHS.REPORT,
        //   label: i18n("报表页面")
        // },
        {
          value: ROUTE_PATHS.GRAPH,
          label: i18n("组态画面")
        },
        {
          value: ROUTE_PATHS.DASHBOARD,
          label: i18n("仪表盘")
        },
        {
          value: ROUTE_PATHS.SODA,
          label: i18n("苏打平台")
        },
        {
          value: ROUTE_PATHS.LINK,
          label: i18n("跳转链接")
        },
        {
          value: ROUTE_PATHS.IFRAME,
          label: i18n("内嵌页面")
        },
        {
          value: ROUTE_PATHS.UNITY_3D,
          label: i18n("Unity3D")
        }
      ],
      page_type_value,
      defaultProps: {
        children: "subMenuList",
        label: "label"
      },
      rules: {
        label: [
          { required: true, message: i18n("请输入菜单中文名称"), trigger: "blur" }
        ],
        label_en: [
          { required: true, message: i18n("请输入菜单英文名称"), trigger: "blur" }
        ]
        // location: [
        //   { required: true, message: "location 不能为空！", trigger: "blur" }
        // ]
        // permission: [
        //   { required: true, message: "permission 不能为空！", trigger: "blur" }
        // ]
      },
      params
    };
  },
  computed: {
    title() {
      return this.isEdit ? i18n("编辑") : i18n("新建");
    },
    isEdit() {
      return !!this.data;
    },
    isMenuItem() {
      return this.form.type === "menuItem";
    },
    // eslint-disable-next-line vue/return-in-computed-property
    componentId() {
      switch (this.page_type_value) {
        case ROUTE_PATHS.REPORT:
          return "FormReport";
        case ROUTE_PATHS.GRAPH:
          return "FormGraph";
        case ROUTE_PATHS.DASHBOARD:
          return "FormDashboard";
        case ROUTE_PATHS.SODA:
          return "FormSoda";
        case ROUTE_PATHS.LINK:
          return "FormLink";
        case ROUTE_PATHS.IFRAME:
          return "FormIFrame";
        case ROUTE_PATHS.UNITY_3D:
          return "FormUnity3d";
      }
    },
    isParamsEmpty() {
      return _.isEmpty(this.params);
    }
  },
  watch: {
    params(params) {
      const data = this.getData(params) || {};
      console.log("data", data)
      const urlSeach = Object.keys(data)
        .map(key => {
          return `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`;
        })
        .join("&");
      if (urlSeach) {
        this.form.location = `${this.page_type_value}?${urlSeach}`;
      }
    },
    page_type_value() {
      this.params = {};
    }
  },
  methods: {
    async onBeforeConfirm() {
      if (this.isMenuItem && !this.page_type_value) {
        this.$message.error("请选择页面类型");
        return false;
      }

      // 新建时权限数据填充
      if (!this.data && !this.form.permission) {
        switch (this.page_type_value) {
          case ROUTE_PATHS.REPORT:
            this.form.permission = "omega_admin_report_" + this.random();
            break;
          case ROUTE_PATHS.GRAPH:
            this.form.permission = "omega_admin_graph_" + this.random();
            break;
          case ROUTE_PATHS.DASHBOARD:
            this.form.permission = "omega_admin_dash_" + this.random();
            break;
          case ROUTE_PATHS.SODA:
            this.form.permission = "omega_admin_soda_" + this.random();
            break;
          case ROUTE_PATHS.LINK:
            this.form.permission = "omega_admin_link_" + this.random();
            break;
          case ROUTE_PATHS.IFRAME:
            this.form.permission = "omega_admin_iframe_" + this.random();
            break;
          case ROUTE_PATHS.UNITY_3D:
            this.form.permission = "omega_admin_unity3d_" + this.random();
            break;
        }
      }

      await Promise.all([
        this.isMenuItem ? this.$refs.subForm.validate() : Promise.resolve(),
        this.$refs.form.validate()
      ]);
      if (this.isMenuItem) {
        return {
          label: this.form.label,
          label_en: this.form.label_en,
          type: this.form.type,
          icon: this.form.icon,
          location: this.form.location,
          permission: this.form.permission,
          autoFullScreen: this.form.autoFullScreen,
          isCustom: true
        };
      } else {
        return {
          label: this.form.label,
          label_en: this.form.label_en,
          type: this.form.type,
          icon: this.form.icon,
          isCustom: true,
          subMenuList: this.form.subMenuList || []
        };
      }
    },
    getData(params) {
      switch (this.page_type_value) {
        case ROUTE_PATHS.REPORT:
          return { uri: params.uri };
        case ROUTE_PATHS.GRAPH:
          if (params.id) {
            const [nodeType, nodeId] = params.id.split("_");
            return { nodeType, nodeId };
          }
          break;
        case ROUTE_PATHS.DASHBOARD:
          return { id: params.dashId, mode: params.dashMode };
        case ROUTE_PATHS.SODA:
          return {
            appId: params.appId,
            entryId: params.entryId,
            type: params.type,
            autoFullScreen: params.autoFullScreen
          };
        case ROUTE_PATHS.LINK:
        case ROUTE_PATHS.IFRAME:
        case ROUTE_PATHS.UNITY_3D:
          return {
            url: params.url
          };
      }
    },
    random() {
      const random = Math.random().toString(36).substr(2);
      const date = new Date().toLocaleDateString();
      return `${date}_${random}`;
    },
    evIconAddClick() {
      const e = showOmegaDialog(omegaIconSelectDialog);
      e.on("confirm", iconSymbolId => {
        this.form.icon = iconSymbolId;
      });
    },
    evIconRemoveClick() {
      this.$confirm(i18n("确定删除图标"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消")
      }).then(() => {
        this.form.icon = "";
      });
    },
    parseLocation(location) {
      const url = new URL(location, window.location.origin);
      return {
        pathname: url.pathname,
        search: Object.fromEntries(url.searchParams)
      };
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.fr {
  float: right;
}

.mr10 {
  margin-right: 10px;
}

.dashbox {
  border: 1px dashed;
}

.icon-editor {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.w220 {
  width: 220px;
}
</style>
