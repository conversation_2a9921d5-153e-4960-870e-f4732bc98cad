<template>
  <omega-dialog
    :title="i18n('编辑原始菜单')"
    width="600px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form
      :model="form"
      ref="form"
      :rules="rules"
      label-width="120px"
      :inline="false"
    >
      <el-form-item :label="i18n('中文名称')" prop="label">
        <el-input class="w220" v-model="form.label"></el-input>
      </el-form-item>
      <el-form-item :label="i18n('英文名称')" prop="label_en">
        <el-input class="w220" v-model="form.label_en"></el-input>
      </el-form-item>
      <el-form-item :label="i18n('图标')">
        <div class="w220 icon-editor">
          <OmegaIcon
            class="dashbox"
            :symbolId="form.icon"
            size="small"
          ></OmegaIcon>
          <span>
            <el-button icon="el-icon-edit" @click="evIconAddClick"></el-button>
            <el-button
              class="mr10"
              icon="el-icon-delete"
              @click="evIconRemoveClick"
            ></el-button>
          </span>
        </div>
      </el-form-item>
      <el-form-item :label="i18n('菜单类型')">
        <el-select disabled v-model="form.type">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <template v-if="isMenuItem">
        <el-form-item :label="i18n('跳转地址')" prop="location">
          <el-input
            class="w220"
            type="textarea"
            v-model="form.location"
            autosize
          ></el-input>
        </el-form-item>
        <el-form-item :label="i18n('权限')" prop="permission">
          <el-input
            type="textarea"
            class="w220"
            v-model="form.permission"
            autosize
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
  </omega-dialog>
</template>
<script>
import { i18n } from "../../../local/index.js";
import OmegaIcon from "@omega/icon";
import { showOmegaDialog } from "@omega/widget";

import omegaIconSelectDialog from "../../components/omegaIconSelect.vue";

export default {
  name: "EditOriginDialog",
  props: {
    data: {
      type: Object,
      require: true
    }
  },
  components: { OmegaIcon },
  data() {
    let form = {
      label: this.data.label,
      label_en: this.data.label_en,
      icon: this.data.icon,
      type: this.data.type,
      location: this.data.location,
      permission: this.data.permission
    };

    return {
      form,
      options: [
        {
          value: "menuItem",
          label: i18n("菜单选项（页面）")
        },
        {
          value: "menuItemGroup",
          label: i18n("菜单组（二级菜单）")
        },
        {
          value: "subMenu",
          label: i18n("子菜单（一级菜单）")
        }
      ],
      rules: {
        label: [
          { required: true, message: "请输入菜单中文名称", trigger: "blur" }
        ],
        label_en: [
          { required: true, message: "请输入菜单英文名称", trigger: "blur" }
        ],
        location: [
          { required: true, message: "location 不能为空！", trigger: "blur" }
        ],
        permission: [
          { required: true, message: "permission 不能为空！", trigger: "blur" }
        ]
      }
    };
  },
  computed: {
    isMenuItem() {
      return this.data.type === "menuItem";
    }
  },
  methods: {
    async onBeforeConfirm() {
      await this.$refs.form.validate();
      if (this.isMenuItem) {
        return {
          label: this.form.label,
          label_en: this.form.label_en,
          type: this.form.type,
          icon: this.form.icon,
          location: this.form.location,
          permission: this.form.permission
        };
      } else {
        return {
          label: this.form.label,
          label_en: this.form.label_en,
          type: this.form.type,
          icon: this.form.icon
        };
      }
    },
    evIconAddClick() {
      const e = showOmegaDialog(omegaIconSelectDialog);
      e.on("confirm", iconSymbolId => {
        this.form.icon = iconSymbolId;
      });
    },
    evIconRemoveClick() {
      this.$confirm(i18n("确定删除图标"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消")
      }).then(() => {
        this.form.icon = "";
      });
    },
    i18n
  }
};
</script>
<style scoped>
.fr {
  float: right;
}

.mr10 {
  margin-right: 10px;
}

.dashbox {
  border: 1px dashed;
}

.w220 {
  width: 220px;
}

.icon-editor {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
