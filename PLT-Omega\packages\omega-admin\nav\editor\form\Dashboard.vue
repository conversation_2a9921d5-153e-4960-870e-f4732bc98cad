<template>
  <el-form
    label-width="120px"
    ref="form"
    :inline="false"
    :model="form"
    :rules="rules"
  >
    <el-form-item :label="i18n('目标仪表盘')" prop="dashId">
      <el-select v-model="form.dashId" placeholder="请选择一个仪表盘页面">
        <el-option
          v-for="item in dashList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item :label="i18n('模式')">
      <el-select
        v-model="form.dashMode"
        class="w230"
        :placeholder="i18n('请选择仪表盘展示模式')"
      >
        <el-option
          v-for="item in modeList"
          :key="item.id"
          :label="item.name"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
import dashApi from "../../../api/dashboard.js";
export default {
  name: "FormReport",
  beforeCreate() {
    dashApi.getDashborardList().then(data => {
      this.dashList = data;
    });
  },
  data() {
    return {
      rules: {
        dashId: [
          {
            required: true,
            message: "请选择一个仪表盘页面",
            trigger: "change"
          }
        ]
      },
      dashList: [],
      modeList: [
        {
          name: "普通模式",
          id: 0
        },
        {
          name: "无标题模式",
          id: 1
        }
      ],
      form: {
        dashMode: 1,
        dashId: null
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
