<template>
  <el-form
    label-width="120px"
    :inline="false"
    ref="form"
    :model="form"
    :rules="rules"
  >
    <el-form-item :label="i18n('图形节点')" prop="ids">
      <el-cascader
        :options="options"
        v-model="form.ids"
        :props="{
          label: 'text',
          value: 'id'
        }"
        placeholder="搜索"
        filterable
        clearable
      >
        <template slot-scope="{ data }">
          <span>{{ data.text }}</span>
          &nbsp;
          <span v-if="data.dir">
            ({{ (data.children && data.children.length) || 0 }})
          </span>
        </template>
      </el-cascader>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
import graphApi from "../../../api/graph.js";
import util from "../../../util.js";
import _ from "lodash";

export default {
  name: "FormGraph",
  data() {
    return {
      options: [],
      form: {
        ids: []
      },
      rules: {
        ids: [
          {
            type: "array",
            required: true,
            message: "请选择一个图性节点",
            trigger: "change"
          }
        ]
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    "form.ids": {
      handler(val) {
        this.$emit("change", { id: val[val.length - 1] });
      }
    }
  },
  created() {
    this.loadPromise = graphApi.getGraphTree().then(data => {
      this.options = this.transformOptions(data);
      return data;
    });

    if (this.data.id) {
      this.loadPromise.then(data => {
        const nodes = util.getTreePathNodes(data, this.data.id, {
          childKey: "children",
          valueKey: "id"
        });
        const ids = nodes.map(node => node.id);
        if (!_.isEqual(this.form.ids, ids)) {
          this.form.ids = ids;
        }
        return data;
      });
    }
  },
  methods: {
    transformOptions(data) {
      function loop(items) {
        for (const item of items) {
          if (item.dir) {
            item.disabled = item.dir && !item.children?.length;
            loop(item.children || []);
          }
        }
      }

      loop(data || []);

      return data;
    },
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
