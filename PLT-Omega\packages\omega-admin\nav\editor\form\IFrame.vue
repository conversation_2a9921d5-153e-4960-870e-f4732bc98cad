<template>
  <el-form
    label-width="120px"
    ref="form"
    :inline="false"
    :model="form"
    :rules="rules"
  >
    <el-form-item :label="i18n('内嵌链接')" prop="url">
      <div class="link-input">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="form.url"
          :placeholder="i18n('请输入链接')"
        ></el-input>
        <el-tooltip
          content="使用${token}、${username}、${theme} 、${userId}、${localhost} 可用于动态传参，例如：http://example.com?token=${token}&username=${username}&theme=${theme}"
        >
          <i class="icon el-icon-question"></i>
        </el-tooltip>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
export default {
  name: "FormIFrame",

  data() {
    return {
      rules: {
        url: [
          {
            required: true,
            message: i18n("请输入链接地址"),
            trigger: "change"
          }
        ]
      },
      list: [],
      form: {
        url: null
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.link-input {
  display: flex;
  align-items: center;
  .icon {
    margin: 0 20px;
  }
}
</style>
