<template>
  <el-form label-width="120px" ref="form" :inline="false" :model="form" :rules="rules">
    <el-form-item :label="i18n('跳转链接')" prop="url">
      <div class="link-input">
        <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" v-model="form.url"
          :placeholder="i18n('请输入链接')"></el-input>
        <el-tooltip
          :content="dynamicParamsExample"
        >
          <i class="icon el-icon-question"></i>
        </el-tooltip>
      </div>
    </el-form-item>
    <el-button style="margin-left: 120px; margin-bottom: 18px;" @click="createURL()">{{ i18n("苏打平台") }}</el-button>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
import sodaApi from "../../../api/soda.js";

export default {
  name: "FormLink",
  data() {
    return {
      rules: {
        url: [
          {
            required: true,
            message: i18n("请输入链接地址"),
            trigger: "change"
          }
        ]
      },
      list: [],
      form: {
        url: null
      },
      dynamicParamsExample: `${i18n("使用")}\${token}、\${locale}、\${username}、\${theme}、\${userId}、\${localhost} ${i18n("可用于动态传参，例如：")}http://example.com?token=\${token}&locale=\${locale}&theme=\${theme}`,
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    async validate() {
      return this.$refs.form.validate();
    },
    i18n,
    //自动生成苏打的URL
    async createURL() {
      try {
        const sodaWebRes = await sodaApi.getSodaWeb();
        this.form.url = `${sodaWebRes.data}/workbench/apps?token=\${token}&theme=\${theme}&locale=\${locale}`;
      } catch (error) {
        console.error(error);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.link-input {
  display: flex;
  align-items: center;
  .icon {
    margin: 0 20px;
  }
}
</style>
