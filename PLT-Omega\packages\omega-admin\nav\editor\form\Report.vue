<template>
  <el-form label-width="120px" ref="form" :inline="false">
    <el-form-item :label="i18n('报表路径')">
      <el-input v-model="form.uri"></el-input>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
export default {
  name: "FormReport",
  data() {
    return {
      form: {
        uri: ""
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
