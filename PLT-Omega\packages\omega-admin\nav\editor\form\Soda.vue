<template>
  <el-form
    label-width="120px"
    ref="form"
    :inline="false"
    :model="form"
    :rules="rules"
  >
    <el-form-item :label="i18n('苏打页面')" prop="entryId">
      <el-cascader
        v-model="cascaderValue"
        :options="list"
        :props="cascaderProps"
        clearable
        @change="handleChangeEntry"
      ></el-cascader>
    </el-form-item>

    <el-form-item :label="i18n('自动全屏')">
      <el-switch
        v-model="form.autoFullScreen"
        active-value="1"
        inactive-value="0"
      ></el-switch>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
import sodaApi from "../../../api/soda.js";
export default {
  name: "FormSoda",
  beforeCreate() {
    sodaApi.querySodaMenuList().then(res => {
      const data = res.data;
      if (!data) return;
      this.list = data.menu;
      this.form.appId = data.app.appId;
    });
  },
  data() {
    return {
      cascaderProps: {
        value: "entryId",
        label: "name",
        emitPath: false
      },
      cascaderValue: null,
      rules: {
        entryId: [
          {
            required: true,
            message: "请选择一个苏打页面",
            trigger: "change"
          }
        ]
      },
      list: [],
      form: {
        entryId: null,
        appId: null,
        type: null,
        autoFullScreen: "0"
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
        this.cascaderValue = this.form.entryId;
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    handleChangeEntry(entryId) {
      let menu = null;
      this.form.entryId = entryId;
      menu = this.findMenu(this.list, entryId);
      if (!menu) return;
      this.form.type = menu.type;
    },
    findMenu(list, entryId) {
      if (!entryId) return null;
      for (const item of list) {
        if (item.entryId === entryId) return item;
        if (!item.children) continue;
        const found = this.findMenu(item.children, entryId);
        if (found) {
          return found;
        }
      }
    },
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
