<template>
  <el-form
    label-width="120px"
    ref="form"
    :inline="false"
    :model="form"
    :rules="rules"
  >
    <el-form-item :label="i18n('链接地址')" prop="url">
      <div class="link-input">
        <el-input
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          v-model="form.url"
          placeholder="请输入链接"
        ></el-input>
        <el-tooltip
          content="使用${localhost} 可用于动态获取当前Web的ip地址，例如：http://${localhost}:18090"
        >
          <i class="icon el-icon-question"></i>
        </el-tooltip>
      </div>
    </el-form-item>
  </el-form>
</template>

<script>
import { i18n } from "../../../local/index.js";
export default {
  name: "FormIFrame",

  data() {
    return {
      rules: {
        url: [
          {
            required: true,
            message: "请输入链接地址",
            trigger: "change"
          }
        ]
      },
      list: [],
      form: {
        url: null
      }
    };
  },
  props: {
    data: Object
  },
  model: {
    prop: "data",
    event: "change"
  },
  watch: {
    data: {
      handler() {
        Object.assign(this.form, this.data);
      },
      immediate: true
    }
  },
  updated() {
    this.$emit("change", { ...this.form });
  },
  methods: {
    async validate() {
      return this.$refs.form.validate();
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.link-input {
  display: flex;
  align-items: center;
  .icon {
    margin: 0 20px;
  }
}
</style>
