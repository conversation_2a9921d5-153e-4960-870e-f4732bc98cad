<script lang="jsx">
import _ from "lodash";
import $ from "jquery";

import VueDraggable from "vuedraggable";
import TextInput from "./textInput.vue";

import api from "../../api/nav";
import OmegaIcon from "@omega/icon";
import omegaIconSelectDialog from "../components/omegaIconSelect.vue";

import editOriginDialog from "./dialog/editOrigin.vue";
import editCustomDialog from "./dialog/editCustom.vue";

import { showOmegaDialog } from "@omega/widget";

import { getOriginNavmenu } from "../../index";

import { i18n } from "../../local/index.js";
import ContextMenu from "./contextMenu.vue";
import { onMove, onEnd } from "./hightlight.js";
import { transformLabel2En } from "./transform.js";

export default {
  name: "Editor",
  props: {
    useTableMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isTableMode: this.useTableMode,
      originNavmenu: _.cloneDeep(getOriginNavmenu()),
      navmenu: [],
      // HACK 处理navmenu 变化监听不到旧值的替代方案
      navmenuShadow: [],
      undoQueue: [],
      redoQueue: [],
      // 锁
      lockFlag: false,
      contextItems: []
    };
  },
  render() {
    return (
      <div class={"editor" + (this.isTableMode ? " editor-table" : "")}>
        <ContextMenu ref="contextmenu" items={this.contextItems}></ContextMenu>
        <div class="editor-header">
          <el-button type="primary" size="mini" onClick={this.evfullDose}>
            {i18n("全量操作")}
          </el-button>
          <el-button type="danger" size="mini" onClick={this.evEmpty}>
            {i18n("清空")}
          </el-button>
          <el-button
            type="info"
            size="mini"
            icon="el-icon-refresh-left"
            disabled={this.isUndoDisabled}
            onClick={this.evUndoClick}
          >
            {i18n("撤销")}
          </el-button>
          <el-button
            type="info"
            size="mini"
            icon="el-icon-right"
            disabled={this.isRedoDisabled}
            onClick={this.evRedoClick}
          >
            {i18n("重做")}
          </el-button>

          <el-button
            icon="el-icon-plus"
            type="primary"
            size="mini"
            onClick={this.evAddClick}
          >
            {i18n("新增根菜单")}
          </el-button>
          <el-button size="mini" onClick={this.evCancelClick}>
            {i18n("取消")}
          </el-button>
          <el-button size="mini" type="primary" onClick={this.evSaveClick}>
            {i18n("保存")}
          </el-button>
        </div>
        <div class="editor-container">
          <el-card class="editor-right">
            {this.isTableMode ? (
              <div slot="header" class="editor-right-header">
                {this.renderNavTableHeader(false)}
              </div>
            ) : (
              ""
            )}
            {this.renderNavmenu(this.originNavmenu, {
              isEditor: false
            })}
            <div class="editor-right-footer">{i18n("源代码菜单配置")}</div>
          </el-card>
          <div class="editor-center">
            <i class="el-icon-d-arrow-right editor-center-arrow" />
          </div>

          <el-card class="editor-left">
            {this.isTableMode ? (
              <div slot="header" class="editor-left-header">
                {this.renderNavTableHeader(true)}
              </div>
            ) : (
              ""
            )}
            {this.renderNavmenu(this.navmenu, {
              isEditor: true,
              isVModel: true,
              parent: this,
              prop: "navmenu"
            })}
            <div class="editor-left-footer">{i18n("当前系统菜单配置")}</div>
          </el-card>
        </div>
        <div class="editor-footer">
          <el-switch
            v-model={this.isTableMode}
            active-text="表格模式"
            inactive-text="菜单模式"
          ></el-switch>
        </div>
      </div>
    );
  },
  created() {
    api.getAdminNavmenu().then(navmenu => {
      this.lock();
      if (navmenu) {
        this.navmenu = navmenu;
      }
    });

    this.addPropLabelEn = transformLabel2En();
  },
  watch: {
    navmenu: {
      deep: true,
      handler(val) {
        this.navmenuShadow = _.cloneDeep(val);
        this.$emit("change", val);
      }
    },
    navmenuShadow(cur, pre) {
      if (this.lockFlag) {
        return;
      }
      // 推入撤销栈
      this.undoQueue.push(_.cloneDeep(pre));
      // 清空重做栈
      this.redoQueue = [];
    }
  },
  computed: {
    isUndoDisabled() {
      return !this.undoQueue.length;
    },
    isRedoDisabled() {
      return !this.redoQueue.length;
    }
  },
  methods: {
    renderNavmenu(navmenu, { isEditor = false } = {}) {
      return (
        <el-menu class="editor-navmenu">
          {this.renderChild(navmenu, {
            isEditor
          })}
        </el-menu>
      );
    },
    renderChild(items, option) {
      const vnodes = [];
      _.forEach(items, item => {
        switch (item.type) {
          case "menuItem":
            vnodes.push(
              <el-menu-item index={item.label}>
                {this.renderNavItem(option, item, items)}
              </el-menu-item>
            );
            break;
          case "subMenu":
            vnodes.push(
              <el-submenu index={item.label}>
                <span slot="title">
                  {this.renderNavItem(option, item, items)}
                </span>
                {this.renderChild(item.subMenuList, option)}
              </el-submenu>
            );
            break;
          case "menuItemGroup":
            vnodes.push(
              <el-menu-item-group>
                <span slot="title">
                  {this.renderNavItem(option, item, items)}
                </span>
                {this.renderChild(item.subMenuList, option)}
              </el-menu-item-group>
            );
            break;
        }
      });

      if (option.isEditor) {
        return (
          <VueDraggable
            class="editor-drag-area"
            group={{
              name: "omega-system-navmenu",
              pull: true,
              put: true
            }}
            list={items}
            move={onMove}
            onEnd={onEnd}
          >
            {vnodes}
          </VueDraggable>
        );
      } else {
        return (
          <VueDraggable
            group={{
              name: "omega-system-navmenu",
              pull: "clone",
              put: false,
              sort: false
            }}
            clone={this.clone}
            list={items}
            move={onMove}
            onEnd={onEnd}
          >
            {vnodes}
          </VueDraggable>
        );
      }
    },
    renderNavItem({ isEditor }, item, items) {
      let textVnode;
      let iconVnode;
      if (isEditor) {
        textVnode = (
          <TextInput
            class={item.isCustom && "editor-custom-node"}
            value={item.label}
            onInput={value => (item.label = value)}
          ></TextInput>
        );
      } else {
        textVnode = item.label;
      }

      iconVnode = [item.icon && <OmegaIcon symbolId={item.icon} />];

      return (
        <div
          oncontextmenu={evt =>
            isEditor && this.evContextmenu(evt, item, items)
          }
          class="editor-navmenu-item"
        >
          <div class="editor-navmenu-icon">{iconVnode}</div>
          <div class="editor-navmenu-label">{textVnode}</div>
          {this.isTableMode && this.renderNavTable(item, isEditor)}
        </div>
      );
    },
    renderNavTableHeader(isEditor) {
      return [
        <div class="editor-navmenu-icon-header">icon</div>,
        <div class="editor-navmenu-label-header">label</div>,
        <div class="editor-navmenu-type-header">type</div>,
        <div class="editor-navmenu-location-header">location</div>,
        <div class="editor-navmenu-permission-header">permission</div>
      ];
    },
    renderNavTable(item, isEditor) {
      return [
        <div class="editor-navmenu-type">{item.type}</div>,
        <div class="editor-navmenu-location" v-tooltip={item.location}>
          {item.location}
        </div>,
        <div class="editor-navmenu-permission" v-tooltip={item.permission}>
          {item.permission}
        </div>
      ];
    },
    evIconAddClick(item) {
      const e = showOmegaDialog(omegaIconSelectDialog);
      e.on("confirm", iconSymbolId => {
        item.icon = iconSymbolId;
      });
    },
    evIconRemoveClick(item) {
      this.$confirm(i18n("确定删除图标"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消")
      }).then(() => {
        item.icon = "";
      });
    },
    evUndoClick() {
      this.lock();
      this.redoQueue.push(_.cloneDeep(this.navmenu));
      this.navmenu = this.undoQueue.pop();
    },
    evRedoClick() {
      this.lock();
      this.undoQueue.push(_.cloneDeep(this.navmenu));
      this.navmenu = this.redoQueue.pop();
    },
    lock() {
      this.lockFlag = true;
      setTimeout(() => {
        this.lockFlag = false;
      });
    },
    evAddClick() {
      const e = showOmegaDialog(editCustomDialog);
      e.on("confirm", item => {
        this.navmenu.push(item);
      });
    },
    evEmpty() {
      this.$confirm(i18n("清空当前系统菜单配置"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消")
      }).then(() => {
        this.navmenu = [];
      });
    },
    async evfullDose() {
      if (this.navmenu.length) {
        await this.$confirm(
          i18n(
            "检查到当前菜单配置不为空，全量操作将会把源代码菜单配置全量的覆盖当前菜单配置，是否要继续直接覆盖？"
          ),
          i18n("提示"),
          {
            confirmButtonText: i18n("确定"),
            cancelButtonText: i18n("取消")
          }
        );
      }

      const navmenu = _.cloneDeep(this.originNavmenu);
      navmenu.forEach(this.addPropLabelEn);
      this.navmenu = navmenu;
    },
    clone(val) {
      // 防止 icon 未设置初始值不能被响应式监听
      const item = _.cloneDeep(Object.assign({ icon: "" }, val));
      this.addPropLabelEn(item);
      return item;
    },
    evContextmenu(evt, item, items) {
      evt.preventDefault();

      this.contextItems = [
        {
          label: i18n("编辑"),
          id: "edit"
        },
        {
          label: i18n("删除"),
          type: "danger",
          id: "delete"
        },
        {
          label: i18n("替换图标"),
          id: "edit_icon"
        },
        {
          label: i18n("删除图标"),
          type: "danger",
          id: "delete_icon"
        }
      ];

      if (item.subMenuList) {
        this.contextItems.unshift({
          label: i18n("新增"),
          id: "add"
        });
      }

      const contextmenu = this.$refs.contextmenu;
      contextmenu.open({ x: evt.clientX, y: evt.clientY });
      contextmenu.$once("contextMenuItemClick", contextid => {
        switch (contextid) {
          case "add":
            this.addItem(item);
            break;
          case "edit":
            this.editItem(item);
            break;
          case "delete":
            this.deleteItem(item, items);
            break;
          case "edit_icon":
            this.evIconAddClick(item);
            break;
          case "delete_icon":
            this.evIconRemoveClick(item);
            break;
        }
      });
      $(document).one("click.contenxtmenu", function () {
        contextmenu.$off("contextMenuItemClick");
        contextmenu.close();
      });
    },
    deleteItem(item, items) {
      this.$confirm(i18n("确定删除该项"), i18n("提示"), {
        confirmButtonText: i18n("确定"),
        cancelButtonText: i18n("取消")
      }).then(() => {
        items.splice(items.indexOf(item), 1);
      });
    },
    editItem(item, items) {
      if (item.isCustom) {
        const e = showOmegaDialog(editCustomDialog, {
          data: item
        });
        e.on("confirm", _item => {
          Object.assign(item, _item);
        });
      } else {
        const e = showOmegaDialog(editOriginDialog, {
          data: {
            label: item.label,
            label_en: item.label_en,
            type: item.type,
            icon: item.icon,
            location: item.location,
            permission: item.permission
          }
        });
        e.on("confirm", _item => {
          Object.assign(item, _item);
        });
      }
      // const e = showOmegaDialog(addDialog);
      // e.on("confirm", item => {
      //   items.push(item);
      // });
    },
    addItem(item) {
      const e = showOmegaDialog(editCustomDialog);
      e.on("confirm", _item => {
        item.subMenuList.push(_item);
      });
    },
    evCancelClick() {
      this.$emit("cancel");
    },
    evSaveClick() {
      this.$emit("save");
    }
  }
};
</script>
<style scoped lang="scss">
.editor {
  height: 100%;
  width: 100%;
}

.editor-header {
  position: absolute;
  top: 10px;
  right: 10px;
  & > * {
    margin-right: 12px;
  }
}
.editor:deep {
  .el-card {
    box-sizing: border-box;
    height: 100%;
    .el-card__body {
      box-sizing: border-box;
      height: calc(100% - 69px);
    }
  }

  .el-menu-item:focus,
  .el-menu-item:hover {
    background-color: transparent;
  }
}
.editor-container {
  box-sizing: border-box;
  height: 100%;
  width: 1000px;
  margin: auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.editor-footer {
  position: absolute;
  bottom: 10px;
  right: 10px;
  & > * {
    margin-right: 12px;
  }
}

.editor-right,
.editor-left {
  position: relative;
}

.editor-left-header,
.editor-right-header {
  display: flex;
}

.editor-left-footer,
.editor-right-footer {
  position: absolute;
  bottom: 10px;
}
.editor-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
.editor-center-arrow {
  font-size: 22px;
  font-weight: 600;
}
.editor-navmenu {
  width: 256px;
  height: 100%;
  overflow: auto;
}

.editor-drag-area {
  min-height: 56px;
  height: 100%;
  // border: 1px dashed #ccc;

  &.dragover {
    background-color: rgba(0, 0, 0, 0.2);
  }
}

.sortable-chosen {
  background-color: rgba(0, 0, 0, 0.2);
}

.editor-custom-node {
  ::after {
    content: " (自定义) ";
  }
}

.editor-navmenu-icon {
  position: relative;
  display: inline-flex;
  margin-right: 16px;
  width: 20px;
  height: 20px;
  font-size: 24px;
  justify-content: center;
  align-items: center;
}
.editor-navmenu-label {
  display: inline-flex;
}

.editor-table::v-deep .el-menu {
  border-right: none;
  .el-submenu__title,
  .el-menu-item {
    height: 50px;
    line-height: 50px;
    &:focus,
    &:hover {
      background-color: transparent;
    }
  }
  .el-menu-item-group__title {
    height: 50px;
    line-height: 50px;
    .editor-navmenu-icon {
      font-size: 12px;
    }
  }
}

.editor-table {
  .editor-navmenu {
    width: auto;
  }
  .editor-container {
    width: auto;
    justify-content: center;
  }
  .single-row {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .editor-navmenu-label {
    width: 215px;
  }
  .editor-navmenu-type {
    margin-left: 10px;
    width: 90px;
    @extend .single-row;
  }
  .editor-navmenu-location {
    margin-left: 10px;
    width: 200px;
    @extend .single-row;
  }
  .editor-navmenu-permission {
    margin-left: 10px;
    width: 150px;
    @extend .single-row;
  }
  .editor-navmenu-custom {
    margin-left: 10px;
    width: 30px;
    @extend .single-row;
  }
  .editor-navmenu-item {
    display: flex;
    align-items: center;
  }

  .editor-navmenu-icon-header {
    width: 46px;
    margin-left: 10px;
  }
  .editor-navmenu-label-header {
    margin-left: 10px;
    width: 215px;
  }
  .editor-navmenu-type-header {
    margin-left: 10px;
    width: 90px;
  }
  .editor-navmenu-location-header {
    margin-left: 10px;
    width: 200px;
  }
  .editor-navmenu-permission-header {
    margin-left: 10px;
    width: 150px;
  }
  .editor-navmenu-custom-header {
    margin-left: 10px;
    width: 70px;
  }
}
</style>
