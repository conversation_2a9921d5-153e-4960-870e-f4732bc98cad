<template>
  <div class="editor-text-input-container" @dblclick.stop="evDbClick">
    <input
      class="editor-text-input"
      type="text"
      v-model="textValue"
      v-if="isEdit"
    />
    <span class="editor-text" v-else>{{ value }}</span>
  </div>
</template>

<script>
import $ from "jquery";

export default {
  name: "TextInput",
  model: {
    prop: "value",
    event: "input"
  },
  props: ["value"],
  data() {
    return {
      isEdit: false
    };
  },
  computed: {
    textValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      }
    }
  },
  methods: {
    evDbClick() {
      this.isEdit = true;

      this.$nextTick(() => {
        // 取消编辑处理 enter/esc/失焦
        const $document = $(window.document);
        $document.on("keyup.edittext", evt => {
          if (evt.which === 13 || evt.which === 27) {
            $document.off("keyup.edittext");
            this.isEdit = false;
          }
        });
        const $input = $(".editor-text-input", this.$el);
        $input.focus();
        $input.on("blur", () => {
          $input.off("blur");
          this.isEdit = false;
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.editor-text-input-container {
  display: inline-block;
}
.editor-text-input {
  &:focus {
    outline: none;
  }
  border: none;
  background: transparent;
  border-bottom: 1px solid #ccc;
}
.editor-text {
  &:hover {
    border-bottom: 1px solid #ccc;
  }
}
</style>
