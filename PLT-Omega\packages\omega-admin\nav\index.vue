<template>
  <el-card class="nav-setting">
    <div slot="header" class="nav-setting-header">
      {{ i18n("导航菜单配置") }}
      <div>
        <ToggleSetting></ToggleSetting>
        <el-button type="primary" @click="evSettingClick">
          <span v-if="isEmpty">{{ i18n("新建配置") }}</span>
          <span v-else>{{ i18n("编辑配置") }}</span>
        </el-button>
      </div>
    </div>
    <div class="nav-setting-empty" v-if="isEmpty">
      <h3>{{ i18n("当前系统未对系统菜单项进行限制") }}</h3>
      <cet-icon iconClass="frame_add" class="empty-icon"></cet-icon>
    </div>
    <NavPreview
      v-else
      class="nav-setting-container"
      :navmenu="navmenu"
    ></NavPreview>
  </el-card>
</template>

<script>
import api from "../api/nav";
import NavPreview from "./components/navPreview.vue";
import { showOmegaDialog } from "@omega/widget";
import NavEditDialog from "./edit.vue";
import ToggleSetting from "./components/toggleSetting.vue";
import { i18n } from "../local/index.js";

export default {
  name: "SystemSetting",
  components: {
    NavPreview,
    ToggleSetting
  },
  data() {
    return {
      navmenu: [],
      activities: []
    };
  },
  computed: {
    isEmpty() {
      return !(this.navmenu && this.navmenu.length);
    }
  },
  created() {
    this.load();
  },
  methods: {
    load() {
      api.getAdminNavmenu().then(navmenu => {
        if (navmenu) {
          this.navmenu = navmenu;
        }
      });
    },
    evSave() {
      const data = this.$refs.mod.getData();
      api.edit(data).then(() => {
        this.$message.success(i18n("保存成功"));
      });
    },
    evSettingClick() {
      showOmegaDialog(NavEditDialog).on("confirm", () => {
        this.load();
      });
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.nav-setting {
  // margin: 20px;
}
.nav-setting::v-deep.el-card {
  box-sizing: border-box;
  height: 100%;
  .el-card__body {
    box-sizing: border-box;
    height: calc(100% - 70px);
    overflow: auto;
  }
}

.nav-setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.nav-setting-empty {
  margin: auto;
  width: 600px;
  text-align: center;
}

.nav-setting-container {
  width: 256px;
  height: 100%;
  margin: auto;
  overflow: auto;
  outline: 999px solid rgba(0, 0, 0, 0.2);
}
</style>
