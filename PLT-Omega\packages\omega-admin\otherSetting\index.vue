<template>
  <el-card class="other-setting">
    <div slot="header" class="other-setting-header">
      <span>系统其他配置信息</span>
      <span>
        <el-alert title="保存成功" type="success" :closable="false" v-show="isSaveSuccess" show-icon></el-alert>
      </span>
      <el-button type="primary" @click="onSaveClick" :loading="loading">
        保存
      </el-button>
    </div>
    <OtherSettingComponent v-model="setting"></OtherSettingComponent>
  </el-card>
</template>
<script>
import settingApi from "../api/setting.js";
import _ from "lodash";

export default {
  name: "OtherSetting",
  components: {
    OtherSettingComponent: null
  },
  data() {
    return {
      loading: false,
      isSaveSuccess: false,
      setting: {}
    };
  },
  async mounted() {
    this.setting = await settingApi.getOtherSetting();
    this.$watch(
      "setting", (val, oldVal) => {
        if (!_.isEqual(val, oldVal)) {
          this.debounceSave(val, false);
        }
      },
      {
        deep: true
      }
    );
  },
  methods: {
    async onSaveClick() {
      return this.save(this.setting, true);
    },
    async sleep(time) {
      return new Promise(resolve => setTimeout(resolve, time));
    },
    debounceSave: _.debounce(function (data, notify) {
      return this.save(data);
    }, 1e3),
    async save(data) {
      this.loading = true;
      await settingApi.updateOtherSetting(data);
      await this.sleep(1e3);
      this.loading = false;

      this.isSaveSuccess = true;
      await this.sleep(2e3);
      this.isSaveSuccess = false;
    }
  }
};
</script>
<style lang="scss" scoped>
.other-setting {

  // margin: 20px;
  &::v-deep.el-card {
    height: 100%;

    .el-card__body {
      height: calc(100% - 70px);
      overflow: auto;
    }
  }
}

.other-setting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
