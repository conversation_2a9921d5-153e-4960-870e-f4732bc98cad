{"name": "@omega/admin", "version": "1.20.7", "description": "omega admin", "main": "index.js", "module": "./index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "wwen", "license": "ISC", "dependencies": {"vuedraggable": "^2.24.3", "crypto-js": "^4.1.1"}, "peerDependencies": {"cet-graph": "*", "@omega/dashboard": "*", "@omega/app": ">=1.8.0", "@omega/auth": "*", "@omega/widget": "*", "@omega/http": "*", "@omega/icon": "*", "@omega/layout": "*", "@omega/i18n": ">=1.2.0", "@omega/unity3d": ">=1.1.1"}}