<template>
  <div class="page">
    <el-empty
      v-if="isEmpty"
      class="is-empty"
      :image-size="200"
      :description="i18n('找不到仪表盘，请重新配置该自定义页面')"
    ></el-empty>
    <dashboardRender v-else :dashboard="dashboard" :mode="mode" />
  </div>
</template>
<script>
import { dashboardRender } from "@omega/dashboard";
import { i18n } from "../local/index.js";
import dashboradApi from "../api/dashboard.js";

export default {
  name: "dashboardPage",
  components: {
    dashboardRender
  },
  data() {
    return {
      isEmpty: false,
      mode: 0,
      dashboard: {
        name: "",
        description: ""
      }
    };
  },
  watch: {
    $route() {
      this.load();
    }
  },
  mounted() {
    this.load();
  },
  methods: {
    async load() {
      const id = +this.$route.query.id;
      const mode = +this.$route.query.mode;
      const dashboard = await dashboradApi.getdDashboardById(id);
      if (dashboard) {
        this.mode = mode;
        this.dashboard = dashboard;
      } else {
        this.isEmpty = true;
      }
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
