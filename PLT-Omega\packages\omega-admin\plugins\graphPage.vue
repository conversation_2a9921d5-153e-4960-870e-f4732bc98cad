<template>
  <div class="page">
    <el-empty
      v-if="isEmpty"
      class="is-empty"
      :image-size="200"
      :description="i18n('找不到组态画面，请重新配置该自定义页面')"
    ></el-empty>
    <CetGraph v-else v-bind="CetGraphData"></CetGraph>
  </div>
</template>

<script>
import CetGraph from "cet-graph";
import omegaAuth from "@omega/auth";
import graphApi from "../api/graph.js";
import { i18n } from "../local/index.js";
export default {
  name: "PageGraph",
  components: { CetGraph },
  data() {
    return {
      isEmpty: false,
      CetGraphData: {
        path_in: "",
        refresh_trigger_in: 0,
        userName_in: omegaAuth.user.getUserName()
      }
    };
  },
  watch: {
    $route() {
      this.load();
    }
  },
  mounted() {
    this.load();
  },
  methods: {
    async load() {
      const id = this.$route.query.nodeType + "_" + this.$route.query.nodeId;
      const node = await graphApi.getGraphNodeById(id);
      if (node) {
        this.CetGraphData.path_in = node.nodeName;
        this.CetGraphData.refresh_trigger_in = Math.random();
      } else {
        this.isEmpty = true;
      }
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
