<template>
  <div class="page">
    <el-empty
      v-if="isEmpty"
      class="is-empty"
      :image-size="200"
      :description="i18n('找不到页面，请重新配置该自定义页面')"
    ></el-empty>
    <iframe
      v-else
      :key="src"
      class="iframe-box"
      :src="src"
      allowfullscreen="true"
    ></iframe>
  </div>
</template>
<script>
import { i18n } from "../local/index.js";
import { getToken, getTheme } from "../util.js";
import { user } from "@omega/auth";
export default {
  name: "iframePage",

  data() {
    return {
      isEmpty: false,
      src: ""
    };
  },
  watch: {
    $route() {
      this.load();
    }
  },
  mounted() {
    this.load();
  },
  methods: {
    load() {
      const url = this.$route.query.url;
      const token = getToken();
      const theme = getTheme();
      const username = user.getUserName();
      const userId = user.getUserId();
      const localhost = window.location.hostname
      
      const src = url
        .replace("${token}", token)
        .replace("${username}", username)
        .replace("${theme}", theme)
        .replace("${userId}", userId)
        .replace("${localhost}", localhost);
      if (url) {
        this.src = src;
      } else {
        this.isEmpty = true;
      }
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.iframe-box {
  width: 100%;
  height: 100%;
}
</style>
