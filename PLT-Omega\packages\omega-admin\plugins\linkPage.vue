<template>
  <div class="page">
    <el-empty
      v-if="isEmpty"
      class="is-empty"
      :image-size="200"
      :description="i18n('无法打开新页面，请重新配置该自定义页面')"
    ></el-empty>
    <div v-else class="link-box">
      <el-link class="link" type="primary" @click="openLink" :underline="false">
        {{ i18n("点击打开新页面") }}
      </el-link>
    </div>
  </div>
</template>
<script>
import { i18n } from "../local/index.js";
import omegaI18n from "@omega/i18n";
import { getToken, getTheme } from "../util.js";
import { user } from "@omega/auth";
export default {
  name: "linkPage",

  data() {
    return {
      isEmpty: false,
      src: ""
    };
  },
  watch: {
    $route() {
      this.load();
    }
  },
  mounted() {
    this.load();
    this.openLink();
  },
  methods: {
    openLink() {
      window.open(this.src, this.src);
    },
    load() {
      const url = "http://*************:3000/workbench/apps?token=${token}&theme=${theme}&locale=${locale}";
      const token = getToken();
      const theme = getTheme();
      const username = user.getUserName();
      const userId = user.getUserId();
      const localhost = window.location.hostname;
      // 框架语言，用于跳转的时动态传参
      const locale = omegaI18n.locale;

      const src = url
        .replace("${token}", token)
        .replace("${username}", username)
        .replace("${theme}", theme)
        .replace("${userId}", userId)
        .replace("${localhost}", localhost)
        .replace("${locale}", locale);
      if (url && token) {
        this.src = src;
      } else {
        this.isEmpty = true;
      }
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}
.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.link-box {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .link {
    transform: translateY(-80px);
  }
}
</style>
