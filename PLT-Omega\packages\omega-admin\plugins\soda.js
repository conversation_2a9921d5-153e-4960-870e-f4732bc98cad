import { LayoutMain } from "@omega/layout";
import { ROUTE_PATHS } from "../CONST.js";

export class SodaPagePlugin {
  constructor(
    { router },
    { layoutPath = "/", layoutComponent = LayoutMain } = {}
  ) {
    router.addRoute({
      path: layoutPath,
      component: layoutComponent,
      children: [
        {
          path: ROUTE_PATHS.SODA,
          component: () => import("./sodaPage.vue")
        }
      ]
    });
  }
}
