<template>
  <div class="page">
    <el-empty v-if="!loading && !src" class="is-empty" :image-size="200"
      :description="i18n('找不到苏打页面，请重新配置该自定义页面')"></el-empty>
    <iframe ref="sodaIframe" title="soda" v-else class="iframe-box soda-iframe" :class="iframeClass" :src="src"
      allowfullscreen="true"></iframe>
  </div>
</template>
<script>
import { i18n } from "../local/index.js";
import omegaI18n from "@omega/i18n";
import sodaApi from "../api/soda.js";
import { getToken, openFullScreen, exitFullScreen } from "../util.js";
export default {
  name: "sodaPage",

  data() {
    return {
      loading: true,
      src: "",
      sodaWeb: "",
      autoFullScreen: "0",
      isFullscreen: false
    };
  },
  watch: {
    $route() {
      this.load();
    }
  },
  computed: {
    iframeClass() {
      if (this.isFullscreen || this.autoFullScreen === "1") {
        return "iframe-fullscreen";
      }
      return "";
    }
  },
  mounted() {
    this.load();
    window.addEventListener("message", this.handleSodaMessage);
    document.addEventListener("fullscreenchange", this.fullscreenchanged);
  },
  beforeDestroy() {
    window.removeEventListener("message", this.handleSodaMessage);
    document.removeEventListener("fullscreenchange", this.fullscreenchanged);
  },
  methods: {
    getIFrameSrc() {
      const type = this.$route.query.type;
      const appId = this.$route.query.appId;
      const entryId = this.$route.query.entryId;
      const theme = this.getTheme();
      const token = getToken();
      // 框架语言，用于跳转的时动态传参
      const locale = omegaI18n.locale;
      const src = `${this.sodaWeb}/release/${type}/${appId}/${entryId}?theme=${theme}&token=${token}&locale=${locale}`;
      if (this.sodaWeb && appId && entryId && type && token) return src;
      return "";
    },
    isValidPort(port) {
      // 正则表达式匹配0到65535之间的整数
      const portRegex = /^([0-9]|[1-9][0-9]{1,3}|[1-5][0-9]{4}|6[0-4][0-9]{3}|65[0-4][0-9]{2}|655[0-2][0-9]|6553[0-5])$/;
      return portRegex.test(port);
    },
    async load() {
      this.loading = true;
      try {
        // 调用接口时会有全屏loading，所以不在template中显示loading
        const sodaWebRes = await sodaApi.getSodaWeb();
        if (this.isValidPort(sodaWebRes.data)) {
          this.sodaWeb = window.location.protocol + "//" + window.location.hostname + ":" + sodaWebRes.data;
        } else {
          this.sodaWeb = sodaWebRes.data;
        }
        this.autoFullScreen = this.$route.query.autoFullScreen;
        this.src = this.getIFrameSrc();
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false;
      }
    },

    // 获取主题参数  只有 light 和 dark两种
    getTheme() {
      const omega_theme = localStorage.getItem("omega_theme");
      return omega_theme.toLowerCase().includes("light") ? "light" : "dark";
    },
    handleSodaMessage(event) {
      if (event.origin === this.sodaWeb) {
        const action = event.data;
        switch (action.type) {
          case "link":
            this.handleActionLink(action);
            break;
          case "fullScreen":
            this.handleActionFullScreen(action);
            break;
        }
      }
    },
    handleActionLink(action) {
      if (action.blank) window.open(action.url, action.url);
      else window.location.href = action.url;
    },
    handleActionFullScreen(action) {
      const isFullscreen = document.fullscreenElement === document.body;

      if (isFullscreen) {
        exitFullScreen();
      } else {
        openFullScreen(document.body);
      }
    },
    fullscreenchanged(event) {
      // 如果有元素处于全屏模式，则 document.fullscreenElement 将指向该元素。如果没有元素处于全屏模式，则该属性的值为 null。
      const isFullscreen = document.fullscreenElement === document.body;
      this.isFullscreen = isFullscreen;
      this.$refs.sodaIframe.contentWindow.postMessage(
        { isFullscreen, type: "fullScreen" },
        this.sodaWeb
      );
    },
    i18n
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}

.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.iframe-box {
  width: 100%;
  height: 100%;
}

.iframe-fullscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}
</style>
