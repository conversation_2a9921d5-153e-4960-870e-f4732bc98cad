import { LayoutMain } from "@omega/layout";
import { ROUTE_PATHS } from "../CONST.js";

export class Unity3DPagePlugin {
  constructor(
    { router },
    { 
      layoutPath = "/",
      layoutComponent = LayoutMain,
      onMessage = () => {}  
    } = {}
  ) {
    router.addRoute({
      path: layoutPath,
      component: layoutComponent,
      children: [
        {
          path: ROUTE_PATHS.UNITY_3D,
          component: () => import("./unity3d.vue")
        }
      ]
    });

    Unity3DPagePlugin.onMessage = onMessage;
  }
}
