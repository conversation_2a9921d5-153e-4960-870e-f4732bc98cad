<template>
  <div class="page">
    <el-empty v-if="isEmpty" class="is-empty" :image-size="200" :description="i18n('找不到组态画面，请重新配置该自定义页面')"></el-empty>
    <unity3d v-else :url="url" @onMessage="onMessage" />
  </div>
</template>
<script>
import { i18n } from "../local/index.js";
import unity3d from "@omega/unity3d";
import { Unity3DPagePlugin } from '@omega/admin/plugins/unity3d.js'
export default {
  name: "Unity3DPage",
  components: { unity3d },
  data() {
    return {
      isEmpty: false
    };
  },
  beforeMount() {
    const url = this.$route.query.url;
    const localhost = window.location.hostname

    const src = url.replace("${localhost}", localhost);
    if (url) {
      this.url = src
    } else {
      this.isEmpty = true
    }
  },
  methods: {
    /**
    * @description: 接受unity3d消息
    */
    onMessage(data, url) {
      Unity3DPagePlugin.onMessage(data.data, url);
    },
    i18n
  },
};


</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
}

.is-empty {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
</style>
