import { HttpBase } from "@omega/http";
import { OmegaAdminPlugin } from "./index.js";
import { MessageBox,Message } from "element-ui";
import settingApi from "./api/setting.js";

export const httping = new HttpBase(
  {},
  {
    responseType: "json"
  }
);

httping.interceptors.request.use(requestInterceptor);

function requestInterceptor(config) {
  OmegaAdminPlugin.register();
  const apiOptions = OmegaAdminPlugin.apiOptions;
  const serviceKeys = Object.keys(apiOptions.prefix);
  if (serviceKeys.length) {
    const regx = new RegExp(`{{(${serviceKeys.join("|")})}}`);
    const url = config.url;
    config.url = url.replace(regx, (match, serviceKey) => {
      return apiOptions.prefix[serviceKey];
    });
  }

  if (apiOptions.requestInterceptor) {
    return apiOptions.requestInterceptor(config);
  }
  return config;
}

function isInWhiteList(url) {
  const writeURLList = [
    "{{bff-service}}/v1/public/",
    "{{bff-service}}/v1/system/sign"
  ];

  return writeURLList.some(item => {
    return url.startsWith(item);
  });
}

export const fetch = new HttpBase({
  auth: false,
  silent: true,
  json: false
});

fetch.interceptors.request.use(requestInterceptor);

fetch.interceptors.request.use(function (config) {
  if (isInWhiteList(config.url)) {
    return config;
  }
  const token = window.sessionStorage.getItem("omega_admin_token");
  if (token) {
    config.headers["Authorization"] = `Bearer ${token}`;
  } else {
    return waitAuth().then(() => {
      config.headers["Authorization"] = `Bearer ${window.sessionStorage.getItem(
        "omega_admin_token"
      )}`;
      return config;
    });
  }
  return config;
});

function isContentTypeJson(res) {
  const contentType = res.headers["content-type"];
  return contentType && contentType.includes("application/json");
}
fetch.interceptors.response.use(function (response) {
  if (isContentTypeJson(response)) {
    const data = response.data;
    if (data?.code === 401) {
      return waitAuth().then(() => {
        return fetch(response.config);
      });
    } else if (data?.code !== 0) {
      if (data?.msg) {
        Message.error(data.msg);
      }

      return Promise.reject(response);
    }
    return response.data;
  } else {
    return response;
  }
});

const waitAuth = (function () {
  async function authPrompt() {
    const { value } = await MessageBox.prompt("请输入Admin密码", "认证", {
      confirmButtonText: "确定",
      showCancelButton: false,
      showClose: false,
      closeOnPressEscape: false,
      closeOnClickModal: false,
      inputType: "password",
      inputValidator(value) {
        if (!value) {
          return "密码不能为空";
        }
      }
    });
    try {
      const data = await settingApi.sign(value);
      window.sessionStorage.setItem("omega_admin_token", data.token);
    } catch (e) {
      console.warn(e);
      return authPrompt();
    }
  }

  let authPromise = null;
  return function _waitAuth() {
    if (authPromise) {
      return authPromise;
    }

    authPromise = authPrompt().then(() => {
      authPromise = null;
    });

    return authPromise;
  };
})();
