export function getTreePathNodes(
  nodes,
  value,
  { diffFn, childKey, valueKey } = {}
) {
  function isSame(node) {
    return diffFn ? diffFn(node[valueKey], value) : node[valueKey] === value;
  }

  function getChild(node) {
    return node[childKey];
  }

  function loop(pathList, childNodes) {
    if (childNodes) {
      for (const node of childNodes) {
        if (isSame(node)) {
          return pathList.concat(node);
        }
        const ret = loop(pathList.concat(node), getChild(node));
        if (ret) {
          return ret;
        }
      }
    }
  }

  return loop([], nodes) || [];
}

/**
 * @param {Array} items
 * @param {Any} value 查找的value
 * @prop {Function} diffFn 对比判定函数
 * @prop {String} childKey
 * @prop {String} key
 */
export function findNodeById(
  items,
  value,
  { diffFn, childKey = "children", valueKey = "id" }
) {
  function isEqual(item) {
    return diffFn
      ? diffFn(item[valueKey], value, item)
      : item[valueKey] === value;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item[childKey]) {
        const ret = loop(item[childKey]);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(items) || null;
}

const TOKEN_KEY = "omega_token";
const THEME_KEY = "omega_theme";

export function getToken() {
  let _token =
    window.sessionStorage.getItem(TOKEN_KEY) ??
    window.localStorage.getItem(TOKEN_KEY);

  return _token;
}
export function getTheme() {
  return window.localStorage.getItem(THEME_KEY);
}

export function openFullScreen(ele) {
  if (ele.requestFullscreen) {
    ele.requestFullscreen();
  } else if (ele.mozRequestFullScreen) {
    ele.mozRequestFullScreen();
  } else if (ele.webkitRequestFullscreen) {
    ele.webkitRequestFullscreen();
  } else if (ele.msRequestFullscreen) {
    ele.msRequestFullscreen();
  }
}

export function exitFullScreen() {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } // 兼容 Webkit
  else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  } // 兼容 Moz
  else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } // 兼容 IE
  else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  }
}

export default {
  getTreePathNodes,
  findNodeById,
  getToken,
  openFullScreen,
  exitFullScreen
};
