# @omega/app

## 1.10.1

### Minor Changes

- [注意] chore: jquery依赖版本修改为\*

## 1.10.0

### Minor Changes

- [注意] feat: jquery依赖版本升级, ^1.12.4 -> ^3.7.1

## 1.9.0

### Minor Changes

- feat: 新增登录页的主视觉大背景图片设置

## 1.8.0

- feat: 新增是否使用自定义的菜单配置标志位

## 1.7.0

- feat: 新增原始菜单数据访问及存储功能

## 1.6.0

- feat: 新增重建应用方法：recreateApp

## 1.5.1

- feat: createApp 增加自定义选项

## 1.5.0

- feat: 新增应用整体刷新方法: refreshApp

## 1.4.0

- fix: 修复 afterAppLogin\completeAppBoot 钩子 await 未将应用进入等待状态的问题

## 1.3.5

- feat: 新增在开发环境时打印 store 信息

## 1.3.4

- feat: esmodule 入口添加

## 1.3.3

- chore: nprogress 进度配置优化

## 1.3.2

- [2022-04-26] fix: 登录页面 logo 适配

## 1.3.1

- [2022-04-25] fix: 登录之后退出再登录无响应

## 1.3.0

- [2022-04-24] feat: 插件机制钩子升级

## 1.2.0

- [2022-04-22] feat: 插件新增 completeAppStartUp 钩子

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

## 1.0.2

- [2022-04-02]

  feat: 国际化功能适配

## 1.0.1

- [2022-03-31] feat: 添加跟组件 OmegaApp 名称标识， 方便在 vue-tools 甄别
