import Vue from "vue";
import VueRouter from "vue-router";
import Vuex from "vuex";
import _ from "lodash";

import plugin from "./pluginManger.js";
import { nprogressPlugin } from "./plugins/nprogress";

import language from "./core/i18n";
import theme from "./core/theme";

import confSchema from "./confSchema";
import { waitComplete } from "./wait.js";

let router;
let store;
let app;
let conf;

plugin.register(nprogressPlugin);

async function createApp({
  el,
  config,
  routerOption = {},
  storeOption = {},
  ...vueOptions
} = {}) {
  conf = new Vue({
    name: "OmegaConfig",
    data() {
      return {
        _rootRouteViewId: _.uniqueId(),
        state: _.defaultsDeep({}, config, confSchema)
      };
    },
    methods: {
      getNavmenu() {
        return this.state.navmenu;
      },
      setNavmenu(navmenu) {
        this.state.navmenu = navmenu;
      },
      getResource() {
        return this.state.resource;
      },
      setResource(resource) {
        _.assign(this.state.resource, resource);
      },
      getOriginNavmenu() {
        return this.state.originNavmenu;
      },
      setOriginNavmenu(originNavmenu) {
        this.state.originNavmenu = originNavmenu;
      },
      useCustomNavmenu(bool) {
        this.state.isUseCustomNavmenu = bool;
      },
      isUseCustomNavmenu() {
        return this.state.isUseCustomNavmenu;
      }
    }
  });
  if (process.env.NODE_ENV === "development") {
    console.info(
      "\x1b[44m%s\x1b[0m",
      "info: @omega/app 应用 store:",
      conf.state
    );
  }
  Vue.use(VueRouter);
  router = new VueRouter(routerOption);

  Vue.use(Vuex);
  store = new Vuex.Store(storeOption);

  plugin.extend({
    $boot() {
      router.beforeEach((to, from, next) => {
        if (to.path === "/login") {
          next();
        } else {
          // 等待应用启动完成后在进行跳转
          waitComplete.wait().then(() => {
            next();
          });
        }
      });

      app = new Vue({
        el: el,
        name: "OmegaApp",
        router,
        store,
        ...vueOptions,
        render(h) {
          // HACK: 跟下面注释同一个意思
          // return <router-view />;
          return h("router-view", {
            key: conf._rootRouteViewId
          });
        }
      });
    },
    $completeAppBoot() {
      waitComplete.exec();
    }
  });

  _exports.refreshApp = () => {
    conf._rootRouteViewId = _.uniqueId();
    app && app.$forceUpdate();
  };

  _exports.recreateApp = () => {
    if (app && app._isDestroyed) {
      app = new Vue({
        el: el,
        name: "OmegaApp",
        router,
        store,
        ...vueOptions,
        render(h) {
          // HACK: 跟下面注释同一个意思
          // return <router-view />;
          return h("router-view", {
            key: conf._rootRouteViewId
          });
        }
      });
    }
    return app;
  }

  await plugin.start({
    conf,
    router,
    store
  });

  return app;
}


const _exports = {
  createApp,
  refreshApp: () => { },
  recreateApp: () => { },
  plugin
}

export { router, store, app, conf, theme, language };

export default _exports;
