{"name": "@omega/app", "version": "1.10.1", "description": "omega plugin register", "main": "index.js", "module": "./index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "ssh://cetsoft-svr1:22/Platforms/PLT-Matterhorn/_git/omega-app"}, "keywords": ["omega-app"], "peerDependencies": {"element-ui": ">=2.11.1", "jquery": "*", "lodash": "4.x", "moment": "2.x", "vue": "2.x", "vue-router": ">=3.4.3 < 4", "vuex": ">=3.5.1 < 4", "nprogress": "^0.2.0"}, "author": "wwen", "license": "ISC"}