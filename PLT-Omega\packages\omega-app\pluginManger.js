class PluginManger {
  plugins = [];

  // 是否需要 auth
  _hasAuth = false;

  isBootFinish = false;

  async start({ router, store, conf }) {
    this.omega = {
      router,
      store,
      conf,
      plugin: this
    };
    this.plugins = this.plugins.map(([pluginClass, option]) => {
      const plugin = new pluginClass(this.omega, option);
      return plugin;
    });

    return this.boot();
  }

  async callHook(hook, ...argv) {
    const run = async plugins => {
      for (let plugin of plugins) {
        const hookFn = plugin[hook];

        if (hookFn) {
          await hookFn.call(plugin, ...argv);
        }
      }
    };

    await run(this.plugins);
  }

  register(pluginClass, option) {
    this.plugins.push([pluginClass, option]);

    if (pluginClass.register) {
      pluginClass.register(option, this);
    }
  }

  async boot() {
    await this.callHook("beforeAppBoot");

    await this.$boot();

    if (this._hasAuth) {
      let ret;
      ret = await this.$beforeAppLogin();

      await this.callHook("beforeAppLogin", ret);

      ret = await this.$afterAppLogin();

      await this.callHook("afterAppLogin", ret);
    }

    await this.callHook("completeAppBoot");

    await this.$completeAppBoot();

    this.isBootFinish = true;
  }

  extend(obj) {
    const methods = [
      "$boot",
      "$completeAppBoot",
      "$beforeAppLogin",
      "$afterAppLogin"
    ];
    Object.keys(obj).forEach(key => {
      if (!methods.includes(key)) {
        throw new Error(`${key} 插件不支持的周期处理函数`);
      }
    });

    Object.assign(this, obj);
  }

  $boot() {}
  $beforeAppLogin() {}
  $afterAppLogin() {}
  $completeAppBoot() {}
}

export default new PluginManger();
