import nprogress from "nprogress";
import "nprogress/nprogress.css";

nprogress.configure({
  showSpinner: false,
  trickleRate: 0.08,
  trickleSpeed: 600
});

export class nprogressPlugin {
  constructor({ router }) {
    this.router = router;
    this.router.beforeEach((to, from, next) => {
      nprogress.start();
      next();
    });
    this.router.afterEach((to, from) => {
      nprogress.done();
    });
  }
}
