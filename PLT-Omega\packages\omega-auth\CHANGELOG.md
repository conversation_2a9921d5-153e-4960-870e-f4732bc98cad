# @omega/auth

## 1.16.1

- feat: ROOT用户登录后没有菜单的弹窗表现修改，关闭弹窗刷新界面

## 1.16.0

- feat: 权限初始化新增 whiteRouteExpression 属性，传入匹配路径的方法可达到和设置 whiteRouteList 相同效果。

```js
//示例
//path为路径参数
whiteRouteExpression(path) {
    const pathCheck = path.includes("/order");
    const isInframe =
      self.frameElement && self.frameElement.tagName === "IFRAME";
    return pathCheck && isInframe;
}
//上述表示路径中包含`order`且为iframe时，路径为白名单
//不设置该属性时默认为
whiteRouteExpression(path) {
  return false;
}
```

## 1.15.1

- fix: User 类方法报错修复

## 1.15.0

- feat: 页面菜单配置为空时提示及交互优化

## 1.14.5

- fix: 退出登录后，重新登录没有进入首页而是进入之前的界面的问题

## 1.14.4

- fix: 兼容适配融合框架插件菜单的首页跳转

## 1.14.2

- feat: 增加角色首页新字段的兼容

## 1.14.1

- fix: 防止特殊情况下路由跳转陷入死循环

## 1.14.0

- feat: 优化首页逻辑：当跳转到的路由不存在或者无权限时默认跳转至当前用户拥有菜单的第一个

## 1.13.2

- fix: 防止请求重放的随机数要随机

## 1.13.1

- fix: 修复重放参数导致的系统重复刷新问题

## 1.13.0

- feat: 重放攻击支持

## 1.12.6

- fix: 导航菜单为空时，添加登录提示信息，修复这种情况下切换账号号时需要刷新界面才能切换的问题

## 1.12.5

- feat: 暴露获取上一次路由记录的方法：getPreRoutePath

## 1.12.4

- fix: 修复在历史模式下手动修改浏览器 url 无法访问到该 url 的问题

## 1.12.3

- fix: URL 中文支持

## 1.12.2

- fix: 修复刷新界面后在控制台上报路由重复的报错信息

## 1.12.1

- fix: 删除 loading 路由跳转

## 1.12.0

- 409a5de: feat: 新增 apiProxy 插件配置，支持在外部劫持 api 方法

## 1.11.0

- feat: token 过期后重新登录跳转至之前的路由界面

- fix: 修复在 URL 带 token 访问应用没有跳转到对应页面问题

## 1.10.1

- feat: 新增暴露插件配置项 checkUserIsROOT 来自定义判定 root 用户

## 1.10.0

- remove: 取消图形节点&dashboard 权限检查

## 1.9.0

- feat: OmegaAuthPlugin 新增自定义权限检查函数配置：checkPermission，配置后将替代默认权限检查行为

## 1.8.2

- chore: 菜单权限过滤转为响应式，菜单会在菜单配置变动后根据权限动态过滤

## 1.8.1

- chore: 路由访问报错信息正常情况下屏蔽

## 1.8.0

- feat: 框架 iframe 嵌入支持

## 1.7.11

- feat: 权限不检查 dashboard 页面

## 1.7.10

- feat: esmodule 入口添加
- Updated dependencies
  - @omega/http@1.2.3

## 1.7.9

- ADD: 新增 loginByApi(fn) 方法，支持在外部处理登录接口
- ADD: 兼容原来在用户信息 customConfig 中首页 homepage 字段

## 1.7.8

- fix: 图形验证码添加 pageId 参数

## 1.7.7

- fix: 调整登录 api，图形验证码登录场景下需要传递 pageId 字段

## 1.7.6

- fix: 调整依赖版本为外部任意版本

## 1.7.5

- fix: 图形节点权限取用用户的 graphNodes 字段

## 1.7.4

- fix: 兼容修复 omega-admin 参数形式导航菜单错乱

## 1.7.3

- fix: 修复访问已有模块 url 时会直接跳转到首页的行为

## 1.7.2

- fix: 删除 debugger

## 1.7.1

- feat: 兼容 root 用户配置了首页

## 1.7.0

- feat: 支持安全登录接口
- refactor: 新增 dep: crypto-js 来替换 lib/md.js

## 1.6.0

- feat: 新增默认首页功能配置选项`defaultHomepage`

## 1.5.6

- fix: 菜单列表为空时,页面错误信息提示

## 1.5.5

- fix: 菜单项为空时添加错误提示信息

## 1.5.4

- fix: 修复设置默认路由情况下跳转失效问题

## 1.5.3

- fix: 获取用户信息接口兼容性处理

## 1.5.2

- fix: 报错问题修复

## 1.5.1

- 优化: 退出登录页面闪烁

## 1.5.0

- feat: 开放接口前缀配置和请求拦截器配置

## 1.4.7

- feat: 新增 isUseSuperAdminRole 配置来向前兼容项目存在超级管理员角色的情况

## 1.4.6

- fix: 代码错误报错

## 1.4.5

- fix: 权限检查方式向前兼容性修改

## 1.4.4

- fix: 网关白名单取消 checktoken 接口

## 1.4.3

- fix: 页面卡在登录界面问题修复

## 1.4.2

- fix: 带查询参数界面刷新被跳转到首页的问题更正

## 1.4.1

- fix: 图性节点权限 ID undefind 问题更正
- fix: 权限过滤导致部分菜单丢失问题

## 1.4.0

- 2461ab5: [2022-05-07] feat:权限检查支持图性节点权限

- 2c99018: fix: 修复刷新界面 url 查询参数消失问题

## 1.3.5

- [2022-05-06] fix: 首次登录后不跳转

## 1.3.4

- [2022-05-06] feat: 完善插件钩子触发时机

## 1.3.3

- [2022-04-28] fix: 非 root 用户登录后不跳转

## 1.3.2

- [2022-04-25] fix: 登录之后退出再登录无响应

## 1.3.1

- 5f62964: [2022-04-25] fix: 修复模拟用户登录时卡在登录界面问题

## 1.3.0

- [2022-04-24] feat: 适配升级插件, 路由钩子不触发问题修正，permission 无效问题修正

## 1.2.1

- [2022-04-24] fix: 修复 permission 报错导致的 loading

## 1.2.0

- [2022-04-22] feat: 取消 /first-screen

## 1.1.1

- [2022-04-19] feat: 登录接口支持验证码功能

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

- Updated dependencies
  - @omega/http@1.1.0
