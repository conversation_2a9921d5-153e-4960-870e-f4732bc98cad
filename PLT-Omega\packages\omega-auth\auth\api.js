// eslint-disable-next-line camelcase
import md5 from "crypto-js/md5";
import aes from "crypto-js/aes";
import encHex from "crypto-js/enc-hex";

import { httping, httpingSlient } from "./request.js";
import { ApiProxy } from "./apiProxy.js";

const fetchingData = config => {
  return httping(config).then(res => res?.data);
};
const encrypto = (value) => {
  const key = encHex.parse("3839323435333432393041424344454631323634313437383930414341423536");
  const iv = encHex.parse("2934577290ABCDEF1264147890ACAE75");

  return aes.encrypt(value, key, { iv }).toString();
}
export default new ApiProxy({
  /**
   * 获取所有的后端操作权限列表
   */
  getPermission() {
    return fetchingData({
      url: `{{auth-service}}/v1/permissions`,
      method: "GET"
    });
  },

  /**
   * @param {String} token
   */
  async checkToken({ token }) {
    let data = {};
    try {
      data = await httpingSlient({
        url: `{{auth-service}}/v1/checktoken`,
        method: "post",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Content-Type": "application/json;charset=UTF-8"
        },
        data: token
      });
      if (data.code) {
        console.warn("token 无效，返回：", data);
        return false;
      }
    }
    catch (e) {
      console.warn("token 无效，返回：", e);
      return false;
    }

    return { userId: data.data };
  },

  /**
   * 获取当前登录用户的信息
   * 
   */
  getCurrentUser(userId) {
    // HACK 正常 User-ID 是由网关解析添加
    // 此处仅仅是为了向前兼容
    return fetchingData(Object.assign({
      url: `{{auth-service}}/v1/user`,
      method: "get",
      headers: {
        "User-ID": userId
      }
    }));
  },

  /**
   * @param {String} userName 用户名
   * @param {String} password 密码
   * 
   * @param {String} captchaCode 验证码
   * @param {String} pageId 前端界面生成的唯一ID (和captchaCode一起使用)
   */
  login({ userName, password, captchaCode, pageId }, { type }) {
    const nowTime = new Date().getTime();
    const appName = "CET_Matterhorn";
    const passwordDigest = md5(`${appName}#${password}#${nowTime}`).toString();
    let captcha = {};
    if (captchaCode) {
      captcha = {
        captchaCode,
        pageId
      };
    }

    if (type === "security") {
      userName = encrypto(userName);
      password = encrypto(password);

      return fetchingData({
        url: `{{auth-service}}/v1/loginSecurity`,
        method: "post",
        data: {
          userName: userName,
          password: password,
          md5: passwordDigest,
          timestamps: nowTime,
          client: "web",
          ...captcha
        }
      });
    }

    return fetchingData({
      url: `{{auth-service}}/v1/login`,
      method: "post",
      data: {
        userName: userName,
        password: passwordDigest,
        timestamps: nowTime,
        client: "web",
        ...captcha
      }
    });
  },

  logout() {
    return fetchingData({
      url: `{{auth-service}}/v1/logout`,
      method: "post"
    });
  }
});
