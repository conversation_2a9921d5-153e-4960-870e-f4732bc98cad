import Vue from "vue";
import util from "./util";
import api from "./api";
import permission from "../permission";
import utilUser from "./user";
import { TokenStore } from "../tokenStore";
import { waitLogin } from "../waitLogin.js";
import { OmegaAuthPlugin } from "../index.js";
import { clearReloadPathRecord } from "../plugins/homePage.js";
/**
 * @param {String} token 必须 - token
 * @param {Object} user 非必须 - 用户信息
 */
async function loginCallback({ token, userId, user } = {}) {
  TokenStore.set(token);

  if (!user) {
    user = await api.getCurrentUser(userId);
  }
  utilUser.init(user);

  // 增加重放攻击检验header
  if (OmegaAuthPlugin.enableReplayProtection) {
    OmegaAuthPlugin.addAntiReplayProtection(utilUser.getUserId());
  }

  // 鉴权函数注册
  const operatePermissionMap = await api.getPermission();

  let rulesCollection;
  Vue.use(permission, {
    checkPermission(rule) {
      if (utilUser.isRoot()) {
        return true;
      }

      // 账户所有的权限都在角色中
      const role = utilUser.getRole();

      if (!role) {
        throw new Error("账号未配置角色：除ROOT用户外其他用户必须配置角色！")
      }

      // 因 dashborad 权限未标准化,暂时不做权限限制
      // if (rule.startsWith('omega_admin_dash')) {
      //   return true;
      // }

      if (!rulesCollection) {
        // 操作权限集合
        const authIds = utilUser.getRoleAuths();
        const authsCollection = authIds.reduce((ret, authId) => {
          const opratePermission = operatePermissionMap.find(
            item => item.id === authId
          );
          // HACK: 向前兼容, 屏蔽查询不到的opratePermission
          opratePermission && ret.push(opratePermission.name);
          return ret;
        }, []);

        // 页面权限集合
        const pageNodes = utilUser.getRolePageNodes();
        const pageCollection = pageNodes.map((item) => item.id);
        // 图形节点集合
        // const graphNodes = utilUser.getUserGraphNodes();
        // const graphCollection = graphNodes.map((item) => item.nodeType + "_" + item.nodeID);

        rulesCollection = [].concat(authsCollection, pageCollection /*, graphCollection */);

        const hasRepeat = new Set(rulesCollection).size !== rulesCollection.length;
        if (hasRepeat) {
          const repeatRules = [];
          const rulesQueue = rulesCollection.slice();
          let rule;
          while ((rule = rulesQueue.pop())) {
            if (rulesQueue.includes(rule)) {
              repeatRules.push(rule);
            }
          }
          const repeatRulesSet = [...new Set(repeatRules)];
          throw new Error(`权限ID, 不允许重复，重复了的rule->${repeatRulesSet}`)
        }
      }

      return rulesCollection.includes(rule);
    }
  });

  return { token, user };
}

async function waitLoginCallback(...argv) {
  return loginCallback(...argv).then(val => {
    waitLogin.exec();
    return val;
  });
}

export async function checkLogin() {
  const token = util.getSearchParam("token") || TokenStore.get();
  if (!token) {
    return false;
  }

  const ret = await api.checkToken({ token });

  // token 无效
  if (ret === false) {
    return false;
  }

  return waitLoginCallback({ token, userId: ret.userId });
}

export async function login({ userName, password, captchaCode = null, pageId }, { type } = {}) {
  const { token, user } = await api.login({ userName, password, captchaCode, pageId }, { type });
  return waitLoginCallback({ token, user });
}

export async function loginByApi(api) {
  const { token, user } = await api;
  return waitLoginCallback({ token, user });
}

export async function logout() {
  return api.logout().then(() => {
    TokenStore.clear();
    clearReloadPathRecord();
  });
}
