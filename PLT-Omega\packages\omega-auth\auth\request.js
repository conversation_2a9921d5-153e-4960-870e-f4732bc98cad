import { HttpBase } from "@omega/http";
import { OmegaAuthPlugin } from "../index.js";

export const httping = new HttpBase(
  {},
  {
    responseType: "json"
  }
);

export const httpingSlient = new HttpBase({ auth: false, silent: true });

httping.interceptors.request.use(requestInterceptor)
httpingSlient.interceptors.request.use(requestInterceptor)

function requestInterceptor(config) {
  const apiOptions = OmegaAuthPlugin.apiOptions;

  const serviceKeys = Object.keys(apiOptions.prefix);
  if (serviceKeys.length) {
    const regx = new RegExp(`{{(${serviceKeys.join("|")})}}`);
    const url = config.url;
    config.url = url.replace(regx, (match, serviceKey) => {
      return apiOptions.prefix[serviceKey];
    })
  }

  if (apiOptions.requestInterceptor) {
    return apiOptions.requestInterceptor(config);
  }
  return config;
}