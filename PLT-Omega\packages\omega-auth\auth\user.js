export class User {
  _isUseSuperAdminRole = false;
  // 扩展配置，用于判定用户是否为ROOT用户
  _checkUserIsROOT;
  _user = {}
  /** 初始化 */
  init(user) {
    this._user = user;
  }

  isRoot() {
    if (this._checkUserIsROOT) {
      return this._checkUserIsROOT(this);
    }
    const userId = this.getUserId();
    if (userId === 1) {
      return true;
    }

    if (this._isUseSuperAdminRole) {
      const role = this.getRole();
      // HACK: 向前兼容 —> 角色 id 为 1 且角色名称为 "超级管理员" 也被认为跟ROOT用户同级
      if (role.id === 1) {
        return true;
      }
    }
  }

  getUserId() {
    return this._user.id;
  }

  getUserName() {
    return this._user.name;
  }

  getUserGraphNodes() {
    return this._user.graphNodes ?? [];
  }

  getUserCustomConfig() {
    const customConfig = this._user.customConfig;
    if (this._usercustomConfig) {
      return this._usercustomConfig;
    }

    return (this._usercustomConfig =
      (customConfig && JSON.parse(customConfig)) || {});
  }

  getUserTenantId() {
    return this._user.tenantId;
  }

  getUserModelNodes() {
    return this._user.modelNodes;
  }

  getRelativeUserGroup() {
    return this._user.relativeUserGroup || [];
  }

  getRole() {
    return (this._user.roles && this._user.roles[0]) || {};
  }

  getRoleId() {
    const role = this.getRole();
    return role.id;
  }

  getRoleCustomConfig() {
    const customConfig = this.getRole().customConfig;
    return (customConfig && JSON.parse(customConfig)) || {};
  }
  getHomePage() {
    return this.getRole()?.homePage;
  }
  getRolePageNodes() {
    return this.getRole().pageNodes ?? [];
  }

  getRoleAuths() {
    return this.getRole().auths ?? [];
  }

  // getRoleGraphNodes() {
  //   return this.getRole().graphNodes ?? [];
  // }

  isSameUser(id) {
    return this.getUserId() === id;
  }
}

export default new User();
