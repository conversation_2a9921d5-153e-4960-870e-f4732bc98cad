/**
 * 获取路径的查询参数
 */
export function getSearchParam(key) {
  const param = {};
  const search = window.location.search;
  if (search.length > 1) {
    const couples = search.substr(1).split("&");

    couples.forEach(couple => {
      const [key, value] = couple.split("=");
      param[decodeURIComponent(key)] = value ? decodeURIComponent(value) : "";
    });
  }

  return key ? param[key] : param;
}

export function noop() { }

/**
 * @param {Array} items
 * @param {Any} value 查找的value
 * @prop {Function} diffFn 对比判定函数
 * @prop {String} childKey
 * @prop {String} key
 */
export function find(
  items,
  value,
  { diffFn, childKey = "child", valueKey = "id" }
) {
  function isEqual(item) {
    return diffFn
      ? diffFn(item[valueKey], value, item)
      : hasProp(item, valueKey) && item[valueKey] === value;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item[childKey]) {
        const ret = loop(item[childKey]);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(items) || null;
}

export function hasProp(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

export function randomStr() {
  return Math.random().toString(16).slice(2);
}


export default {
  getSearchParam,
  noop,
  find,
  randomStr
};
