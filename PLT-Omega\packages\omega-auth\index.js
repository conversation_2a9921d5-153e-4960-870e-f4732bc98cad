import { checkLogin, login, logout, loginByApi } from "./auth/index";
import permission, { Permission } from "./permission";
import { TokenStore } from "./tokenStore";
import user from "./auth/user";
import { waitLogin } from "./waitLogin.js";

import { HomePagePlugin, getPreRoutePath } from "./plugins/homePage";
import { PageAccessPlugin } from "./plugins/pageAccess";
import { ApiProxy } from "./auth/apiProxy.js";

import { HttpBase } from "@omega/http";
import md5 from "crypto-js/md5";

const checkPermission = permission.checkPermission.bind(permission);
const isRootUser = () => user.isRoot();

export {
  login,
  loginByApi,
  logout,
  checkPermission,
  isRootUser,
  user,
  getPreRoutePath
};

export default {
  login,
  loginByApi,
  logout,
  checkPermission,
  isRootUser,
  user,
  getPreRoutePath
};

export class OmegaAuthPlugin {
  static register(
    {
      isTokenPersist = false,
      openPermissionCheck = true,
      whiteRouteList = [],
      checkUserIsROOT, // function(user) {}
      openHomepage = true,
      defaultHomepage = "",
      openPagePermission = true,
      isUseSuperAdminRole = false,
      enableReplayProtection = false,
      checkPermission = null, // 自定义权限验证方法，配置后权限验证将不会使用默认的权限验证方法
      apiProxy = {},
      apiPrefix = {},
      apiRequestInterceptor,
      whiteRouteExpression = path => {
        return false;
      }
    } = {},
    plugin
  ) {
    ApiProxy.api = apiProxy;

    OmegaAuthPlugin.apiOptions = {
      prefix: Object.assign(
        {},
        {
          "auth-service": "/auth"
        },
        apiPrefix
      ),
      requestInterceptor: apiRequestInterceptor
    };
    OmegaAuthPlugin.enableReplayProtection = enableReplayProtection;

    TokenStore.isPersist = isTokenPersist;
    Permission.isOpen = openPermissionCheck;
    Permission.checkPermission = checkPermission;

    user._isUseSuperAdminRole = isUseSuperAdminRole;
    user._checkUserIsROOT = checkUserIsROOT;

    if (openPagePermission) {
      plugin.register(PageAccessPlugin, {
        whiteRouteExpression,
        whiteRouteList: [
          ...whiteRouteList,
          "/login",
          // HACK 为了兼容旧版本，如果没有配置默认首页，那么就不需要添加默认首页的路由
          ...(defaultHomepage ? [defaultHomepage] : [])
        ]
      });
    }

    if (openHomepage) {
      plugin.register(HomePagePlugin, {
        whiteRouteExpression,
        defaultHomepage,
        whiteRouteList
      });
    }
  }

  constructor({ conf, plugin, router }, option) {
    this.conf = conf;
    this.plugin = plugin;
    this.router = router;
    plugin._hasAuth = true;

    plugin.extend({
      async $afterAppLogin() {
        const isLogin = await checkLogin();

        if (!isLogin) {
          await router.push("/login").catch(err => {
            console.warn(err);
          });
        }

        await waitLogin.wait();

        return {
          user,
          isRoot: user.isRoot()
        };
      }
    });

    // HACK: 系统进入 login 界面强制刷新保证系统登录用户信息
    // 重新进行初始化
    router.beforeEach((to, from, next) => {
      next();

      if (to.path === "/login" && plugin.isBootFinish) {
        window.location.reload();
      }
    });
  }

  static addAntiReplayProtection(userid) {
    HttpBase.setHeadersOperate(function () {
      let randomSix = "";
      for (let i = 0; i < 6; i++) {
        randomSix += Math.floor(Math.random() * 10);
      }
      let timestamp = Date.now() + randomSix;
      let sign = md5(timestamp + userid + "cet:matterhorn");
      return {
        sign: sign,
        timestamp: timestamp
      };
    });
  }
}
