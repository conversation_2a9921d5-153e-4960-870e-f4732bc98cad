{"name": "@omega/auth", "version": "1.16.1", "description": "omega auth plugin", "main": "index.js", "module": "./index.js", "directories": {"lib": "lib"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://cetsoft-svr1/Platforms/PLT-Matterhorn/_git/omega-theme"}, "keywords": ["omega-plugin"], "peerDependencies": {"element-ui": ">=2.11.1", "@omega/http": "*", "lodash": "4.x", "vue-router": ">=3.4.3 < 4"}, "author": "wwen", "license": "ISC", "dependencies": {"crypto-js": "^4.1.1"}}