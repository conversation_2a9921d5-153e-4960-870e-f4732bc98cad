/**
 * 功能点：
 * 1. checkPermission 方法来进行权限点的验证
 *    该方法在同时挂在了Vue原型对象上，方便开发在组件中调用
 * 2. v-permission 指令来处理需要权限点验证的组件\DOM
 *    Note：受vue限制，v-permission 不支持template标签
 */

export class Permission {
  static isOpen = true;
  static checkPermission;
  /**
   * @param {Router} router vue-router实例对象
   * @param {Function} checkPermission 检查权限函数
   * @param {Array} whiteRouteList 路由白名单，支持路由别名、路径，跳转时直接跳至该页面
   * @param {String} noPermissionRoute 无权限时跳转到的路由，支持路由别名、路径
   */
  install(Vue, { checkPermission } = {}) {
    // 注册 v-permission 指令
    this.registerPermissionDirective(Vue);

    this._checkPermission = checkPermission;
    // 在vue原型挂一个权限点验证方法，方便开发使用
    const _this = this;
    Vue.prototype.$checkPermission = function (...argv) {
      return _this.checkPermission(...argv);
    };
  }

  registerPermissionDirective(Vue) {
    this.check = this.check.bind(this);
    Vue.directive("permission", {
      inserted: this.check,
      update: this.check
    });
  }

  check(el, { value }) {
    if (!this.checkPermission(value)) {
      el.parentNode && el.parentNode.removeChild(el);
    }
  }

  /**
   * 用户权限检查
   * @param {String} ruleExpression 多个权限用 空格 分割，例如："user_xx1 user_xxx2"
   */
  checkPermission(ruleExpression) {
    if (!Permission.isOpen || !ruleExpression) {
      return true;
    }
    const rules = ruleExpression.trim().split(/\s/);
    for (const rule of rules) {
      // 自定义checkPermission方法
      if (Permission.checkPermission) {
        return !!Permission.checkPermission(rule)
      } else {
        return !!this._checkPermission(rule)
      }
    }
  }

  /**
   * 检查 vnode 的 v-permission指令
   * 部分组件在特定情况下会需要针对指令单独适配
   * e.g. cet-tabs 组件
   */
  // checkVnodePermission(vnode) {
  //   const directives = (vnode.data && vnode.data.directives) || [];

  //   const permissionDesc = _.find(directives, { name: "permission" });
  //   if (permissionDesc) {
  //     return this.checkPermission(permissionDesc.value);
  //   }

  //   return true;
  // }
}

export default new Permission();
