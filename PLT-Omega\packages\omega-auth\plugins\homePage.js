import { getNavItem, not_exist_page_path } from "./pageAccess";
import utilUser from "../auth/user";
import util from "../auth/util";
import { MessageBox } from "element-ui";
import { logout } from "../auth/index.js";

function getInitHomepagePathRecord(mode) {
  let path = "";
  if (mode === "hash") {
    path = window.decodeURIComponent(window.location.hash).replace(/^#/, "");
  } else {
    path = window.decodeURIComponent(window.location.pathname);
  }

  return path;
}

// HACK 支持在iframe中进行应用嵌入
function getRecordKey() {
  function isInIframe() {
    return self.frameElement && self.frameElement.tagName === "IFRAME";
  }
  function random() {
    return Math.random().toString(16).slice(2);
  }

  let key = "omega_reload_path_record";

  if (isInIframe()) {
    key = "omega_reload_path_record_iframe_" + random();

    window.addEventListener("unload", () => {
      window.sessionStorage.removeItem(key);
    });
  }

  return key;
}

const omega_reload_path_record_key = getRecordKey();

export function getPreRoutePath() {
  return window.sessionStorage.getItem(omega_reload_path_record_key);
}

export function clearReloadPathRecord() {
  window.sessionStorage.removeItem(omega_reload_path_record_key);
}

export class HomePagePlugin {
  constructor(
    { conf, router, plugin },
    { defaultHomepage, whiteRouteList = [], whiteRouteExpression }
  ) {
    this.conf = conf;
    this.router = router;
    this.whiteRouteList = whiteRouteList;
    this.whiteRouteExpression = whiteRouteExpression;
    this.pluginManager = plugin;
    this.option = { defaultHomepage };

    router.beforeEach((to, from, next) => {
      if (to.path === not_exist_page_path) {
        next(this.firstPage);
      } else {
        next();
      }
    });
  }

  async completeAppBoot() {
    this.router.beforeEach((to, from, next) => {
      // 记录刷新时/token过期后重新登录跳转至之前的路由界面
      const unRecords = ["/login", not_exist_page_path];
      if (!unRecords.includes(to.path)) {
        window.sessionStorage.setItem(
          omega_reload_path_record_key,
          to.fullPath
        );
      }
      next();
    });

    const navmenu = this.conf.getNavmenu();
    if (!(navmenu && navmenu.length)) {
      await this.handleNavmenuEmpty();
    }

    // 刷新页面保持原路由
    const navItem = getNavItem(
      this.conf.getNavmenu(),
      this.preRoutePath,
      this.router
    );

    if (navItem || this.isInWhiteRouteList(this.preRoutePath)) {
      this.routerPush(this.preRoutePath);
      return;
    }
    const homePagePageNodeId = this.getHomePageId();

    // 系统默认首页
    let defaultLocation = this.option.defaultHomepage;

    // 用户设置了首页
    if (homePagePageNodeId) {
      const item = util.find(this.conf.getNavmenu(), homePagePageNodeId, {
        childKey: "subMenuList",
        valueKey: "permission"
      });
      defaultLocation = item?.location;
    }
    if (!defaultLocation) {
      // 没有设置首页使用菜单第一项作为首页
      defaultLocation = this.firstPage;
    }
    if (!defaultLocation) {
      throw new Error(`当前用户无项目首页可用！`);
    }

    this.routerPush(defaultLocation);
  }

  get firstPage() {
    const item = util.find(this.conf.getNavmenu(), "menuItem", {
      childKey: "subMenuList",
      valueKey: "type"
    });

    // 没有设置首页使用菜单第一项作为首页
    return item?.location;
  }

  get preRoutePath() {
    return getPreRoutePath() || getInitHomepagePathRecord(this.router.mode);
  }

  routerPush(path) {
    if (this.router.history.current.path !== path) {
      this.router.push(path);
    }
  }

  isInWhiteRouteList(path) {
    const isHas = this.whiteRouteList.find(r => {
      const { route } = this.router.resolve(r);
      return route.path === path;
    });
    const isMate = this.whiteRouteExpression(path);
    return !!isHas || isMate;
  }

  getHomePageId() {
    const config = utilUser.getRoleCustomConfig();
    // 向前兼容：部分项目使用的 homepage, 后续项目统一使用 homePagePageNodeId
    return (
      utilUser.getHomePage() ||
      config.homePagePageNodeId ||
      config.homepage ||
      ""
    );
  }

  getOmegaAdminPlugin() {
    return this.pluginManager.plugins.find(plugin => {
      if (plugin.constructor.name === "OmegaAdminPlugin") {
        return true;
      }
    });
  }

  async handleNavmenuEmpty() {
    if (utilUser.isRoot()) {
      const omegaAdminPlugin = this.getOmegaAdminPlugin();
      await MessageBox.alert(
        "当前系统可访问的菜单列表为空，请检查菜单配置。",
        "提示",
        {
          showClose: true,
          showConfirmButton: !!omegaAdminPlugin,
          confirmButtonText: "前往设置"
        }
      ).catch(async err => {
        await logout();
        return window.location.reload();
      });
      await omegaAdminPlugin._handlerInDialog(false);
    } else {
      await MessageBox.alert(
        "当前账号可访问的菜单列表为空，请检查当前账号权限设置。是否切换别的账号登录？",
        "提示",
        {
          showClose: false
        }
      );

      await logout();

      return window.location.reload();
    }
  }
}
