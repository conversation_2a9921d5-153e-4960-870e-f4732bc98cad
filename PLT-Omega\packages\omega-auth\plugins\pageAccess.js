import permissionIns from "../permission";
import { waitLogin } from "../waitLogin.js";
import { isEqual } from "lodash";

export const not_exist_page_path = "/omega_not_exist_page";

function parsePaths(uri) {
  const index = uri.indexOf("?");
  if (~index) {
    // FIXME: 临时方案，兼容omega-admin参数形式的url
    // 在刷新时菜单导航错乱
    return uri.split(/\/|&|\?/).map(uri_mod => decodeURIComponent(uri_mod));
  } else {
    return uri.split("/").map(uri_mod => decodeURIComponent(uri_mod));
  }
}
// 完整路径判定包含逻辑：菜单路径在完整路径中从开头位置起，
// 能找到对应的路径命名则判定包含
const isRoutePathContain = function (source_path, target_path) {
  const source_paths = parsePaths(source_path);
  const target_paths = parsePaths(target_path);

  return target_paths.every((ele, index) => ele === source_paths[index]);
};

/**
 * 获取 navmenu 配置中 location 和现有路由匹配的 permission
 */
export function getNavItem(list, path, router) {
  if (!path) {
    return null;
  }
  for (const item of list) {
    if (item.location) {
      // fix: 修复设置默认路由情况下跳转失效问题
      // const { route } = router.resolve(item.location);

      // 完整路径包含 location 实际路由 path 则认定为相同路由
      if (isRoutePathContain(path, item.location)) {
        return item;
      }
    }
    if (item.subMenuList) {
      const ret = getNavItem(item.subMenuList, path, router);
      if (ret) {
        return ret;
      }
    }
  }
}

const filterNavmenu = (function () {
  function copyMenuItem(navItem, list) {
    if (permissionIns.checkPermission(navItem.permission)) {
      list.push(Object.assign({}, navItem));
    }
  }

  function copyMenuList(navItem, list) {
    const item = Object.assign({}, navItem, {
      subMenuList: []
    });
    list.push(item);
    return item;
  }

  function filterWithPermission(list, navmenu) {
    for (let navItem of navmenu) {
      if (navItem.type === "menuItem") {
        copyMenuItem(navItem, list);
      } else {
        const newMenuList = copyMenuList(navItem, list);
        filterWithPermission(newMenuList.subMenuList, navItem.subMenuList);
      }
    }
  }

  function hasNavItem(list) {
    function loop(list) {
      for (let navItem of list) {
        if (navItem.type === "menuItem") {
          return true;
        } else if (loop(navItem.subMenuList)) {
          return true;
        }
      }
    }

    return !!loop(list);
  }
  // 过滤掉 subMenuList 为空的
  function filterEmpty(res) {
    for (var i = 0; i < res.length; ) {
      const navItem = res[i];
      if (navItem.type !== "menuItem") {
        if (hasNavItem(navItem.subMenuList)) {
          filterEmpty(navItem.subMenuList);
        } else {
          res.splice(i, 1);
          continue;
        }
      }
      i++;
    }
  }

  return function (navmenu) {
    const res = [];
    filterWithPermission(res, navmenu);
    filterEmpty(res);
    return res;
  };
})();

const warn = _.throttle(function (to, from, next) {
  console.warn(
    `无权限访问${to.path}, 已重定向至项目默认一个页面，可能原因：
      1. 如果跳转页面过来的请检查是否添加了 'token' , 格式: http://xxx/?token=xxx#/xxx(请务必在 #/xxx 前面加上 ?token=xxx)
      2. 在保证步骤1的前提下, 请检查你要访问的页面是否是前端导航菜单的一项或者联系项目开发者当前路由是否有必要是配置在 @omega/auth 'whiteRouteList' 白名单里
      3. 忽略该警告信息， 继续访问即可
    `
  );

  next(not_exist_page_path);
}, 300);

export class PageAccessPlugin {
  constructor({ conf, router }, { whiteRouteList = [], whiteRouteExpression }) {
    this.conf = conf;
    this.router = router;
    this.whiteRouteList = [not_exist_page_path, ...whiteRouteList];
    this.whiteRouteExpression = whiteRouteExpression;
    this.router.beforeEach((to, from, next) => {
      if (this.isInWhiteRouteList(to.path)) {
        next();
      } else {
        waitLogin.wait().then(() => {
          const nav = this.conf.getNavmenu();
          if (!nav.length) {
            return next(false);
          }
          // 权限处理
          const navItem = getNavItem(
            nav,
            to.fullPath,
            this.router
          );
          if (this.checkNavItemPermission(navItem, to.path)) {
            next();
          } else {
            warn(to, from, next);
          }
        });
      }
    });
  }

  afterAppLogin() {
    // 过滤 navmenu;
    this.conf.$watch(
      () => {
        return this.conf.state.navmenu;
      },
      (newVal, oldValue) => {
        if (isEqual(newVal, oldValue)) {
          return;
        }
        const navmenu = this.conf.getNavmenu();
        const res = filterNavmenu(navmenu);
        this.conf.setNavmenu(res);
      },
      {
        immediate: true
      }
    );
  }

  isInWhiteRouteList(path) {
    const isHas = this.whiteRouteList.find(r => {
      const { route } = this.router.resolve(r);
      return route.path === path;
    });
    const isMate = this.whiteRouteExpression(path);
    return !!isHas || isMate;
  }

  checkNavItemPermission(navItem, path) {
    if (navItem) {
      return permissionIns.checkPermission(navItem.permission);
    } else {
      return false;
    }
  }
}
