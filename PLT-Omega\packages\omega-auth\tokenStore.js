export class TokenStore {
  static isPersist = false;

  static TOKEN_KEY = "omega_token";

  static set(token) {
    window.sessionStorage.setItem(TokenStore.TOKEN_KEY, token);
    if (TokenStore.isPersist) {
      window.localStorage.setItem(TokenStore.TOKEN_KEY, token);
    }
  }

  static get() {
    return (
      TokenStore.isPersist ?
        window.localStorage.getItem(TokenStore.TOKEN_KEY) :
        window.sessionStorage.getItem(TokenStore.TOKEN_KEY)
    );
  }

  static clear() {
    window.sessionStorage.removeItem(TokenStore.TOKEN_KEY);
    window.localStorage.removeItem(TokenStore.TOKEN_KEY);
  }
}
