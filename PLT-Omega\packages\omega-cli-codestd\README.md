# 项目环境开发配置推荐

项目代码开发规范配置，包括 eslint 、 prettier 配置，以及 vscode 插件推荐和 vscode 编辑器项目配置。

## 代码检查 eslint 配置

```json
{
  // 相关规则说明
  // https://eslint.vuejs.org/rules/#priority-b-strongly-recommended-improving-readability-for-vue-js-2-x
  // https://eslint.bootcss.com/docs/rules/
  "extends": ["plugin:vue/essential", "eslint:recommended", "prettier"],
  // add your custom rules here
  // "off" 或 0 - 关闭规则
  // "warn" 或 1 - 开启规则，使用警告级别的错误：warn (不会导致程序退出)
  // "error" 或 2 - 开启规则，使用错误级别的错误：error (当被触发的时候，程序会退出)
  "rules": {
    // 关闭结尾分号规则
    "semi": 0,
    // 关闭括号规则
    "quotes": 0,
    // 要求或禁止函数圆括号之前有一个空格
    "space-before-function-paren": 0,
    // 要求箭头函数的参数使用圆括号
    "arrow-parens": 0,
    // allow async-await
    "generator-star-spacing": 0,
    // debugger 进行警告提醒
    "no-debugger": 1,
    // 要求或禁止在注释前有空白
    "spaced-comment": 0,
    // 允许重复导入
    "import/no-duplicates": "off",
    // 关闭vue对prop字段短横线校验
    "vue/attribute-hyphenation": [
      "off",
      "never",
      {
        "ignore": []
      }
    ],
    // 已定义, 未使用的变量规则降级为警告.
    "no-unused-vars": [
      "warn",
      { "vars": "all", "args": "after-used", "ignoreRestSiblings": false }
    ]
  }
}
```

## 格式化代码 prettier 配置

```json
{
  // 列宽
  "printWidth": 80,
  // 缩进宽度
  "tabWidth": 2,
  // 是否使用 tab 缩进
  "useTabs": false,
  // 是否使用分号
  "semi": true,
  // 是否使用箭头函数的参数
  "arrowParens": "avoid",
  // 是否在对象字面量的两个花括号内侧使用空格作为间隔
  "bracketSpacing": true,
  // 换行符
  "endOfLine": "lf",
  // 是否使用空白字符格式化 HTML 文件
  "htmlWhitespaceSensitivity": "ignore",
  // 多属性html标签的‘>’折行放置
  "jsxBracketSameLine": false,
  // jsx中使用单引号
  "jsxSingleQuote": false,
  // 多行时尽可能打印尾随逗号
  "trailingComma": "none",
  // markdown 折行
  "proseWrap": "preserve"
}
```

## vscode 插件推荐

推荐的插件列表(名称右侧为插件 ID)

- 简体中文 `ms-ceintl.vscode-language-pack-zh-hans`

- eslint `dbaeumer.vscode-eslint`

- prettier `esbenp.prettier-vscode`

- veture `octref.vetur`

- Gitlens `eamodio.gitlens`

- SCSS 语法提示 `mrmlnc.vscode-scss`

- svg 预览 `simonsiefke.svg-preview`

- Turbo Console Log `chakrounanas.turbo-console-log`

- chrome debugger `msjsdiag.debugger-for-chrome`

- es6 代码片段 `xabikos.javascriptsnippets`

- html 代码片段 `abusaidm.html-snippets`

> 大家有推荐的还可以提出来加进去

## vscode 项目代码本地配置

> .vscode/setting.json

```json
{
  "files.autoSave": "afterDelay",
  "editor.formatOnSave": true,
  "vetur.validation.template": false,
  "vetur.format.enable": false,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "javascript.preferences.importModuleSpecifierEnding": "js",
  "[ignore]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[shellscript]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[dockerfile]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  },
  "[hosts]": {
    "editor.defaultFormatter": "foxundermoon.shell-format"
  }
}
```

# 主要命令

omega-cli-codestd init

初始化上述配置文件

# 更新日志

v0.0.2 [2022-1-11] 更新说明文档
