#!/usr/bin/env node

const { program } = require("commander");
const shell = require("shelljs");
const packageJson = require("../package.json");
const path = require("path");

function resolveExternalPath(filepath) {
  return path.join(process.cwd(), filepath);
}

function resolvePath(filepath) {
  return path.join(__dirname, filepath);
}

class FileHandler {
  constructor({ source, target }) {
    this.source = source;
    this.target = target;
  }

  handler() {
    // TODO 不同类型采用不同的处理方式
    this.copy(this.source, this.target);
  }

  copy(source, dest) {
    shell.mkdir(path.dirname(dest));
    shell.cp("-r", source, dest);
  }
}

const files = [
  {
    source: resolvePath("../.eslintignore"),
    target: resolveExternalPath(".eslintignore")
  },
  {
    source: resolvePath("../.eslintrc.json"),
    target: resolveExternalPath(".eslintrc.json")
  },
  {
    source: resolvePath("../.prettierignore"),
    target: resolveExternalPath(".prettierignore")
  },
  {
    source: resolvePath("../.prettierrc.json"),
    target: resolveExternalPath(".prettierrc.json")
  },
  {
    source: resolvePath("../.vscode/extensions.json"),
    target: resolveExternalPath(".vscode/extensions.json")
  },
  {
    source: resolvePath("../.vscode/settings.json"),
    target: resolveExternalPath(".vscode/settings.json")
  }
];

// shelljs 使用本地命令
process.env.PATH +=
  path.delimiter + path.join(process.cwd(), "node_modules", ".bin");

program
  .version(packageJson.version)
  .description("omega code standards 命令行工具");

program
  .command("init")
  .description("初始化代码开发规范")
  .action(function () {
    files.forEach(opt => {
      new FileHandler(opt).handler();
    });
  });

program.parse(process.argv);
