{"name": "@omega/cli-codestd", "version": "0.0.2", "description": "前端代码规范、格式、vscode插件推荐", "main": "null", "bin": {"omega-cli-codestd": "./bin/omega-codestd.js"}, "keywords": ["code", "standards"], "repository": {"type": "git", "url": "https://cetsoft-svr1/Platforms/PLT-Matterhorn/_git/omega-cli-codestd"}, "author": "", "license": "ISC", "dependencies": {"commander": "^8.3.0", "shelljs": "^0.8.4", "@babel/eslint-parser": "^7.16.0", "@babel/core": "^7.11.0", "eslint": "^8.2.0", "prettier": "^2.4.1", "vue-eslint-parser": "^8.0.1"}}