# @omega/cli-devops

## 1.4.8

- fix: 脚本的格式修复

## 1.4.7

- feat: 新增可设置不进行发送钉钉消息操作，`dingDingRobot.enableSend`字段，默认为true，发送消息

## 1.4.6

- fix: 默认配置公共服务IP变更为 *************

## 1.4.5

- refactor: 打印日志优化
- refator: axios 替代原 superagent
- fix: 修复命令执行错误后未中断任务的问题
- fix: 本地打包 docker 远程服务器端口变更适配

## 1.4.4

- opt: 修改私库默认密码

## 1.4.3

- fix: 提交记录消息通知格式优化

## 1.4.2

- fix: 修复 jenkins 环境 start 命令执行时使用 powershell 命令问题

## 1.4.1

- refactor: jenkins 打包过程中获取提交记录过程中 jenkins 网络异常问题屏蔽

## 1.4.0

- feat: 新增支持本地构建, 版本号使用跟之前一致

## 1.3.0

- feat:新增 --conf 支持用户指定自定义的类似 build.conf.js 的文件

## 1.2.9

- fix: 使用指定版本号 undefined 的问题

## 1.2.8

- chore: 打包发版，sh 文件 LF 换行

## 1.2.7

- chore: cli-devops 保留 Docker 缓存以及清理最老镜像

## 1.2.6

- feat: 在两次构建之间无提交记录的时候不递增版本号

## 1.2.5

- fix: 修复镜像下载功能，镜像下载功能变更到最新地址

## 1.2.4

- fix: 修复私库上镜像版本名称在类似 v3.3.1.1-armv8-SNAPSHOT 版本标签后版本出现 3.3.1.NAN 问题

## 1.2.3

- fix: 取消 dingding 消息发送的默认代理

## 1.2.2

- fix: Harbor 2.0 API 上版本自增问题修复

## 1.2.1

- fix: harbor—V2 版本 api 版本自增错误修复

## 1.2.0

- feat: 支持 Harbor 私库 2.0API

## 1.1.1

- fix: 修复在 bff 环境下配置 dockerDirContext 无效问题

## 1.1.0

- feat: 开放打包构建命令为外部配置方式来适应 vite/vue-cli/bff 打包
  feat: 完善本地开发时调试命令

## 1.0.3

- hotfix: 默认 IP 配置适配更新

## 1.0.2

- fix: nginx 配置报错修复

## 1.0.1

- fix: cp undefined 问题修复

## 1.0.0

- feat: nginx 配置开启 Gzip

## 0.1.0

- feat: 将 package.json 放入打包后的静态文件目录，方便维护后期查看版本依赖
