# 主要命令

## omega-cli-devops init

初始化打包需要的配置文件。文件位于项目根目录 `.omega-cli` 目录下。

文件列表：

- .omega-cli/dockerContext/Dockerfile - docker image 构建配置文件
- .omega-cli/dockerContext/nginx.conf - nginx 配置文件
- .omega-cli/build.conf.js - 自动化脚本配置文件，详细见文件配置。

~~## omega-cli-devops devops ~~

~~启动自动化脚本打包。其中 uri 参数为是本地开发调试使用(具体见命令行解释)。~~

~~## [新增] omega-cli-devops start [--url] [--vite]~~

~~--url 跟上 uri~~
~~--vite 如果使用的 vite 打包 （版本要求>= 0.0.7） 需要添加 --vite 参数 （即 omega-cli-devops devops --vite）~~

## omega-cli-devops start

启动自动化脚本打包。

## [新增] omega-cli-devops local

在本地 windows 系统上进行打包构建，并将镜像推送至远程私库。不依赖本地 docker

## omega-cli-devops debug [uri]

启动自动化脚本打包。其中 uri 参数为是本地开发调试使用(具体见命令行解释)。

# 使用说明

该命令行工具运行环境为 jenkins。所以使用是在 jenkins 打包任务中使用。

1. 检查 `.omega-cli/dockerContext/nginx.conf` 相关服务是否都配置好了。（一般情况下仅需要在项目初始阶段配置后端业务服务。）

2. 配置`.omega-cli/build.conf.js`。 包括镜像库名称和版本号，及钉钉机器人。[钉钉自定义机器人创建详细步骤](http://191.0.0.158:4999/web/#/5?page_id=140)

3. 新建 jenkins 打包任务。选择复制`template-webframe`项目。新建成功后修改代码仓库配置即可。

## 如何使用 viet 打包/ bff ts 打包

```js
// .omega-cli/build.conf.js

// vite 打包
...
 buildOption: {
    commond: "vite build --outDir {{buildDirPath}}",
  }
...

// bff
...
  buildOption: {
    dockerContextDir: process.cwd(),
    buildDir: 'dist',
    commond: "tsc --outDir {{buildDirPath}}",
    movePackageJsonToBuildDir: false
  }
...
```

# 功能

## 1. Docker 镜像库管理

### 1.1 版本自动化管理

支持自动/手动版本号管理。

### 1.2 镜像仓库占用资源管理

受镜像仓库服务器资源限制，不能无限制的存储所有版本的镜像，默认会保存 5 个最新的镜像，以往的镜像会自动清理调。不需要像以往一样担心项目镜像仓库爆了，也不需要自己去手动删除

## 2. 服务通知

### 2.1 钉钉群消息通知

对接了钉钉自定义机器人的群消息通知，打包成功后发布相关消息通知。消息内容包括镜像名称、docker 私库项目跳转链接，jenkins 日志链接, 代码变更记录，代码变更对比链接，另外可以通过配置@所有人或者通过手机号@指定人员（群内）。

## 3. 代码质量平台自动对接

~~### 3.1 开发和质量平台规则的一致性~~

~~开发的代码质量检查和平台保持了一致，（全部借由 eslint 检查和生成报告上传至 sonar 平台）~~

~~### 3.2 版本对接~~

~~镜像库版本和 sonar 代码检查版本保持一致。可以清晰的看到历史版本代码质量情况及走势~~

~~## 4. Azure Devops 平台~~

~~查看对比提交代码。~~

## 5. 代码变更记录

### 5.1 代码提交记录说明

对接 jenkins,钉钉消息中的代码变更记录，为两次成功构建之间的代码提交记录。

## 6. 一键下载镜像功能。

# 更新日志说明

http://10.12.135.149:7777/verdaccio/changelog?packageName=@omega/cli-devops&packageVersion=1.4.0
