#!/usr/bin/env node
const path = require("path");

const { program } = require("commander");
const shell = require("shelljs");
const packageJson = require("../package.json");
const { replaceMacro, isWindows } = require("../util.js");
const { AxiosError } = require("axios");

const {
  omegaDir,
  dockerBuildShPath,
  dockerBuildPs1Path,
  packageJsonPath
} = require("../config/build.config.file.js");

const { log } = require("../log");

function initConfig(configpath) {
  const { initConf } = require("../config/build.conf.js")
  if (initConf) {
    return initConf(configpath);
  }
  else {
    return require("../config/build.conf.js")
  }
}

// 监听全局异常
process.on("uncaughtException", (err) => {
  if (err instanceof AxiosError) {
    log.error(`接口请求失败：${JSON.stringify({
      url: (err.response.config.baseURL || "") + err.response.config.url,
      status: err.response.status,
      message: err.response.statusText
    })}`);
  }

  process.exit(1);
});

function commandExec(command) {
  log.info(`执行命令：${command}`);
  const result = shell.exec(command);
  if (result.code !== 0) {
    log.error(`执行命令失败：${command}`);
    process.exit(1);
  }
}

async function commandAction(options, isWinlocal) {
  const { buildOption } = initConfig(options.conf);
  const { init } = require("../init.js");
  const { startPushMsgToDingDing } = require("../dingding.js");
  const { version, imageName, harborUrl, downloadRepoImageUrl } = await init({ isWinlocal });

  log.info(`打包版本号：${version}`);
  log.info(`打包镜像名：${imageName}`);

  const buildDirPath = `${buildOption.dockerContextDir}/${buildOption.buildDir}`;
  const command = replaceMacro(buildOption.commond, {
    buildDirPath,
  })
  log.info(`执行打包编译命令`);

  commandExec(command);

  // 取消默认移动 package.json 到打包目录
  if (buildOption.movePackageJsonToBuildDir) {
    commandExec(`cp -f ${packageJsonPath} ${buildDirPath}`);
  }

  // 打包成 docker 镜像
  log.info(`构建 docker 镜像`);
  if (isWinlocal) {
    commandExec(`powershell -File ${dockerBuildPs1Path}`);
  } else {
    commandExec(`sh ${dockerBuildShPath}`);
  }

  await startPushMsgToDingDing();

  log.info(`
      *************************************************
      *               打包结束：                       *
      *************************************************
      镜像: ${imageName}:${version} 
      镜像私库地址: ${harborUrl}
      镜像下载链接：${downloadRepoImageUrl}
    `);
}

// shelljs 使用本地命令
process.env.PATH +=
  path.delimiter + path.join(process.cwd(), "node_modules", ".bin");

program.version(packageJson.version).description("omega 自动化命令行工具");

program
  .command("init")
  .description("初始化 omega 打包配置文件")
  .action(async function () {
    shell.cp("-r", path.join(__dirname, "../initTemplate/*"), omegaDir);
    log.info(`
      .omega-cli/dockerContext/Dockerfile
      .omega-cli/dockerContext/nginx.conf
      .omega-cli/build.conf.js
      配置文件模板生成成功，请根据项目配置调整上述文件配置。主要是nginx.conf和build.conf.js
    `);
  });

//  warn 未来 1.0 版本废弃
// program
//   .command("devops")
//   .description(
//     "[warn 废弃] 改用 start 命令 在 jenkins 中开始打包流程， uri 为本地调试，模拟jenkins环境时使用，该uri为某次构建插件injectedEnvVars插件显示面板的uri, 示例：{jenkins}/view/{视图}/job/{任务名称}/{构建号}/injectedEnvVars/"
//   )
//   .action(commandAction);

program
  .command("jenkins")
  .option("-c, --conf <conf>", "使用指定配置文件, 示例 -c .omega-cli/master.build.conf.js, 不指定默认使用build.conf.js")
  .description("在jenkins打包构建")
  .action((options) => commandAction(options));
program
  .command("start")
  .option("-c, --conf <conf>", "使用指定配置文件, 示例 -c .omega-cli/master.build.conf.js, 不指定默认使用build.conf.js")
  .description("在jenkins打包构建")
  .action((options) => commandAction(options));

program
  .command("local")
  .option("-c, --conf <conf>", "使用指定配置文件, 示例 -c .omega-cli/master.build.conf.js, 不指定默认使用build.conf.js")
  .description("在本地主机打包构建")
  .action((options) => commandAction(options, true));

program
  .command("debug")
  .argument('<url>', '调试url')
  .description(`
    本地调试,uri为某次构建插件injectedEnvVars插件显示面板的uri
    示例：http://*************:9080/view/%E4%BD%8E%E4%BB%A3%E7%A0%81/job/lowcode-web/10/injectedEnvVars/
  `)
  .option("-c, --conf <conf>", "使用指定配置文件, 示例 -c .omega-cli/master.build.conf.js, 不指定默认使用build.conf.js")
  .action(async (uri, options) => {
    initConfig(options.conf);
    if (uri) {
      const { mockJenkinsEnv } = require("../mockJenkinsEnv.js");
      await mockJenkinsEnv(uri);
      log.info(`injectEnvVar uri: ${uri}, mock jenkins env 成功！`);
    }
    await commandAction(options);
  });

program.parseAsync(process.argv);
