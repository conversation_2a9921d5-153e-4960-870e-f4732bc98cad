const { dockerContextDir } = require("../config/build.config.file.js");
const { resolvePath } = require("../util.js");
/**
 * @fileoverview
 * 脚本开发提取的一些默认配置项
 * NOTE：无特殊情况开发人员不需要配置此文件
 */
module.exports = {
  // docker镜像仓库
  dockerReposServer: {
    // 镜像仓库名称
    // repo_name: "front-frame/web-frame-test",
    // 版本号支持 2~4位版本号，以4位版本号为例 v1.3.1.{n}, {n}会根据镜像私库的近999条版本号进行排序，计算出其下一个版本号的{n}。
    // {n} 仅支持放在最后一位。
    // 如需要手动管理版本号只需要将{n}替换成想要的版本即可。例如v1.3.1.13代表我推送到镜像库的为v1.3.1.13版本
    // tag: "v0.0.{n}"
    // 开发环境的镜像仓库
    host: "*************",
    user: {
      name: "dev",
      password: "Dev57611_Dev57611_Dev57611_Dev57611"
    },
    // docker仓库保存最近版本的镜像的最大数目
    image_max: 20
  },
  dockerBuildServer: {
    host: "*************",
    port: 57611,
    username: "root",
    rsaKeyPath: resolvePath("./config/id_rsa")
  },
  // 钉钉群自定义机器人
  dingDingRobot: {
    enableSend: true,
    // proxy: "http://*************:9898",
    proxy: "",
    robots: [
      {
        secret:
          "SEC0b6f90cc3b7891b787288e7c77ce8148dfa010326045a6c3248935e3f3c6c2ba",
        webhook:
          "https://oapi.dingtalk.com/robot/send?access_token=a8ccb0d67d745e82f602560329fdd39cebea2fda2c098fed515312ab1ad3a0f9"
      }
    ],
    isAtAll: false,
    // 通过手机号@相关人
    // atMobiles: ["18062123947"]
    atMobiles: []
  },
  // jenkins
  jenkinsServer: {
    user: {
      name: "wangwen",
      password: "Wangwen%$#@!"
    }
  },
  dockerExportHost: "*************:7777",
  buildOption: {
    dockerContextDir,
    buildDir: "dist",
    commond: "vue-cli-service build --dest {{buildDirPath}}",
    movePackageJsonToBuildDir: false
  }
};
