const { resolveExternalPath } = require("../util.js");
const lodash = require("lodash");
const { buildConfPath } = require("./build.config.file.js");
const defautBuildConf = require("./build.conf.default.js");


exports.initConf = function (configPath) {
  let buildConf = null;
  if (configPath) {
    // 用户自定义配置文件
    buildConf = require(resolveExternalPath(configPath));
  }
  else {
    buildConf = require(buildConfPath);
  }
  return module.exports = lodash.merge(defautBuildConf, buildConf);
};