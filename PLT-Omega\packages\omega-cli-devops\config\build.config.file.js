const { resolveExternalPath } = require("../util.js");

const omegaDir = resolveExternalPath(".omega-cli");
const dockerContextDir = resolveExternalPath(".omega-cli/dockerContext");
const buildConfPath = resolveExternalPath(".omega-cli/build.conf.js");
const dingDingMsgJsPath = resolveExternalPath(".omega-cli/dingDingMsg.js");
const injectEnvVarsPropertiesPath = resolveExternalPath(
  ".omega-cli/inject_env_vars.properties"
);
const dockerBuildShPath = resolveExternalPath(".omega-cli/docker_build.sh");
const dockerBuildPs1Path = resolveExternalPath(".omega-cli/docker_build.ps1");
const packageJsonPath = resolveExternalPath("package.json");

module.exports = {
  omegaDir,
  dockerContextDir,
  buildConfPath,
  dingDingMsgJsPath,
  injectEnvVarsPropertiesPath,
  dockerBuildShPath,
  dockerBuildPs1Path,
  packageJsonPath
};
