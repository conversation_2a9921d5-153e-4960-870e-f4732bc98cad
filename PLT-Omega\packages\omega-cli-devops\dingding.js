const { dingDingRobot } = require("./config/build.conf.js");

const crypto = require("crypto");

const { log } = require("./log");
const axios = require("axios");

async function pushToDingDingRobot(url, message, { secret }) {
  const timestamp = Date.now();
  const sign = encodeURIComponent(
    crypto
      .createHmac("sha256", secret)
      .update(timestamp + "\n" + secret)
      .digest("base64")
  );

  return axios.post(url, message, {
    proxy: dingDingRobot.proxy,
    params: {
      timestamp,
      sign
    }
  });
}

function handlerRetryPush() {
  let count = 0;
  return async function send(webhook, message, secret) {
    try {
      log.info(`尝试第${count + 1}次推送钉钉消息`);
      const res = await pushToDingDingRobot(webhook, message, {
        secret
      });
      if (res.data.errcode !== 0) {
        log.error("推送失败:", res.data);
      }
      log.info(`推送成功:${webhook} 钉钉消息: ${JSON.stringify(message, 2)}`);
    } catch (e) {
      // 重发
      if (count++ < 2) {
        await send(webhook, message, secret);
      }
      log.error("无法推送，请检查代理是否正常", e);
    }
  };
}

async function pushToRobots(message) {
  for (const { secret, webhook } of dingDingRobot.robots) {
    await handlerRetryPush()(webhook, message, secret);
  }
}

const { dingDingMsgJsPath } = require("./config/build.config.file.js");

async function startPushMsgToDingDing() {
  if (!dingDingRobot.enableSend) return;
  const dingdingMsg = require(dingDingMsgJsPath);
  const title = dingdingMsg.markdown.title;
  await pushToRobots(dingdingMsg);

  const { atMobiles } = dingDingRobot;
  if (atMobiles && atMobiles.length) {
    const atUsersContent = atMobiles.map(phoneNum => `@${phoneNum}`).join(" ");
    await pushToRobots({
      msgtype: "text",
      text: {
        content: `${title} ${atUsersContent}`
      },
      at: {
        atMobiles: atMobiles
      }
    });
  }
}

module.exports = {
  pushToDingDingRobot,
  pushToRobots,
  startPushMsgToDingDing
};
