/**
 * Harbor 1.x 版本API
 */
const { log } = require("../log");
const { dockerReposServer } = require("../config/build.conf.js");
const { host, repo_name, image_max } = dockerReposServer;
const { parseRepoTag, parseTag, validTag, parseNextTagByRepoTag, isAutoTag } = require("./util.js");
const _ = require("lodash");

const request = require("./request.js");

// 自动清理仓库的 docker 镜像-防止镜像太多将项目的存储用量使用完
async function checkAndClearImageList() {
  log.info(
    `检查docker私库数目是否大于指定数目 ${image_max}，并清理 docker 私库`
  );

  const list = await request.get(
    `/api/repositories/${repo_name}/tags?detail=true`
  ).catch(err => {
    log.error(`获取docker私库镜像列表失败, 可能原因：检查 ${repo_name} 项目是否存在，不存在的话需要新建项目`);
    return Promise.reject(err);
  });

  if (Array.isArray(list)) {
    // 按照推送时间升序
    list.sort((a, b) => new Date(a.push_time).valueOf() - new Date(b.push_time).valueOf())
  }

  // 默认所有的镜像数量总数保持在10个
  const count = (image_max || 10) - 1;
  while (list.length > count) {
    const imageItem = list.shift();
    await request.delete(
      `/api/repositories/${repo_name}/tags/${imageItem.name}`
    );
  }
}

async function getDockRepoLastImageTagVersion(tag) {
  const imagesList = await request.get(`/api/logs`, {
    params: {
      repository: repo_name,
      // fixbug: 取消自增原则
      tag: tag.replace("{n}", ""),
      operation: "push",
      page: 1,
      page_size: 999
    }
  });
  // fix: 服务器时间校对时时间错乱BUG
  const _imagesList = imagesList
    .map(image => {
      // TODO: 去除自增原则，对比根据版本号长度做一遍筛选不一致时功能适配
      const versions = parseTag(parseRepoTag(image.repo_tag));
      image.versionNum = versions.join("");
      return image;
    })
    .sort((a, b) => b.versionNum - a.versionNum);
  return _imagesList[0]?.repo_tag;
}

// 获取下一个版本的镜像信息
async function getImageNextTag() {
  log.info("获取下一个版本号的镜像信息");
  const { tag } = dockerReposServer;
  // tag 有效性判断
  validTag(tag);

  const lastTagVersion = await getDockRepoLastImageTagVersion(tag);
  let nextTag;
  if (!isAutoTag(tag)) {
    nextTag = tag
  } else {
    // 对比
    nextTag = parseNextTagByRepoTag(tag, lastTagVersion);
    log.info(`获取下一个版本号的镜像版本号成功，下一版本号：${nextTag}`);
  }

  log.info(`将要使用的版本号：${nextTag}，私库最新的版本号：${lastTagVersion}`);
  return { nextTag, lastTag: lastTagVersion };
}

async function getRepoProjectId() {
  const data = await request.get(`/api/search`, {
    params: {
      q: repo_name
    }
  });
  const project = data.repository && data.repository[0];
  return project && project.project_id;
}

async function isV1() {
  try {
    const res = await request.get("/api/systeminfo");
    log.info(`docker私库【${host}】版本：${res.harbor_version}`);
    return true;
  }
  catch (e) {
    return false;
  }
}

exports.isV1 = isV1;
exports.initDockerRepoV1 = async function () {
  await checkAndClearImageList();
  const { nextTag, lastTag } = await getImageNextTag();
  const project_id = await getRepoProjectId();
  return { nextTag, lastTag, project_id };
};
