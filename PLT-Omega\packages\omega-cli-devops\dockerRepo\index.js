const { initDockerRepoV1, isV1 } = require("./dockerRepo.js");
const { initDockerRepoV2, isV2 } = require("./dockerRepoV2.js");

module.exports = async function initDockerRepo() {
  try {
    if (await isV1()) {
      return initDockerRepoV1();
    }
    else if (isV2()) {
      return initDockerRepoV2();
    }
    else {
      throw new Error("omega-cli-devops 不支持当前Harbor私库版本API！,请联系前端平台相关人")
    }
  }
  catch (error) {
    log.error('版本自增功能受限：docker私库访问出现问题，如紧急使用可以使用固定版本号，否则请联系前端平台相关人');
    throw error;
  }
};