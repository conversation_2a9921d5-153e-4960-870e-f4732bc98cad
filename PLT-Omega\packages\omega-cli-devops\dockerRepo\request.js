process.env.NODE_TLS_REJECT_UNAUTHORIZED = 0;

const { dockerReposServer } = require("../config/build.conf.js");
const { user, host } = dockerReposServer;
const axios = require("axios");
const { log } = require("../log");

const request = axios.create({
  baseURL: `https://${host}`,
  auth: {
    username: user.name,
    password: user.password
  }
});

request.interceptors.response.use(function (res) {
  return res.data;
});

module.exports = request;