function validTag(tag) {
  if (tag.charAt(0) !== "v") {
    throw new Error("tag必须位字符 v 开头");
  }
  const versions = tag.split(".");
  if (versions.length > 4 || versions.length < 2) {
    throw new Error(
      "tag版本最大支持4个版本号（v1.0.0.1/v1.0.0.{n}）, 最小支持2个版本号（v1.2/v1.{n}）!"
    );
  }

  if (~versions.slice(0, versions.length - 1).indexOf("{n}")) {
    throw new Error(
      "自动管理的版本号{n}, 只能在最后一位使用, e.g. v1.2.{n}, v1.3.1.{n}"
    );
  }
}

function isAutoTag(tag) {
  const regxTag = /v(?:(\d+)\.)+{n}/;
  const isAuto = regxTag.test(tag);
  return isAuto;
}

// 补0
function padStart(str, len) {
  if (typeof str !== "string") {
    throw new Error("padStart argument[0] must be string");
  }
  let instanc = len - str.length;
  while (instanc-- > 0) {
    str = "0" + str;
  }
  return str;
}
function parseRepoTag(tag) {
  const [versionTag] = tag.match(/v\d+(?:\.\d+)*/);
  return versionTag;
}

function parseTag(tag) {
  let versions = tag ? tag.slice(1).split(".") : ["0"];

  let dif = 4 - versions.length;
  while (dif) {
    versions.push("0");
    --dif;
  }
  // 情况分析
  // 版本一致
  versions = versions.map(v => {
    if (v === "{n}") {
      return "{n}";
    }
    return padStart(v, 4);
  });

  return versions;
}

function parseNextTagByRepoTag(settingTag, lastRepoTag) {
  let nextTag;
  if (lastRepoTag) {
    const versions = parseTag(settingTag);
    const repoVersions = parseTag(lastRepoTag);

    const difIndex = versions.indexOf("{n}");

    const versionsDiff = versions.slice(0, difIndex).join("");
    const repoVersionsDiff = repoVersions.slice(0, difIndex).join("");
    const isSame = versionsDiff === repoVersionsDiff;

    if (isSame) {
      // 修订号版本自动加一
      nextTag = settingTag.replace("{n}", ++repoVersions[difIndex]);
    } else {
      // 大的版本有更新重新开始版本计数
      nextTag = settingTag.replace("{n}", 0);
    }
  } else {
    // 初始版本
    nextTag = settingTag.replace("{n}", 1);
  }

  return nextTag;
}

module.exports = {
  validTag,
  isAutoTag,
  parseTag,
  parseRepoTag,
  parseNextTagByRepoTag
}