#!/usr/bin/env node
/* eslint-disable camelcase */

/**
 * @fileoverview 自动更新版本号-修订版本号自动加一
 */
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

const path = require("path");

const { log } = require("./log");
const buildConf = require("./config/build.conf.js");

const initDockerRepo = require("./dockerRepo");

const { getChangeLog, BUILD_URL } = require("./jenkins");
const { generateFileByTpl, resolvePath, randomDir } = require("./util");
const { isAutoTag } = require("./dockerRepo/util.js");
const { dockerReposServer, dingDingRobot, dockerExportHost, dockerBuildServer } = buildConf;

const {
  dockerBuildShPath,
  dockerBuildPs1Path,
  dingDingMsgJsPath,
  injectEnvVarsPropertiesPath
} = require("./config/build.config.file.js");

async function init({ isWinlocal }) {
  let { tag } = dockerReposServer, changeLog = [], harbor_project_id = null;

  if (isAutoTag(tag)) {
    const { nextTag, lastTag, project_id } = await initDockerRepo();
    harbor_project_id = project_id;
    log.info(`当前docker镜像项目ID, ${project_id}`);
    if (isWinlocal) {
      tag = nextTag;
    }
    else {
      changeLog = await getChangeLog();
      // 判断是否存在代码提交记录，是则版本自动加一，否则使用最后一次镜像版本
      tag = changeLog.includes("无代码提交记录") ? (lastTag || nextTag) : nextTag;
      log.info(`无代码提交记录, 取消版本自增，将要使用的版本号：${tag}`);
    }
  }

  const { repo_name, host, user } = dockerReposServer;

  const imageName = `${host}/${repo_name}`;
  // 镜像私库地址
  const harborUrl = harbor_project_id ? `https://${host}/harbor/projects/${harbor_project_id}/repositories/${encodeURIComponent(
    repo_name
  )}` : `https://${host}/harbor/projects`;

  const downloadRepoImageUrl = [
    `http://${dockerExportHost}/docker/export?`,
    `host=${encodeURIComponent(host)}&`,
    `tag=${encodeURIComponent(tag)}&`,
    `repoName=${encodeURIComponent(repo_name)}`
  ].join("")

  const { buildOption } = require("./config/build.conf.js");

  // dev_ops/docker_build.sh
  generateFileByTpl(
    resolvePath("tpl/docker_build.sh.tpl"),
    {
      repo_name,
      host,
      tag,
      dockerContextDir: buildOption.dockerContextDir,
      user: user.name,
      password: user.password
    },
    dockerBuildShPath
  );

  generateFileByTpl(
    resolvePath("tpl/docker_build.ps1.tpl"),
    {
      repo_name,
      host,
      tag,
      dockerContextDir: buildOption.dockerContextDir,
      user: user.name,
      password: user.password,
      docker_build_remote_server: dockerBuildServer.host,
      docker_build_remote_server_user: dockerBuildServer.username,
      docker_build_remote_server_rsa_path: dockerBuildServer.rsaKeyPath,
      docker_build_remote_server_contextdir: `/tmp/omega_devops/${randomDir()}`,
      docker_build_remote_server_ssh_port: dockerBuildServer.port
    },
    dockerBuildPs1Path
  );

  log.info(`生成 docker 镜像打包脚本, 镜像名称：${host}/${repo_name}:${tag}`);

  // dev_ops/dingding.json
  const { isAtAll } = dingDingRobot;

  generateFileByTpl(
    resolvePath("tpl/dingDingMsg.js.tpl"),
    {
      repo_name,
      tag,
      logUrl: BUILD_URL ? BUILD_URL + "console" : "",
      repoImageUrl: harborUrl,
      changeLog,
      // http://localhost:3000/docker/export?host=***********&tag=v0.0.1&repoName=front-frame%2Fweb-frame
      downloadRepoImageUrl,
      isAtAll: isAtAll
    },
    dingDingMsgJsPath
  );

  // dev_ops/inject_env_vars.properties
  generateFileByTpl(
    resolvePath("tpl/inject_env_vars.properties.tpl"),
    {
      tag,
      imageName: imageName
    },
    injectEnvVarsPropertiesPath
  );

  return { version: tag, imageName, harborUrl, downloadRepoImageUrl };
}

module.exports = {
  init
};
