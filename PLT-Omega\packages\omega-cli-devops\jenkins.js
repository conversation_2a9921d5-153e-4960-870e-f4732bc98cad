const html2Md = require("html-to-md");

const { log } = require("./log");
const { jenkinsServer } = require("./config/build.conf.js");
const { mockEnv } = require("./mockJenkinsEnv");
const axios = require("axios");

const request = axios.create({
  auth: {
    username: jenkinsServer.user.name,
    password: jenkinsServer.user.password
  }
});

request.interceptors.response.use(function (res) {
  return res.data;
});

// 本地调试时模拟jenkins环境
if (mockEnv) {
  const keys = [
    "BUILD_URL",
    "JENKINS_URL",
    "BUILD_NUMBER",
    "JOB_NAME",
    "JOB_URL",
    // "GIT_URL",
    // "GIT_BRANCH",
    // "TFS_SERVERURL",
    // "TFS_PROJECTPATH"
  ].map(key => {
    process.env[key] = mockEnv[key];
  });
}

const BUILD_URL = process.env["BUILD_URL"];
const JENKINS_URL = process.env["JENKINS_URL"];
const BUILD_NUMBER = process.env["BUILD_NUMBER"];
const JOB_NAME = process.env["JOB_NAME"];
const JOB_URL = process.env["JOB_URL"];

function getBuildInfoUrl(buildNum) {
  return `${JOB_URL}/${buildNum}/api/json`;
}

// function getCommitUrl(changeSetItem) {
//   const map = {
//     Git: function (commitId) {
//       const GIT_URL = process.env["GIT_URL"];
//       const GIT_BRANCH = process.env["GIT_BRANCH"];

//       return `${GIT_URL}/commit/${commitId}?refName=refs/heads/${GIT_BRANCH}`;
//     },
//     TFS: function (commitId) {
//       const TFS_SERVERURL = process.env["TFS_SERVERURL"];
//       const TFS_PROJECTPATH = process.env["TFS_PROJECTPATH"];

//       const projectName = TFS_PROJECTPATH.split("/")[1];

//       return `${TFS_SERVERURL}${projectName}/_versionControl/changeset/${commitId}`;
//     }
//   };
//   switch (changeSetItem._class) {
//     // TFS
//     case "hudson.plugins.tfs.model.ChangeSet":
//       return map["TFS"](changeSetItem.commitId);
//     // GIT
//     case "hudson.plugins.git.GitChangeSet":
//       return map["Git"](changeSetItem.commitId);
//   }
// }

async function getChangeSetItems() {
  let buildNum = BUILD_NUMBER;
  // 获取本次构建的提交记录
  const data = await request.get(getBuildInfoUrl(buildNum));
  const changeSetItems = data.changeSet.items || [];

  // 获取从上次构建成功到前一次的构建记录
  // eslint-disable-next-line no-constant-condition
  while (true) {
    --buildNum;
    try {
      const data = await request.get(getBuildInfoUrl(buildNum));

      if (data.result === "SUCCESS") {
        break;
      } else {
        const record = data.changeSet.items;
        changeSetItems.push(...record);
      }
    } catch (e) {
      break;
    }
  }

  return changeSetItems;
}

async function getChangeLog() {
  log.info(`获取代码提交日志信息`);
  const md = ["---", `**变更记录**  `];
  let changeSetItems = [];
  try {
    changeSetItems = await getChangeSetItems();
    if (!changeSetItems.length) {
      md.push("无代码提交记录");
    }
  }
  catch (e) {
    md.push("未获取到代码提交记录");
    log.warn(`获取代码提交日志信息失败`, e);
  }

  const itemsHTML = changeSetItems
    .map(item => {
      // const commitUrl = getCommitUrl(item);
      // return `<li> ${item.msg} <a href="${commitUrl}">Azure -> diff</a></li>`;
      return `<li> ${item.msg} </li>`;
    })
    .join("");
  md.push(html2Md(`<ul>${itemsHTML}</ul>`));

  return JSON.stringify(md);
}

module.exports = {
  getChangeLog,
  BUILD_URL,
  JENKINS_URL,
  JOB_NAME,
  JOB_URL
};
