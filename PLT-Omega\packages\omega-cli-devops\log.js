const log4js = require("log4js");
const { resolveExternalPath } = require("./util.js");

const access_log_file = resolveExternalPath(".omega-cli/log/access_log.log");
const error_log_file = resolveExternalPath(".omega-cli/log/error_log.log");

log4js.configure({
  appenders: {
    out: { type: "stdout" },
    access_log: {
      type: "file",
      filename: access_log_file,
      maxLogSize: 300 * 1024
    },
    error_log: {
      type: "file",
      maxLogSize: 300 * 1024,
      level: "error",
      filename: error_log_file
    }
  },
  categories: {
    default: { appenders: ["out", "access_log", "error_log"], level: "all" }
  }
});
const log = log4js.getLogger("omega_devops");
log.level = "ALL";

exports.log = log;
exports.access_log_file = access_log_file;
exports.error_log_file = error_log_file;
