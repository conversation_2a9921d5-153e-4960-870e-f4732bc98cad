const axios = require("axios");
const { log } = require("./log");

async function mockJenkinsEnv(uri) {
  const { jenkinsServer } = require("./config/build.conf.js");
  const request = axios.create({
    auth: {
      username: jenkinsServer.user.name,
      password: jenkinsServer.user.password
    }
  });

  log.info("加载模拟本地环境的jenkins环境变量");
  const res = await request.get(uri + "api/json");
  exports.mockEnv = res.data.envMap;
  log.info(
    "获取到jenkins环境变量: \n",
    JSON.stringify(exports.mockEnv, null, 2)
  );
}

exports.mockJenkinsEnv = mockJenkinsEnv;
