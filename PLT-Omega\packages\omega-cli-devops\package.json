{"name": "@omega/cli-devops", "version": "1.4.8", "description": "devops webframe", "main": "null", "bin": {"omega-cli-devops": "./bin/omega_devops.js"}, "scripts": {}, "author": "", "repository": {"type": "git", "url": "https://cetsoft-svr1/Platforms/PLT-Matterhorn/_git/omega-cli-devops"}, "license": "ISC", "dependencies": {"axios": "^1.7.2", "commander": "^8.3.0", "html-to-md": "^0.5.0", "lodash": "^4.17.21", "log4js": "^6.3.0", "shelljs": "^0.8.4"}}