const buildDesc = "{{logUrl}}" ? "- [jenkins -> 构建日志]({{logUrl}})" : "- 本地构建";
module.exports = {
  msgtype: "markdown",
  markdown: {
    title: "版本发布(前端) {{tag}} ",
    text: [
      "# 版本发布(前端) {{tag}}",
      "- {{repo_name}}:{{tag}}",
      "- [docker -> 镜像仓库：]({{repoImageUrl}})",
      buildDesc,
      "---",
      "- **镜像下载**  [点击下载镜像]({{downloadRepoImageUrl}})",
    ].concat({{changeLog}}).join("\n")
  },
  at: {
    isAtAll: {{isAtAll}}
  }
};
