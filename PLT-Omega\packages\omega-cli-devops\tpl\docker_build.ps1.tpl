$ErrorActionPreference = 'Stop'
# Define variables
$image_full_name = "{{host}}/{{repo_name}}:{{tag}}"
$dockerContextDir = "{{dockerContextDir}}"
$user = "{{user}}"
$password = "{{password}}"
$harbor_host = "{{host}}"

$remote_server = "{{docker_build_remote_server}}"
$remote_user = "{{docker_build_remote_server_user}}"
$remote_dockercontextdir = "{{docker_build_remote_server_contextdir}}"
$remote_rsa_path = "{{docker_build_remote_server_rsa_path}}"
$remote_port = {{docker_build_remote_server_ssh_port}}

Write-Output "Using remote Docker."

function Invoke-CommandWithCheck {
    param (
        [string]$command
    )
    Write-Output $command
    Invoke-Expression $command
    if ($LASTEXITCODE -ne 0) {
        Write-Error "The command '$command' failed with exit code $LASTEXITCODE."
        exit $LASTEXITCODE
    }
}

# Copy Docker context directory to remote server
Invoke-CommandWithCheck "ssh -i $remote_rsa_path -p $remote_port $remote_user@$remote_server 'mkdir -p $remote_dockercontextdir'"
$scpCommand = "scp -i $remote_rsa_path -r -P $remote_port $dockerContextDir/* $remote_user@$($remote_server):$($remote_dockercontextdir)"
Invoke-CommandWithCheck "$scpCommand"

# Build and push Docker image on remote server
$sshCommand = @"
    docker build -t $image_full_name $remote_dockercontextdir &&
    echo $($password) | docker login --username $user --password-stdin $harbor_host &&
    docker push $image_full_name
"@
# Use SSH with key-based authentication
Invoke-CommandWithCheck "ssh -i $remote_rsa_path -p $remote_port $remote_user@$remote_server '$sshCommand'"

# Output information
Write-Output "***********************************"
Write-Output "* version ->    {{tag}} "
Write-Output "***********************************"