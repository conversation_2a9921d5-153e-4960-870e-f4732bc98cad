#!/bin/sh
set -e
image_full_name={{host}}/{{repo_name}}:{{tag}}
docker build -t $image_full_name {{dockerContextDir}}
# docker login -u {{user}} -p {{password}} {{host}}
# https://stackoverflow.com/questions/51489359/docker-using-password-via-the-cli-is-insecure-use-password-stdin
echo "{{password}}" | docker login --username {{user}} --password-stdin {{host}}
docker push $image_full_name

# TODO 应该在 Jenkins 定时删除镜像，为了构建速度，我们应该允许缓存
# docker rmi -f $image_full_name

echo "***********************************"
echo "* web 版本号 ->    {{tag}} "  
echo "***********************************"
