const fs = require("fs");
const path = require("path");
const os = require('os');

function replaceMacro(str, obj) {
  return str.replace(/{{(\w+)}}/g, function (matchStr, prop) {
    // eslint-disable-next-line no-prototype-builtins
    if (!obj.hasOwnProperty(prop)) {
      throw new Error(
        `宏替换对象属性 [${prop}] 不存在，请检查配置项` + "\n" + str
      );
    }
    return obj[prop];
  });
}

function generateFileByTpl(tplPath, macroObj, dist) {
  const tpl = fs.readFileSync(tplPath, "utf8");
  const file = replaceMacro(tpl, macroObj);

  if (fs.existsSync(dist)) {
    fs.unlinkSync(dist);
  }

  fs.writeFileSync(dist, file);
}

function resolveExternalPath(filepath) {
  return path.join(process.cwd(), filepath);
}

function resolvePath(filePath) {
  return path.join(__dirname, filePath);
}

function isWindows() {
  return os.platform() === 'win32';
}

function randomDir() {
  const dateStr = new Date().toLocaleDateString().split('/').join('_');
  return Math.random().toString(36).substring(2) + '_' + dateStr;
}

module.exports = { generateFileByTpl, randomDir, replaceMacro, resolvePath, resolveExternalPath, isWindows };
