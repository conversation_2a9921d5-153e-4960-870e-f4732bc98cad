# @omega/cli-devserver

## 1.2.1

- feat: CORS暴露所有Headers

## 1.2.0

- feat: 支持 CORS 跨域

## 1.1.1

- fix: 修复 Authorization 中 token 负载无 id 信息时的导致的返回报错

## 1.1.0

- 83b8942: feat: 兼容 vue-cli 5

## 1.0.5

- fix: 适配 omega-auth1.5.8 用户接口接口报错问题

## 1.0.4

- 4b66531: fix: 仅在开发环境有效

## 1.0.3

- [2022-04-20] fix: mockAuth 开关无效 Bug 修复

## 1.0.2

- e6702fc: [2022-04-19] fix: 修复启动 require 报错问题

## 1.0.1

- fix(omega-cli-devserver): 修复在没有配置本地私有代理配置文件情况下的报错
