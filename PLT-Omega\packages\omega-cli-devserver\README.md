# 开发环境优化

# 功能

# 模拟网关服务

模拟网关服务，解析但不验证 token，添加 HTPP 消息头部 User-ID。

# 自动签发模拟对应用户 token，跳过登录

自动模拟签发 token 实现跳过登录

> note: 一旦开启了模拟跳过登录，每次刷新界面都会通过注入的 js 脚本向 sessionStorage 中插入 omega-token, 如果想通过账号登录，重新配置`omegaCliDevserver.mockAuth=false`后启动，如果你知道用户 ID 的话也可以直接修改`omegaCliDevserver.mockAuthUserId`后重新启动。

# 使用方式

```js
// vue.config.js
const omegaCliDevserverHandler = require("@omega/cli-devserver");
module.exports = omegaCliDevserverHandler(vueconfig);

// 如果需要修改，在本地新建一个 .env.local(项目模板中该文件已经添加至.gitignore)
omegaCliDevserver.mockAuth = true;
omegaCliDevserver.mockAuthUserId = 1;
omegaCliDevserver.proxyFilename = "proxy.local.js";
```

# option 配置说明

- mockAuth {`Boolean`} - 是否开启模拟用户 token 跳过登录功能

- mockAuthUserId {`Number`} - 需要模拟的用户 ID，默认为 1（ROOT）。

- proxyFilename {`String`} - 使用的代理文件的名称，该文件格式为抛出的代理配置对象，方便本地多环境配置

# TODO

可以直接退出当前帐号登录其他账号而不产生用户冲突问题
