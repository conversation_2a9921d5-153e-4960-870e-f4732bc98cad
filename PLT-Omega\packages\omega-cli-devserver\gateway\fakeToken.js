const hallmark = "omegaclidevserver";

function fakeGatewayToken(userId) {
  let payload = JSON.stringify({
    id: userId
  });
  payload = Buffer.from(payload, "utf-8").toString("base64");

  return `${hallmark}.${payload}.${hallmark}`;
}

function parseGetwayTokenPayload(token) {
  const [, payloadBase64] = token.split(".");
  // 解码
  const payloadJsonString = Buffer.from(payloadBase64, "base64").toString();
  let payload;
  try {
    payload = JSON.parse(payloadJsonString);
  } catch (e) {
    throw new Error("payload Json String 解析json解析失败！", e);
  }
  return payload;
}

function isFakeToken(token) {
  return token.startsWith(hallmark);
}

function parseToken(authorization) {
  const [, token] = (authorization && authorization.split(/\s+/)) || [];
  return token;
}

module.exports = {
  fakeGatewayToken,
  parseGetwayTokenPayload,
  isFakeToken,
  parseToken
};
