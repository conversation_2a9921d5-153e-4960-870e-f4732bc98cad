const {
  isFakeToken,
  parseToken,
  parseGetwayTokenPayload
} = require("./fakeToken");
const log = require("../log");

module.exports = function mockGatewayMiddlerware({ mockAuth, mockAuthUserId } = {}) {
  return function (req, res, next) {
    /**
     * (模拟网关服务) 通过token添加User-ID头部
     * note: 开发环境代理可以正常使用的前提
     */
    const token = parseToken(req.get("Authorization"));

    if (token && req.get("User-ID") === undefined) {
      const payload = parseGetwayTokenPayload(token);
      if (payload && payload.id) {
        req.headers["User-ID"] = payload.id;
      }
    }

    if (mockAuth) {
      let isContinue = true;

      // 劫持 checktoken 接口
      if (req.path === "/auth/v1/checktoken") {
        res.json({
          code: 0,
          msg: "前端开发环境劫持模拟数据",
          data: +mockAuthUserId
        });
        isContinue = false;
      }

      if (!isContinue) {
        return;
      }
    }

    next();
  };
};
