const { fakeGatewayToken } = require("./fakeToken");
const getWebpackVersion = require("../getWebpackVersion.js");

// HACK 通过compiler对象获取 HtmlWebpackPlugin 原始构造函数
function getHtmlWebpackPluginClass(compiler) {
  const plugin = compiler.options.plugins.find((plugin) => {
    return plugin.__pluginConstructorName === "HtmlWebpackPlugin";
  });

  return plugin.constructor;
}

module.exports = function (compiler, { mockAuthUserId, mockAuth }) {
  if (mockAuth) {
    const token = fakeGatewayToken(mockAuthUserId);
    const handlerHtml = function (htmlStr) {
      let script = `<script data-desc="inject by omega-cli-devserver">
        window.sessionStorage.setItem("omega_token", "${token}");
    </script>`;
      return htmlStr.replace(/<body>/, "<body>" + script);
    }

    if (getWebpackVersion() === 5) {
      const HtmlWebpackPlugin = getHtmlWebpackPluginClass(compiler);
      compiler.hooks.compilation.tap("compilation", (compilation) => {
        HtmlWebpackPlugin.getHooks(compilation).beforeEmit.tapAsync(
          'OmegaCliServer', // <-- Set a meaningful name here for stacktraces
          (data, cb) => {
            // Manipulate the content
            data.html = handlerHtml(data.html);
            // Tell webpack to move on
            cb(null, data)
          }
        )
      });
    }
    else {
      compiler.hooks.compilation.tap("compilation", compilation => {
        compilation.hooks.htmlWebpackPluginAfterHtmlProcessing.tap(
          "htmlWebpackPluginAfterHtmlProcessing",
          htmlPluginData => {
            // 获取html文件字符串
            const htmlStr = htmlPluginData.html.toString();
            // 字符串替换，在<body>字符串后追加 script
            htmlPluginData.html = handlerHtml(htmlStr);
          }
        );
      });
    }
  }
};
