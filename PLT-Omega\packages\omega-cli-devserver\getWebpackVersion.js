const path = require("path");
const semvar = require("semver");

const packagePath = path.join(process.cwd(), "package.json");
const packageJson = require(packagePath);
const deps = Object.assign({}, packageJson.devDependencies, packageJson.dependencies);
const vueCliServiceVersion = deps["@vue/cli-service"];

module.exports = function getWebpackVersion() {
  if (semvar.gt(semvar.coerce(vueCliServiceVersion), "5.0.0")) {
    return 5;
  }
  else {
    return 4;
  }
};

