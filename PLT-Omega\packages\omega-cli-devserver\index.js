const middlewareMockGateway = require("./gateway/middlewareMockGateway");
const webpackInjectScript = require("./gateway/webpackInjectScript");
const proxyHandler = require("./proxy/index");
const log = require("./log");
const getWebpackVersion = require("./getWebpackVersion.js");
const cors = require('cors');
const env = process.env || {};

module.exports = function (
  vueconfig,
  {
    mockAuth = env["omegaCliDevserver.mockAuth"] === "true",
    mockAuthUserId = env["omegaCliDevserver.mockAuthUserId"] || 1,
    proxyFilename = env["omegaCliDevserver.proxyFilename"] || "proxy.local.js"
  } = {}
) {
  if (process.env.NODE_ENV === "development") {

    mockAuth && log.info(`开启跳过登录功能 当前模拟用户ID: ${mockAuthUserId}`);

    proxyHandler(vueconfig, { proxyFilename });

    const devServer = vueconfig.devServer || {};
    if (getWebpackVersion() === 5) {
      const before = devServer.onBeforeSetupMiddleware;
      devServer.onBeforeSetupMiddleware = function (devserver) {
        before && before(devserver);
        devserver.app.use(
          middlewareMockGateway({
            mockAuth,
            mockAuthUserId
          })
        );
        devserver.app.use(cors({ exposedHeaders: "*" }));
      };
      const after = devServer.onAfterSetupMiddleware;
      devServer.onAfterSetupMiddleware = function (devserver) {
        after && after(devserver);
        webpackInjectScript(devserver.compiler, { mockAuthUserId, mockAuth });
      }
    }
    else {
      const before = devServer.before;
      devServer.before = function (app, server, compiler) {
        before && before(app);
        app.use(
          middlewareMockGateway({
            mockAuth,
            mockAuthUserId
          })
        );
        app.use(cors({ exposedHeaders: "*" }));
      };

      const after = devServer.after;
      devServer.after = function (app, server, compiler) {
        after && after(app);
        webpackInjectScript(compiler, { mockAuthUserId, mockAuth });
      };
    }

  }

  return vueconfig;
};
