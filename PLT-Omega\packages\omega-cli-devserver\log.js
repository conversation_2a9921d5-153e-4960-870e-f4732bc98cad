module.exports = {
  error(...argv) {
    console.log(`\x1B[31m%s\x1B[0m`, "omega-cli-devserver:[error]", ...argv);
  },
  success(...argv) {
    console.log(`\x1B[32m%s\x1B[0m`, "omega-cli-devserver:[success]", ...argv);
  },
  warn(...argv) {
    console.log(`\x1B[33m%s\x1B[0m`, "omega-cli-devserver:[warn]", ...argv);
  },
  info(...argv) {
    console.log(`\x1B[34m%s\x1B[0m`, "omega-cli-devserver:[info]", ...argv);
  }
};
