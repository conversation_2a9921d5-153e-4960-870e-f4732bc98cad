const path = require("path");
const fs = require("fs");
const log = require("../log");

function resolveVarPath(filename) {
  return path.join(process.cwd(), "var", filename);
}

module.exports = function (config, { proxyFilename = "proxy.local.js" } = {}) {
  let localProxy;
  const filename = process.env.proxyFilename || proxyFilename;

  const filepath = resolveVarPath(filename);
  if (fs.existsSync(filepath)) {
    localProxy = require(filepath);
    if (localProxy) {
      Object.assign(config.devServer, { proxy: localProxy });
    }
    log.warn(`注意：当前使用的为 var/${filename} 本地私有代理配置！`);
  } else {
    log.warn(
      `注意：文件 var/${filename} 不存在（如需使用本地私有代理配置请在该项目根目录新建var/${filename}），使用 vue.config.js 默认代理配置。`
    );
  }
};
