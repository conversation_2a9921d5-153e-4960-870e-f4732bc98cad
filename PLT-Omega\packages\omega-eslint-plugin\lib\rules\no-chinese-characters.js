/**
 * @fileoverview 检测是否有中文
 * <AUTHOR>
 */
// "use strict";

//------------------------------------------------------------------------------
// Rule Definition
//------------------------------------------------------------------------------

/** @type {import('eslint').Rule.RuleModule} */
module.exports = {
  // eslint-disable-next-line eslint-plugin/prefer-message-ids
  meta: {
    type: "problem", // `problem`, `suggestion`, or `layout`
    docs: {
      description: "检测是否有中文"
      // recommended: false,
      // url: null, // URL to the documentation page for this rule
    },
    fixable: "code", // Or `code` or `whitespace`
    schema: [], // Add a schema if the rule has options
    // 报错信息描述
    messages: {
      noChineseCharactersAllowed: "代码中不允许有中文字符: {{ notBar }}."
    }
  },

  create(context) {
    // variables should be defined here

    //----------------------------------------------------------------------
    // Helpers
    //----------------------------------------------------------------------

    // any helper functions should go here or else delete this section

    //----------------------------------------------------------------------
    // Public
    //----------------------------------------------------------------------

    return {
      // visitor functions for different types of nodes
      Literal(node) {
        // console.log()  允许中文
        if (
          node &&
          node.parent &&
          node.parent.callee &&
          node.parent.callee.object &&
          node.parent.callee.object.name === "console"
        ) {
          return;
        }
        // Check if a `const` variable declaration
        if (
          typeof node.value === "string" &&
          /[\u4e00-\u9fa5]/.test(node.value)
        ) {
          // const comments = context.getSourceCode().getCommentsInside(node);
          var sourceCode = context.sourceCode;
          const comments = sourceCode.getCommentsInside(node);
          const hasChineseInComments = comments.some(comment =>
            /[\u4e00-\u9fa5]/.test(comment.value)
          );
          if (!hasChineseInComments) {
            context.report({
              node,
              messageId: "noChineseCharactersAllowed",
              data: {
                notBar: node.value
              }
            });
          }
        }
      }
    };
  }
};