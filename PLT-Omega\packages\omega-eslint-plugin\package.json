{"name": "@omega/eslint-plugin", "version": "0.0.1", "description": "omega eslint rules", "keywords": ["eslint", "eslintplugin", "eslint-plugin"], "author": "qi", "main": "./lib/index.js", "exports": "./lib/index.js", "scripts": {"lint": "npm-run-all \"lint:*\"", "lint:eslint-docs": "npm-run-all \"update:eslint-docs -- --check\"", "lint:js": "eslint .", "test": "mocha tests --recursive", "update:eslint-docs": "eslint-doc-generator"}, "dependencies": {"requireindex": "^1.2.0"}, "devDependencies": {"eslint": "^8.19.0", "eslint-doc-generator": "^1.0.0", "eslint-plugin-eslint-plugin": "^5.0.0", "eslint-plugin-node": "^11.1.0", "mocha": "^10.0.0", "npm-run-all": "^4.1.5"}, "engines": {"node": "^14.17.0 || ^16.0.0 || >= 18.0.0"}, "peerDependencies": {"eslint": ">=7"}, "license": "ISC"}