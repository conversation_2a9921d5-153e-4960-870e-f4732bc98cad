# @omega/http

## 1.8.0

- refator: 屏蔽 isPlatformBusiness 配置。

## 1.7.1

- fix: 接口权限错误正常不重复触发弹窗以及正常await

## 1.7.0

- feat: 新增 fb-language header 通知后端国际化

## 1.6.0

- feat: 提升全局拦截器执行权重

## 1.5.2

- feat: HttpBase.baseURL 新增支持传入函数，函数的参数为请求的 config，需返回字符串

## 1.5.1

- fix: 兼容适配融合框架子应用 loading 无法全屏的问题

## 1.5.0

- feat: 添加全局拦截器配置

## 1.4.2

- fix: 修复 getJsonFile 方法报错

## 1.4.1

- optimize: 暴露 getJsonFile 方法

## 1.4.0

- feat: 项目 ID 标准化，提供 omega_project_id 为项目 ID 标准字段，支持平台类型及单项目类型，另外提供插件配置 isPlatformBusiness 来配置项目类型方式，决定项目 ID 默认值的不同

## 1.3.6

- fix: 优化报错消息及 Alert 弹框重复问题，确保相同信息只有一次

## 1.3.5

- fix: 修复 token 无效时，多个接口调用的情况下弹出多个窗口的问题

## 1.3.4

- fix: 接口权限错误时存在弹窗，不再额外 message 提示

## 1.3.3

- fix: 禁止在外部添加 Authorization 全局头部信息

## 1.3.2

- fix: auth 为 false 时不对权限返回的错误信息做额外信息提示

## 1.3.1

- feat: 错误码判定取绝对值以应对不走网关的情况下部分项目使用负值

## 1.3.0

- feat: 新增 skipLoginWithNoPrompt 配置项，当登录过期时不再提示直接跳转 @董兰兰

## 1.2.3

- feat: esmodule 入口添加

## 1.2.2

- feat: 新增获取静态 JSON 文件方法

## 1.2.1

- feat: 新增全局 baseURL 插件配置

## 1.2.0

- feat: 支持原 setHeadersOperate 设置头部信息

## 1.1.9

- feat: 下载文件未指定时使用解析 header 头部文件名

## 1.1.8

- fix: 多实例情况下 loading 问题修复

## 1.1.7

- feat: errorCode 不为 0 是否 reject 配置添加，实例参数配置：rejectErrorCode

## 1.1.6

- 优化: token 失效提示信息

## 1.1.5

- fix: 国际化提示信息完善

## 1.1.4

- feat: 适配权限服务新版本 http 状态码的 token 失效的提示信息（含国际化）

## 1.1.3

- [2022-04-25] fix: 返回 code 非 0 但错误不中断问题

## 1.1.2

- [2022-04-20] fix: 正式环境请求缺失鉴权 header 导致的接口 401 问题修复

## 1.1.1

- b60912b: [2022-04-19] fix: 修复错误信息不提示的问题

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

## 1.0.1

- [2022-04-02]

  feat: 国际化功能适配
