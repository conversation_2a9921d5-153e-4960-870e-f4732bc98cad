import axios from "axios";
import { getJsonFile } from "./getJson.js";
import { FullScreenLoading } from "./loading";
import { getToken } from "./getToken";
import util from "./util";
import message from "./message";
import omegaI18n from "@omega/i18n";
import { i18n } from "./local/index";
import projectId from "./projectId.js";

const TokenExpire = 402;
const TokenChange = 403;

const noop = config => config;

class HttpBase {
  static skipLoginWithNoPrompt = false;
  static baseURL = "";
  static globalRequestInterceptor = noop;
  static globalResponseInterceptor = noop;
  constructor(
    { auth = true, loading = true, silent = false, json = true, rejectErrorCode = true } = {},
    defaultconfig
  ) {
    const _instance = axios.create(defaultconfig);
    this._interceptors = _instance.interceptors;
    this.handleGlobalInterceptors();
    // 国际化处理
    HttpBase.setHeadersOperate(() => {
      return {
        "fb-language": omegaI18n.locale
      }
    });

    this.handlerRequestConfig();

    if (auth) {
      this.handlerUnauthorized();
      this.handlerAuthToken();
    }

    loading && this.handlerLoading();

    silent || this.handlerErrorRes(rejectErrorCode, auth);

    json && this.handlerJsonRes();


    return _instance;
  }

  handlerRequestConfig() {
    const { request } = this._interceptors;
    request.use((config) => {
      Object.assign(config.headers, HttpBase.getHeaders());

      if (HttpBase.getBaseUrl(config) && !config.baseURL) {
        config.baseURL = HttpBase.getBaseUrl(config);
      }
      return config;
    });
  }

  handlerAuthToken() {
    const { request } = this._interceptors;
    request.use(function (config) {
      const token = getToken();
      if (token) {
        config.headers["Authorization"] = `Bearer ${token}`;
      }
      return config;
    });
  }

  handlerUnauthorized() {
    const { response } = this._interceptors;

    let unauthorized = async (msg) => {
      if (!HttpBase.skipLoginWithNoPrompt) {
        await message.alertTip(msg);
      }
      return HttpBase.Unauthorized();
    };

    response.use(
      res => {
        if (util.isContentTypeJson(res)) {
          const code = res.data?.code;
          if (code) {
            // HACK 402/403要兼容绝对值，权限服务那边如果不走网关的话就是-402和-403
            const errcode = Math.abs(code);
            if (TokenExpire === errcode) {
              unauthorized(i18n("当前用户登录过期，请重新登录。"))
            } else if (TokenChange === errcode) {
              unauthorized(i18n("账号在别处登陆, 请重新登录。"));
            }
          }
        }
        return res;
      },
      err => {
        const status = err.response?.status;
        if (status === 401) {
          unauthorized(i18n("当前用户登录过期，请重新登录。"));
        }
        return Promise.reject(err);
      }
    );
  }

  handlerLoading() {
    const { request, response } = this._interceptors;
    const loading = new FullScreenLoading();
    request.use(config => {
      loading.showLoading();
      return config;
    });
    response.use(
      res => {
        loading.hideLoading();
        return res;
      },
      err => {
        loading.hideLoading();
        return Promise.reject(err);
      }
    );
  }

  handlerErrorRes(rejectErrorCode, auth) {
    const { response } = this._interceptors;
    response.use(
      res => {
        if (util.isContentTypeJson(res)) {
          const data = res.data;
          /**
           * @description 后端接口错误信息格式 {code: Number, msg: String}
           * 成功 code = 0
           */
          const code = data.code;
          if (code) {
            // 开启权限的情况下，权限相关错误提示单独处理
            if (!(auth && [TokenExpire, TokenChange].includes(code))) {
              message.errorTip(data.msg);
            }
            // eslint-disable-next-line prefer-promise-reject-errors
            if (rejectErrorCode) {
              return Promise.reject(data);
            }
          }
        }
        return res;
      },
      err => {
        const status = err.response.status;

        // 权限相关错误提示单独处理
        if (status !== 401) {
          message.alertTip(i18n("请检查网络连接是否正常。"));
        }

        return Promise.reject(err);
      }
    );
  }

  handlerJsonRes() {
    const { response } = this._interceptors;
    response.use(
      res => {
        if (util.isContentTypeJson(res)) {
          return res.data;
        }
        return res;
      },
      err => Promise.reject(err)
    );
  }

  handleGlobalInterceptors() {
    const { request, response } = this._interceptors;
    request.use((config) => {
      return HttpBase.globalRequestInterceptor(config);
    })
    response.use((config) => {
      return HttpBase.globalResponseInterceptor(config);
    });
  }

  static Unauthorized = function () { };

  static headersOperates = [];

  static setHeadersOperate(op) {
    this.headersOperates.push(op);
  }


  static getBaseUrl(config) {
    let base = "";
    if (typeof HttpBase.baseURL === "function") {
      base = HttpBase.baseURL(config)
    }
    if (typeof HttpBase.baseURL === "string") {
      base = HttpBase.baseURL
    }
    return base;
  }

  static getHeaders() {
    const headers = {};
    this.headersOperates.forEach(handler => {
      let item;
      if (Object.prototype.toString.call(handler) === '[object Object]') {
        item = handler;
      }
      if (typeof handler === "function") {
        item = handler(headers);
      }
      if (item['Authorization']) {
        throw new Error('禁止在全局上使用 Authorization 头，如需要请开启auth选项使用或者通过拦截器自定义实现')
      }
      Object.assign(headers, item);
    });
    return headers;
  }

  // static apiPrefix = {
  //   "auth-service": "/auth",
  //   "model-service": "/model",
  //   "model-service-meta": "/model-meta",
  //   "device-data-service": "/devicedata",
  //   "notice-service": "/messageServer",
  //   "file-service": "/filemanager"
  // }
}

const option = {
  headers: {
    post: {
      "Content-Type": "application/json;charset=UTF-8 "
    }
  },
  responseType: "json"
}

const http = new HttpBase(
  { loading: false },
  option
);
const httping = new HttpBase(
  {},
  option
);

// 二进制流方式下载文件
const download = (url, { data = {}, filename = "" } = {}) => {
  return httping({
    url,
    method: "POST",
    data,
    responseType: "blob"
  }).then((res) => {
    util.download(window.URL.createObjectURL(res.data), filename || util.getContentDispositionFileNmae(res));
  });
};

export { HttpBase, http, httping, download, projectId, getJsonFile };

export default { HttpBase, http, httping, projectId, download, getJsonFile };

export { OmegaHttpPlugin } from "./plugin.js";
