import { Loading } from "element-ui";
import { i18n } from "./local/index";

window.OMEGA_HTTP_LOADDING_COUNT = 0;

let loadingInstance = null;
export class FullScreenLoading {
  showLoading() {
    if (OMEGA_HTTP_LOADDING_COUNT === 0) {
      loadingInstance = Loading.service(
        {
          lock: true,
          fullscreen: true,
          text: i18n("加载中……"),
          background: "rgba(255, 255, 255, 0.5)"
        }
      );
    }
    OMEGA_HTTP_LOADDING_COUNT++;
  }

  hideLoading() {
    OMEGA_HTTP_LOADDING_COUNT--;
    if (OMEGA_HTTP_LOADDING_COUNT === 0) {
      loadingInstance.close();
      loadingInstance = null;
      return true;
    }
    // 重置
    if (OMEGA_HTTP_LOADDING_COUNT < 0) {
      OMEGA_HTTP_LOADDING_COUNT = 0;
    }
  }
}