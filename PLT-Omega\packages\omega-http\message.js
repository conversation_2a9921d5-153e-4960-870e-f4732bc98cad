/**
 * @fileoverview 对提示消息进行防抖处理，防止同时弹出多条相同的提示信息
 */
import { MessageBox, Message } from "element-ui";
import util from "./util";
import { i18n } from "./local/index";

function alert(msg) {
  return MessageBox.alert(msg, i18n("提示"), {
    confirmButtonText: i18n("确定")
  });
}

function message(type, msg) {
  const duration = 3e3;
  Message({
    showClose: true,
    type: type,
    message: msg,
    duration: 3e3
  });
  return util.sleep(duration);
}

const error = message.bind(null, "error");
const info = message.bind(null, "info ");

const msger = {
  // _msgs: [],
  // push(msg, handler) {
  //   if (!this._msgs.includes(msg)) {
  //     this._msgs.push(msg);
  //   }

  //   this._flush(handler);
  // },
  // _flush: util.debounce(function (fn) {
  //   let msg;
  //   while ((msg = this._msgs.shift())) {
  //     fn(msg);
  //   }
  // }, 1e3)
  //根据value删除map中的项
  deleteByValue(value, map) {
    for (let [key, val] of map) {
      if (val === value) {
        map.delete(key);
        return true;
      }
    }
  },
  //删除信息队列中，重复的msg的项
  deleteMsg(msg) {
    this._msgsPromises.delete(msg);
    this.deleteByValue(msg, this.msgQueue);
  },
  msgQueue: new Map(), //消息队列
  _msgsPromises: new Map(),
  resolveMsg(msg, handler) {
    //每个消息设置一个执行后即删除的消息函数
    this._msgsPromises.set(msg, async () => {
      await handler(msg);
      this._msgsPromises.delete(msg);
      return true;
    });
    const timeUnix = Date.now(); //每个消息的时间戳
    this.msgQueue.set(timeUnix, msg);
    return new Promise((resolve, reject) => {
      setTimeout(async () => {
        //消息函数和消息队列同时存在时，才执行改消息函数
        if (this.msgQueue.has(timeUnix) && this._msgsPromises.has(msg)) {
          this.msgQueue.delete(timeUnix); //执行即删除信息队列中的对应一条
          await this._msgsPromises.get(msg)();
          resolve();
        }
      }, 100);
    });
  },
  async push(msg, handler) {
    if (this._msgsPromises.has(msg)) {
      this.deleteMsg(msg);
    }
    return await this.resolveMsg(msg, handler);
  }
};

export default {
  async alertTip(msg) {
    return await msger.push(msg, alert);
  },
  async errorTip(msg) {
    return await msger.push(msg, error);
  },
  async infoTip(msg) {
    return await msger.push(msg, info);
  }
};
