{"name": "@omega/http", "version": "1.8.0", "description": "", "main": "index.js", "module": "./index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "ssh://cetsoft-svr1:22/Platforms/PLT-Matterhorn/_git/omega-http"}, "keywords": ["omega-core"], "peerDependencies": {"element-ui": ">=2.11.1"}, "author": "", "license": "ISC", "dependencies": {"axios": "0.21.1"}}