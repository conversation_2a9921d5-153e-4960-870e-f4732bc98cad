import { HttpBase } from "./index.js";


export class OmegaHttpPlugin {
  constructor({ router }, {
    baseURL = "",
    skipLoginWithNoPrompt = false,
    globalRequestInterceptor = config => config,
    globalResponseInterceptor = config => config
  } = {}
  ) {
    HttpBase.Unauthorized = () => {
      router.push("/login");
    };

    HttpBase.skipLoginWithNoPrompt = skipLoginWithNoPrompt;
    HttpBase.baseURL = baseURL;
    HttpBase.globalRequestInterceptor = globalRequestInterceptor;
    HttpBase.globalResponseInterceptor = globalResponseInterceptor;
  }
}
