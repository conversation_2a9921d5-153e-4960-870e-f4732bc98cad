export default {
  isContentTypeJson(res) {
    const contentType = res.headers["content-type"];
    return contentType && contentType.includes("application/json");
  },

  /**
   * 通过header头，content-disposition 获取文件名称
   */
  getContentDispositionFileNmae(res) {
    const contentDisposition = res.headers["content-disposition"];
    const regExp = /filename=([^;]*)/;
    if (contentDisposition) {
      const [, fileName] = regExp.exec(contentDisposition);
      return window.decodeURIComponent(fileName);
    }
  },

  // 防抖函数
  debounce(fn, time) {
    let canRun = true;
    return function (...argv) {
      if (canRun) {
        canRun = false;
        setTimeout(() => {
          fn.apply(this, argv);
          canRun = true;
        }, time);
      }
    };
  },
  /**
   * 下载文件
   * @param url 文件url
   * @param name 文件名
   */
  download(url, name) {
    const a = document.createElement("a");
    a.download = name;
    a.href = url;
    a.target = "_blank";
    document.head.appendChild(a);
    a.click();
    document.head.removeChild(a);
  },

  sleep(time) {
    return new Promise((resolve) => {
      setTimeout(resolve, time);
    });
  }
};
