# @omega/i18n

## 1.7.0

- 新增特性: omegaI18n增加globalMap属性可指定全局的国际化词条，可在OmegaI18nPlugin中进行初始化，通过函数属性配置
  ```js
  achieveGlobal() {
    return {
      en: {}
    };
  }
  ```
## 1.6.0

- feature: 将语言key值的初始化移动到beforeAppBoot中

## 1.5.1

- fix: 部分情况下 lang 属性设置为国际化 key 无效

## 1.5.0

- feat: 新增 html lang 属性设置为国际化 key

## 1.4.0

- feat: 新增 fb-language header 通知后端国际化

## 1.3.0

- feat: 暴露方法支持设置默认值

## 1.2.0

- feat: omega-admin 国际化支持

## 1.1.5

- feat: esmodule 入口添加

## 1.1.4

- fix: 修复中文环境下内容未替换问题

## 1.1.3

- fix: 文本提示和当前语言冲突

## 1.1.2

- feat: i18n 实时响应 localstore

## 1.1.1

- feat: 新增默认语言 defaultActive 配置项

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

## 1.0.1

- [2022-04-02]
  docs: 使用说明文档更新
  fix: 修复 locale 拼写错误
