# 换肤前提（规范）

1. `localStorage` 固定 key 为 `"omega_language"`。
2. 业务只需要中英文，暂时仅支持语言环境缩写固定为 [`"zh_cn"` , `"en"`]
3. 切换语言不支持热更新，需要重新刷新界面切换

## 换肤模板方法说明

支持格式：

_方式一：_ `template("总共 {0} 页 共 {1} 条", 100, 120)`

_方式二：_ `template("总共 {page} 页 共 {strip} 条", {page: 100, strip: 120})`

## 相关 api 说明

- omegaI18n.locale - 获取/设置当前环境

```js
// 获取
let locale = omegaI18n.locale;
...

// 设置(会更新localstorage并刷新界面)
omegaI18n.locale = "en";
```

- omegaI18n.init({scope, map}) - 初始化并返回格式化函数

```
const i18n = omegaI18n.init({
  // 范围设置
  scope: "@omega/layout",
  // 语言map表 (注意格式，还有一层 en)
  map: {
    en: {
      "布局格式": "Layout Mode"
    }
  }
})
```
