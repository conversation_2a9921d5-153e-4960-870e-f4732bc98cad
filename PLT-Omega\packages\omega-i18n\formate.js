const RE_NARGS = /\{([0-9a-zA-Z_]+)\}/g;

/**
 * 替换传入的字符串模板处理
 * 支持格式：
 * 方式一： template("总共 {0} 页 共 {1} 条", 100, 120)
 * 方式二： template("总共 {page} 页 共 {strip} 条", {page: 100, strip: 120})
 *
 * @param {String} string
 * @param {Array} ...args
 * @return {String}
 */

export default function template(string, ...args) {
  if (args.length === 1 && typeof args[0] === "object") {
    args = args[0];
  }

  return string.replace(RE_NARGS, (match, i, index) => {
    let result;
    if (string[index - 1] === "{" && string[index + match.length] === "}") {
      return i;
    } else {
      result = Object.prototype.hasOwnProperty.call(args, i) ? args[i] : null;
      if (result === null || result === undefined) {
        return "";
      }

      return result;
    }
  });
}
