import formate from "./formate";
import scopes from "./scopes";
const LANGUAGE_LIST = ["zh_cn", "en"];
const store = {
  LANGUAGE_KEY: "omega_language",
  get() {
    return window.localStorage.getItem(this.LANGUAGE_KEY);
  },
  set(val) {
    if (!LANGUAGE_LIST.includes(val)) {
      throw new Error(
        `支持的国际化缩写格式'[zh_cn, en]'! 当前值${val} 不支持！`
      );
    }
    window.localStorage.setItem(this.LANGUAGE_KEY, val);
  }
};

const globalMap = {
  map: {},
  get() {
    return this.map;
  },
  set(val) {
    this.map = val;
  }
};

export { OmegaI18nPlugin, setDefaultLanguage } from "./plugin.js";
export default {
  init({ scope, map }) {
    this.register(scope, map);
    return this.scope(scope);
  },
  scope(name, local) {
    const map = scopes.get(name);
    const _local = local || store.get();
    const lang = map?.[_local];
    const _this = this;
    return function i18n(str, ...argv) {
      const globalLangStr = _this.globalLangStr(_local, str);
      let value = globalLangStr || lang?.[str] || str;
      return formate(value, ...argv);
    };
  },
  globalLangStr(local, str) {
    const map = globalMap.get();
    return map?.[local]?.[str];
  },
  setGlobalMap(map) {
    globalMap.set(map);
  },
  getGlobalMap() {
    globalMap.get();
  },
  store,
  register(name, map) {
    scopes.set(name, map);
  },
  set locale(val) {
    store.set(val);
    window.location.reload();
  },
  get locale() {
    return store.get();
  }
};
