import Vue from "vue";
import omegaI18n from "./index";

import moment from "moment";
import momentZhCnLang from "moment/locale/zh-cn.js";

import elementUi from "element-ui";
import elementUiEnLang from "element-ui/lib/locale/lang/en";

// HACK
let hasSetDefaultLanguage = false;
export function setDefaultLanguage(lang) {
  if (hasSetDefaultLanguage) {
    omegaI18n.store.set(lang);
    setTimeout(() => {
      window.location.reload();
    }, 0);
  }
}

function noop() {
  return {};
}
export class OmegaI18nPlugin {
  // 默认中文
  static registerInfo;
  static register({
    en = {},
    handler,
    defaultActive = "zh_cn",
    achieveGlobal = noop
  } = {}) {
    const $T = omegaI18n.init({
      scope: "project",
      map: { en }
    });

    Vue.prototype.$T = window.$T = $T;
    OmegaI18nPlugin.registerInfo = {
      en,
      handler,
      defaultActive,
      achieveGlobal
    };
  }
  async beforeAppBoot() {
    const { handler, defaultActive, achieveGlobal } =
      OmegaI18nPlugin.registerInfo;
    const local = omegaI18n.store.get();
    const globalMap = await achieveGlobal();
    omegaI18n.setGlobalMap(globalMap);
    if (!local) {
      hasSetDefaultLanguage = true;
      omegaI18n.store.set(defaultActive);
    }

    // moment 默认为英语（美国）， 所以只需要是在中文环境下设置中文即可
    if (omegaI18n.locale === "zh_cn") {
      moment.locale("zh_cn", momentZhCnLang);
    }

    // element-ui 默认为中文，所以只需要在英文环境下设置中文即可
    if (omegaI18n.locale === "en") {
      elementUi.locale(elementUiEnLang);
    }

    window.document.documentElement.lang = omegaI18n.locale;

    if (handler) {
      handler(omegaI18n.locale);
    }
  }
}
