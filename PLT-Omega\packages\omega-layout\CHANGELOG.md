# @omega/layout

## 1.13.2

- fix: 国际化词条翻译规范性检查

## 1.13.1

### Minor Changes

- [注意] feat: jquery依赖版本修改为\*

## 1.13.0

### Minor Changes

- [注意] feat: jquery依赖版本升级, ^1.12.4 -> ^3.7.1

## 1.12.3

- 优化：引入更多英文字体文件

## 1.12.2

- 优化：优化字体显示，普通情况（目前仅中文）使用Inter, "Microsoft YaHei", "微软雅黑",
  <PERSON><PERSON>, sans-serif;
  英文使用<PERSON><PERSON>, <PERSON>, sans-serif。系统中下载了Roboto-Regular字体

## 1.12.1

- 新增：store中新增刷新菜单渲染的方法`store.refreshNavmenu()`

## 1.12.0

- 优化：添加内容去最小宽高限制为 1568px\*783px
- 新增：限制 Navbars 展示个数配置

## 1.11.1

- 优化：优化 Navbars 样式

## 1.11.0

- 优化：增加默认字体配置，沿用element-ui的默认字体配置
- 优化：添加内容去最小宽高限制为 1568px\*791px。防止在缩放浏览器时导致布局错乱

## 1.10.3

- 优化：增加刷新翻译

## 1.10.2

- 优化：菜单搜索框clear后重新输入后的触发查询

## 1.10.1

- fix: 修复 keepAlive 页面不跳转问题

## 1.10.0

- feat: navbar 新增刷新界面功能

## 1.9.3

- fix: 全屏页面之间切换时，菜单抽屉收缩

## 1.9.2

- fix: 全屏边距样式权重修改

## 1.9.1

- fix: 1.9.0 全屏边距优化

## 1.9.0

- feat: 新增将页面全屏的方法，以及对应全屏中调出菜单的方法
  目前只支持在 vertical 布局情况下使用

  切换全屏状态
  import {store} from "@omega/layout";
  const isFull = store.state.isFullScreen;
  store.fullScreen(isFull);
  全屏中调出菜单
  store.fullScreenNavTrigger();

## 1.8.1

- fix: 1.8.0 字体颜色及底线优化

## 1.8.0

- 优化：图标为子菜单的情况下，新增显示当前图标的菜单标题

## 1.7.8

- 优化：面包屑过长时由多行更改为单行展示

## 1.7.7

- chore: 移除错误的 prettier 引用

## 1.7.6

- fix: 修复子菜单名称重复时的同时展开问题

## 1.7.5

- fix: 动态切换菜单交互效果优化

## 1.7.4

- fix: 修复动态修改菜单后菜单默认高亮选中效果丢失问题

## 1.7.3

- fix: 添加文字溢出缩率效果

## 1.7.2

- fix: 布局,侧边栏收起,导航子菜单仅展开一个及混色导航本地存储无效修复

## 1.7.1

- fix: 导航混色设置没有存入 localstorage 问题修复

## 1.7.0

- feat: 导航混色模式优化改进，新增 store.setNavmenuMixTheme 混色导航设置方法

## 1.6.7

- feat: 新增路由缓存 keepalive 的重置方法：resetRouteViewKeepAlive

## 1.6.6

- fix: URL 中文支持

## 1.6.5

- fix: 对外暴露内部 store 来提供配置相关组件是否展示需要

## 1.6.4

- cdf9926: fix: 侧边栏收缩图标样式更正

## 1.6.3

- fix: 子菜单缩进问题修复

## 1.6.2

- feat: 增加 isIconSubmenuOffset 插件配置，标识是否带图标的子项要偏移

## 1.6.1

- chore: 换肤组件颜色块间距调整

## 1.6.0

- feat: 优化 SCSS 函数构建方式减小打包体积
- feat: 新增深蓝色皮肤

## 1.5.3

- fix: 兼容的 svg-symbol 导航菜单 svg 图标颜色没有跟随字体

## 1.5.2

- feat: 配置项 settingThemeList 数组参数形式扩展，支持自定义颜色块

## 1.5.1

- chore: 优化子菜单项偏移 8px & 兼容原 svg 图标配置方式（svg symbol）

## 1.5.0

- feat: 新增导航菜单项缩进&收缩展开效果优化

## 1.4.9

- fix: 优化侧边栏收缩视觉效果，修复之前收缩时会显现部分导航栏文字的问题

## 1.4.8

- fix: 修复导航菜单重新设置数据后选中项菜单不展开问题

## 1.4.7

- fix: 侧边栏收缩问题修复

## 1.4.6

- fix: 删除 console 信息

## 1.4.5

- feat: esmodule 入口添加

## 1.4.4

- feat: 多套皮肤增加限制选项'settingThemeList'@周胜志@董兰兰

## 1.4.3

feat: 为适配平台类型项目, 特性调整 navmenu 修改时默认会进行 navbar 清空并设置当前路由操作 @凌英

## 1.4.2

- fix: 兼容修复 omega-admin 参数形式导航菜单错乱

## 1.4.1

- fix: 面包屑匹配在特殊情况下错乱问题，导航路径和路由配置为完全路径且一致

## 1.4.0

- 83b8942: feat: vue-cli5 适配

- Updated dependencies [83b8942]
  - @omega/theme@1.3.0

## 1.3.5

- fix: 优化当菜单选项选中样式

## 1.3.4

- fix: 收藏图标选中后不高亮问题修复

## 1.3.3

- chore: 删除多余的国际化&debugger

## 1.3.2

- fix: 修复侧边栏收缩 BUG

## 1.3.1

- fix: omega-icon 适配

## 1.3.0

- feat:

  - 混色导航模式修改
  - 布局模式采用二选一模式

## 1.2.6

- fix: 暴露国际化按钮隐藏配置

## 1.2.5

- d1b44b6: fix: 方法缺失报错

## 1.2.4

- fix: 页面报 render 错误

## 1.2.3

- [2022-05-05] fix: 侧边栏布局导航菜单 footer 渲染报错

## 1.2.2

- [2022-04-23] feat: 新增 renderNavFooter 入口

## 1.2.1

- [2022-04-26] feat: 新增布局模式 render 支持 flex 布局方式调整顺序

## 1.2.0

- [2022-04-26] feat: 支持显示隐藏搜索框

## 1.1.2

- [2022-04-24] 插件机制适配
- Updated dependencies
  - @omega/theme@1.2.1

## 1.1.1

- [2022-04-22] chore: 适配新插件钩子 completeAppStartUp

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

- Updated dependencies
  - @omega/i18n@1.1.0
  - @omega/theme@1.1.0

## 1.0.3

- [2022-04-02] fix: 修复 i18n.locale 拼写错误

## 1.0.2

- [2022-04-02]

  feat: 国际化功能适配

## 1.0.1

- [2020-03-31] fix: 修复覆盖 navmenu 配置时默认项不高亮
