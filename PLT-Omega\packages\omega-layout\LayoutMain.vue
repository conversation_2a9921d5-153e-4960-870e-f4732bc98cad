<template>
  <VLayout :navmenu="navmenu" v-if="isVLayout">
    <template #before-navmenu>
      <VLayoutSidebarToggle>
        <template #full>
          <div class="frame-vlayout-nav-logo frame-vlayout-nav-logo-full" :style="vlayoutNavLogoFull" />
        </template>
        <template #short>
          <div class="frame-vlayout-nav-logo frame-vlayout-nav-logo-short" :style="vlayoutNavLogoShort" />
        </template>
      </VLayoutSidebarToggle>
    </template>
    <template #after-navmenu>
      <Render class="frame-vlayout-navmenu-footer" :render="renderNavFooter"></Render>
    </template>
    <template #after-breadcrumb>
      <FavoritesAction v-if="isShowFavorites" :navmenu="navmenu" />
    </template>
    <template #header-right>
      <NavSearch :navmenu="navmenu" v-if="isShowSearch" />
      <Render class="frame-header-right-tools" :render="renderHeaderRightTools">
        <Favorites v-if="isShowFavorites" />
        <Setting data-testid="frame_tools_setting" v-if="isShowSetting" />
      </Render>
    </template>
  </VLayout>
  <HLayout :navmenu="navmenu" v-else>
    <template #before-breadcrumb>
      <OmegaIcon symbolId="home-lin" class="icon-hover-normal" />
    </template>
    <template #after-breadcrumb>
      <FavoritesAction v-if="isShowFavorites" :navmenu="navmenu" />
    </template>
    <template #header-left>
      <div class="frame-hlayout-nav-logo" :style="hlayoutNavLogo" />
    </template>
    <template #header-right>
      <NavSearch :navmenu="navmenu" v-if="isShowSearch" />
      <Render class="frame-header-right-tools" :render="renderHeaderRightTools">
        <Favorites v-if="isShowFavorites" />
        <Setting data-testid="frame_tools_setting" v-if="isShowSetting" />
      </Render>
    </template>
  </HLayout>
</template>
<script>
import VLayout from "./widget/VLayout.vue";
import HLayout from "./widget/HLayout.vue";
import VLayoutSidebarToggle from "./widget/VLayoutSidebarToggle.vue";

import NavSearch from "./components/nav-search/index.vue";
import Favorites from "./components/favorites/Favorites.vue";
import FavoritesAction from "./components/favorites/FavoritesAction.vue";
import Setting from "./components/setting/index.vue";
import Render from "./components/render";

import store from "./store";
import OmegaIcon from "@omega/icon";

export default {
  name: "LayoutMain",
  components: {
    VLayout,
    HLayout,
    VLayoutSidebarToggle,
    NavSearch,
    Favorites,
    FavoritesAction,
    Setting,
    Render,
    OmegaIcon
  },
  computed: {
    navmenu() {
      return store.innerState.navmenu;
    },
    isVLayout() {
      return store.state.layoutMode === "vertical";
    },
    renderHeaderRightTools() {
      return store.renderHeaderRightTools;
    },
    renderNavFooter() {
      return store.renderNavFooter;
    },
    hlayoutNavLogo() {
      return store.innerState.hlayoutNavLogo
        ? `background-image: url(${store.innerState.hlayoutNavLogo})`
        : "";
    },
    vlayoutNavLogoFull() {
      return store.innerState.vlayoutNavLogoFull
        ? `background-image: url(${store.innerState.vlayoutNavLogoFull})`
        : "";
    },
    vlayoutNavLogoShort() {
      return store.innerState.vlayoutNavLogoShort
        ? `background-image: url(${store.innerState.vlayoutNavLogoShort})`
        : "";
    },
    isShowFavorites() {
      return store.state.isShowFavorites;
    },
    isShowSetting() {
      return store.state.isShowSetting;
    },
    isShowSearch() {
      return store.state.isShowSearch;
    },
    navmenuMixTheme() {
      return store.state.navmenuMixTheme;
    }
  },
  mounted() {
    if (this.navmenuMixTheme) {
      store.setNavmenuMixTheme(this.navmenuMixTheme);
    }
  }
};
</script>
<style>
html,
body {
  margin: 0px;
  height: 100%;
  font-family: Inter, "Microsoft YaHei", "微软雅黑",
    Arial, sans-serif;
}

html[lang="en"] body {
  font-family: Roboto, Barlow, sans-serif;
}

@font-face {
  font-family: 'Roboto';
  src:url('./font/Roboto/Roboto-Regular.ttf') format('woff');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Roboto';
  src:url('./font/Roboto/Roboto-Bold.ttf') format('woff');
  font-weight: bold;
  font-style: normal;
}
</style>
<style lang="scss">
.frame-vlayout-nav-logo {
  box-sizing: border-box;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.frame-vlayout-nav-logo-full {
  height: 80px;
  background-size: 200px;
  /* webpackIgnore: true */
  background-image: url(/static/image/logo_full.png);
}

.frame-vlayout-nav-logo-short {
  height: 80px;
  /* webpackIgnore: true */
  background-image: url(/static/image/logo.png);
}

.frame-header-right-tools {
  display: flex;

  &>* {
    @include margin_right(J1);
  }
}

.frame-hlayout-nav-logo {
  height: 60px;
  width: 248px;
  box-sizing: border-box;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  /* webpackIgnore: true */
  background-image: url(/static/image/logo_full.png);
}

.frame-nav-search {
  @include margin_right(J3);
}
</style>
