<template>
  <el-popover
    class="frame-favorites"
    trigger="click"
    placement="bottom-end"
    width="300"
    v-model="visible"
    @show="onPopoverShow"
  >
    <OmegaIcon
      slot="reference"
      symbolId="favorites-lin"
      class="icon-hover-normal icon-p6"
    ></OmegaIcon>
    <div class="frame-favorites-head">
      {{ i18n("收藏夹") }}
    </div>
    <div class="frame-favorites-body">
      <div
        class="frame-favorites-item"
        :class="{ 'self-active': isActive(item) }"
        v-for="item in items"
        :key="item.location"
        @click="evClick(item)"
      >
        <OmegaIcon class="frame-favorites-item-icon" symbolId="collect-lin" />
        <span class="text-ellipsis" :title="item.label">{{ item.label }}</span>
      </div>
    </div>
  </el-popover>
</template>

<script>
import { localStorageDb } from "../../utils/db";
import navmenuUtil from "../../frame/navmenuUtil";
import OmegaIcon from "@omega/icon";
import { i18n } from "../../local";

export default {
  name: "Favorites",
  components: { OmegaIcon },
  data() {
    return {
      visible: false,
      items: []
    };
  },
  methods: {
    isActive(item) {
      return navmenuUtil.isEqual(item.location, this.$route);
    },
    onPopoverShow() {
      this.items = localStorageDb.get("favorites");
    },
    evClick(item) {
      this.$router.push(item.location);
    },
    i18n
  }
};
</script>

<style lang="scss" scoped>
.frame-favorites {
  position: relative;
}
.frame-favorites-head {
  height: 50px;
  box-sizing: border-box;
  border-bottom: 1px solid;
  margin: -12px -12px 0 -12px;
  @include padding(14px J3);
  @include font_size(H3);
  @include font_color(T2);
  @include border_direction_color(B2, "bottom");
}
.frame-favorites-body {
  height: 350px;
  box-sizing: border-box;
  margin: 0 -12px;
}
.frame-favorites-item {
  height: 32px;
  display: flex;
  align-items: center;
  @include padding(0 J3);
  @extend %J1;
  @include hover();
}
.frame-favorites-item-icon {
  width: 16px;
  height: 16px;
}

.self-active {
  &.frame-favorites-item {
    @include background_color(BG3);
  }
  .frame-favorites-item-icon {
    @include fill_color(ZS);
  }
}
</style>
