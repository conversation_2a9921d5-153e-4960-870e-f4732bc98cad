<template>
  <OmegaIcon
    :symbolId="isActive ? 'collect' : 'collect-lin'"
    class="frame-favorites-action icon-hover-normal p8"
    :class="{ 'self-active': isActive }"
    @click="evClick"
  />
</template>
<script>
import { localStorageDb } from "../../utils/db";
import navmenuUtil from "../../frame/navmenuUtil";
import _ from "lodash";

import OmegaIcon from "@omega/icon";
import { i18n } from "../../local/index.js";

export default {
  name: "FavoritesAction",
  components: { OmegaIcon },
  data() {
    return {
      isActive: false
    };
  },
  props: ["navmenu"],
  watch: {
    $route: {
      handler() {
        if (!this.navmenu.length) {
          return;
        }

        const navItem = navmenuUtil.getNavmenuTreeNode(
          this.$route,
          this.navmenu,
          this.$router
        );
        if (navItem) {
          const favorites = localStorageDb.get("favorites", []);
          this.isActive = !!_.find(favorites, {
            location: navItem.location
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    hasFavorites() {},
    async evClick() {
      if (this.isActive) {
        await this.$confirm(i18n("确认取消收藏"), i18n("提示"), {
          confirmButtonText: i18n("确定"),
          cancelButtonText: i18n("取消"),
          type: "warning"
        });
        this.updateActive();
      } else {
        this.updateActive();
        this.$message.success(i18n("收藏成功！"));
      }
    },
    updateActive() {
      this.isActive = !this.isActive;

      const pathItems = navmenuUtil.getNavmenuTreeNodePath(
        this.$route,
        this.navmenu,
        this.$router
      );
      const navItem = pathItems[pathItems.length - 1];

      const item = {
        label: pathItems.map(t => t.label).join("/"),
        location: navItem.location
      };

      localStorageDb.operate(data => {
        if (this.isActive) {
          _.set(
            data,
            "favorites",
            _(_.get(data, "favorites", []))
              .filter(t => t.location !== navItem.location)
              .push(item)
              .value()
          );
        } else {
          _.remove(_.get(data, "favorites"), {
            location: navItem.location
          });
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.self-active {
  @include font_color(ZS);
}
</style>
