export default {
  name: "<PERSON><PERSON>",
  props: {
    direction: {
      type: String,
      default: "row"
    },
    render: {
      require: true,
      type: [Function, Object]
    }
  },
  render(h) {
    let vnodes = [];

    if (this.$slots.default) {
      vnodes = vnodes.concat(this.$slots.default);
    }

    const render = this.$props.render;
    let child = render(h);
    vnodes = vnodes.concat(child);

    return (
      <div style={`display: flex; flex-direction: ${this.direction}`}>
        {vnodes}
      </div>
    );
  }
};
