<script lang="jsx">
export default {
  name: "SettingItem",
  props: ["title", "symbolId", "clazz"],
  render(h) {
    return (
      <div class="layout-setting-item">
        <div class="layout-setting-item-head">
          <OmegaIcon
            class={
              "layout-setting-item-icon icon-size-I1 " +
              (this.$props.clazz || "")
            }
            symbolId={this.$props.symbolId}
          ></OmegaIcon>
          <span class="layout-setting-item-title">{this.$props.title}</span>
        </div>
        <div class="layout-setting-item-body">{this.$slots.default}</div>
      </div>
    );
  }
};
</script>
<style lang="scss" scoped>
.layout-setting-item {
  @include padding(0 J3);
  &-head {
    display: flex;
    align-items: center;
    @include padding(J1 0);
  }
  &-body {
    @include padding(J1 0);
  }
  &-title {
    @include font_size(H3);
    @include margin_left(J1);
  }
}
</style>
