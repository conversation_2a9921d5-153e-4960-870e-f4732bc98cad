<template>
  <el-radio-group v-model="value" @change="onChange">
    <el-radio-button
      :data-testid="'lang_switch_' + item.lang"
      v-for="item in items"
      :label="item.lang"
      :key="item.lang"
    >
      {{ item.label }}
    </el-radio-button>
  </el-radio-group>
</template>

<script>
import omegaI18n from "@omega/i18n";
import enums from "../../../enums.js";
import { i18n } from "../../../local/index.js";

export default {
  name: "Language",
  data() {
    const list = enums.list("LAUGUAGE_MAP");
    const items = list.map(({ value, desc }) => {
      return {
        lang: value,
        label: desc
      };
    });

    return {
      value: omegaI18n.locale,
      items
    };
  },
  methods: {
    onChange(value) {
      this.$loading({
        lock: true,
        fullscreen: true,
        text: i18n("切换语言..."),
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.6)"
      });
      omegaI18n.locale = value;
    }
  }
};
</script>

<style lang="scss" scoped>
.active {
  @include background_color("BG4");
}
</style>
