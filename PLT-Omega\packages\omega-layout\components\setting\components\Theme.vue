<template>
  <div class="layout-theme">
    <div
      v-for="item in items"
      class="layout-theme-item"
      :title="item.label"
      :style="{ 'background-color': item.color }"
      :key="item.id"
      :data-testid="'theme_switch_' + item.id"
      @click="evClick(item)"
    >
      <transition name="el-fade-in-linear">
        <i v-show="item.active" class="el-icon-success" />
      </transition>
    </div>
  </div>
</template>

<script>
import theme from "../../../utils/theme";
import enums from "../../../enums";
import _ from "lodash";

export default {
  name: "Theme",
  props: {
    themeList: {
      type: Array,
      default: () => ["light", "dark"]
      // [
      //  ["light", "浅色", "#fff"],
      //  ["dark", "深色", "#000""],
      //  ["blue", "蓝色", "#409eff"],
      // ]
    }
  },
  computed: {
    items() {
      const activeId = theme.get();

      return this.themeList.map(item => {
        let _item = item;
        if (_.isString(item)) {
          _item = enums._get(`THEME_MAP.${item.toUpperCase()}`);
        }
        // 数组形式
        const [id, label, color] = _item;
        return {
          id,
          label,
          color,
          active: activeId === id
        };
      });
    }
  },
  methods: {
    evClick(item) {
      const activeId = theme.get();
      if (item.id === activeId) {
        return;
      }
      theme.set(item.id);
    }
  }
};
</script>
<style lang="scss" scoped>
.layout-theme {
  display: flex;
  justify-content: space-around;
}
.layout-theme-item {
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 30px;
  height: 30px;
  border-radius: 4px;
  border: 4px solid;
  cursor: pointer;
  transition-duration: 0.3s;
  &:hover {
    opacity: 0.8;
  }
  @include border_color(B2);
  @include font_color(ZS);
}
</style>
