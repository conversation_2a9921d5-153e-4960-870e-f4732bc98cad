<template>
  <el-popover
    class="frame-setting"
    trigger="click"
    placement="bottom-end"
    width="240"
    v-model="visible"
  >
    <OmegaIcon
      slot="reference"
      class="icon-hover-normal"
      symbolId="settings-lin"
    />
    <Render
      class="frame-setting-body"
      direction="column"
      :render="renderSettingItems"
    >
      <SettingItem
        :title="i18n('皮肤')"
        symbolId="skin-lin"
        class="frame-setting-nosapcing"
        v-if="isShowSettingTheme"
      >
        <Theme :themeList="themeList" />
      </SettingItem>
      <SettingItem
        class="frame-setting-nosapcing"
        :title="i18n('语言')"
        symbolId="layout-language-lin"
        v-if="isShowSettingLanguage"
      >
        <div class="text-center">
          <Language />
        </div>
      </SettingItem>
    </Render>
  </el-popover>
</template>

<script>
import Theme from "./components/Theme.vue";
import Language from "./components/Language.vue";
import OmegaIcon from "@omega/icon";
import SettingItem from "./SettingItem.vue";

import Render from "../render";
import store from "../../store";
import { i18n } from "../../local/index.js";

export default {
  name: "LayoutSetting",
  components: {
    OmegaIcon,
    Theme,
    Language,
    Render,
    SettingItem
  },
  data() {
    return {
      visible: false
    };
  },
  computed: {
    themeList() {
      return store.state.settingThemeList;
    },
    renderSettingItems() {
      return store.renderSettingItems;
    },
    isShowSettingTheme() {
      return store.state.isShowSettingTheme;
    },
    isShowSettingLanguage() {
      return store.state.isShowSettingLanguage;
    }
  },
  methods: {
    i18n
  }
};
</script>

<style lang="scss" scoped>
.frame-setting {
  position: relative;
}
.frame-setting-body {
  box-sizing: border-box;
  // margin: -12px -12px;
  & > *:not(:last-child):not(.frame-setting-nosapcing) {
    box-shadow: 0 1px 0 rgba(0, 0, 0, 0.12);
    margin-bottom: 4px;
  }
}
</style>
