@mixin theme_vars {
  // 侧边栏
  .frame-vertical .frame-navmenu {
    @content;
  }
  .frame-navmenu-submenu.el-menu--vertical {
    @content;
  }
  .frame-navmenu-vertical-popper {
    @content;
  }

  // 顶栏
  .frame-horizontal .frame-navmenu {
    @content;
  }
  .frame-horizontal .frame-main-header {
    @content;
  }
  .frame-navmenu-submenu.el-menu--horizontal {
    @content;
  }
  .frame-navmenu-horizontal-popper {
    @content;
  }
}

[data-mix-nav-theme="light"] {
  @include theme_vars {
    /* >>>> 文字色 */
    /* 主要 */
    --T1: #333333;
    /* 常规 */
    --T2: #666666;

    /* >>>> 背景色 */
    --BG: #f0f0f0;
    /* 主要 */
    --BG1: #ffffff;
    /* 滑入 */
    --BG2: #e6e6e6;
    /* 点击 */
    --BG3: #d7d7d7;
    /* 选中 */
    --BG4: #eaf7ef;
  }
}

[data-mix-nav-theme="dark"] {
  @include theme_vars {
    /* >>>> 文字色 */
    /* 主要 */
    --T1: #f0f1f2;
    /* 常规 */
    --T2: #e6e8ea;

    /* >>>> 背景色 */
    --BG: #16191a;
    /* 主要 */
    --BG1: #2b3244;
    /* 滑入 */
    --BG2: #363d50;
    /* 点击 */
    --BG3: #586073;
    /* 选中 */
    --BG4: #283a57;
  }
}

[data-mix-nav-theme="blue"] {
  @include theme_vars {
    /* >>>> 文字色 */
    /* 主要 */
    --T1: #ddeeff;
    /* 常规 */
    --T2: #ddeeff;

    /* >>>> 背景色 */
    --BG: #0b336f;
    /* 主要 */
    --BG1: #0e408c;
    /* 滑入 */
    --BG2: rgba(38, 87, 156, 0.7);
    /* 点击 */
    --BG3: rgba(48, 114, 213, 0.5);
    /* 选中 */
    --BG4: rgba(33, 88, 169, 0.3);
  }
}

[data-mix-nav-theme="bluex"] {
  @include theme_vars {
    /* >>>> 文字色 */
    /* 主要 */
    --T1: #f0f1f2;
    /* 常规 */
    --T2: #e6e8ea;

    /* >>>> 背景色 */
    --BG: #213566;
    /* 主要 */
    --BG1: #0e1b47;
    /* 滑入 */
    --BG2: #1f2b54;
    /* 点击 */
    --BG3: #334572;
    /* 选中 */
    --BG4: #0e2659;
  }
}

//声明一个根据Key获取颜色的function
@function themed_nav($key) {
  @if type-of($key) != "number" {
    $key: map-get($theme_base, $key);
  }
  @return $key;
}

@mixin background_color_nav($color, $important: null) {
  background-color: themed_nav($color) $important;
}
@mixin font_color_nav($color, $important: null) {
  color: themed_nav($color) $important;
}

@mixin custome-el-menu {
  @include background_color_nav(BG1);
  .el-submenu__title,
  .el-menu-item {
    @include font_color_nav(T2);
    &:hover {
      @include background_color_nav(BG2);
      @include font_color_nav(T1);
    }
    &:focus {
      @include background_color_nav(BG3);
      @include font_color_nav(T1);
    }
    &.is-active {
      @include background_color_nav(BG3);
      @include font_color_nav(T1);
    }
  }
  .el-menu-item-group__title {
    @include font_color_nav(T3);
  }
  /* 子选项被选中的标题显示的颜色 */
  .el-submenu.is-active {
    & > .el-submenu__title {
      @include font_color_nav(T1);
      @include background_color_nav(BG3);
    }
  }
  &.el-menu--inline {
    @include background_color_nav(BG1);
    @include font_color_nav(T1);
    .el-submenu__title,
    .el-menu-item {
      @include font_color_nav(T2);
      @include background_color_nav(BG1);
      &:hover {
        @include background_color_nav(BG2);
      }
      &:focus {
        @include background_color_nav(BG3);
      }
      &.is-active {
        @include background_color_nav(BG4);
      }
    }
  }
}

// 侧边栏
.frame-vertical {
  .frame-navmenu {
    @include background_color_nav(BG1);
  }
  .frame-vertical-navmenu .el-menu {
    @include custome-el-menu();
  }
}

.el-menu--popup {
  @include custome-el-menu();
}
// 顶栏
.frame-horizontal {
  .frame-navmenu {
    @include background_color_nav(BG1);
    .el-menu.el-menu--horizontal {
      @include background_color_nav(BG1);
      .el-submenu__title,
      .el-menu-item {
        @include font_color_nav(T2);
        &:hover {
          @include background_color_nav(BG2);
          @include font_color_nav(T1);
        }
        &:focus {
          @include font_color_nav(T1);
          @include background_color_nav(BG4);
        }
      }
      .is-active {
        & > .el-submenu__title,
        &.el-menu-item {
          @include font_color_nav(T1);
          @include background_color_nav(BG4);
        }
      }
    }
  }
  .frame-nav-search input {
    @include background_color_nav(BG);
  }
  .frame-main-header {
    .omega-icon {
      @include font_color_nav(T2);
      &:hover {
        @include background_color_nav(BG2);
      }
      &:focus {
        @include background_color_nav(BG3);
      }
    }
  }
}
