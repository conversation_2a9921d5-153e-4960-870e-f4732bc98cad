<script lang="jsx">
import { Breadcrumb, BreadcrumbItem } from "element-ui";
import navmenuUtil from "./navmenuUtil";
import util from "../utils/util";

export default {
  name: "FrameBrandcrumb",
  data() {
    return {
      items: []
    };
  },
  render(h) {
    return (
      <Breadcrumb>
        {this.items.map(item => {
          return (
            <BreadcrumbItem key={util.randomStr()}>{item.label}</BreadcrumbItem>
          );
        })}
      </Breadcrumb>
    );
  },
  created() {
    this.updateByRoute(this.$route);
  },
  watch: {
    $route(to, from) {
      this.updateByRoute(to);
    }
  },
  props: ["navmenu"],
  methods: {
    updateByRoute($route) {
      if (this.navmenu.length) {
        const nodes = navmenuUtil.getNavmenuTreeNodePath(
          $route,
          this.navmenu,
          this.$router
        );
        const items = nodes.map(node => {
          return { label: node.label };
        });
        this.items = items;
      }
    }
  }
};
</script>
