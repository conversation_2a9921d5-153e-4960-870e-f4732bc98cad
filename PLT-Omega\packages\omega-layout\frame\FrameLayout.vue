<template>
  <div class="frame-layout" :class="layoutClass">
    <div class="frame-navmenu">
      <slot name="navmenu" />
    </div>
    <div class="frame-main">
      <div class="frame-main-header">
        <div class="frame-main-header-left">
          <slot name="headerLeft" />
        </div>
        <div class="frame-main-header-right">
          <slot name="headerRight" />
        </div>
      </div>
      <div class="frame-main-insert">
        <slot name="insert" />
      </div>
      <div class="frame-main-container">
        <slot name="container" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FrameLayout",
  props: {
    layoutMode: String
  },
  computed: {
    layoutClass() {
      return this.layoutMode === "horizontal"
        ? "frame-horizontal"
        : "frame-vertical";
    }
  }
};
</script>

<style lang="scss">
.frame-layout {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.frame-horizontal {
  .frame {
    &-main {
      position: relative;
      height: calc(100% - 60px);
      &-header {
        height: 0;
        &-left {
          position: fixed;
          left: 0;
          top: 0;
          height: 60px;
          display: inline-flex;
          align-items: center;
          @include margin_left(J3);
        }
        &-right {
          position: fixed;
          right: 0;
          top: 0;
          height: 60px;
          display: inline-flex;
          align-items: center;
          @include margin_right(J3);
        }
      }
      &-insert {
        display: flex;
        height: 40px;
        align-items: flex-end;
        box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
        @include padding(0 J3);
        @include background_color(BG1);
      }
      &-container {
        height: calc(100% - 40px);
        overflow: hidden;
        box-sizing: border-box;
        @include padding(J3);
      }
    }
    &-navmenu {
      @include background_color(BG1);
    }
  }
}
.frame-vertical {
  .frame {
    &-main {
      position: relative;
      margin-left: 256px;
      height: inherit;
      transition-duration: 0.3s;
      @include background_color(BG);
      &-header {
        height: 50px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-around;
        align-items: center;
        @include padding(0 J3);
        @include background_color(BG1);
        &-left {
          display: flex;
          justify-content: flex-start;
          flex: 0 0 50%;
          & > *:not(:last-child) {
            @include margin_right(J2);
          }
        }
        &-right {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          flex: 1 1 50%;
          // & > *:not(:last-child) {
          //   @include margin_right(J2);
          // }
        }
      }
      &-insert {
        height: 30px;
        box-sizing: border-box;
        box-shadow: 0 1px 0 0 rgba(0, 0, 0, 0.1);
        @include padding(0 J3);
        @include background_color(BG1);
      }
      &-container {
        position: absolute;
        top: 80px;
        bottom: 0;
        width: 100%;
        overflow: auto;
        box-sizing: border-box;
        @include padding(J3);
      }
    }
    &-navmenu {
      position: absolute;
      top: 0;
      bottom: 0;
      box-sizing: border-box;
      overflow: hidden;
      width: 256px;
      transition-duration: 0.3s;
      display: flex;
      flex-direction: column;
      @include background_color(BG1);
    }
  }
}
</style>
