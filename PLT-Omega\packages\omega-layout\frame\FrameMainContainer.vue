<template>
  <div class="frame-main-container-innerwrap">
    <transition name="el-fade-in-linear" mode="out-in">
      <keep-alive ref="keepAlive" :include="include">
        <router-view v-if="recreateFlag && $route.meta.keepAlive" ref="keepAliveRouterView" />
      </keep-alive>
    </transition>
    <transition name="el-fade-in-linear" mode="out-in">
      <router-view :key="routerViewRefreshKey" v-if="!$route.meta.keepAlive" />
    </transition>
  </div>
</template>

<script>
import store from "../store.js";

export default {
  name: "FrameMainContainer",
  data() {
    return {
      recreateFlag: true,
      routerViewRefreshKey: store.innerState.routerViewKey,
    }
  },
  computed: {
    include() {
      return store.innerState.include;
    }
  },
  mounted() {
    store.$watch("innerState.routerViewKey", (val) => {
      if (this.$route.meta.keepAlive) {
        this.refreshKeepAliveView();
      }
      else {
        this.routerViewRefreshKey = val;
      }
    });
  },
  methods: {
    refreshKeepAliveView() {
      const key = this.$refs.keepAliveRouterView.$vnode.key;
      const keepAliveVnode = this.$refs.keepAliveRouterView.$vnode.parent;
      if (keepAliveVnode) {
        const { keys, cache } = keepAliveVnode.componentInstance;
        const currentComponentInstance = cache[key];

        currentComponentInstance.componentInstance.$destroy();
        cache[key] = null;

        const index = keys.indexOf(key);
        if (index > -1) {
          keys.splice(index, 1);
        }
      }

      this.recreateFlag = false;
      this.$nextTick(() => {
        this.recreateFlag = true;
      });
    }
  }
};
</script>
<style>
.frame-main-container-innerwrap {
  width: 100%;
  height: 100%;
  min-width: 1568px;
  min-height: 783px;
}
</style>
