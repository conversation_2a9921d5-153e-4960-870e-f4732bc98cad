<template>
  <div class="frame-navbars">
    <transition-group class="frame-navbars-view" name="el-fade-in" tag="div">
      <div v-for="item in items" :key="item.id" class="frame-navbars-item" :class="{ 's-active': isActived(item.id) }"
        @click="evItemClick(item.id)" :title="item.label" @contextmenu.prevent="evItemContextmenu($event, item.id)">
        <span class="frame-navbars-item-label">{{ item.label }}</span>
        <i class="frame-navbars-item-shadow" />
        <i class="frame-navbars-item-remove el-icon-close" v-if="canRemove(item)" @click.stop="removeItem(item.id)" />
      </div>
    </transition-group>
    <ContextMenu ref="contextmenu" :items="ctxItems" />
  </div>
</template>

<script lang="jsx">
import { getNavmenuTreeNode } from "./navmenuUtil";
import _ from "lodash";
import $ from "jquery";
import { i18n } from "../local/index.js";
import store from "../store.js";

export default {
  name: "FrameNavBars",
  components: {
    ContextMenu: {
      name: "ContextMenu",
      data() {
        return {
          isShow: false,
          x: 0,
          y: 0
        };
      },
      computed: {
        top() {
          return this.y + this.padding + "px";
        },
        left() {
          return this.x + this.padding + "px";
        }
      },
      props: {
        items: {
          type: Array,
          default() {
            return [
              {
                label: i18n("关闭"),
                id: "close"
              },
              {
                label: i18n("关闭其他"),
                id: "closeOther"
              },
              {
                label: i18n("刷新"),
                id: "refresh"
              }
            ];
          }
        },
        padding: {
          type: Number,
          default: 4
        }
      },
      render(h) {
        if (!this.isShow) {
          return;
        }

        const stylePos = {
          top: this.top,
          left: this.left
        };

        return (
          <ul class="frame-navbars-contextmenu" style={stylePos}>
            {this.items.map(item => {
              return (
                <li
                  class="frame-navbars-contextmenu-item"
                  key={item.id}
                  onClick={this.evContextMenuItemClick.bind(this, item)}
                >
                  {item.label}
                </li>
              );
            })}
          </ul>
        );
      },
      methods: {
        evContextMenuItemClick(item) {
          this.$emit("contextMenuItemClick", item.id);
        },
        open({ x, y }) {
          this.x = x;
          this.y = y;

          this.isShow = true;
        },
        close() {
          this.isShow = false;
        }
      }
    }
  },
  watch: {
    $route(to, from) {
      this.updateByRoute(to, this.navmenu);
    },
    activeId(id) {
      const item = _.find(this.items, { id });
      // HACK 屏蔽因重复 router.push 产生的无用报错
      this.$router.push(item.path || item.id).catch(e => { });
    },
    // HACK:适配平台模式，清空navbars
    navmenu(newValue, oldValue) {
      this.items = [];
      if (this.$route) {
        this.updateByRoute(this.$route, newValue);
      }
    }
  },
  props: ["navmenu", "max"],
  data() {
    let items = [];
    // if (this.homePagePath) {
    //   const node = getNavmenuTreeNode(this.homePagePath, this.navmenu);
    //   items = [
    //     {
    //       label: i18n("首页-$0", node.label),
    //       id: node.location,
    //       fixed: true
    //     }
    //   ];
    // }

    return {
      activeId: null,
      items,
      ctxItems: [],
    };
  },
  methods: {
    canRemove(item) {
      if (item.fixed) {
        return false;
      }

      return this.items.length > 1;
    },
    updateByRoute($route, navmenu) {
      if (!navmenu) {
        return;
      }
      const node = getNavmenuTreeNode($route, navmenu, this.$router);
      if (node) {
        this.updateItem(node, $route.fullPath);
        this.activeId = node.location;
      }
    },
    isActived(id) {
      if (this.activeId === null) {
        return true;
      }
      return id === this.activeId;
    },
    updateItem(node, path) {
      const item = _.find(this.items, { id: node.location });

      if (!item) {
        this.items.push({
          id: node.location,
          path: path,
          label: node.label
        });
        if (this.max && this.items.length > this.max) {
          this.items.shift();
        }
      } else {
        item.path = path;
      }
    },
    removeItem(id) {
      if (this.canRemove(_.find(this.items, { id }))) {
        const index = _.findIndex(this.items, { id });
        if (id === this.activeId) {
          // 删除的为自身
          // 优先使用后面的来顶替
          // 后面没有则用前一个来顶替
          let nextIndex = index + 1;
          if (!this.items[nextIndex]) {
            nextIndex = index - 1;
          }

          const nextItem = this.items[nextIndex];
          this.activeId = nextItem.id;
        }
        this.items.splice(index, 1);
      } else {
        this.$message.info(i18n("该项不能被删除！"));
      }
    },
    evItemClick(id) {
      this.activeId = id;
    },
    setCtxItems(id) {
      const items = [
        {
          label: i18n("关闭"),
          id: "close"
        },
        {
          label: i18n("关闭其他"),
          id: "closeOther"
        }
      ];
      const isActive = this.activeId === id;

      if (isActive) {
        items.push({
          label: i18n("刷新"),
          id: "refresh"
        });
      }

      this.ctxItems = items;
    },
    evItemContextmenu(evt, id) {
      const contextmenu = this.$refs.contextmenu;
      this.setCtxItems(id);
      contextmenu.open({ x: evt.clientX, y: evt.clientY });
      contextmenu.$once("contextMenuItemClick", mid => {
        switch (mid) {
          case "close":
            this.removeItem(id);
            break;
          case "closeOther":
            this.removeOtherItem(id);
            break;
          case "refresh":
            this.refresh(id);
        }
      });
      $(document).one("click.contenxtmenu", function () {
        contextmenu.$off("contextMenuItemClick");
        contextmenu.close();
      });
    },
    removeOtherItem(id) {
      this.items = this.items.filter(item => {
        return item.fixed || item.id === id;
      });
      this.activeId = id;
    },
    refresh(id) {
      store.refreshRouteView();
    }
  },
  mounted() {
    this.updateByRoute(this.$route, this.navmenu);
  }
};
</script>
<style lang="scss">
.frame-navbars {
  white-space: nowrap;
  user-select: none;
  @include background_color(BG1);
  overflow: hidden;
}

.frame-navbars-view {
  height: 30px;
  box-sizing: border-box;
  display: flex;

  &>*:not(:last-child) {
    @include margin_right(J1);
  }
}

.frame-navbars-item {
  display: inline-block;
  flex: 1;
  cursor: pointer;
  height: 30px;
  border-radius: 4px 4px 0 0;
  transition-duration: 0.3s;
  overflow: hidden;
  max-width: 120px;
  @include padding(0 0 0 J2);
  @include font_color(T3);
  @include background_color(BG2);

  .frame-navbars-item-shadow {
    width: 2px;
    box-shadow: -2px 0 9px 4px themedbase(BG2);
  }

  &:hover {
    @include font_color(T2);
    @include background_color(BG3);

    .frame-navbars-item-shadow {
      box-shadow: -2px 0 9px 4px themedbase(BG3);
    }
  }

  &.s-active {
    @include font_color(T1);
    @include background_color(BG3);
    font-weight: bold;

    .frame-navbars-item-shadow {
      box-shadow: -2px 0 9px 4px themedbase(BG3);
    }
  }

  &-label {
    line-height: 30px;
    font-size: 12px;
    vertical-align: middle;
    display: inline-block;
    width: calc(100% - 26px);
    overflow: hidden;
    white-space: nowrap;
  }

  &-shadow {
    height: 12px;
    display: inline-block;
    vertical-align: middle;
    box-shadow: -2px 0 9px 4px themedbase(BG1);
  }

  &-remove {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    vertical-align: middle;
    border: 2px solid transparent;
    font-size: 14px;
    @include hover();
  }
}

.frame-navbars-contextmenu {
  @include background_color(BG1);
  @include font_color(T1);
  min-width: 80px;
  z-index: 9999;
  position: fixed;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.frame-navbars-contextmenu-item {
  padding: 4px 8px;
  @include hover();
}
</style>
