<script lang="jsx">
import { Menu, Submenu, MenuItem, MenuItemGroup } from "element-ui";
import navmenuUtil from "./navmenuUtil.js";
import util from "../utils/util";
import OmegaIcon from "@omega/icon";
import { i18n } from "../local/index.js";
/**
 * 支持
 * 1. 字体              -直接用class
 * 2. html(e.g. svg)    -标签字符串
 * 3. url               -配置路径
 * 4. [废弃] 以 svg 结尾的认定为svg文件，使用 cet-icon 引入
 */

// 判定标准
// 1. 以 png,gif,jpg,jpeg,webP 结尾的认定为url
// 2. 默认即为 omega-icon 形式
const isImgURLRegx = /png|gif|jpg|jpeg|webP$/;
const isBase64Regx = /^data:.*;base64,/;
const isSvgRegx = /svg$/;

const Icon = {
  name: "Icon",
  render() {
    const { source } = this.$attrs;
    if (isSvgRegx.test(source)) {
      const fileName = source.replace(".svg", "");
      return (
        <svg class="frame-nav-icon" aria-hidden="true">
          <use href={"#" + fileName} />
        </svg>
      );
    }

    if (isImgURLRegx.test(source) || isBase64Regx.test(source)) {
      return <img src={source} />;
    }

    return <OmegaIcon symbolId={source} class="frame-nav-icon"></OmegaIcon>;
  }
};

export default {
  name: "FrameMenuList",
  props: {
    navmenu: Array,
    // horizontal   顶栏
    // vertical     侧边栏
    mode: {
      type: String,
      default: "vertical"
    },
    // only -> mode = "vertical"
    collapse: {
      type: Boolean
    },
    uniqueOpened: {
      type: Boolean,
      default: false
    },

    // 第一层为Submenu的子项文本是否便宜
    isIconSubmenuOffset: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    menuClass() {
      return this.mode === "horizontal"
        ? "frame-navmenu-horizontal"
        : "frame-navmenu-vertical";
    },
    menuPopClass() {
      return this.mode === "horizontal"
        ? "frame-navmenu-horizontal-popper"
        : "frame-navmenu-vertical-popper";
    },
    // HACK 修复动态修改菜单后，默认选中无效问题
    uumenuKey() {
      if (this.navmenu) {
        return util.guid("menu_");
      }
    }
  },
  render: function () {
    if (!this.navmenu.length) {
      // console.warn("导航菜单为空，请配置导航菜单选项，如以配置请忽略该提醒。");
      return;
    }
    const { mode, collapse } = this.$props;

    if (mode === "vertical") {
      return this.createMenu({ mode, collapse });
    } else {
      return this.createMenu({ mode });
    }
  },
  methods: {
    createMenu(option) {
      const navMenuItem = navmenuUtil.getNavmenuTreeNode(
        this.$route,
        this.navmenu,
        this.$router
      );

      let attrs = {};
      if (navMenuItem) {
        attrs = {
          "default-active": navMenuItem.location,
          "unique-opened": this.uniqueOpened
        };
      }
      // HACK 防止空白菜单
      if (this.$refs.menu && this.$refs.menu.$vnode.key !== this.uumenuKey) {
        this.$refs.menu.$el.style.zIndex = -1;
      }
      return this.$createElement(
        Menu,
        {
          key: this.uumenuKey,
          staticClass: this.menuClass,
          attrs: Object.assign(
            {
              ...attrs
            },
            option
          ),
          on: {
            "hook:mounted": () => {
              this.$refs.menu.$el.style.zIndex = 0;
            }
          },
          ref: "menu"
        },
        this.mode === "horizontal"
          ? this.createHorizontalSubMenuList(this.navmenu)
          : this.createSubMenuList(this.navmenu)
      );
    },
    createHorizontalSubMenuList(navmenu) {
      const vnodes = [];
      const moreSubMenuItem = {
        label: i18n("更多"),
        type: "subMenu",
        subMenuList: []
      };

      navmenu.forEach(item => {
        if (vnodes.length === 5) {
          moreSubMenuItem.subMenuList.push(item);
          return;
        }

        const vnode = this.createComponent(item);
        if (vnode) {
          vnodes.push(vnode);
        }
      });

      if (vnodes.length === 5) {
        vnodes.push(this.createSubmenu(moreSubMenuItem));
      }
      return vnodes;
    },
    createSubMenuList(items, parent) {
      const vnodes = [];
      items.forEach(item => {
        // 验证权限点
        vnodes.push(this.createComponent(item, parent));
      });
      return vnodes;
    },
    createComponent(item, parent) {
      switch (item.type) {
        case "menuItemGroup":
          return this.createMenuItemGroup(item);
        case "subMenu":
          return this.createSubmenu(item, parent);
        case "menuItem":
          return this.createMenuItem(item);
      }
    },
    createMenuItemGroup(item) {
      return this.$createElement(
        MenuItemGroup,
        this.createSoltSubMenuList(item, true)
      );
    },
    createSubmenu(item, parent) {
      let index = item.label + "_" + item.icon;
      if (parent) {
        index = parent.label + "_" + parent.icon + "_" + index;
      }
      return this.$createElement(
        Submenu,
        {
          attrs: {
            index,
            "popper-class": this.menuPopClass,
            "show-timeout": 0,
            "hide-timeout": 100
            // disabled: "",
          }
        },
        this.createSoltSubMenuList(item, parent)
      );
    },
    createMenuItem(item) {
      const vm = this;
      return this.$createElement(
        MenuItem,
        {
          attrs: {
            key: item.location,
            index: item.location
          },
          on: {
            click() {
              if (item.location) {
                if (item.location === vm.$route.path) {
                  return;
                }
                vm.$router.push(item.location);
              }
            }
          }
        },
        [
          item.icon ? (
            <Icon class="frame-navmenu-item-icon" source={item.icon} />
          ) : (
            <i
              role="placeholder"
              class={
                this.isIconSubmenuOffset
                  ? "frame-navmenu-icon-placeholder-offset"
                  : "frame-navmenu-icon-placeholder"
              }
            />
          ),
          <div class="frame-navmenu-item" slot="title">
            {item.label ? (
              <span class="frame-navmenu-item-text" title={item.label}>
                {item.label}
              </span>
            ) : (
              ""
            )}
          </div>
        ]
      );
    },
    createSoltSubMenuList(item, parent) {
      const vnode = [
        <div class="frame-navmenu-item" slot="title">
          {item.icon ? (
            <Icon class="frame-navmenu-item-icon" source={item.icon} />
          ) : (
            <i
              role="placeholder"
              class={
                this.isIconSubmenuOffset
                  ? "frame-navmenu-icon-placeholder-offset"
                  : "frame-navmenu-icon-placeholder"
              }
            />
          )}
          <span class="frame-navmenu-item-text" title={item.label}>
            {item.label}
          </span>
        </div>
      ];

      if (!parent && this.collapse) {
        vnode.push(<div class="frame-nvemenu-submenu-collapse-title">{item.label}</div>)
      }
      if (item.subMenuList) {
        vnode.push(this.createSubMenuList(item.subMenuList, item));
      }
      return vnode;
    }
  }
};
</script>
<style lang="scss">
.frame-navmenu-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
  width: 100%;
}
.frame-navmenu-item-icon {
  vertical-align: middle !important;
  user-select: none;
  margin-right: 8px;
}
.frame-navmenu-item-text {
  vertical-align: middle !important;
  // @include padding(0 J2);
}

.frame-navmenu-vertical {
  &.el-menu--collapse {
    width: 64px;
  }
  @mixin custome-el-menu {
    border-right: none;
    @include font_size(Aa);
    .el-menu-item {
      // margin-right: -4px;
      height: 50px;
      line-height: 50px;
      // @include padding(0 20px, !important);
      &:hover {
        @include background_color(BG2);
        @include font_color(ZS);
      }
      &:focus {
        @include font_color(ZS);
        @include background_color(BG3);
      }
      &.is-active {
        font-weight: bold;
        @include font_color(ZS);
        @include background_color(BG4);
        position: relative;
        &::before {
          content: "";
          position: absolute;
          height: 100%;
          width: 4px;
          left: 0;
          @include background_color(ZS);
        }
      }
    }
    .el-menu-item-group__title {
      height: 50px;
      line-height: 50px;
      // @include padding(0 20px, !important);
      @include font_size(Ab);
      @include font_color(T3);
    }
    .el-submenu {
      .el-submenu__title {
        // margin-right: -4px;
        height: 50px;
        line-height: 50px;
        @include font_color(T2);
        @include font_size(Aa);
        // @include padding(0 24px);
        &:hover {
          @include background_color(BG2);
          @include font_color(T1);
        }
        &:focus {
          @include font_color(T1);
          @include background_color(BG3);
        }
      }
      /* 子选项被选中的标题显示的颜色 */
      &.is-active {
        & > .el-submenu__title {
          fill: currentColor;
          font-weight: bold;
          @include font_color(T1);
          @include background_color(BG3);
        }
      }
    }

    .omega-icon.frame-navmenu-item-icon {
      width: 20px;
      height: 20px;
    }
    .frame-nav-icon.frame-navmenu-item-icon {
      fill: currentColor;
      width: 20px;
      height: 20px;
    }
    // 图标占位空间
    .frame-navmenu-icon-placeholder-offset {
      // HACK 保证子项和父项的文字偏移8px
      width: 8px;
      height: 20px;
      display: inline-block;
      margin-right: 8px;
    }

    .frame-navmenu-icon-placeholder {
      // HACK 子项和父项的文字对齐
      width: 0px;
      height: 20px;
      display: inline-block;
      margin-right: 8px;
    }
  }
  &.el-menu {
    @include background_color(BG1);
    @include custome-el-menu();
  }
  &-popper.el-menu--vertical {
    .el-menu--popup {
      @include custome-el-menu();
      .el-submenu__title,
      .el-menu-item {
        // fix: 图标占位导致的文字不居中
        @include padding_right(mh-get(J3) + 20px, !important);
      }
    }
  }

 
}
.frame-navmenu-horizontal {
  &.el-menu {
    margin-left: 500px;
    margin-right: 500px;
    display: flex;
    justify-content: flex-start;
    border-right: none;
    border-bottom: none;
    & > * {
      flex: 1 1 160px;
    }
    @include background_color(BG1);
    .el-submenu__title,
    .el-menu-item {
      @include padding(0 16px, !important);
      @include font_color(T2);
      @include font_size(Aa);
      &:hover {
        @include background_color(BG2);
        @include font_color(T1);
      }
      &:focus {
        @include font_color(T1);
        @include background_color(BG4);
      }
    }
    .is-active {
      .el-submenu__title,
      &.el-menu-item {
        border-bottom: none;
        font-weight: bold;
        @include font_color(T1);
        @include background_color(BG4);
        box-sizing: border-box;
        &::before {
          content: "";
          position: absolute;
          height: 4px;
          width: 100%;
          top: 0;
          left: 0;
          @include background_color(ZS);
        }
      }
    }
    & .omega-icon.frame-navmenu-item-icon {
      width: 24px;
      height: 24px;
    }
    & .frame-nav-icon.frame-navmenu-item-icon {
      fill: currentColor;
      width: 20px;
      height: 20px;
    }
  }
  &-popper.el-menu--horizontal {
    .el-menu--popup {
      .el-menu-item-group {
        float: left;
        min-width: 200px;
        @include margin(J2);
        .el-menu-item-group__title {
          height: 50px;
          line-height: 50px;
          @include padding(0 8px, !important);
          @include padding_right(mh-get(J3) + 8px, !important);
          @include font_size(Ab);
          @include font_color(T3);
        }
      }
      .el-submenu__title,
      .el-menu-item {
        height: 50px;
        line-height: 50px;
        transition-property: all;
        transition-duration: 0.3s;
        @include padding(0 8px, !important);
        @include padding_right(mh-get(J3) + 8px, !important);
        @include background_color(BG1);
        @include font_color(T2);
        @include font_size(Aa);
        &:hover {
          @include background_color(BG2);
          @include font_color(ZS);
        }
        &:focus {
          @include font_color(ZS);
          @include background_color(BG3);
        }
        &.is-active {
          font-weight: bold;
          position: relative;
          @include font_color(ZS);
          @include background_color(BG4);
        }
      }
    }
    .omega-icon.frame-navmenu-item-icon {
      width: 24px;
      height: 24px;
    }
    .frame-nav-icon.frame-navmenu-item-icon {
      fill: currentColor;
      width: 20px;
      height: 20px;
    }
    // 图标占位空间
    .frame-navmenu-icon-placeholder {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
  }
}

.frame-nvemenu-submenu-collapse-title {
  padding: 5px 36px;
  height: 32px;
  line-height: 32px;
  color: var(--T3);
  border-bottom: 1px solid var(--B2);
}
</style>
