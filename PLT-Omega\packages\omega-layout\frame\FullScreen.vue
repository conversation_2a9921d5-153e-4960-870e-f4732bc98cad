<template>
  <div class="frame-layout" :class="layoutClass">
    <el-drawer
      title=""
      :visible.sync="drawer"
      direction="ltr"
      size="256px"
      :with-header="false"
    >
      <div class="frame-navmenu">
        <slot name="navmenu" />
      </div>
    </el-drawer>
    <div class="frame-main">
      <div class="frame-main-container">
        <slot name="container" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "FrameLayout",
  props: {
    layoutMode: String,
    inFullScreenNavTrigger: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      drawer: false
    };
  },
  watch: {
    $route:{
      deep: true,
      immediate: true,
      handler(to, from) {
        this.drawer = false;  
      }
    },
    inFullScreenNavTrigger(val) {
      this.drawer = !this.drawer;
    }
  },
  computed: {
    layoutClass() {
      return this.layoutMode === "horizontal"
        ? "frame-horizontal"
        : "frame-vertical";
    }
  }
};
</script>

<style lang="scss" scoped>
.frame-layout {
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.frame-vertical {
  .frame {
    &-main {
      position: relative;
      margin-left: 0px;
      height: inherit;
      transition-duration: 0.3s;
      @include background_color(BG);
      &-container {
        position: absolute;
        top: 0px;
        bottom: 0;
        width: 100%;
        height: 100%;
        overflow: auto;
        box-sizing: border-box;
        padding: 0;
      }
    }
    &-navmenu {
      position: absolute;
      top: 0;
      bottom: 0;
      box-sizing: border-box;
      overflow: hidden;
      width: 256px;
      transition-duration: 0.3s;
      display: flex;
      flex-direction: column;
      @include background_color(BG1);
    }
  }
}
</style>
