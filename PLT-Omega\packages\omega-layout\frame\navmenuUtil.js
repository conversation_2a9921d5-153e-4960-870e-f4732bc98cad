import util from "../utils/util";

function parsePaths(uri) {
  const index = uri.indexOf("?");
  if (~index) {
    // FIXME: 临时方案，兼容omega-admin参数形式的url
    // 在刷新时菜单导航错乱
    return uri.split(/\/|&|\?/).map((uri_mod) => decodeURIComponent(uri_mod));
  }
  else {
    return uri.split("/").map((uri_mod) => decodeURIComponent(uri_mod))
  }
}
// NOTE: 全路径匹配，导航菜单配置及路由配置需要为完全路径且需要完全一致
// 完整路径判定包含逻辑：菜单路径在完整路径中从开头位置起，
// 能找到对应的路径命名则判定包含
function isRoutePathContain(source_path, target_path) {
  const source_paths = parsePaths(source_path);
  const target_paths = parsePaths(target_path);
  return target_paths.every((ele, index) => ele === source_paths[index]);
};

function getRoutePath(location, $router) {
  const { route } = $router.resolve(location);
  // fix: 使用全路径方便匹配参数形式
  return route.fullPath;
}

function diff(location, path) {
  if (location) {
    return isRoutePathContain(path, location);
  }
}

export function isEqual(location, route) {
  return diff(location, route.path);
}

export function getNavmenuTreeNodePath($route, navmenu, $router) {
  return util.getTreePathNodes(navmenu, getRoutePath($route, $router), {
    diffFn: diff,
    childKey: "subMenuList",
    valueKey: "location"
  });
}

export function getNavmenuTreeNode($route, navmenu, $router) {
  return util.find(navmenu, getRoutePath($route, $router), {
    diffFn: diff,
    childKey: "subMenuList",
    valueKey: "location"
  });
}

export default {
  isEqual,
  getNavmenuTreeNodePath,
  getNavmenuTreeNode
};
