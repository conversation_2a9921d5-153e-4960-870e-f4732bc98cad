import _ from "lodash";
import store from "./store";
import LayoutMain from "./LayoutMain.vue";
import VLayoutSidebarToggle from "./widget/VLayoutSidebarToggle.vue";
import SettingItem from "./components/setting/SettingItem.vue";

import "./extend/navmenuMix.scss";

function noop(h) {}

const { toggleSideBarCollapse, resetRouteViewKeepAlive } = store;

export default {
  toggleSideBarCollapse,
  resetRouteViewKeepAlive,
  LayoutMain,
  SettingItem,
  VLayoutSidebarToggle,
  store
};

export {
  toggleSideBarCollapse,
  resetRouteViewKeepAlive,
  LayoutMain,
  SettingItem,
  VLayoutSidebarToggle,
  store
};

export class OmegaLayoutPlugin {
  constructor(
    { conf },
    {
      isShowSetting = true,
      isShowSearch = true,
      isShowFavorites = true,
      isSidebarCollapse = false,
      layoutMode = "vertical",
      isShowSettingLayoutMode = true,
      isShowSettingTheme = true,
      settingThemeList = ["light", "dark"],
      isShowSettingLanguage = true,
      isNavmenuUniqueOpened = false,
      maxNavBarsNum = 0,
      isIconSubmenuOffset = true,
      renderHeaderRightTools = noop,
      renderSettingItems = noop,
      renderNavFooter = noop
    } = {}
  ) {
    this.conf = conf;

    this.option = {
      isShowSetting,
      isShowSearch,
      isShowFavorites,
      isSidebarCollapse,
      layoutMode,
      isShowSettingLayoutMode,
      isShowSettingTheme,
      settingThemeList,
      isShowSettingLanguage,
      isNavmenuUniqueOpened,
      maxNavBarsNum,
      isIconSubmenuOffset
    };

    this.renderHeaderRightTools = renderHeaderRightTools;
    this.renderSettingItems = renderSettingItems;
    this.renderNavFooter = renderNavFooter;
  }

  completeAppBoot() {
    Object.assign(store.state, this.option);
    store.$watch(
      () => {
        return this.conf.state.navmenu;
      },
      newVal => {
        store.innerState.navmenu = newVal;
      },
      {
        immediate: true
      }
    );

    store.$watch(
      () => {
        return this.conf.state.resource;
      },
      newVal => {
        store.innerState.hlayoutNavLogo = newVal.layout_vertical_image_url;
        store.innerState.vlayoutNavLogoFull =
          newVal.layout_horizontal_full_image_url;
        store.innerState.vlayoutNavLogoShort =
          newVal.layout_horizontal_short_image_url;
      },
      {
        immediate: true,
        deep: true
      }
    );

    store.renderHeaderRightTools = this.renderHeaderRightTools;
    store.renderSettingItems = this.renderSettingItems;
    store.renderNavFooter = this.renderNavFooter;
  }
}
