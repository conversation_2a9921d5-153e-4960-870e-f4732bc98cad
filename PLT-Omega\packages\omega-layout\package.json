{"name": "@omega/layout", "version": "1.13.2", "description": "omega layout plugin", "main": "index.js", "module": "./index.js", "scripts": {}, "repository": {"type": "git", "url": "ssh://cetsoft-svr1:22/Platforms/PLT-Matterhorn/_git/omega-layout"}, "keywords": ["layout", "plugin"], "peerDependencies": {"element-ui": ">=2.11.1", "jquery": "*", "lodash": "*", "vue": ">=2.6.11 < 3", "vue-router": ">=3.4.3 < 4", "vuex": ">=3.5.1 < 4", "@omega/icon": "*"}, "author": "wwen", "license": "ISC"}