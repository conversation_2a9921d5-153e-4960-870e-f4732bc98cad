import Vue from "vue";
import { getDefaultValue } from "./localStorageSetting.js";
import $ from "jquery";
import { localStorageDb } from "./utils/db";

export default new Vue({
  data() {
    const isSidebarCollapse = getDefaultValue("isSidebarCollapse");
    const layoutMode = getDefaultValue("layoutMode");
    const isNavmenuUniqueOpened = getDefaultValue("isNavmenuUniqueOpened");
    const navmenuMixTheme = getDefaultValue("navmenuMixTheme");
    return {
      state: {
        navmenuKey: Date.now(),
        // setting
        isSidebarCollapse,
        layoutMode,
        isNavmenuUniqueOpened,
        maxNavBarsNum: 0,
        isShowSearch: true,
        isShowFavorites: true,
        isShowSetting: true,
        isShowSettingTheme: true,
        settingThemeList: [],
        isShowSettingLanguage: true,
        isIconSubmenuOffset: true,
        navmenuMixTheme,
        inFullScreenNavTrigger: 0,
        isFullScreen: false
      },
      innerState: {
        navmenu: [],
        hlayoutNavLogo: "",
        vlayoutNavLogoFull: "",
        vlayoutNavLogoShort: "",
        include: undefined,
        routerViewKey: Date.now()
      },
      renderHeaderRightTools(h) {},
      renderSettingItems(h) {},
      renderNavFooter(h) {}
    };
  },
  watch: {
    "state.isSidebarCollapse": function () {
      localStorageDb.set("isSidebarCollapse", this.state.isSidebarCollapse);
    },
    "state.layoutMode": function () {
      localStorageDb.set("layoutMode", this.state.layoutMode);
    },
    "state.isNavmenuUniqueOpened": function () {
      localStorageDb.set("layoutMode", this.state.isNavmenuUniqueOpened);
    },
    "state.navmenuMixTheme": function () {
      localStorageDb.set("navmenuMixTheme", this.state.navmenuMixTheme);
    }
  },
  methods: {
    fullScreen(val) {
      this.state.isFullScreen = val ? true : false;
    },
    fullScreenNavTrigger() {
      this.state.inFullScreenNavTrigger = Date.now();
    },
    toggleSideBarCollapse(isSidebarCollapse) {
      this.state.isSidebarCollapse = isSidebarCollapse;
    },
    setNavmenuMixTheme(theme) {
      this.state.navmenuMixTheme = theme;

      if (!theme) {
        $(document.body).removeAttr("data-mix-nav-theme");
      } else {
        $(document.body).attr("data-mix-nav-theme", theme);
      }
    },
    refreshNavmenu() {
      this.state.navmenuKey = Date.now();
    },
    resetRouteViewKeepAlive() {
      this.innerState.include = [];
      this.$nextTick(() => {
        this.innerState.include = undefined;
      });
    },
    refreshRouteView() {
      this.innerState.routerViewKey = Date.now();
    }
  }
});
