import _ from "lodash";

class LocalStorage {
  constructor(key) {
    this.key = key;
  }
  read() {
    const value = localStorage.getItem(this.key);
    if (value === null) {
      return null;
    }
    return JSON.parse(value);
  }
  write(obj) {
    localStorage.setItem(this.key, JSON.stringify(obj));
  }
}

class LowSync {
  constructor(adapter) {
    this.data = null;
    if (adapter) {
      this.adapter = adapter;
    }
    else {
      throw new MissingAdapterError();
    }
  }
  read() {
    this.data = this.adapter.read();
  }
  write() {
    if (this.data !== null) {
      this.adapter.write(this.data);
    }
  }
}


class Factory {
  create(type, name) {
    this._db = this._create(type, name);
    return this;
  }

  operate(handler) {
    const data = this._read();
    handler(data);
    this._write(data);
  }

  /**
   * @param path -[必须]字段路径
   * @param default -[可选]默认值
   * @note:详见lodash.get
   */
  get(path, defaultValue) {
    const data = this._read();
    const ret = _.get(data, path);
    return ret !== undefined ? ret : defaultValue;
  }

  /**
   * @param path -[必须]字段路径
   * @param value -[可选]填充值
   * @note:详见lodash.set
   */
  set(path, value) {
    const data = this._read();
    this._write(_.set(data, path, value));
  }

  _create(type, name) {
    switch (type) {
      case "localStorage":
        return new LowSync(new LocalStorage(name));
    }
  }

  _read() {
    const db = this._db;
    db.read();
    return db.data || {};
  }

  _write(data) {
    const db = this._db;
    db.data = data;
    db.write();
  }
}

export const factory = new Factory();

let dbName = "omega_layout";
// fix: 开发环境有时候不同项目之间配置会串
// 借助 sessionStorage 当前页刷新时状态不变
if (process.env.NODE_ENV === "development") {
  let omega_layout_db = window.sessionStorage.getItem("omega_layout_db");
  if (!omega_layout_db || !window.localStorage.getItem(omega_layout_db)) {
    dbName += `_dev_${Date.now()}`;
    window.sessionStorage.setItem("omega_layout_db", dbName);
  } else {
    dbName = omega_layout_db;
  }
}

export const localStorageDb = factory.create("localStorage", dbName);
