export function hasProp(obj, key) {
  return Object.prototype.hasOwnProperty.call(obj, key);
}

let gid = 0;
/**
 * 获取全局的唯一ID
 *
 * @param prefix 前缀
 */
export function guid(prefix) {
  if (gid > 100000) {
    gid = 0;
  }
  return (prefix || "") + gid++;
}

/**
 * 生成局部的唯一ID
 */
export function genUid(prefix, { initNumber = 0 } = {}) {
  let id = initNumber;
  return function (prefixExt = "") {
    if (prefix) {
      return prefix + prefixExt + id++;
    }
    return prefixExt + id++;
  };
}

export function randomStr() {
  return guid("r") + Math.random().toString(16).slice(2);
}
/**
 * @param {Array} items
 * @param {Any} value 查找的value
 * @prop {Function} diffFn 对比判定函数
 * @prop {String} childKey
 * @prop {String} key
 */
export function find(
  items,
  value,
  { diffFn, childKey = "child", valueKey = "id" }
) {
  function isEqual(item) {
    return diffFn
      ? diffFn(item[valueKey], value, item)
      : hasProp(item, valueKey) && item[valueKey] === value;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item[childKey]) {
        const ret = loop(item[childKey]);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(items) || null;
}

/**
 * 获取多节点树，某一节点的路径节点集合
 *
 * @param {any} value 待对比的值
 * @param {array} nodes 结构参考 @/common/modules/config - navmenu
 * @param {function} diffFn 对比函数
 * @param {string} childKey 子节点集合的 key
 * @param {string} valueKey 节点对比 value 的 key
 */
export function getTreePathNodes(
  nodes,
  value,
  { diffFn, childKey, valueKey } = {}
) {
  function isSame(node) {
    return diffFn ? diffFn(node[valueKey], value) : node[valueKey] === value;
  }

  function getChild(node) {
    return node[childKey];
  }

  function loop(pathList, childNodes) {
    if (childNodes) {
      for (const node of childNodes) {
        if (isSame(node)) {
          return pathList.concat(node);
        }
        const ret = loop(pathList.concat(node), getChild(node));
        if (ret) {
          return ret;
        }
      }
    }
  }

  return loop([], nodes) || [];
}

export default {
  guid,
  genUid,
  randomStr,
  find,
  getTreePathNodes
};
