<template>
  <FrameLayout layoutMode="horizontal">
    <template v-slot:navmenu>
      <FrameNavmenu :navmenu="navmenu" mode="horizontal" />
    </template>
    <template v-slot:headerLeft>
      <slot name="header-left" />
    </template>
    <template v-slot:headerRight>
      <slot name="header-right" />
    </template>
    <template #insert>
      <div class="frame-breadcrumb-container">
        <slot name="before-breadcrumb" />
        <FrameBreadcrumb :navmenu="navmenu" />
        <slot name="after-breadcrumb" />
      </div>
      <FrameNavBars :navmenu="navmenu" />
    </template>
    <template v-slot:container>
      <FrameMainContainer />
    </template>
  </FrameLayout>
</template>
<script>
import FrameLayout from "../frame/FrameLayout.vue";
import FrameNavmenu from "../frame/FrameNavmenu.vue";
import FrameBreadcrumb from "../frame/FrameBreadcrumb.vue";
import FrameMainContainer from "../frame/FrameMainContainer.vue";
import FrameNavBars from "../frame/FrameNavBars.vue";

export default {
  name: "HLayout",
  props: {
    navmenu: Array
  },
  components: {
    FrameLayout,
    FrameNavmenu,
    FrameBreadcrumb,
    FrameMainContainer,
    FrameNavBars
  },
  data() {
    return {};
  }
};
</script>
<style lang="scss">
.frame-breadcrumb-container {
  flex: 1 0 500px;
  margin-right: -24px;
}
.frame-navbars {
  flex: 1;
}

.el-breadcrumb {
  display: inline-block;
}
</style>
