<template>
  <FrameLayout layoutMode="vertical" :class="[isSidebarCollapse ? 'frame-vertical-collapse' : '']" v-if="!isFullScreen">
    <template v-slot:navmenu>
      <slot name="before-navmenu" />
      <div class="frame-vertical-navmenu">
        <FrameNavmenu :class="{ 'frame-vertical-navmenu-hide': isShowNavmenuShadow }" :navmenu="navmenu"
          class="frame-vertical-navmenu-content" mode="vertical" :uniqueOpened="uniqueOpened"
          :collapse="isSidebarCollapse" :isIconSubmenuOffset="isIconSubmenuOffset" :key="navmenuKey" />
      </div>
      <slot name="after-navmenu" />
    </template>
    <template v-slot:headerLeft>
      <div class="frame-vertical-navmenu-switch">
        <OmegaIcon class="frame-vertical-navmenu-switch-icon icon-hover-normal icon-p6" :symbolId="iconSymbolId"
          @click="handlerClick" />
      </div>
      <div class="frame-breadcrumb-container">
        <FrameBreadcrumb :navmenu="navmenu" class="frame-breadcrumb" />
        <slot name="after-breadcrumb" />
      </div>
      <slot name="header-left" />
    </template>
    <template v-slot:headerRight>
      <slot name="header-right" />
    </template>
    <template #insert>
      <FrameNavBars :navmenu="navmenu" :max="maxNavBarsNum" />
    </template>
    <template v-slot:container>
      <FrameMainContainer />
    </template>
  </FrameLayout>
  <FullScreen layoutMode="vertical" :class="[isSidebarCollapse ? 'frame-vertical-collapse' : '']"
    :inFullScreenNavTrigger="inFullScreenNavTrigger" v-else>
    <template v-slot:navmenu>
      <slot name="before-navmenu" />
      <div class="frame-vertical-navmenu">
        <FrameNavmenu :class="{ 'frame-vertical-navmenu-hide': isShowNavmenuShadow }" :navmenu="navmenu"
          class="frame-vertical-navmenu-content" mode="vertical" :uniqueOpened="uniqueOpened"
          :collapse="isSidebarCollapse" :isIconSubmenuOffset="isIconSubmenuOffset" :key="navmenuKey" />
      </div>
      <slot name="after-navmenu" />
    </template>
    <template v-slot:container>
      <FrameMainContainer />
    </template>
  </FullScreen>
</template>
<script>
import FrameLayout from "../frame/FrameLayout.vue";
import FullScreen from "../frame/FullScreen.vue";
import FrameNavmenu from "../frame/FrameNavmenu.vue";
import FrameBreadcrumb from "../frame/FrameBreadcrumb.vue";
import FrameMainContainer from "../frame/FrameMainContainer.vue";
import FrameNavBars from "../frame/FrameNavBars";

import store from "../store";
import OmegaIcon from "@omega/icon";

export default {
  name: "VLayout",
  props: {
    navmenu: Array,
  },
  components: {
    FullScreen,
    OmegaIcon,
    FrameNavBars,
    FrameLayout,
    FrameNavmenu,
    FrameBreadcrumb,
    FrameMainContainer
  },
  computed: {
    navmenuKey() {
      return store.state.navmenuKey
    },
    isFullScreen() {
      return store.state.isFullScreen
    },
    inFullScreenNavTrigger() {
      return store.state.inFullScreenNavTrigger
    },
    isSidebarCollapse() {
      return store.state.isSidebarCollapse;
    },
    uniqueOpened() {
      return store.state.isNavmenuUniqueOpened;
    },
    isIconSubmenuOffset() {
      return store.state.isIconSubmenuOffset;
    },
    iconSymbolId() {
      if (store.state.isSidebarCollapse) {
        return "menu-fold";
      } else {
        return "menu-unfold";
      }
    },
    maxNavBarsNum() {
      return store.state.maxNavBarsNum;
    }
  },
  data() {
    return {
      isShowNavmenuShadow: false
    };
  },
  watch: {
    isSidebarCollapse() {
      this.isShowNavmenuShadow = true;
      setTimeout(() => {
        this.isShowNavmenuShadow = false;
      }, 100);
    }
  },
  methods: {
    handlerClick() {
      store.toggleSideBarCollapse(!this.isSidebarCollapse);
    }
  }
};
</script>
<style lang="scss" scoped>
.frame-vertical-navmenu {
  position: relative;
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;

  &-content {
    height: 100%;
  }

  &-switch {
    display: flex;
    align-items: center;
  }
}

.frame-vertical-navmenu-hide {
  z-index: -1;
}

.frame-vertical-collapse ::v-deep {
  .frame {
    &-main {
      margin-left: 64px;
    }

    &-navmenu {
      width: 64px;
    }
  }

  .frame-vertical-navmenu-switch-icon {
    transform: rotate(0);
  }
}

.frame-breadcrumb-container {
  display: flex;
  align-items: center;

  &>*:not(:last-child) {
    @include margin_right(J1);
  }
}
</style>
