<template>
  <div v-on="$listeners">
    <slot v-if="isUseFullSlot()" name="full" />
    <slot v-if="isUseShortSlot()" name="short" />
    <span v-if="!isUseFullSlot() && !isSidebarCollapse">{{ full }}</span>
    <span v-if="!isUseShortSlot() && isSidebarCollapse">{{ short }}</span>
  </div>
</template>

<script>
import store from "../store";

export default {
  name: "VLayoutNavBtn",
  props: {
    full: String,
    short: String
  },
  computed: {
    isSidebarCollapse() {
      return store.state.isSidebarCollapse;
    }
  },
  methods: {
    isUseFullSlot() {
      if (!this.full && !this.isSidebarCollapse) {
        return true;
      }
      return false;
    },
    isUseShortSlot() {
      if (!this.short && this.isSidebarCollapse) {
        return true;
      }
      return false;
    }
  }
};
</script>
