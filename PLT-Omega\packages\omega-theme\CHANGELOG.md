# @omega/theme

## 1.11.1

### Minor Changes

- [注意] feat: jquery依赖版本修改为\*

## 1.11.0

### Minor Changes

- [注意] feat: jquery依赖版本升级, ^1.12.4 -> ^3.7.1

## 1.10.0

- [注意] feature: 修改亮色皮肤下Sta4和Sta5的颜色以及暗色下Sta5的颜色为最新UI规范色

## 1.9.0

- feature: 将主题key值的初始化移动到beforeAppBoot中

## 1.8.4

- fix: 确保element-ui的样式在最前面，以确保后续样式可以正常覆盖其样式

## 1.8.3

- fix: tooltip 透明度添加

## 1.8.2

- feat: 优化 tooltip 在不同皮肤下的样式

## 1.8.1

- fix: 修复图标通用样式无效的问题，提升通用图标样式权重

## 1.8.0

- feat: 暴露方法支持设置默认值

## 1.7.2

- fix: 修复 1.6.4 版本在部分生产环境场景下无效的问题：修复 elementui 样式权重过高问题（诸如外部样式覆盖皮肤样式覆盖不了，全局样式无效等等问题）

## 1.7.1

- fix: 修复 themeLimits 为非必填项

## 1.7.0

- feat: 新增插件配置项 themeLimits ,限制应用使用的皮肤主题

## 1.6.4

- fix: 修复 elementui 样式权重过高问题（诸如外部样式覆盖皮肤样式覆盖不了，全局样式无效等等问题）

## 1.6.3

- fix: 兼容皮肤不存在时使用默认皮肤

## 1.6.2

- fix: 框架自定义 element-ui 样式无效修复

## 1.6.1

- fix: 样式设置调整至 beforeAppBoot

## 1.6.0

- feat: 优化 SCSS 函数构建方式减小打包体积
- feat: 新增深蓝色皮肤

## 1.5.0

- feat: 开发注册 elementui 皮肤接口 registerElementTheme 方法

## 1.4.1

- feat: esmodule 入口添加

## 1.4.0

- feat: 新增科技蓝皮肤 @周胜志

## 1.3.1

- fix: 滚动条样式丢失

## 1.3.0

- 83b8942: feat: vue-cli 5 根路径静态资源适配方法添加：background_image_static background_static
  feat: tailwind & scss 融合，全局变量采用 css 变量
  feat: 支持自定义暗色和亮色皮肤

## 1.2.4

- fix: 修复通用样式类（common.scss） .vertical-middle .horizontal-middle 无效的问题
- feat: 新增 flex 垂直水平居中通用样式类

## 1.2.3

- d402c3b: fix: loading 置于最顶层

## 1.2.2

- [2022-04-25] fix: 修复 loading 遮不住 dialog 问题

## 1.2.1

- [2022-04-24] 插件机制适配

## 1.2.0

- [2022-04-20] 换肤方案升级，三方组件库按换肤标准实现

## 1.1.2

- [2022-04-14] fix: tailwind 换肤无效

## 1.1.1

- [2022-04-14] fix: 代码错误

## 1.1.0

- [2022-04-13] feat: 新插件机制适配

## 1.0.2

- 708e80e: feat(omega-theme): tailwind css 变量收入 theme 包

## 1.0.1

- fix: 修复 element tooptip 暗色模式样式问题
