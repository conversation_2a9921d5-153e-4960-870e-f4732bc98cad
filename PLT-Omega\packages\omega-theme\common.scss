/*布局类的通用样式*/
.no-overflow {
  overflow: hidden;
}
.relative {
  position: relative;
}
.absolute {
  position: absolute;
}

.border-box {
  box-sizing: border-box;
}

.fullfilled {
  width: 100%;
  height: 100%;
}

.fullwidth {
  width: 100%;
}

.fullheight {
  height: 100%;
}

.inline {
  display: inline;
}
.inline-block {
  display: inline-block;
}
.not-in {
  display: none;
}

/*清除浮动*/
.clearfix {
  zoom: 1;
  &:after {
    content: " ";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}

.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}
.text-center {
  text-align: center;
}

.text-middle {
  vertical-align: middle;
}
.text-top {
  vertical-align: top;
}
.text-bottom {
  vertical-align: bottom;
}

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.nowrap {
  white-space: nowrap;
}
.fr,
.rfloat {
  float: right;
}
.fl,
.lfloat {
  float: left;
}
.middle-flex {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.vertical-middle {
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.horizontal-middle {
  display: flex;
  align-items: center;
  flex-direction: column;
}

/*margin padding快捷写法*/
/* margin */
.no-margin,
.margin0 {
  margin: 0;
}

/* padding */
.no-padding,
.padding0 {
  padding: 0;
}

$maxMP: 35;
@each $name, $prop, $direct in (m, margin), (mt, margin, top), (mr, margin, right), (mb, margin, bottom),
  (ml, margin, left), (p, padding), (pt, padding, top), (pr, padding, right), (pb, padding, bottom), (pl, padding, left)
{
  $i: 0;
  @while $i <= $maxMP {
    .#{$name}#{$i} {
      @if $direct != null {
        #{$prop}-#{$direct}: #{$i}px !important;
      } @else {
        #{$prop}: #{$i}px !important;
      }
    }
    $i: $i + 5;
  }
}

$maxMP: 32;
@each $name, $prop, $direct in (m, margin), (mt, margin, top), (mr, margin, right), (mb, margin, bottom),
  (ml, margin, left), (p, padding), (pt, padding, top), (pr, padding, right), (pb, padding, bottom), (pl, padding, left)
{
  $i: 8;
  @while $i <= $maxMP {
    .#{$name}#{$i} {
      @if $direct != null {
        #{$prop}-#{$direct}: #{$i}px !important;
      } @else {
        #{$prop}: #{$i}px !important;
      }
    }
    $i: $i + 8;
  }
}

$j: 15;
@while $j <= 50 {
  .lh#{$j} {
    line-height: #{$j}px;
  }
  $j: $j + 5;
}

$k: 12;
@while $k <= 40 {
  .fs#{$k} {
    font-size: #{$k}px !important;
  }
  $k: $k + 2;
}

$l: 20;
@while $l <= 600 {
  .w#{$l} {
    width: #{$l}px !important;
  }
  .h#{$l} {
    height: #{$l}px !important;
  }
  $l: $l + 20;
}
