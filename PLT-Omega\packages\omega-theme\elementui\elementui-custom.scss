/*一些全局性的对elementui的css扩展*/
@import "../resource/_handle.scss";

// HACK 修复tooltip样式
[data-theme="dark"] .el-tooltip__popper,
[data-theme="blue"] .el-tooltip__popper,
[data-theme="bluex"] .el-tooltip__popper {
  background: rgb(255, 255, 255, 0.8) !important;
  color: #333 !important;

  & .popper__arrow::after {
    border-right-color: rgb(255, 255, 255, 0.8) !important;
  }

  &[x-placement^="right"] .popper__arrow {
    border-right-color: rgb(255, 255, 255, 0.8) !important;
  }
}

[data-theme="light"] .el-tooltip__popper {
  background: rgb(51, 51, 51, 0.8) !important;
  color: #fff !important;

  & .popper__arrow::after {
    border-right-color: rgb(51, 51, 51, 0.8) !important;
  }

  &[x-placement^="right"] .popper__arrow {
    border-right-color: rgb(51, 51, 51, 0.8) !important;
  }
}
