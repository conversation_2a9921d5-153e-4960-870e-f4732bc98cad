// 定制的 elment-ui 样式
@import "./elementui/elementui-custom.scss";
// elemnt修复的样式
@import "./elementui/elementui-fix.scss";
// 通用样式类
@import "./common.scss";

@import "./resource/_handle.scss";
@import "./resource/_icon.scss";
/* body布局, 字体颜色等样式*/
body {
  @include font_color(T1);
  @include background(BG);
  background-size: cover;
  scroll-behavior: smooth;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
// 滚动槽
::-webkit-scrollbar-track {
  background: transparent;
}
// 滚动条滑块
::-webkit-scrollbar-thumb {
  border-radius: 6px;
  right: 4px;
  background: var(--SCR);
  box-shadow: inset 0 0 2px var(--SCR);
}
// 隐藏角落方块
::-webkit-scrollbar-corner {
  background-color: transparent;
}

// nprogress 适配
#nprogress .bar {
  @include background_color(ZS);
  z-index: 9001;
}
