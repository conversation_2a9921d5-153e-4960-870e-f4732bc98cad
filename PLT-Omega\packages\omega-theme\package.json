{"name": "@omega/theme", "version": "1.11.1", "description": "omega-core theme", "main": "index.js", "module": "./index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "release": "npm publish --registry http://***********:4873"}, "repository": {"type": "git", "url": "ssh://cetsoft-svr1:22/Platforms/PLT-Matterhorn/_git/omega-theme"}, "keywords": ["omega-core"], "peerDependencies": {"sass": "*", "axios": "*", "jquery": "*"}, "author": "wwen", "license": "ISC"}