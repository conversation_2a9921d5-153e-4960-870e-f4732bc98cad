
import { store } from "./store.js";
import { themeMap } from "./theme.js";

const DOM_ATTR = "data-theme";


function chekThemeValid(theme, themeLimits) {
  if (!themeMap.has(theme)) {
    throw new Error(
      `defaultActive 默认皮肤${theme}不支持，支持的皮肤: ${[...themeMap.keys()].join(
        ","
      )}`
    );
  }

  if (themeLimits.length && !themeLimits.includes(theme)) {
    throw new Error(
      `themeLimits 皮肤限制 ${themeLimits.join(',')}, 当前皮肤${theme}不在限制皮肤内！`
    );
  }
}

let hasSetDefaultTheme = false;
export function setDefaultTheme(theme) {
  chekThemeValid(theme, setDefaultTheme._themeLimits);

  if (hasSetDefaultTheme) {
    store.setTheme(theme);
    setTimeout(() => {
      window.location.reload();
    }, 0);
  }
}
export class OmegaThemePlugin {
  static registerInfo;
  static register({ defaultActive = "light", themeLimits = [] } = {}) {
    OmegaThemePlugin.registerInfo = { defaultActive, themeLimits }
  }

  async beforeAppBoot() {
    const { defaultActive, themeLimits } = OmegaThemePlugin.registerInfo
    chekThemeValid(defaultActive, themeLimits);

    setDefaultTheme._themeLimits = themeLimits;

    let active = store.getTheme();

    if (!active || !themeMap.get(active) || themeLimits.length && !themeLimits.includes(active)) {
      hasSetDefaultTheme = true;
      active = defaultActive;
      store.setTheme(active);
    }
    // const active = store.getTheme();

    window.document.documentElement.setAttribute(DOM_ATTR, active);
    // tailwindcss dark mode strategy: class
    window.document.documentElement.classList.add(active)

    await themeMap.get(active)();
    // FIXME: 待优化，应该为获取DOM最后一次插入style/link的元素
    // HACK: 临时方案：因为目前样式都是head.appendChild操作，所以取head最后一个link(生产环境)/style(开发环境)）元素即可
    // 原因：head.appendChild插入导致皮肤样式覆盖优先权被默认提高了覆盖了我们的部分全局调整的样式

    function getHeadLastStyleOrLinkDOM() {
      let currentEle = document.head.lastElementChild;

      while (currentEle) {
        const tag = currentEle.tagName.toLowerCase()
        if (["style", "link"].includes(tag)) {
          return currentEle;
        }
        else {
          currentEle = currentEle.previousElementSibling;
        }
      }
    }

    const elementUIStyleOrLink = getHeadLastStyleOrLinkDOM();

    document.head.insertBefore(elementUIStyleOrLink, document.head.firstElementChild);

    // HACK: 适配微前端框架 确保element-ui的样式在最前面，以确保后续样式可以正常覆盖其样式
    var observer = new MutationObserver(function (mutationsList, observer) {
      if (document.head.firstElementChild === elementUIStyleOrLink) {
        return;
      }
      else {
        document.head.insertBefore(elementUIStyleOrLink, document.head.firstElementChild);
      }
    });

    // 开始观察目标节点
    observer.observe(document.head, { childList: true });
  }
}
