// 图标常用样式类封装
@import "./_handle.scss";
.icon-hover {
  cursor: pointer;
  &:hover {
    @include background_color(BG2, !important);
  }
  &:focus {
    @include background_color(BG3, !important);
  }
}
.icon-active {
  @include background_color(ZS, !important);
}

.icon-hover-normal {
  @extend .icon-hover;
  @extend .icon-size-I2;
  @extend .icon-p5;
}

.icon-size-I1 {
  width: mh-get(I1) !important;
  height: mh-get(I1) !important;
}
.icon-size-I2 {
  width: mh-get(I2) !important;
  height: mh-get(I2) !important;
}
.icon-size-I3 {
  width: mh-get(I3) !important;
  height: mh-get(I3) !important;
}
.icon-size-I4 {
  width: mh-get(I4) !important;
  height: mh-get(I4) !important;
}
.icon-size-I5 {
  width: mh-get(I5) !important;
  height: mh-get(I5) !important;
}

.icon-p1 {
  padding: 1px !important;
}
.icon-p2 {
  padding: 2px !important;
}
.icon-p3 {
  padding: 3px !important;
}
.icon-p4 {
  padding: 4px !important;
}
.icon-p5 {
  padding: 5px !important;
}
.icon-p6 {
  padding: 6px !important;
}
.icon-p7 {
  padding: 7px !important;
}
.icon-p8 {
  padding: 8px !important;
}
