@use "sass:map";

$omega_theme_extend: () !default;

$theme_base: (
  // >>>> 品牌色
  // 主色
  ZS: var(--ZS),
  // 辅助色1
  F1: var(--F1),
  // 辅助色2
  F2: var(--F2),
  // 辅助色3
  // F3: var(--F3),
  // >>>> 状态色
  // 成功
  Sta1: var(--Sta1),
  // 警告
  Sta2: var(--Sta2),
  // 危险
  Sta3: var(--Sta3),
  // 一般
  Sta4: var(--Sta4),
  // 次要
  Sta5: var(--Sta5),
  // 状态
  Sta6: var(--Sta6),
  // >>>> 文字色
  // 主要
  T1: var(--T1),
  // 常规
  T2: var(--T2),
  // 次要
  T3: var(--T3),
  // 占位
  T4: var(--T4),
  // 带背景的文字色
  T5: var(--T5),
  // >>>> 边框色
  // 主要
  B1: var(--B1),
  // 次要
  B2: var(--B2),
  // >>>> 背景色
  BG: var(--BG),
  // 主要
  BG1: var(--BG1),
  // 滑入
  BG2: var(--BG2),
  // 点击
  BG3: var(--BG3),
  // 选中
  BG4: var(--BG4)
);

$themes_frame: (
  "light": $theme_base,
  "dark": $theme_base,
  "blue": $theme_base,
  "bluex": $theme_base
);
$themes: map.deep-merge($themes_frame, $omega_theme_extend);
