@tailwind base;
@tailwind components;
@tailwind utilities;
:root {
  /* 间距 */
  --J1: 8px;
  --J2: 16px;
  --J3: 24px;
  --J4: 32px;

  /* 图标 */
  --I1: 24px;
  --I2: 32px;
  --I3: 40px;
  --I4: 64px;
  --I5: 96px;

  /* 字体 */
  --H: 28px;
  --H1: 22px;
  --H2: 18px;
  --H3: 16px;
  --Aa: 14px;
  --Ab: 12px;

  /* 强投影 */
  --S1: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  /* 浅色投影 */
  --S2: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  /* 滚动条 */
  --SCR: rgba(120, 120, 120, 0.3);
}

[data-theme="light"] {
  /* 主色 */
  --ZS: #29b061;
  /* 辅助色1 */
  --F1: #1a7940;
  /* 辅助色2 */
  --F2: #d53b3b;
  /* 成功 */
  --Sta1: #29b061;
  /* 警告 */
  --Sta2: #fcb92c;
  /* 危险 */
  --Sta3: #f95e5a;
  /* 一般 */
  --Sta4: #01a2ff;
  /* 次要 */
  --Sta5: #3166ef;
  /* 状态 */
  --Sta6: #d7d7d7;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #333333;
  /* 常规 */
  --T2: #666666;
  /* 次要 */
  --T3: #989898;
  /* 占位 */
  --T4: #cccccc;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #d7d7d7;
  /* 次要 */
  --B2: #f0f0f0;
  /* >>>> 背景色 */
  --BG: #f0f0f0;
  /* 主要 */
  --BG1: #ffffff;
  /* 滑入 */
  --BG2: #e6e6e6;
  /* 点击 */
  --BG3: #d7d7d7;
  /* 选中 */
  --BG4: #eaf7ef;
  /* 滚动条 */
  --SCR: rgba(102, 102, 102, 0.3);
}

[data-theme="dark"] {
  --ZS: #0d86ff;
  --F1: #1375da;
  --F2: #d53c40;
  /* 成功 */
  --Sta1: #0dff86;
  /* 警告 */
  --Sta2: #ff782b;
  /* 危险 */
  --Sta3: #ff3f3f;
  /* 一般 */
  --Sta4: #15e3e3;
  /* 次要 */
  --Sta5: #5e77ff;
  /* 状态 */
  --Sta6: #eef0f2;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #f0f1f2;
  /* 常规 */
  --T2: #e6e8ea;
  /* 次要 */
  --T3: #ced2d6;
  /* 占位 */
  --T4: #b1b5b8;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #3d4251;
  /* 次要 */
  --B2: #3a506b;
  /* >>>> 背景色 */
  --BG: #16191a;
  /* 主要 */
  --BG1: #2b3244;
  /* 滑入 */
  --BG2: #363d50;
  /* 点击 */
  --BG3: #586073;
  /* 选中 */
  --BG4: #283a57;
  /* 滚动条 */
  --SCR: rgba(230, 232, 234, 0.3);
}

[data-theme="blue"] {
  --ZS: #006aff;
  --F1: #3072d5;
  --F2: #e64a4a;
  /* 成功 */
  --Sta1: #95f204;
  /* 警告 */
  --Sta2: #ffd35f;
  /* 危险 */
  --Sta3: #f95e5a;
  /* 一般 */
  --Sta4: #2b71c3;
  /* 次要 */
  --Sta5: #7eb2ee;
  /* 状态 */
  --Sta6: #d7d7d7;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #ddeeff;
  /* 常规 */
  --T2: #ddeeff;
  /* 次要 */
  --T3: #8cb9e9;
  /* 占位 */
  --T4: #8cb9e9;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #104da2;
  /* 次要 */
  --B2: #3072d5;
  /* >>>> 背景色 */
  --BG: #0b336f;
  /* 主要 */
  --BG1: #0e408c;
  /* 滑入 */
  --BG2: rgba(38, 87, 156, 0.7);
  /* 点击 */
  --BG3: rgba(48, 114, 213, 0.5);
  /* 选中 */
  --BG4: rgba(33, 88, 169, 0.3);
  /* 滚动条 */
  --SCR: rgba(180, 180, 180, 0.3);
}

[data-theme="bluex"] {
  --ZS: #0d86ff;
  --F1: #56aaff;
  --F2: #ff5c5c;
  /* 成功 */
  --Sta1: #29b061;
  /* 警告 */
  --Sta2: #ff842b;
  /* 危险 */
  --Sta3: #ff3f3f;
  /* 一般 */
  --Sta4: #0d86ff;
  /* 次要 */
  --Sta5: #213566;
  /* 状态 */
  --Sta6: #1dd97f;
  /* >>>> 文字色 */
  /* 主要 */
  --T1: #f0f1f2;
  /* 常规 */
  --T2: #e6e8ea;
  /* 次要 */
  --T3: #9096a8;
  /* 占位 */
  --T4: #646d89;
  /* 带背景的文字色 */
  --T5: #ffffff;
  /* >>>> 边框色 */
  /* 主要 */
  --B1: #515a7a;
  /* 次要 */
  --B2: #414b6e;
  /* >>>> 背景色 */
  --BG: #213566;
  /* 主要 */
  --BG1: #0e1b47;
  /* 滑入 */
  --BG2: #1f2b54;
  /* 点击 */
  --BG3: #334572;
  /* 选中 */
  --BG4: #0e2659;
  /* 滚动条 */
  --SCR: rgba(180, 180, 180, 0.3);
}

@layer base {
  /* https://tailwindcss.com/docs/preflight#border-styles-are-reset-globally */
  *,
  ::before,
  ::after {
    border-width: 0;
    border-style: solid;
    border-color: theme("borderColor.DEFAULT", currentColor);
  }
}
