export const themeMap = new Map([
  ["dark", () => import("./elementui/dark/theme/index.css")],
  ["light", () => import("./elementui/light/theme/index.css")],
  ["blue", () => import("./elementui/blue/theme/index.css")],
  ["bluex", () => import("./elementui/bluex/theme/index.css")],
]);

// 示例
//  registerElementTheme(
//    "blue",
//    () => import("./elementui/blue/theme/index.css")
//  ),
export function registerElementTheme(name, theme) {
  themeMap.set(name, theme);
};