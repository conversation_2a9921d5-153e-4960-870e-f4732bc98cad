import $ from "jquery";

/**
 * 添加 link 标签，引入css
 */
export function loadCss(url) {
  const linkDOM = document.createElement("link");
  linkDOM.type = "text/css";
  linkDOM.rel = "stylesheet";
  linkDOM.href = url;
  // fix: 降低基础样式的优先级，保证在其他地方可以100%覆盖
  $(document.head).prepend(linkDOM);
  return new Promise(function (resolve, reject) {
    linkDOM.onload = resolve;
    linkDOM.onerror = reject;
  });
}
/**
 * 执行js脚本文件
 */
export function loadScript(url) {
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.src = url;
  document.body.appendChild(script);
  return new Promise(function (resolve, reject) {
    script.onload = resolve;
    script.onerror = reject;
  });
}
