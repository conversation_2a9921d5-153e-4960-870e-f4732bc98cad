import vToolTip from "./directives/v-tooltip.jsx";

import {
  OmegaDialog,
  showOmegaDialog,
  showAsyncOmegaDialog
} from "./omega-dialog";
import OmegaRender from "./omega-render/index.jsx";

export { OmegaDialog, OmegaRender, showOmegaDialog, showAsyncOmegaDialog };

const components = [OmegaDialog, OmegaRender];
export default {
  install(Vue) {
    Vue.directive("tooltip", vToolTip);

    components.forEach(component => {
      Vue.component(component.name, component);
    });
  }
};
