import Vue from "vue";
import nprogress from "nprogress";

function createEvent() {
  return {
    _confirm_cbs: [],
    _cancel_cbs: [],
    on(type, cb) {
      switch (type) {
        case "confirm":
          this._confirm_cbs.push(cb);
          break;
        case "cancel":
          this._cancel_cbs.push(cb);
          break;
        default:
          throw new Error(type, "仅支持 confirm/cancel");
      }
    },
    _emit(type, payload) {
      switch (type) {
        case "confirm":
          this._confirm_cbs.forEach(cb => cb(payload));
          break;
        case "cancel":
          this._cancel_cbs.forEach(cb => cb(payload));
          break;
        default:
          throw new Error(type, "仅支持 confirm/cancel");
      }
    }
  };
}

const util = {
  isPromise(obj) {
    return (
      !!obj &&
      (typeof obj === "object" || typeof obj === "function") &&
      typeof obj.then === "function"
    );
  },
  isAsyncComponent(module) {
    return util.isPromise(module);
  }
};

/**
 * 显示编辑弹框
 * @param {VueOptions} dialogOption 弹框组件
 * @param {attrs} attrs 传递给弹框组件的配置
 */
export const showOmegaDialog = function (dialogOption, attrs = {}) {
  const Dialog = Vue.extend(dialogOption);
  const vm = new Dialog({
    propsData: attrs
  });

  vm.$mount();
  window.document.body.appendChild(vm.$el);
  const remove = () => {
    vm.$destroy();
    window.document.body.removeChild(vm.$el);
  };

  const event = createEvent();
  vm.$on("OmegaDialogConfirm", payload => {
    remove();
    event._emit("confirm", payload);
  });

  vm.$on("OmegaDialogCancel", () => {
    remove();
    event._emit("cancel");
  });

  return event;
};

/**
 * 显示异步编辑弹框
 * @param {VueOptions} DialogOption 弹框组件
 * @param {attrs} attrs 传递给弹框组件的配置
 */
export const showAsyncOmegaDialog = async function (modulePromise, attrs = {}) {
  nprogress.set(0.4);
  const { default: dialogOption } = await modulePromise;
  nprogress.done();
  return showOmegaDialog(dialogOption, attrs);
};
