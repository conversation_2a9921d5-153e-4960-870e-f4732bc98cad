export default {
  name: "<PERSON><PERSON><PERSON>",
  props: {
    render: {
      require: true,
      type: [Function, Object]
    }
  },
  render(h) {
    const render = this.$props.render;
    if (_.isFunction(render)) {
      let child = render(h);
      if (Array.isArray(child)) {
        return <div>{child}</div>;
      } else {
        return child;
      }
    } else {
      return render;
    }
  }
};
