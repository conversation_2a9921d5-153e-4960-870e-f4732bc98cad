
user  nginx;
worker_processes  1;

error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
                debug '[$time_local] "$request" $remote_addr $status '
                      '$proxy_pass $upstream_addr $upstream_status ';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    keepalive_timeout  65;

    #gzip  on;

    #include /etc/nginx/conf.d/*.conf;
	
	server {
		#定义网站的ip 端口号
        listen       80;
        server_name  localhost;

        #charset koi8-r;
        charset utf-8;
        charset_types text/xml text/plain text/vnd.wap.wml application/javascript application/rss+xml text/css;
        # 修复安全漏洞：CVE-2016-2183
        ssl_ciphers HIGH:!aNULL:!MD5:!3DES;
        #access_log  logs/host.access.log  main;
        #前端部署静态资源服务器
        location / {
            root   /usr/share/nginx/html;
            index  index.html index.htm index.php;
            client_max_body_size 20m;
        }
        #网关
        location /gateway {
            root   /usr/share/nginx/html;
            index  index.html index.htm index.php;
            proxy_pass http://gateway:4001/;
            proxy_redirect default;
            client_max_body_size 1000m;
        }
        #权限服务
        location /auth {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/cloud-auth-service/cloud/api/auth;
            proxy_redirect default;
            client_max_body_size 1000m;
        }
        #模型
        location /model-meta {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/model-service/model-meta/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        location /model {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/model-service/model/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        # 设备数据服务
        location /devicedata {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/device-data-service/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        # 消息通知服务
        location /messageServer {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://notice:5070/;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
        }
        # 报表服务
        location /ureport {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/mreport-service/ureport/;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }
        # 文件服务
        location /filemanager {
            root html;
            index index.html index.htm index.php;
            proxy_pass http://gateway:4001/filemanager-service/filemanager;
            proxy_redirect default;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header REMOTE-HOST $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        }

        error_page   500 502 503 504  /50x.html;
        location = /50x.html {
            root   /usr/share/nginx/html;
        }
    }
}
