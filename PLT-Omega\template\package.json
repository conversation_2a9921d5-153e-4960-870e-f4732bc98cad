{"name": "omega-tpl", "version": "1.1.26", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "inspect": "vue-cli-service inspect --mode production >> inspect.js", "build:report": "vue-cli-service build --report", "update:omega": "npm update @omega/app @omega/auth @omega/layout @omega/http @omega/i18n @omega/theme @omega/widget @omega/icon @omega/cli-devserver cet-chart cet-common"}, "devDependencies": {"@babel/core": "^7.11.0", "@babel/eslint-parser": "^7.16.0", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-service": "~5.0.8", "compression-webpack-plugin": "^10.0.0", "core-js": "^3.36.0", "css-loader": "3.6.0", "eslint": "^8.11.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-vue": "^9.3.0", "postcss-import": "^14.1.0", "prettier": "^2.4.1", "sass": "1.77.6", "sass-loader": "^13.0.2", "svg-sprite-loader": "^6.0.11", "tailwindcss": "^3.1.6", "vue-demi": "^0.13.6", "vue-eslint-parser": "^8.0.1", "vue-loader": "15.10.0"}, "dependencies": {"@omega/tracking": "^1.0.0", "@omega/admin": "^1.18.1", "@omega/app": "^1.8.0", "@omega/auth": "^1.16.0", "@omega/cli-codestd": "0.0.2", "@omega/cli-devops": "^1.4.5", "@omega/cli-devserver": "^1.2.1", "@omega/dashboard": "^1.2.11", "@omega/http": "^1.8.0", "@omega/i18n": "^1.5.1", "@omega/icon": "^1.28.0", "@omega/layout": "^1.11.1", "@omega/theme": "^1.8.4", "@omega/trend": "^1.2.13", "@omega/weather": "^1.0.2", "@omega/widget": "^1.0.3", "cet-chart": "^1.6.0", "cet-common": "^1.4.1", "element-ui": "^2.15.14", "jquery": "^3.7.1", "lodash": "^4.17.21", "moment": "^2.29.1", "nprogress": "^0.2.0", "vue": "^2.7.8", "vue-router": "^3.5.3", "vuex": "^3.5.1", "@babel/helper-validator-identifier": "^7.19.1", "cet-graph": "^1.3.27"}}