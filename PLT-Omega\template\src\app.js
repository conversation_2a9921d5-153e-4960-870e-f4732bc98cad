/*
 * @Author: your name
 * @Date: 2022-04-06 16:16:36
 * @LastEditTime: 2022-04-14 15:29:15
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\template\src\main.js
 */
import omegaApp from "@omega/app";
import "./omega/index";
import "./resources/index.css";
import "./resources/index.scss";
import "./icons/index.js";

import Vue from "vue";

import lodash from "lodash";
import moment from "moment";
import ElementUI from "element-ui";

import routerOption from "./router";
import storeOption from "./store";

import config from "./config/config";
import enums from "./config/enums";

import customApi from "./api/custom";
import CetCommon from "cet-common";
import CetChart, { registerTheme } from "cet-chart";

Vue.use(CetCommon, {
  api: customApi,
  CetDialog: {
    isDraggable: false
  }
});
Vue.use(CetChart, {
  // themeConf: {
  // backgroundColor: "#000"
  // }
});

Vue.config.productionTip = false;

Vue.prototype.$enums = enums;

// 工具库
Vue.prototype._ = lodash;
Vue.prototype.$moment = moment;

// ElementUI
Vue.use(ElementUI, { size: "small" });

//注册dashboard预定义组件
// import { registerComponent } from "@omega/dashboard";
import componentList from "./plugins/dashboard/componentList";
// registerComponent(componentList);

//引入趋势曲线组件
import OmegaTend from "@omega/trend";
Vue.use(OmegaTend);

// 启动
omegaApp.createApp({
  el: "#app",
  config,
  routerOption,
  storeOption
});