/*
 * @Author: your name
 * @Date: 2022-04-06 16:16:36
 * @LastEditTime: 2023-05-04 17:46:34
 * @LastEditors: 'zhuyunxia' <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\template\src\config\config.js
 */
export default {
  // 导航
  navmenu: [
    {
      label: $T("饼图"),
      type: "menuItem",
      location: "/demo",
      permission: "p.demo.change"
    },
    {
      label: $T("图形"),
      type: "subMenu",
      icon: "chart-pie",
      subMenuList: [
        {
          label: $T("趋势曲线"),
          type: "menuItem",
          location: "/trenddemo",
          permission: "p.trenddemo"
        }
      ]
    },
    {
      label: $T("业务模板"),
      type: "subMenu",
      icon: "data-screen-lin",
      subMenuList: [
        {
          label: $T("PecReport报表"),
          type: "menuItem",
          location: "/pecreport",
          permission: "p.pecreport"
        },
        {
          label: $T("用户中心"),
          type: "menuItem",
          location: "/usercenter",
          permission: "p.usercenter"
        }
      ]
    },
    {
      label: $T("UI组件调试"),
      type: "subMenu",
      icon: "tool-lin",
      subMenuList: [
        {
          label: $T("日期组件"),
          type: "menuItem",
          location: "/datatime",
          permission: "p.datatime"
        },
        {
          label: $T("table组件"),
          type: "menuItem",
          location: "/CetTable",
          permission: "p.datatime"
        }
      ]
    },
    {
      label: $T("性能测试"),
      type: "subMenu",
      icon: "trend-chart",
      subMenuList: [
        {
          label: $T("表格图标渲染性能"),
          type: "menuItem",
          location: "/grid",
          permission: "p.grid.change"
        }
      ]
    },
    {
      label: $T("系统集成"),
      type: "subMenu",
      icon: "capacity-manage-lin",
      subMenuList: [
        {
          label: $T("Iframe集成"),
          type: "menuItem",
          location: "/homepage",
          permission: "p.demo.change"
        },
        {
          label: $T("苏打平台"),
          type: "menuItem",
          location: "/soda",
          permission: "p.demo.change"
        },
        {
          label: $T("跳转测试"),
          type: "menuItem",
          location: "/sodaTest",
          permission: "p.demo.change"
        }
      ]
    },
    {
      label: $T("百度地图"),
      type: "subMenu",
      icon: "local",
      subMenuList: [
        {
          label: $T("百度地图选点"),
          type: "menuItem",
          location: "/selectMapDemo",
          permission: "p.trenddemo"
        }
      ]
    },
    {
      label: $T("仪表盘"),
      type: "subMenu",
      icon: "battery-list-card-lin",
      subMenuList: [
        {
          label: $T("Dashboard"),
          type: "menuItem",
          location: "/dashboard",
          permission: "p.dashboard"
        },
        {
          label: $T("Dashboard页面模式1无标题"),
          type: "menuItem",
          location: "/fullscreendashboard/测试/1?autoHeight=1",
          permission: "p.dashboard.11"
        },
        {
          label: $T("Dashboard页面普通模式"),
          type: "menuItem",
          location: "/fullscreendashboard/测试/0?autoHeight=0",
          permission: "p.dashboard.00"
        },
        {
          label: $T("Dashboard页面自适应高度"),
          type: "menuItem",
          location: "/fullscreendashboard/测试/0?autoHeight=1",
          permission: "p.dashboard.22"
        }
      ]
    },
    {
      label: $T("框架验证"),
      type: "subMenu",
      icon: "function",
      subMenuList: [
        {
          label: $T("tailwind功能验证"),
          type: "menuItem",
          location: "/tailwind",
          permission: "p.tailwind"
        }
      ]
    }
  ]
};
