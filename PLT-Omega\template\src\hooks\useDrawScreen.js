import { ref, onBeforeUnmount } from "vue";

export default function useIndex() {
  // * 指向最外层容器
  const appRef = ref();
  // * 定时函数
  const timer = ref(0);
  // * 默认缩放值
  const scale = ref({
    width: 1920,
    height: 1080
  });
  // * 设计稿尺寸（px）
  const baseWidth = 1920;
  const baseHeight = 1080;
  // * 需保持的比例（默认1.77778）
  const baseProportion = Math.round((baseWidth / baseHeight) * 100000) / 100000;
  const delay = 200;

  const initScreen = () => {
    const screenWidth = window.screen.width;
    const screenHeight = window.screen.height;

    scale.value.width = screenWidth;
    scale.value.height = screenHeight;

    appRef.value.style.width = `${baseWidth}px`;
    appRef.value.style.height = `${baseHeight}px`;

    appRef.value.style.overflow = `hidden`;
    appRef.value.style.position = `absolute`;
    appRef.value.style.top = `50%`;
    appRef.value.style.left = `50%`;
    appRef.value.style.transform = `translate(-50%, -50%)`;
    appRef.value.style.transformOrigin = `left top`;
    appRef.value.style.transitionProperty = `opacity, transform`;
    appRef.value.style.transitionTimingFunction = `ease`;
    appRef.value.style.transitionDuration = `300ms`;
  };

  const calcRate = () => {
    // 当前宽高比
    const currentRate =
      Math.round((window.innerWidth / window.innerHeight) * 100000) / 100000;
    if (appRef.value) {
      const isLandscape = currentRate > baseProportion;
      const newScale = isLandscape
        ? {
            width:
              Math.round(
                ((window.innerHeight * baseProportion) / baseWidth) * 100000
              ) / 100000,
            height:
              Math.round((window.innerHeight / baseHeight) * 100000) / 100000
          }
        : {
            height:
              Math.round(
                (window.innerWidth / baseProportion / baseHeight) * 100000
              ) / 100000,
            width: Math.round((window.innerWidth / baseWidth) * 100000) / 100000
          };

      scale.value = newScale;
      appRef.value.style.transform = `scale(${newScale.width}, ${newScale.height}) translate(-50%, -50%)`;
    }
  };

  const resize = () => {
    clearTimeout(timer.value);
    timer.value = setTimeout(() => {
      requestAnimationFrame(calcRate);
    }, delay);
  };

  // 改变窗口大小重新绘制
  const windowDraw = () => {
    window.addEventListener("resize", resize);

    // 组件销毁时清理定时器和事件监听
    onBeforeUnmount(() => {
      clearTimeout(timer.value);
      window.removeEventListener("resize", resize);
    });
  };

  return {
    appRef,
    initScreen,
    calcRate,
    windowDraw
  };
}
