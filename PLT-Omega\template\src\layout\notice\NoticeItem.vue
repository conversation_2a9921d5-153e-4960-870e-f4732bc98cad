<template>
  <div class="notice-item">
    <div class="notice-item-left">
      <el-tag class="notice-item-tag" :type="tag.type" effect="plain">
        {{ tag.label }}
      </el-tag>
    </div>
    <div class="notice-item-main">
      <div class="notice-item-main-head">
        <div class="notice-item-time">
          {{ time }}
        </div>
        <div class="notice-item-operate">
          <el-link
            class="notice-item-operate-btn"
            type="primary"
            @click="evGotoClick(item)"
          >
            {{ $T("转到") }}
          </el-link>
          <el-link
            class="notice-item-operate-btn"
            :underline="false"
            type="primary"
            @click="evReadClick(item)"
          >
            {{ $T("已读") }}
          </el-link>
        </div>
      </div>
      <div class="notice-item-main-content" :title="content">
        {{ content }}
      </div>
    </div>
  </div>
</template>
<script>
import moment from "moment";
const LEVEL_MAP = [
  [[1], "等级一", ""],
  [[2], "等级二", "success"],
  [[3], "等级三", "info"],
  [[4], "等级四", "warning"],
  [[5], "等级五", "danger"]
];
export default {
  name: "NoticeItem",
  props: {
    item: {
      type: Object,
      default() {
        return {
          content: "前端测试节点3 10号台区 电压不平衡度 告警 908 - 不平衡跳闸",
          logType: 1,
          logTypeName: "事故事件",
          time: 1629698497968
        };
      }
    }
  },
  data() {
    const time = moment(this.item.time).format("YYYY-MM-DD HH:mm");
    const content = this.item.content;

    const [, label, type] =
      LEVEL_MAP.find(([level]) => {
        return level.includes(this.item.logType);
      }) || LEVEL_MAP[0];

    return {
      tag: {
        label: label,
        type: type
      },
      time,
      content
    };
  },
  methods: {
    evReadClick(item) {
      this.removeItem(item);
    },
    evGotoClick(item) {
      // 业务侧处理
      // ...
      // TODO

      this.removeItem(item);
    },
    removeItem(item) {
      this.$emit("removeItem", item);
    }
  }
};
</script>
<style lang="scss" scoped>
.notice-item {
  height: 50px;
  margin-right: 4px;
  position: relative;
  &-left {
    position: absolute;
    width: 58px;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
  }
  &-main {
    margin-left: 58px + 8px;
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 30px;
    }
    &-content {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      height: 20px;
      line-height: 20px;
      @include font_color(T2);
      @include font_size(Ab);
    }
  }
  &-time {
    @include font_color(T3);
    @include font_size(Ab);
  }
  &-operate {
    &-btn {
      width: 40px;
      height: 30px;
      @include font_size(Ab);
      &:hover {
        @include background_color(BG2);
      }
      &:active {
        @include background_color(BG3);
      }
    }
  }
  &-tag {
    width: 58px;
    height: 30px;
    line-height: 28px;
  }
}
</style>
