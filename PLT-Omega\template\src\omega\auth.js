import omegaApp from "@omega/app";
import { OmegaAuthPlugin } from "@omega/auth";
import system from "@/config/system.js";

omegaApp.plugin.register(OmegaAuthPlugin, {
  defaultHomepage: "/homepage",
  isTokenPersist: true,
  // enableReplayProtection: true,
  // openPermissionCheck: true,
  // whiteRouteList: [
  // "/aa/testttt"
  // ],
  // openHomepage: true,
  // openPagePermission: true,
  // isUseSuperAdminRole: false,
  // 自定义权限检查
  // checkPermission: function (rule) {
  //   if (rule === 'p.usercenter') {
  //     return true;
  //   }
  // }
  apiProxy: {
    async getPermission() {
      return [];
    },
    async checkToken() {
      return {
        userId: 1
      }
    },
    async getCurrentUser() {
      return {
        id: 1
      }
    }
  }
  // apiPrefix: {
  //   "auth-service": "/auth"
  // },
  // apiRequestInterceptor: function(config) {return config}
});
