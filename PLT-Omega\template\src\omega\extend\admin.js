import omegaApp from "@omega/app";
import { OmegaAdminPlugin } from "@omega/admin";
import { DashboradPagePlugin } from "@omega/admin/plugins/dashborad";
import { GraphPagePlugin } from "@omega/admin/plugins/graph";
import { SodaPagePlugin } from "@omega/admin/plugins/soda";

import { LinkPagePlugin } from "@omega/admin/plugins/link";
import { IFramePagePlugin } from "@omega/admin/plugins/iframe";
import { Unity3DPagePlugin } from "@omega/admin/plugins/unity3d.js";

import OtherSettingComponent from "./otherSettingComponent.vue";
import { setDefaultLanguage } from "@omega/i18n";
import { setDefaultTheme } from "@omega/theme";

omegaApp.plugin.register(OmegaAdminPlugin, {
  // apiPrefix: {
  //   "device-data-service": "/device-data"
  // },
  // apiRequestInterceptor: function(config) {return config}
  otherSertting: {
    component: OtherSettingComponent,
    handler(setting) {
      if (setting.i18n) {
        setDefaultLanguage("en");
      }
      if (setting.theme) setDefaultTheme(setting.theme);
    }
  }
});

omegaApp.plugin.register(DashboradPagePlugin);
omegaApp.plugin.register(GraphPagePlugin);
omegaApp.plugin.register(SodaPagePlugin);
omegaApp.plugin.register(LinkPagePlugin);
omegaApp.plugin.register(IFramePagePlugin);
omegaApp.plugin.register(Unity3DPagePlugin);
