import { OmegaLayoutPlugin } from "@omega/layout";
import omegaApp from "@omega/app";

import User from "../layout/user/index.vue";

omegaApp.plugin.register(OmegaLayoutPlugin, {
  // isSidebarCollapse: false,
  // layoutMode: "vertical",
  // isShowFavorites: true,
  // isShowSetting: true,
  // isShowSettingTheme: true,
  // isShowSearch: true,
  // isShowSettingLanguage: false,
  // 采用内置皮肤设置
  settingThemeList: ["light", "dark", "blue", "bluex"],
  // 自定义 [id, label, color]
  // id: 全局皮肤唯一id
  // label: 皮肤名称
  // color: 换肤颜色块颜色
  // settingThemeList: [
  // ["light", "浅色", "#987770"],
  // ["dark", "深色", "#ccc"]
  // ],
  // isNavmenuUniqueOpened: true,
  // maxNavBarsNum: 20,
  // isIconSubmenuOffset: false,
  renderHeaderRightTools(h) {
    return [<User />];
  }
  // renderSettingItems(h) {}
});
