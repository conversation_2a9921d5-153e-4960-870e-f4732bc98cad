/*
 * @Author: your name
 * @Date: 2021-08-04 15:30:24
 * @LastEditTime: 2021-08-06 16:51:14
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \frame\src\plugins\dashboard\componentList.js
 */
/**
 * dashboard预定义组件定义, 插件化运行
 * id为唯一标识
 * name为展示名称
 * viewCmp为显示组件
 * cfgCmp为配置组件
 * route可以配置组件跳转到的功能页面路由
 * paltform 配置是否属于平台的预定义组件, 和用于项目的预定义组件区分
 */
export default [
  {
    id: "pue",
    name: "PUE",
    viewCmp: () => import(`./pue/view`),
    cfgCmp: () => import(`./pue/config`),
    paltform: true,
    tag: "电能质量"
  },
  {
    id: "test",
    name: "测试",
    viewCmp: () => import(`./test/view`),
    cfgCmp: () => import(`./test/config`),
    route: "trenddemo",
    tag: "能耗管理"
  },
  {
    id: "test2",
    name: "测试2",
    viewCmp: () => import(`./test/view`),
    cfgCmp: () => import(`./test/config`),
    route: "trenddemo",
    tag: "能耗管理"
  },
  {
    id: "test3",
    name: "测试3",
    viewCmp: () => import(`./test/view`),
    cfgCmp: () => import(`./test/config`),
    route: "trenddemo",
    tag: "能耗管理"
  }
];
