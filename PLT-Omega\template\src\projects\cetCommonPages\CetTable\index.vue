<template>
  <div style="height: 100%">
    <div style="height: calc(100% - 45px)">
      <CetTable
        :data.sync="CetTable_1.data"
        :dynamicInput.sync="CetTable_1.dynamicInput"
        v-bind="CetTable_1"
        v-on="CetTable_1.event"
        ref="myTab"
        :hasColumsHeaderDoLayout="true"
      >
        <el-table-column type="index" />
        <el-table-column
          prop="date"
          width="100"
          sortable
          label="dateMyTabdateMyTabdateMyTabdateMyTabdateMyTabdateMyTab"
        >
          <template #header>
            <div
              style="
                display: inline-block;
                width: 10px;
                height: 10px;
                background-color: #ff3333;
              "
            ></div>
            {{ "dateMyTabdateMyTabdateMyTabdateMyTabdateMyTabdateMyTab" }}
            <el-tooltip class="item" effect="dark" placement="top-start">
              <template slot="content">
                <div>碳足迹=单位产品排放活动数据×排放因子</div>
              </template>
              <i class="el-icon-question mlJ1"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          prop="name"
          label="姓名姓名名姓名姓名"
          sortable
        ></el-table-column>
        <el-table-column
          prop="province"
          label="省份省份省份省份"
        ></el-table-column>
        <el-table-column
          prop="city"
          label="市区市区市区市区市区"
        ></el-table-column>
        <el-table-column
          v-for="item in customColumns"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
        >
          <template #default="scope">
            <el-tooltip placement="top-start">
              <template #content>
                <span>{{ "tooltip显示" + scope.row["address"] }}</span>
              </template>
              <span>{{ scope.row["address"] }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="text" size="small">
              查看
            </el-button>
            <el-button type="text" size="small" @click="handleClick($event)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </CetTable>
    </div>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="currentPage"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="totalCount"
    ></el-pagination>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";

onMounted(() => {});

const currentPage = ref(1);
const pageSize = ref(20);
const totalCount = ref(0);
const handleSizeChange = val => {
  pageSize.value = val;
  initData();
};
const handleCurrentChange = () => {};

const handleClick = event => {
  this.$refs.myTab.doExport(this.TableData);
  console.log(event);
};
const TableData = ref([]);
const customColumns = ref([]);
const initData = () => {
  TableData.value = [];

  // 初始化自定义列数据
  let temp = [];
  for (let i = 0; i < 10; i++) {
    temp.push({ prop: `column${i + 1}`, label: `第${i + 1}列` });
  }
  customColumns.value = temp;

  for (let i = 0; i < pageSize.value; i++) {
    TableData.value.push({
      date: "2016-05-02",
      name: "王小虎",
      province: "上海",
      city: "普陀区",
      address: "上海市普陀区金沙江路 1518 弄",
      zip: i
    });
  }

  totalCount.value = TableData.value?.length || 0;
};

initData();

const CetTable_1 = ref({
  //组件模式设置项
  queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
  dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
  //组件数据绑定设置项
  dataConfig: {
    queryFunc: "",
    deleteFunc: "",
    modelLabel: "",
    dataIndex: [],
    modelList: [],
    filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
    hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
  },
  //组件输入项
  data: TableData,
  highlightCurrentRow: false,
  dynamicInput: {},
  queryNode_in: null,
  queryTrigger_in: new Date().getTime(),
  exportTrigger_in: new Date().getTime(),
  deleteTrigger_in: new Date().getTime(),
  localDeleteTrigger_in: new Date().getTime(),
  refreshTrigger_in: new Date().getTime(),
  addData_in: {},
  editData_in: {},
  showPagination: false,
  paginationCfg: {},
  exportFileName: "",
  //defaultSort: { prop: "code"  order: "descending" },
  event: {}
});
</script>
