<template>
  <div class="page">
    <el-card class="card">
      <CetChart v-bind="CetChart_test" />
    </el-card>
    <el-card class="card">
      <CetChart v-bind="CetChart_pie" />
    </el-card>
  </div>
</template>
<script>
// import CetChart from "cet-chart";
import echarts from "cet-chart/echarts";

export default {
  name: "ChartDemo",
  // components: { CetChart },
  data() {
    return {
      CetChart_test: {
        inputData_in: [],
        options: {
          legend: {},
          tooltip: {},
          //初始值设置方法如下
          // dataset: {
          //   source: [
          //     { product: "Matcha Latte", "2015": 43.3, "2016": 85.8, "2017": 93.7 },
          //     { product: "Milk Tea", "2015": 83.1, "2016": 73.4, "2017": 55.1 },
          //     { product: "Cheese Cocoa", "2015": 86.4, "2016": 65.2, "2017": 82.5 },
          //     { product: "Walnut Brownie", "2015": 72.4, "2016": 53.9, "2017": 39.1 }
          //   ]
          // },
          xAxis: { type: "category" },
          yAxis: {},
          series: [
            { type: "bar", encode: { x: "product", y: "2015" } },
            { type: "bar", encode: { x: "product", y: "2016" } },
            { type: "bar", encode: { x: "product", y: "2017" } }
          ]
        }
      },
      CetChart_pie: {
        options: {
          legend: {
            orient: "vertical",
            left: "left",
            data: ["Apple", "Grapes", "Pineapples", "Oranges", "Bananas"]
          },
          series: [
            {
              type: "pie",
              data: [
                {
                  value: 335,
                  name: "Apple"
                },
                {
                  value: 310,
                  name: "Grapes"
                },
                {
                  value: 234,
                  name: "Pineapples"
                },
                {
                  value: 135,
                  name: "Oranges"
                },
                {
                  value: 1548,
                  name: "Bananas"
                }
              ],
              label: {
                // fontWeight: "normal",
                // borderWidth: 3.5,
                // borderType: "dotted",
                // color: "rgba(197, 34, 34, 1)",
                // backgroundColor: null,
                // shadowColor: "rgba(113, 136, 38, 1)",
                // textBorderWidth: 1,
                // textShadowColor: "rgba(151, 56, 234, 1)",
                // textBorderColor: "rgba(184, 210, 19, 1)",
                // borderColor: null
              }
            }
          ]
        }
      }
    };
  },
  created: function () {
    console.log("Xxxxxxxxxxxxxxx");
    setTimeout(() => {
      this.CetChart_test.inputData_in = [
        { product: "Matcha Latte", 2015: 43.3, 2016: 85.8, 2017: 93.7 },
        { product: "Milk Tea", 2015: 83.1, 2016: 73.4, 2017: 55.1 },
        { product: "Cheese Cocoa", 2015: 86.4, 2016: 65.2, 2017: 82.5 },
        { product: "Walnut Brownie", 2015: 72.4, 2016: 53.9, 2017: 39.1 }
      ];
    }, 2000);
  }
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}

.card {
  &:deep .el-card__body {
    height: 600px;
  }
}
</style>
