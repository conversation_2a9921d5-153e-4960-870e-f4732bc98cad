<!--
 * @Author: your name
 * @Date: 2022-03-17 14:13:18
 * @LastEditTime: 2022-04-06 15:07:01
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \Dashboard\src\projects\dashboard\index.vue
-->
<template>
  <div class="page">
    <CetDashboard dashboard-title="看板" :tree-node-model="treeNodeModel" />
  </div>
</template>
<script>
import CetDashboard from "@omega/dashboard";
export default {
  name: "Dashboard",

  components: {
    CetDashboard
  },

  computed: {},

  data() {
    return {
      treeNodeModel: ["project", "building", "floor"]
    };
  },
  watch: {},

  methods: {},
  created: function () {},
  activated: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
}
</style>
