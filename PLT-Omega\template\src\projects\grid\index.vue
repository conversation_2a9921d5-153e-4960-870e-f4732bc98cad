<template>
  <CetTable
    :data.sync="CetTable_1.data"
    :dynamicInput.sync="CetTable_1.dynamicInput"
    v-bind="CetTable_1"
    v-on="CetTable_1.event"
    ref="myTab"
  >
    <el-table-column type="index" />
    <el-table-column
      fixed
      prop="date"
      label="日期"
      width="150"
    ></el-table-column>
    <el-table-column prop="name" label="姓名" width="120"></el-table-column>
    <el-table-column prop="province" label="省份" width="120"></el-table-column>
    <el-table-column prop="city" label="市区" width="120"></el-table-column>
    <el-table-column prop="address" label="地址" width="300"></el-table-column>
    <el-table-column prop="zip" label="邮编" width="120"></el-table-column>
    <el-table-column
      v-for="item in customColumns"
      :key="item.prop"
      :prop="item.prop"
      :label="item.label"
    >
      <template #default="scope">
        <el-tooltip placement="top-start">
          <template #content>
            <span>{{ "tooltip显示" + scope.row["address"] }}</span>
          </template>
          <span>{{ scope.row["address"] }}</span>
        </el-tooltip>
      </template>
    </el-table-column>
    <el-table-column fixed="right" label="操作" width="100">
      <template slot-scope="scope">
        <el-button @click="handleClick(scope.row)" type="text" size="small">
          查看
        </el-button>
        <el-button type="text" size="small" @click="handleClick($event)">
          编辑
          <OmegaIcon
            symbolId="collect-lin"
            size="middle"
            @click="evCollectClick"
          ></OmegaIcon>
        </el-button>
        <!-- 
        <OmegaIcon symbolId="pop-ups-lin"></OmegaIcon>
        <OmegaIcon symbolId="error-lin"></OmegaIcon> -->
      </template>
    </el-table-column>
  </CetTable>
</template>

<script>
import OmegaIcon from "@omega/icon";

export default {
  name: "gridDemo",
  components: {
    OmegaIcon
  },
  created() {
    this.initColums();
  },
  methods: {
    handleClick(event) {
      this.$refs.myTab.doExport(this.tableData);
      console.log(event);
    },
    evCollectClick() {
      console.log("xxxxxxxxxxxx");
    },
    initColums() {
      // 初始化自定义列数据
      let temp = [];
      for (let i = 0; i < 10; i++) {
        temp.push({ prop: `column${i + 1}`, label: `第${i + 1}列` });
      }
      this.customColumns = temp;
    }
  },

  data() {
    let tableData = [];
    for (let i = 0; i < 20; i++) {
      tableData.push({
        date: "2016-05-02",
        name: "王小虎",
        province: "上海",
        city: "普陀区",
        address: "上海市普陀区金沙江路 1518 弄",
        zip: i
      });
    }
    return {
      tableData,
      customColumns: [],
      CetTable_1: {
        //组件模式设置项
        queryMode: "trigger", //查询按钮触发  trigger  ，或者查询条件变化立即查询  diff
        dataMode: "component", // 数据获取模式：   backendInterface    后端接口 ；其他组件  component   ; 静态数据   static
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: tableData,
        highlightCurrentRow: false,
        dynamicInput: {},
        queryNode_in: null,
        queryTrigger_in: new Date().getTime(),
        exportTrigger_in: new Date().getTime(),
        deleteTrigger_in: new Date().getTime(),
        localDeleteTrigger_in: new Date().getTime(),
        refreshTrigger_in: new Date().getTime(),
        addData_in: {},
        editData_in: {},
        showPagination: false,
        paginationCfg: {},
        exportFileName: "",
        //defaultSort: { prop: "code"  order: "descending" },
        event: {}
      }
    };
  }
};
</script>
