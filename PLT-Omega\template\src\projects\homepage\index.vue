<template>
  <el-switch v-model="value" active-text="亮色" inactive-text="暗色"></el-switch>
</template>
<script>
import { store } from "@omega/layout";

export default {
  data() {
    let value = false;
    if (store.state.navmenuMixTheme === "dark") {
      value = true;
    }
    return {
      value
    };
  },
  watch: {
    value(val) {
      if (val) {
        store.setNavmenuMixTheme("dark");
      } else {
        store.setNavmenuMixTheme();
      }
    }
  }
};
</script>
<style>
.page {
  width: 100%;
  height: 100%;
  border: 0;
}
</style>
