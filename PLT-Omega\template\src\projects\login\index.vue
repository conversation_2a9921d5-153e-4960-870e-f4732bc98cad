<template>
  <div class="login" :style="primartBackgroudStyle">
    <div class="login-main">
      <div class="login-main-right">
        <div class="login-logo">
          <div class="login-logo-main">
            <div class="login-logo-img" :style="logoBackgroundStyle" />
            <div class="login-logo-text">
              {{ $T("Matterhorn综合能源管理平台") }}
            </div>
          </div>
        </div>
        <div class="login-form">
          <LoginNormal />
        </div>
      </div>
      <div class="login-main-left" :style="mainLeftBackgroundStyle"></div>
    </div>
  </div>
</template>

<script>
import LoginNormal from "./components/loginNormal.vue";
// import LoginMobile from "./components/loginMobile.vue";
// import LoginQrcode from "./components/loginQrcode.vue";
import { conf } from "@omega/app";
import $ from "jquery";

export default {
  name: "Login",
  components: {
    LoginNormal
    // LoginMobile,
    // LoginQrcode,
  },
  computed: {
    mainLeftBackgroundStyle() {
      return conf.state.resource.login_background_image_url
        ? `background-image: url(${conf.state.resource.login_background_image_url})`
        : "";
    },
    logoBackgroundStyle() {
      return conf.state.resource.login_logo_image_url
        ? `background-image: url(${conf.state.resource.login_logo_image_url})`
        : "";
    },
    primartBackgroudStyle() {
      return conf.state.resource.login_primary_image_url ? `background-image: url(${conf.state.resource.login_primary_image_url})` : "";
    }
  },
  mounted() {
    const cb = evt => {
      if (evt.key === "Enter") {
        $(this.$el).find(".login-form .login-btn").click();
      }
    };

    const $document = $(window.document);
    $document.on("keyup", cb);
    this.$on("hook:beforeDestroy", () => $document.off("keyup", cb));
  }
};
</script>

<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  @include background_static(LOGIN_BG);
  background-size: cover !important;

  &-main {
    position: absolute;
    top: 50%;
    margin-top: -350px;
    left: 50%;
    margin-left: -700px;
    width: 1400px;
    height: 700px;

    &-right {
      position: absolute;
      width: 400px;
      height: 100%;
      top: 0px;
      right: 0px;
      background-repeat: no-repeat;
      border-radius: 4px;

      @include background_color(BG1);
      @include box_shadow(S1);
      background-size: 100% 100%;
      z-index: 1000;
    }

    &-left {
      position: absolute;
      height: 100%;
      top: 0px;
      right: 400px;
      left: 0px;
      background-size: 100% 100%;
      @include background_image_static(LOGIN_CAROUSEL_IMG);
    }
  }
}

.login-logo {
  position: relative;
  height: 280px;
  display: flex;
  justify-content: center;
  align-items: center;

  &-main {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &-img {
    background-size: contain;
    background-repeat: no-repeat;
    width: 207px;
    height: 30px;
    @include background_image_static(LOGIN_LOGO_IMG);
    @include margin_bottom(J3);
  }

  &-text {
    @include font_size(H1);
    @include line_height(H1);
    @include font_color(T3);
  }
}

.login-form {
  height: calc(100% - 280px);
  @include padding(J2);
}

.login-form::v-deep .el-tabs {
  .el-tabs__nav {
    width: 100%;
  }

  .el-tabs__item {
    width: 33%;
    text-align: center;
    padding-right: 0;

    &:not(.is-active) {
      @include font_color(T3);
    }
  }

  .el-tabs__nav-wrap::after {
    display: none;
  }
}
</style>
