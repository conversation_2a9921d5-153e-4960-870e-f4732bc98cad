<template>
  <cet-aside class="pecreport">
    <template #aside>
      <el-card class="pecreport-aside">
        <cet-ztree
          ref="ztree"
          :setting="setting"
          :props="{ name: 'nodeName', children: 'children' }"
        ></cet-ztree>
      </el-card>
    </template>
    <template #container>
      <PecReportGrid :param="param"></PecReportGrid>
    </template>
  </cet-aside>
</template>

<script>
import { httping } from "@omega/http";
import PecReportGrid from "./pecReportGrid.vue";
export default {
  name: "CetPecReport",
  components: { PecReportGrid },
  props: {
    handlerData: {
      type: Function,
      default() {
        return httping({
          method: "GET",
          url: "/devicedata/api/comm/v1/report/tree"
        });
      }
    }
  },
  data() {
    return {
      setting: {
        callback: { onClick: this.onClick }
      },
      param: {}
    };
  },
  mounted() {
    this.handlerData().then(({ data }) => {
      this.$refs.ztree.setData(data);
    });
  },
  methods: {
    onClick(event, treeId, treeNode) {
      this.param = {
        reportID: treeNode.nodeId,
        reportName: treeNode.nodeName
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.pecreport {
  width: 100%;
  height: 100%;
  position: relative;
}
.pecreport-aside::v-deep.el-card {
  height: 100%;
  .el-card__body {
    height: 100%;
    padding: 16px;
  }
}
</style>
