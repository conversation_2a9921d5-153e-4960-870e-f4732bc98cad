<template>
  <el-card class="pecreport-grid">
    <template #header>
      <div class="pecreport-grid-header">
        <el-date-picker
          v-model="dateTimeRange"
          type="datetimerange"
          align="right"
          value-format="yyyy-MM-dd HH:mm:ss"
          :start-placeholder="$T('开始时间')"
          :end-placeholder="$T('结束时间')"
          :default-time="['00:00:00', '00:00:00']"
        ></el-date-picker>
        <el-button type="primary" @click="evExportClick">
          {{ $T("导出") }}
        </el-button>
      </div>
    </template>
    <div class="pecreport-grid-empty" v-if="!html">
      <omega-icon
        class="pecreport-grid-empty-icon"
        iconClass="frame_empty"
      ></omega-icon>
    </div>
    <PecReportParse v-else :html="html"></PecReportParse>
  </el-card>
</template>

<script>
import PecReportParse from "./pecReportParse.vue";
import moment from "moment";
import { httping } from "@omega/http";
import util from "./util";

// 报表类型
export const REPORT_TYPE = {
  // PecStar报表文件（0x10420000）
  PECSTAR: 0x10420000,
  // PQ报表（0x10421000）
  PQ: 0x10421000,
  // EEM报表文件（0x10421100）
  EEM: 0x10421100,
};

// 导出类型
export const EXPOR_TTYPE = {
  // html 格式
  HTML: 1,
  // xlsx 格式
  XLSX: 2,
};

export default {
  name: "",
  components: { PecReportParse },
  props: {
    baseUrl: {
      type: String,
      default: "/devicedata",
    },
    handlerData: {
      type: Function,
      default({ startTime, endTime, reportID, reportName }) {
        /**
         * @description 实际的查询
         *
         * startTime: 2021-10-01 00:00:00       // 开始时间
         * endTime: 2021-11-01 00:00:00         // 结束时间
         * queryNodeID: 0                       // 未知
         * queryNodeType: 0                     // 未知
         * reportType: 272760832                // 报表类型 见：REPORT_TYPE
         * reportID: 6                          // 报表的ID （业务侧主要使用字段）
         * reportName: 金山医院市政表月报表       // 报表名称 （业务侧主要使用字段）
         * exportType: 0                        // 导出类型 见：EXPOR_TTYPE。目前来看好像没啥用
         * intervalType: 0                      // 时间间隔日，周，月，季，年 1，2，3，4，5 （不确定，目前来看好像没啥用）
         */
        return httping({
          method: "POST",
          url: this.baseUrl + "/api/report/v1/html",
          data: {
            startTime,
            endTime,
            reportID,
            reportName,
            reportType: REPORT_TYPE.PECSTAR,
            exportType: 0,
            intervalType: 0,
            queryNodeID: 0,
            queryNodeType: 0,
          },
          responseType: "text",
        });
      },
    },
    handlerExport: {
      type: Function,
      default({ startTime, endTime, reportID, reportName }) {
        return httping({
          method: "POST",
          url: this.baseUrl + "/api/report/v1/excel",
          data: {
            startTime,
            endTime,
            reportID,
            reportName,
            reportType: REPORT_TYPE.PECSTAR,
            exportType: EXPOR_TTYPE.XLSX,
            intervalType: 0,
            queryNodeID: 0,
            queryNodeType: 0,
          },
          responseType: "blob",
        });
      },
    },
    param: {
      type: Object,
      default() {
        return { reportID: 0, reportName: "" };
      },
    },
    immediate: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const today = moment();
    return {
      html: "",
      startTime: today.format("YYYY-MM-DD 00:00:00"),
      endTime: today.add(1, "day").format("YYYY-MM-DD 00:00:00"),
    };
  },
  computed: {
    dateTimeRange: {
      get() {
        return [this.startTime, this.endTime];
      },
      set([startTime, endTime]) {
        this.startTime = startTime;
        this.endTime = endTime;
        this.reload();
      },
    },
  },
  watch: {
    param() {
      this.reload();
    },
  },
  mounted() {
    if (this.immediate) {
      this.reload();
    }
  },
  methods: {
    reload() {
      this.handlerData(
        Object.assign(
          {
            startTime: this.startTime,
            endTime: this.endTime,
          },
          this.param
        )
      ).then(({ data }) => {
        this.html = data;
      });
    },
    evExportClick() {
      this.handlerExport({
        startTime: this.startTime,
        endTime: this.endTime,
        reportID: this.reportID,
        reportName: this.reportName,
      }).then((res) => {
        const url = window.URL.createObjectURL(res.data);
        // const fileName = httpUtil.getContentDispositionFileNmae(res);
        const fileName = `${this.reportName}_${this.startTime}-${this.endTime}.xlsx`;
        util.download(url, fileName);
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.pecreport-grid {
  width: 100%;
  height: 100%;
  position: relative;
}
.pecreport-grid-header {
  text-align: right;
  & > * {
    @include margin_right(J2);
  }
}
.pecreport-grid::v-deep.el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  .el-card__header {
    padding: 16px;
  }
  .el-card__body {
    flex: 1;
    padding: 16px;
    overflow: hidden;
  }
}
.pecreport-grid-empty {
  position: relative;
  height: 100%;
  width: 100%;
  &-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50%;
    height: 50%;
  }
}
</style>
