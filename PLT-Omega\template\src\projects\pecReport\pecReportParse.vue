<template>
  <div class="pec-report-parse"></div>
</template>

<script>
import $ from "jquery";

export default {
  name: "PecReport",
  props: {
    html: {
      type: String,
    },
  },
  watch: {
    html: {
      handler() {
        this.init();
      },
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      $(this.$el).empty().append(this.parseReportHTML(this.html));
    },
    parseReportHTML(html) {
      const $report = $(html);
      const $table = $report.find("table");
      const $trs = $report.find("table tr");
      const $tds = $report.find("table td");
      // 删除 table 标签的白色背景
      $table.css({
        color: "",
        backgroundColor: "",
      });

      // 删除所有 tr 标签的 onmouseover 和 onmouseoout 事件
      $trs.each(function () {
        $(this).removeAttr("onmouseout");
        $(this).removeAttr("onmouseover");
      });

      // 删除所有 td 标签的背景色和字体颜色
      $tds.each(function () {
        $(this).css({
          color: "",
          backgroundColor: "",
        });
      });
      return $table.get(0);
    },
  },
};
</script>

<style lang="scss" scoped>
.pec-report-parse {
  height: 100%;
  overflow: auto;
}
.pec-report-parse ::v-deep {
  table,
  tr,
  td {
    @include border_color(B1, !important);
  }

  table {
    white-space: nowrap;
  }
  tr {
    &:hover {
      @include background_color(BG2);
    }
  }
}
</style>
