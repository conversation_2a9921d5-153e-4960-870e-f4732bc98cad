<template>
  <div class="page markdown-body">
    <!-- <SelectMapPointDemo1 />  -->
    <div class="w-full">
      <div class="card">
        <div class="pb-4">范围选择是否开启：{{ showPolygon }}</div>
        <el-button @click="handleClick">选择地图上的点</el-button>
        <el-button @click="toggleShowPolygon">{{ btnText }}</el-button>
      </div>

      <SelectPointDialog
        v-model="show"
        :showPolygon="showPolygon"
        :initialCoordinates="initialCoordinates"
        @confirm="handlePointSelect"
      />

      <div class="card">
        <div>
          <span class="inline-block mr-2">请选择您心仪的地产：</span>
          <SelectPointInput class="inline-block" v-model="coordinates" />
        </div>
      </div>

      <div class="card">
        <div class="mb-4">今天天气如何?</div>
        <div class="dark:bg-green-400 px-4 bg-blue-300">
          <CetWeather />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import SelectPointDialog from "cet-common/cet-selectMapPoint/SelectMapPointDialog.vue";
import SelectPointInput from "cet-common/cet-selectMapPoint/SelectMapPoint.vue";
import CetWeather from "@omega/weather";

export default {
  name: "SelectPointDemo1",
  components: { SelectPointDialog, SelectPointInput, CetWeather },
  data() {
    return {
      show: false,
      showPolygon: false,
      initialCoordinates: [114.458176, 30.425081],
      coordinates: [114.458176, 30.425081]
    };
  },
  computed: {
    btnText() {
      return this.showPolygon ? "关闭选择范围" : "开启选择范围";
    }
  },
  methods: {
    toggleShowPolygon() {
      this.showPolygon = !this.showPolygon;
    },
    handleClick() {
      this.show = true;
    },
    handlePointSelect(coordinates, path) {
      if (!coordinates.length) {
        this.$message.warning("没有选择哦");
      } else {
        const [lng, lat] = coordinates;
        let msg = `经度：${lng} 纬度：${lat}`;
        if (this.showPolygon && path) {
          msg += `；多边行路径为：${JSON.stringify(path)}`;
        }
        // console.log(path);
        this.$alert(msg, "提示");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.page {
  position: relative;
  display: flex;
  margin: -24px;
  padding: 24px;
}
.card {
  @apply bg-BG2 p-6 rounded-md w-full box-border mb-6;
}
</style>
