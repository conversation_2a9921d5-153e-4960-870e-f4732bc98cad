<template>
    <div>
      <!-- <FormLink></FormLink> -->
      <!-- <FormLink2></FormLink2> -->
      <!-- <FormIFrame></FormIFrame> -->
      <!-- <sodaPage></sodaPage> -->
      <!-- <editCustom></editCustom> -->
      <linkPage></linkPage>
    </div>
  </template>
  <script>
  import _ from "lodash";
  // import Soda from "../../../../packages/omega-admin/nav/editor/form/Soda.vue";
  // import  FormIFrame  from "../../../../packages/omega-admin/nav/editor/form/IFrame.vue";
  import editCustom from "../../../../packages/omega-admin/nav/editor/dialog/editCustom.vue";
  import linkPage from "../../../../packages/omega-admin/plugins/linkPage.vue";
  import sodaPage from "../../../../packages/omega-admin/plugins/sodaPage.vue";
  export default {
    name: "dataTime",
    components: {
    //   FormLink: () => import("../../../../packages/omega-admin/nav/editor/form/Link.vue"),
    //   editCustom,
      linkPage,
    //   sodaPage,
    },
  };
  </script>
  