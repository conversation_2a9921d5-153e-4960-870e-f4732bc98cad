<template>
  <div class="page">
    <el-container style="height: 100%">
      <el-header class="trend-class" height="25px">
        <!-- 查询按钮组件 -->
        <CetButton
          :visible_in="CetButton_query.visible_in"
          :disable_in="CetButton_query.disable_in"
          v-bind="CetButton_query.config"
          @statusTrigger_out="CetButton_query_statusTrigger_out"
        ></CetButton>
        <!-- zantai按钮组件 -->
        <CetButton
          v-bind="CetButton_zantai"
          v-on="CetButton_zantai.event"
        ></CetButton>
        <!-- clear按钮组件 -->
        <CetButton
          v-bind="CetButton_clear"
          v-on="CetButton_clear.event"
        ></CetButton>
        <!-- changeImgName按钮组件 -->
        <CetButton
          v-bind="CetButton_changeImgName"
          v-on="CetButton_changeImgName.event"
        ></CetButton>
      </el-header>
      <el-main>
        <CetTrend
          :queryMode="CetTrend_test.queryMode"
          :dataConfig="CetTrend_test.dataConfig"
          :queryTime_in="CetTrend_test.queryTime_in"
          :params_in="CetTrend_test.params_in"
          :interval_in="CetTrend_test.interval_in"
          :title_in="CetTrend_test.title_in"
          :viewSize="CetTrend_test.viewSize"
          :exportImgName_in="CetTrend_test.exportImgName_in"
          :queryTrigger_in="CetTrend_test.queryTrigger_in"
          :clearTrigger_in="CetTrend_test.clearTrigger_in"
          :scatter_in="CetTrend_test.scatter_in"
          @scatterClick_out="CetTrend_test_scatterClick_out"
          v-bind="CetTrend_test.config"
        ></CetTrend>
      </el-main>
    </el-container>
  </div>
</template>
<script>
export default {
  name: "TrendDemo",

  computed: {
    token() {
      return this.$store.state.token;
    },
    userRootNode() {
      var vm = this;
      return vm.$store.state.rootNode;
    }
  },

  data() {
    return {
      // test组件
      CetTrend_test: {
        //组件模式设置项
        queryMode: "diff", //查询按钮触发trigger，或者查询条件变化立即查询diff
        //组件数据绑定设置项
        dataConfig: {
          queryUrl:
            "/devicedata/api/v1/batch/datalog/span/group?fill=true&p35=false",
          // queryUrl: "/pqService/v1/realDataAnalysis/queryTrendCurve",
          type: "POST"
        },
        queryTrigger_in: new Date().getTime(),
        clearTrigger_in: new Date().getTime(),
        // 格式为{timeType:1,time:null} time字段中一个是起始时间，两个第一个是起始时间，第二个是终止时间,初始化查询需要设置该值
        queryTime_in: {
          timeType: 1,
          time: ["2023-10-09", "2023-10-16"]
          // time: [new Date("2021-07-02"), new Date("2021-07-03")]
        },
        title_in: "趋势曲线",
        exportImgName_in: "测试导出图片名称",
        params_in: [],
        interval_in: 0,
        scatter_in: [],
        viewSize: "small",
        config: {
          color: [
            "red",
            "black",
            "green",
            "#91c7ae",
            "#749f83",
            "#ca8622",
            "#bda29a"
          ],
          showTableButton: true,
          showLegend: true,
          showExtremButton: true,
          showAverageButton: true,
          showPointButton: true,
          showLimitButton: true,
          showRawMarkButton: true,
          showDiffButton: true,
          // splitNumber: 10, 配置趋势曲线Y轴刻度线的数量
          limitText: {
            buttonText: "国标限值",
            upperLimitText: "国标上限",
            lowerLimitText: "国标下限"
          }
        }
      },

      // 查询按钮组件
      CetButton_query: {
        visible_in: true,
        disable_in: false,
        config: {
          title: "查询",
          type: "primary",
          plain: true,
          style: {
            float: "left"
          }
        }
      },
      // zantai组件
      CetButton_zantai: {
        visible_in: true,
        disable_in: false,
        title: "暂态",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_zantai_statusTrigger_out
        }
      },
      // clear组件
      CetButton_clear: {
        visible_in: true,
        disable_in: false,
        title: "清空曲线",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_clear_statusTrigger_out
        }
      },
      // changeImgName组件
      CetButton_changeImgName: {
        visible_in: true,
        disable_in: false,
        title: "修改导出图片名称",
        type: "primary",
        plain: true,
        event: {
          statusTrigger_out: this.CetButton_changeImgName_statusTrigger_out
        }
      }
    };
  },
  watch: {},

  methods: {
    //region 查询按钮out方法
    /**
     * @description:
     * @param {*} val
     * @return {*}
     */
    CetButton_query_statusTrigger_out(val) {
      // this.CetTrend_test.queryTrigger_in = this._.cloneDeep(val);
      let deviceId = 96;
      this.CetTrend_test.params_in = [
        {
          dataId: 1,
          dataName: "频率",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点1223333",
          logicalId: 1,
          unit: "V",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 2,
          dataName: "A相电压",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点1223333",
          logicalId: 1,
          unit: "V",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 3,
          dataName: "B相电压",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点1223333",
          logicalId: 1,
          unit: "V",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 1000001,
          dataName: "电流",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点efefefefefefeegt分分分分分feffefefe",
          logicalId: 1,
          unit: "A",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 1000002,
          dataName: "电流33",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点efefefefefefeegt分分分分分feffefefe",
          logicalId: 1,
          unit: "A",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 1000003,
          dataName: "电流434",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点efefefefefefeegt分分分分分feffefefe",
          logicalId: 1,
          unit: "A",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 1000004,
          dataName: "电流53",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点efefefefefefeegt分分分分分feffefefe",
          logicalId: 1,
          unit: "A",
          upperLimit: 5,
          lowerLimit: 1
        },
        {
          dataId: 1000005,
          dataName: "电流344",
          dataTypeId: 1,
          dataTypeName: "实时值",
          deviceId: deviceId,
          deviceName: "节点efefefefefefeegt分分分分分feffefefe",
          logicalId: 1,
          unit: "A",
          upperLimit: 5,
          lowerLimit: 1
        }
      ];
    },
    // zantai输出
    CetButton_zantai_statusTrigger_out(val) {
      this.CetTrend_test.scatter_in = [
        { x: 1599771600000, a: 33, fe: 34, ffe: 33 },
        { x: 1599782400000, fefg: 33, gh: 33 },
        { x: 1599787080000, bcd: 34, efg: 33 }
      ];
    },
    // clear输出
    CetButton_clear_statusTrigger_out(val) {
      this.CetTrend_test.clearTrigger_in = new Date().getTime();
      this.CetTrend_test.params_in = [];
    },
    CetTrend_test_scatterClick_out(val) {
      console.log(val);
    },
    // changeImgName输出
    CetButton_changeImgName_statusTrigger_out(val) {
      this.CetTrend_test.exportImgName_in = new Date().toString();
    }
  },
  created: function () {},
  activated: function () {},
  deactivated: function () {}
};
</script>
<style lang="scss" scoped>
.page {
  width: 100%;
  height: 600px;
  position: relative;
}

.el-header .device-Input,
.el-header .device-Select {
  display: inline-block;
}
</style>
