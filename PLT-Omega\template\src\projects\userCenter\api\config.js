import { httping } from "@omega/http";

export default {
  page: {
    items: [
      {
        name: $T("用户中心"),
        children: [
          {
            name: "用户管理",
            id: "xxx.p.usercenter",
          }
        ]
      },
    ],
  },
  operate: {
    groups: new Map([
      ["user", "用户"],
      ["usergroup", "用户组"],
      ["role", "角色"],
      // ["event", "事件管理"],
    ]),
    action: {
      default: new Map([
        ["browser", "查看"],
        ["update", "修改"],
        ["create", "新建"],
        ["delete", "删除"],
        ["upload", "上传"],
        ["download", "下载"],
        ["mark", "标记"],
        ["discard", "废弃"],
        ["verify", "审核"],
        ["commit", "处理"],
        ["assign", "指派"],
        ["publish", "发布"],
        ["claim", "接单"],
      ]),
      custom: new Map([
        // ["event_confirm", "确认事件报警"],
      ]),
    },
  },
};

export const userPermissionNodesOptions = [
  // {
  //   label: $T("项目节点权限"),
  //   handlerData() {
  //     return httping({
  //       url: "/carbon/v1/group/nodeTree?hideFacility=true",
  //       method: "GET",
  //     }).then(res => {
  //       return res.data;
  //     });
  //   },
  //   component: "ModelNodes"
  // },
  {
    label: $T("图形库节点权限"),
    handlerData() {
      return httping({
        // url: "/datacenter/epms/v1/user-manage/query/graph-tree",
        url: "/devicedata/api/comm/v1/graph/tree",
        method: "GET"
      }).then(res => {
        return res.data;
      });
    },
    component: "GraphNodes"
  },
  // {
  //   label: $T("报表节点权限"),
  //   handlerData() {
  //     return httping({
  //       url: "/datacenter/epms/v1/user-manage/query/report-tree",
  //       method: "GET"
  //     }).then(res => {
  //       return res.data;
  //     });
  //   },
  //   component: "PecstarNodes"
  // }
];
