import config from "./config";
import _ from "lodash";
import util from "../utils/util";
import omegaAuth from "@omega/auth";

/**********  页面权限 **********/
export function filterPageNodes(pageNodes) {
  const items = _.get(config, "page.items");
  const res = [];
  const checkNodes = nodes => {
    return !!util.find(nodes, null, {
      diffFn: function (id) {
        return !!_.find(pageNodes, {
          id
        });
      },
      childKey: "children",
      valueKey: "id"
    });
  };

  const loop = (nodes, res) => {
    for (const node of nodes) {
      if (
        node.id &&
        _.find(pageNodes, {
          id: node.id
        })
      ) {
        res.push({
          name: node.name,
          id: node.id
        });
      } else if (node.children) {
        if (checkNodes(node.children)) {
          const _node = {
            name: node.name
          };
          const _children = (_node.children = []);
          res.push(_node);
          loop(node.children, _children);
        }
      }
    }
  };
  loop(items, res);
  return res;
}

export function getPagePermission() {
  // 返回的页面权限数据要根据当前用户
  // 所拥有的页面权限进行过滤操作
  const items = _.get(config, "page.items");
  if (omegaAuth.user.isRoot()) {
    return items;
  }

  const rolePageNodes = omegaAuth.user.getRolePageNodes();

  return filterPageNodes(rolePageNodes);
}

/***********    操作权限  *****************/
export function parseOperatePermission(permissions) {
  const groups = _.get(config, "operate.groups");
  const action = _.get(config, "operate.action");

  const res = [];
  permissions.forEach(permission => {
    if (!omegaAuth.checkPermission(permission.name)) {
      return;
    }
    const [groupKey, operateKey] = permission.name.split("_");
    if (!groupKey || !operateKey) {
      throw new Error("权限整理出错, 格式不正确");
    }

    const groupName = groups.get(groupKey);

    const operationName =
      action.custom.get(permission.name) || action.default.get(operateKey);

    let group = _.find(res, {
      name: groupName
    });
    if (!group) {
      group = {
        name: groupName,
        children: []
      };
      res.push(group);
    }
    group.children.push({
      id: permission.id,
      name: operationName
    });
  });
  return res;
}
