import {
  parseOperatePermission,
  getPagePermission,
  filterPageNodes,
} from "./parse";
import { httping, http } from "@omega/http";

export const UserApi = {
  async get({ id }) {
    const res = await httping({
      url: `/auth/v1/user/queryByUserId`,
      method: "GET",
      params: {
        id
      }
    });
    return res.data;
  },

  async edit(data, tenantId = 1) {
    data.tenantId = tenantId;

    if (data.id) {
      // 更新用户
      return httping({
        url: "auth/v1/user",
        method: "PUT",
        data,
      });
    }

    // 新建用户
    return http({
      url: "auth/v1/user",
      method: "POST",
      data,
    });
  },

  async remove(data, tenantId = 1) {
    data.tenantId = tenantId;
    return await httping({
      url: `/auth/v1/user`,
      method: "DELETE",
      params: data,
    });
  },

  // 获取用户组列表
  async getUsers({ tenantId = 1 } = {}) {
    const res = await httping({
      url: `/auth/v1/users`,
      method: "GET",
      params: {
        tenantId,
        includeRole: false,
      },
    });
    return res.data || [];
  },

  async getRoles({ tenantId = 1 } = {}) {
    const res = await httping({
      url: "/auth/v1/roles",
      method: "GET",
      params: {
        tenantId,
      },
    });

    return res.data || [];
  },

  async getUserGroupByUserId({ userId, tenantId = 1 } = {}) {
    const res = await httping({
      url: "/auth/v1/relativeUserGroups",
      params: {
        id: userId,
        tenantId
      }
    });

    return res.data && res.data[0];
  },

  async getUserGroups({ tenantId = 1 } = {}) {
    const res = await httping({
      url: "/auth/v1/usergroups",
      method: "GET",
      params: {
        tenantId,
      },
    });

    return res.data || [];
  },

  async resetPassword(param) {
    return httping({
      url: "auth/v1/user/password/updateByRoot",
      method: "PUT",
      data: param,
    });
  },
};

export const UserGroupApi = {
  async get({ id }) {
    const res = await httping({
      url: "auth/v1/usergroup/queryById",
      method: "GET",
      params: {
        userGroupId: id,
      },
    });
    return res.data || {};
  },

  async edit(data, tenantId = 1) {
    data.tenantId = tenantId;

    if (data.id) {
      // 编辑
      return httping({
        url: "/auth/v1/usergroup",
        method: "PUT",
        data,
      });
    } else {
      // 新建
      return httping({
        url: "/auth/v1/usergroup",
        method: "POST",
        data,
      });
    }
  },

  async remove(data, tenantId = 1) {
    data.tenantId = tenantId;
    return httping({
      url: "/auth/v1/usergroup",
      method: "DELETE",
      params: data,
    });
  },

  async list({ tenantId = 1 } = {}) {
    const res = await httping({
      url: "/auth/v1/usergroups",
      method: "GET",
      params: {
        tenantId,
      },
    });
    return res.data || [];
  },
  // 获取某用户组下的所有用户
  async getGroupUsers({ id }) {
    const data = await this.get({ id });

    return data.users || [];
  },
};

export const RoleApi = {
  async list({ tenantId = 1 } = {}) {
    const res = await httping({
      url: "/auth/v1/roles",
      method: "GET",
      params: {
        tenantId,
      },
    });

    return res.data;
  },

  async get({ id }) {
    const res = await httping({
      url: `/auth/v1/role/${id}`,
      method: "GET",
    });

    return res.data;
  },

  async edit(data, tenantId = 1) {
    data.tenantId = tenantId;
    if (data.id) {
      // 编辑
      return httping({
        url: `/auth/v1/role`,
        method: "PUT",
        data,
      });
    } else {
      // 新建
      return httping({
        url: `/auth/v1/role`,
        method: "POST",
        data,
      });
    }
  },

  async remove(data, tenantId = 1) {
    data.tenantId = tenantId;
    return httping({
      url: "/auth/v1/role",
      method: "DELETE",
      params: data,
    });
  },

  async getRoleOperatePermissions({ id }) {
    const res = await httping({
      url: `/auth/v1/role/${id}/permissions`,
      method: "GET",
    });
    return parseOperatePermission(res.data);
  },

  async getOperatePermissions() {
    const res = await httping({
      url: `/auth/v1/permissions`,
      method: "GET",
    });
     return parseOperatePermission(res.data);
  },

  // TODO
  async getRolePagePermission() {
    return getPagePermission();
  },
  // TODO
  async getRolePagePermissionByUser(pageNodes) {
    return filterPageNodes(pageNodes);
  },
  // 查询角色下所有的用户
  async getUsersByRole(roleId) {
    const res = await httping({
      url: `/auth/v1/user/queryByRole?roleId=${roleId}`,
      method: "GET",
    });
    return res.data || [];
  },
};
