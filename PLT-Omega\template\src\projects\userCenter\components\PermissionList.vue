<template>
  <div class="permission-list">
    <div class="permission-list-header">
      <span class="h-label">{{ $T("模式：") }}</span>
      <el-select class="h-select" v-model="select.value">
        <el-option
          v-for="item in select.options"
          :key="item.value"
          :label="item.desc"
          :value="item.value"
        />
      </el-select>
    </div>
    <div class="permission-list-main">
      <el-transfer
        :titles="titles"
        filterable
        :filter-method="filterMethod"
        :filter-placeholder="$T('搜索')"
        v-model="transfer.value"
        :data="transfer.data"
        :props="props"
        v-bind="$attrs"
      />
    </div>
  </div>
</template>

<script>
export const PERMISSION_MODE = {
  WHITELIST: 1,
  BLACKLIST: 2,
};

export default {
  name: "PermissionList",
  model: {
    prop: "value",
    event: "change",
  },
  props: {
    value: Object,
    titles: {
      type: Array,
      default() {
        return [$T("列表"), $T("已选项")];
      },
    },
    handlerData: {
      type: Function,
      default() {
        return Promise.resolve([]);
      },
    },
    autoload: {
      type: Boolean,
      default: true,
    },
    props: {
      type: Object,
      default() {
        return {
          key: "projectId",
          label: "name",
        };
      },
    },
  },
  watch: {
    value(val) {
      this.select.value = val.mode;
      this.transfer.value = val.list;
    },
    "select.value": {
      immediate: true,
      handler(val) {
        this.$emit("change", {
          mode: val,
          list: this.transfer.value,
        });
      },
    },
    "transfer.value": {
      immediate: true,
      handler(val) {
        this.$emit("change", {
          mode: this.select.value,
          list: val,
        });
      },
    },
  },
  data() {
    const options = [
      {
        desc: $T("白名单"),
        value: PERMISSION_MODE.WHITELIST,
      },
      {
        desc: $T("黑名单"),
        value: PERMISSION_MODE.BLACKLIST,
      },
    ];

    return {
      select: {
        value: this.value.mode || PERMISSION_MODE.BLACKLIST,
        options: options,
      },
      transfer: {
        value: this.value.list || [],
        data: [],
      },
    };
  },
  created() {
    if (this.autoload) {
      this.handlerData().then((data) => {
        this.transfer.data = data;
      });
    }
  },
  methods: {
    filterMethod(query, item) {
      const value = item[this.props.label];
      return ~value.indexOf(query);
    },
  },
};
</script>

<style lang="scss" scoped >
.permission-list-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
  & .h-label {
    margin-right: 10px;
  }
  & .h-select {
    width: 120px;
  }
}
.permission-list {
  padding: 10px;
  border: 1px solid #dcdfe6;
}
</style>
