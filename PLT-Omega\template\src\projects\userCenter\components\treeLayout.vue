<template>
  <cet-aside>
    <template #aside>
      <CardWrap>
        <el-container class="fullfilled" direction="vertical">
          <el-main class="padding0">
            <CetTree
              :selectNode.sync="CetTree.selectNode"
              v-bind="CetTree"
              v-on="CetTree.event"
            >
              <omega-render
                slot-scope="{ node }"
                :render="renderCardItem(node)"
              ></omega-render>
            </CetTree>
          </el-main>
          <el-footer height="40px" class="padding0">
            <slot name="aside-footer" />
          </el-footer>
        </el-container>
      </CardWrap>
    </template>
    <template #container>
      <slot name="container" />
    </template>
  </cet-aside>
</template>

<script>
import CardItem from "../components/CardItem.vue";
import CardWrap from "../components/CardWrap.vue";

export default {
  name: "TreeLayout",
  components: {
    CardWrap
  },
  props: {
    CetTree: {
      type: Object,
      require: true
    },
    onCardItemCommand: {
      type: Function,
      require: true
    },
    opearateButtons: {
      type: Array,
      require: true
    }
  },
  methods: {
    renderCardItem(node) {
      return (
        <CardItem
          label={node.label}
          buttons={this.opearateButtons}
          onCommand={id => {
            this.onCardItemCommand(id, node.data);
          }}
        />
      );
    }
  }
};
</script>

<style lang="scss" scoped></style>
