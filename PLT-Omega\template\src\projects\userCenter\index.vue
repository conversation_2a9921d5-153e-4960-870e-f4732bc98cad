<template>
  <cet-tabs class="user-manager">
    <User :label="$T('用户')" />
    <Role :label="$T('角色')" />
    <UserGroup :label="$T('用户组')" />
  </cet-tabs>
</template>

<script>
import Role from "./role/Role.vue";
import User from "./user/User.vue";
import UserGroup from "./userGroup/UserGroup.vue";

export default {
  name: "UserManager",
  components: { Role, User, UserGroup }
};
</script>

<style lang="scss" scoped>
.user-manager {
  height: 100%;
  &::v-deep.el-tabs {
    .el-tabs__item {
      min-width: 100px;
      text-align: center;
    }
  }
}
</style>
