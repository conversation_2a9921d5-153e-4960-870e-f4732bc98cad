<template>
  <el-row class="fullheight" :gutter="16">
    <el-col class="fullheight" :span="6">
      <CardWrap>
        <header class="header">
          {{ $T("页面权限") }}
        </header>
        <section class="container">
          <CetTree v-bind="CetTree_page">
            <div
              class="text-ellipsis fullwidth"
              slot-scope="{ node }"
              :title="node.data.name"
            >
              <span
                v-if="node.data.id && node.data.id === homePagePageNodeId"
                class="rfloat mr5"
              >
                {{ $T("首页") }}
              </span>
              {{ node.data.name }}
            </div>
          </CetTree>
        </section>
      </CardWrap>
    </el-col>
    <el-col class="fullheight" :span="6">
      <CardWrap>
        <header class="header">
          {{ $T("操作权限") }}
        </header>
        <section class="container">
          <CetTree v-bind="CetTree_operate" />
        </section>
      </CardWrap>
    </el-col>
    <el-col class="fullheight" :span="12">
      <CardWrap>
        <header class="header">
          {{ $T("角色包含的用户") }}
        </header>
        <section class="container">
          <CetTable :data.sync="CetTable_Users.data" v-bind="CetTable_Users">
            <el-table-column
              v-for="(column, index) in Columns_Users"
              v-bind="column"
              :key="index"
            />
          </CetTable>
        </section>
      </CardWrap>
    </el-col>
  </el-row>
</template>

<script>
import { RoleApi } from "../api/userCenter";
import CardWrap from "../components/CardWrap.vue";
import _ from "lodash";
export default {
  name: "DetailRole",
  components: { CardWrap },
  props: {
    selectNode: Object
  },
  data() {
    return {
      // 操作权限树组件
      CetTree_operate: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true
      },
      // 页面权限树组件
      CetTree_page: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true
      },
      CetTable_Users: {
        dataMode: "component",
        //组件数据绑定设置项
        dataConfig: {
          queryFunc: "",
          deleteFunc: "",
          modelLabel: "",
          dataIndex: [],
          modelList: [],
          filters: [], // 指定属性的过滤方法 示例： { name: "name_in", operator: "LIKE", prop: "name" }, 小于 "LT"  小于等于 "LE" 大于 "GT" 大于等于"GE" 相等  "EQ"  不等于 "NE" 像"LIKE" 间于"BETWEEN"
          hasQueryNode: true // 是否有queryNode入参, 没有则需要配置为false
        },
        //组件输入项
        data: [],
        dynamicInput: {},
        queryNode_in: null
      },
      Columns_Users: [
        {
          type: "index",
          prop: "id",
          minWidth: "",
          width: 60,
          label: "序号",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "name",
          minWidth: 120,
          width: "",
          label: "用户名",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "mobilePhone",
          minWidth: "",
          width: 150,
          label: "移动电话",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        },
        {
          type: "",
          prop: "email",
          minWidth: 100,
          width: "",
          label: "电子邮箱",
          sortable: false,
          headerAlign: "center",
          align: "center",
          showOverflowTooltip: true,
          formatter: null
        }
      ]
    };
  },
  watch: {
    selectNode(node) {
      if (!node || _.isEmpty(node)) {
        this.reset();
      } else {
        this.load(node);
      }
    }
  },
  methods: {
    load(node) {
      const customConfig = node.customConfig && JSON.parse(node.customConfig);
      this.homePagePageNodeId = customConfig && customConfig.homePagePageNodeId;

      Promise.all([
        RoleApi.getRoleOperatePermissions({ id: node.id }),
        RoleApi.getRolePagePermissionByUser(node.pageNodes),
        RoleApi.getUsersByRole(node.id)
      ]).then(([operateNodes, pageNodes, users]) => {
        this.CetTree_operate.inputData_in = operateNodes;
        this.CetTree_page.inputData_in = pageNodes;
        this.CetTable_Users.data = users;
      });
    },
    reset() {
      this.CetTree_operate.inputData_in = [];
      this.CetTree_page.inputData_in = [];
      this.CetTable_Users.data = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.header {
  height: 40px;
}
.container {
  height: calc(100% - 40px);
}
</style>
