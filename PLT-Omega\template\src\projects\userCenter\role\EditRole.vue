<template>
  <omega-dialog :title="title" :on-before-confirm="onBeforeConfirm">
    <el-form class="edit-form" ref="form" :model="formData" :rules="rules">
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item
            :label="$T('角色名称')"
            prop="name"
            class="custom-form-item"
          >
            <el-input
              class="w260"
              clearable
              maxlength="30"
              :placeholder="$T('请输入角色名称')"
              v-model.trim="formData.name"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="" class="custom-form-item">
            <template #label>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  {{
                    $T("在下面已选择的页面权限节点中，选择一个页面作为首页展示")
                  }}
                </div>
                <i class="el-icon-question"></i>
              </el-tooltip>
              {{ $T("角色首页") }}
            </template>
            <PSelect
              filterable
              v-bind="ElSelect_HomePage"
              v-model="ElSelect_HomePage.value"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <el-row :gutter="16">
      <el-col :span="12">
        <CardWrap>
          <template #header>
            {{ $T("操作权限") }}
          </template>
          <CetTree
            class="permission-tree-container"
            :checked-nodes.sync="CetTree_operate.checkedNodes"
            v-bind="CetTree_operate"
          />
        </CardWrap>
      </el-col>
      <el-col :span="12">
        <CardWrap>
          <template #header>
            {{ $T("页面权限") }}
          </template>
          <CetTree
            class="permission-tree-container"
            :checked-nodes.sync="CetTree_page.checkedNodes"
            v-bind="CetTree_page"
            v-on="CetTree_page.event"
          />
        </CardWrap>
      </el-col>
    </el-row>
  </omega-dialog>
</template>

<script>
import omegaAuth from "@omega/auth";

import PSelect from "../components/p-select.vue";
import { RoleApi } from "../api/userCenter";
import CardWrap from "../components/CardWrap.vue";

export default {
  name: "EditServiceUser",
  components: {
    PSelect,
    CardWrap
  },
  props: {
    id: Number
  },
  computed: {
    isEdit() {
      return !!this.id;
    },
    title() {
      return this.isEdit ? $T("编辑角色") : $T("添加角色");
    }
  },
  data() {
    return {
      formData: {
        name: ""
      },
      rules: {
        name: [{ required: true, message: "用户名不能为空！", trigger: "blur" }]
      },
      ElSelect_HomePage: {
        autoload: false,
        items: [],
        value: null
      },
      // 操作权限树组件
      CetTree_operate: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        defaultExpandAll: true
      },
      // 操作权限树组件
      CetTree_page: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: false,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          children: "children"
        },
        highlightCurrent: true,
        showCheckbox: true,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          checkedNodes_out: this.CetTree_page_checkedNodes_out
        }
      }
    };
  },
  created() {
    if (this.isEdit) {
      RoleApi.get({ id: this.id }).then(data => {
        this.originData = data;
        this.formData = {
          name: data.name
        };

        this.CetTree_operate.checkedNodes = data.auths.map(id => {
          return { id };
        });
        this.CetTree_page.checkedNodes = data.pageNodes.map(({ id }) => {
          return { id };
        });

        const customConfig = JSON.parse(data.config);
        this.ElSelect_HomePage.value =
          customConfig && customConfig.homePagePageNodeId;
      });
    }
    // 当前用户角色下的所有权限

    const getRolePermission = async () => {
      if (omegaAuth.isRootUser()) {
        return RoleApi.getOperatePermissions();
      }
      return RoleApi.getRoleOperatePermissions({
        id: omegaAuth.user.getRoleId()
      });
    };

    getRolePermission().then(data => {
      const permissionTreeData = [
        {
          name: "全选",
          children: data
        }
      ];
      this.CetTree_operate.inputData_in = permissionTreeData;
    });
    RoleApi.getRolePagePermission({}).then(data => {
      const permissionTreeData = [
        {
          name: "全选",
          children: data
        }
      ];
      this.CetTree_page.inputData_in = permissionTreeData;
    });
  },
  methods: {
    async onBeforeConfirm() {
      const { form } = this.$refs;
      await form.validate();
      const res = await RoleApi.edit(this.getData());
      const id = res.data;
      return id;
    },

    getOperatePermission() {
      const checkedNodes = this.CetTree_operate.checkedNodes;
      const auths = [];
      checkedNodes.forEach(node => {
        if (!node.children) {
          auths.push(node.id);
        }
      });
      return auths;
    },

    getPagePermission() {
      const checkedNodes = this.CetTree_page.checkedNodes;
      const items = [];
      checkedNodes.forEach(node => {
        if (!node.children) {
          items.push({
            authIds: [],
            disabled: false,
            id: node.id,
            label: node.label,
            rule: ""
          });
        }
      });
      return items;
    },

    CetTree_page_checkedNodes_out(checkedNodes) {
      const items = [];
      checkedNodes.forEach(node => {
        if (!node.children) {
          items.push({
            id: node.id,
            name: node.name
          });
        }
      });

      this.ElSelect_HomePage.items = items;
    },

    getData() {
      const formData = this.formData;

      const _data = {
        name: formData.name,
        auths: this.getOperatePermission(),
        pageNodes: this.getPagePermission(),
        customConfig: JSON.stringify({
          homePagePageNodeId: this.ElSelect_HomePage.value
        })
      };

      if (this.isEdit) {
        return Object.assign({}, this.originData, _data);
      }
      return Object.assign({}, _data);
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-form {
  padding: 15px;
  & ::v-deep .el-form-item {
    margin-bottom: 0;
  }
}
.permission-tree-container {
  height: 400px;
  overflow: auto;
}
</style>
