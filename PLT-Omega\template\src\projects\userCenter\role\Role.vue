<template>
  <TreeLayout
    class="role"
    :CetTree="CetTree"
    :opearateButtons="opearateButtons"
    :onCardItemCommand="onCardItemCommand"
  >
    <template #aside-footer>
      <el-button class="fullwidth" type="primary" @click="evAddRoleClick">
        {{ $T("+ 添加角色") }}
      </el-button>
    </template>
    <template #container>
      <DetailRole :select-node="selectNode" />
    </template>
  </TreeLayout>
</template>
<script>
import { RoleApi } from "../api/userCenter";
import EditRole from "./EditRole.vue";
import DetailRole from "./DetailRole.vue";
import TreeLayout from "../components/treeLayout.vue";
import { showOmegaDialog } from "@omega/widget";

export default {
  name: "Role",
  components: {
    DetailRole,
    TreeLayout,
  },
  data() {
    return {
      // 用户树组件
      CetTree: {
        inputData_in: [],
        selectNode: {}, //要包含nodeKey属性, 例:{ tree_id: "city_53" }
        checkedNodes: [], //要包含nodeKey属性, 例:[{ tree_id: "city_54" }]
        filterNodes_in: null, //[]表示过滤掉所有节点
        searchText_in: "",
        showFilter: true,
        ShowRootNode: false,
        nodeKey: "id",
        props: {
          label: "name",
          // children: ""
        },
        highlightCurrent: true,
        showCheckbox: false,
        checkStrictly: false,
        defaultExpandAll: true,
        event: {
          currentNode_out: this.CetTree_currentNode_out,
        },
      },
      opearateButtons: [
        {
          label: "编辑",
          id: "edit",
          type: "primary",
        },
        {
          label: "删除",
          id: "delete",
          type: "danger",
        },
      ],
      selectNode: {},
    };
  },
  methods: {
    CetTree_currentNode_out(node) {
      this.selectNode = node;
    },

    evAddRoleClick() {
      const e = showOmegaDialog(EditRole);
      e.on("confirm", (id) => {
        this.CetTree.selectNode = {
          id: id,
        };
        this.refresh();
      });
    },

    edit(node) {
      const e = showOmegaDialog(EditRole, {
        id: node.id,
      });
      e.on("confirm", (id) => {
        this.CetTree.selectNode = {
          id: id,
        };
        this.refresh();
      });
    },

    async remove(node) {
      await this.$confirm($T("确认删除该角色？"));

      await RoleApi.remove({
        id: node.id,
        name: node.name,
      });
      if (node.id === this.CetTree.selectNode.id) {
        this.CetTree.selectNode = {};
      }
      this.refresh();
    },
    // 刷新数据
    refresh() {
      const tree = this.CetTree;
      RoleApi.list().then((val) => {
        const treeData = val || [];
        tree.inputData_in = treeData;

        // 默认选中第一个用户
        if (this._.isEmpty(tree.selectNode) && treeData.length) {
          const id = treeData[0] && treeData[0].id;
          if (id) {
            tree.selectNode = {
              id,
            };
          }
        }
      });
    },

    onCardItemCommand(id, node) {
      switch (id) {
        case "delete":
          this.remove(node);
          break;
        case "edit":
          this.edit(node);
          break;
        default:
          return;
      }
    },
  },
  activated() {
    this.refresh();
  },
};
</script>
<style lang="scss" scoped>
.role ::v-deep .cet-content-aside-sidebar .el-tree {
  overflow: auto;
  .el-tree-node__content {
    height: 60px;
  }
}
</style>
