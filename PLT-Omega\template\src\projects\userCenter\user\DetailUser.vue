<template>
  <el-row class="user-detail" :gutter="16" :key="randomKey">
    <el-col class="user-permission" :span="18">
      <CardWrap>
        <el-tabs class="user-permission-tabs" type="border-card">
          <el-tab-pane
            v-for="(option, index) in permissionNodesOptions"
            :key="index"
            :label="option.label"
          >
            <component
              :is="option.component"
              :handlerData="option.handlerData"
              :props="option.prop"
              :user="userDetail"
              :isView="true"
            ></component>
          </el-tab-pane>
        </el-tabs>
      </CardWrap>
    </el-col>
    <el-col class="user-info" :span="6">
      <div class="user-form">
        <CardWrap>
          <div class="user-form-header">
            <omega-icon class="I4" symbolId="user-two-lin" size="large" />
          </div>
          <el-form ref="form">
            <el-form-item v-for="(item, index) in items" :key="index">
              <template #label>
                <omega-icon class="p2 mr10 fs18" :symbolId="item.icon" />
                <span>{{ item.label }}</span>
              </template>
              <div class="text-right text-ellipsis">{{ item.value }}</div>
            </el-form-item>
          </el-form>
          <div class="text-right">
            <el-button type="danger" @click="evResetPasswordClick">
              {{ $T("重置密码") }}
            </el-button>
          </div>
        </CardWrap>
      </div>
      <div class="user-group">
        <CardWrap>
          <template #header>
            {{ $T("所属用户组") }}
          </template>
          <span
            class="text-center text-ellipsis"
            :title="userDetail.relativeUserGroupName"
          >
            {{ userDetail.relativeUserGroupName || "--" }}
          </span>
        </CardWrap>
      </div>
    </el-col>
  </el-row>
</template>
<script>
import ResetPassword from "./ResetPassword";
import CardWrap from "../components/CardWrap.vue";
import { userPermissionNodesOptions } from "../api/config";
import { UserApi } from "../api/userCenter.js";

import ModelNodes from "./modules/ModelNodes.vue";
import GraphNodes from "./modules/GraphNodes.vue";
import PecstarNodes from "./modules/PecstarNodes.vue";
import { showOmegaDialog } from "@omega/widget";

import _ from "lodash";
export default {
  name: "DetailUser",
  components: {
    CardWrap,
    /* eslint-disable no-undef */
    // 注册权限节点的相关组件模块
    ModelNodes,
    GraphNodes,
    PecstarNodes
    /* eslint-disable no-undef */
  },
  props: {
    selectNode: Object
  },
  watch: {
    selectNode(node) {
      if (node && !_.isEmpty(node)) {
        this.load(node);
      }
    }
  },
  data() {
    return {
      userDetail: {
        name: "",
        nicName: "",
        mobilePhone: "",
        email: "",
        roleName: "",
        relativeUserGroupName: "",
        relativeUserGroupLogo: null
      },
      permissionNodesOptions: userPermissionNodesOptions
    };
  },
  computed: {
    items() {
      return [
        {
          icon: "user-one-lin",
          label: $T("用户名："),
          value: this.userDetail.name || "--"
        },
        {
          icon: "user-one-lin",
          label: $T("昵称："),
          value: this.userDetail.nicName || "--"
        },
        {
          icon: "account-lin",
          label: $T("角色："),
          value: this.userDetail.roleName || "--"
        },
        {
          icon: "phone-lin",
          label: $T("电话："),
          value: this.userDetail.mobilePhone || "--"
        },
        {
          icon: "mailbox-lin",
          label: $T("邮箱："),
          value: this.userDetail.email || "--"
        }
      ];
    },
    randomKey() {
      return this.selectNode.id;
    }
  },
  methods: {
    async load(node) {
      const [user, userGroup] = await Promise.all([
        UserApi.get({
          id: node.id
        }),
        UserApi.getUserGroupByUserId({
          userId: node.id
        })
      ]);

      const roleName = _.get(user, "roles[0].name", "--");
      const userGroupName = _.get(userGroup, "name", "--");
      this.userDetail = {
        name: user.name,
        nicName: user.nicName,
        mobilePhone: user.mobilePhone,
        email: user.email,
        roleName: roleName,
        relativeUserGroupName: userGroupName,
        relativeUserGroupLogo: null
      };
    },
    evResetPasswordClick() {
      showOmegaDialog(ResetPassword, {
        id: this.userDetail.id,
        userName: this.userDetail.name
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.user-detail {
  height: 100%;
}
.user-permission-tabs::v-deep.el-tabs {
  height: 100%;
  & > .el-tabs__header.is-top {
    margin: 0 0 5px;
  }
  & > .el-tabs__content {
    height: calc(100% - 45px);
    & > .el-tab-pane {
      height: 100%;
    }
  }
}
.user-permission {
  height: 100%;
}
.user-info {
  height: 100%;
  overflow: auto;

  .user-form {
    height: 400px;
    margin-bottom: 16px;
    box-sizing: border-box;
    ::v-deep .el-form-item__label {
      display: flex;
      align-items: center;
    }
  }
  .user-form-header {
    height: 80px;
    line-height: 80px;
    text-align: center;
  }
  .user-group {
    height: calc(100% - 418px);
    box-sizing: border-box;
    min-height: 240px;
  }
}
</style>
