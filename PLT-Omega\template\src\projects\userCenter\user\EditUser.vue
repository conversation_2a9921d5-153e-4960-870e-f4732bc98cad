<template>
  <omega-dialog
    :title="title"
    width="980px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form
      ref="form"
      class="user-form"
      label-width="100px"
      label-position="left"
      :model="formData"
      :rules="rules"
    >
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item
            :label="$T('用户名')"
            prop="name"
            class="custom-form-item"
          >
            <el-input
              clearable
              maxlength="30"
              :placeholder="$T('请输入用户名')"
              v-model.trim="formData.name"
            />
          </el-form-item>
          <el-form-item
            :label="$T('昵称')"
            prop="nicName"
            class="custom-form-item"
          >
            <el-input
              clearable
              maxlength="30"
              :placeholder="$T('请输入昵称')"
              v-model.trim="formData.nicName"
            />
          </el-form-item>
          <template v-if="!isEdit">
            <el-form-item
              :label="$T('账户密码')"
              prop="password"
              class="custom-form-item"
            >
              <el-input
                clearable
                maxlength="18"
                type="password"
                :placeholder="$T('请输入账户密码')"
                v-model.trim="formData.password"
              />
            </el-form-item>
            <el-form-item
              :label="$T('密码确认')"
              prop="_checkPassword"
              class="custom-form-item"
            >
              <el-input
                clearable
                maxlength="18"
                type="password"
                :placeholder="$T('请输入确认密码')"
                v-model.trim="formData._checkPassword"
              />
            </el-form-item>
          </template>
          <el-form-item
            :label="$T('移动电话')"
            prop="mobilePhone"
            class="custom-form-item"
          >
            <el-input
              clearable
              maxlength="30"
              :placeholder="$T('请输入移动电话')"
              v-model.trim="formData.mobilePhone"
            />
          </el-form-item>
          <el-form-item
            :label="$T('电子邮箱')"
            prop="email"
            class="custom-form-item"
          >
            <el-input
              clearable
              maxlength="30"
              :placeholder="$T('请输入电子邮箱')"
              v-model.trim="formData.email"
            />
          </el-form-item>
          <el-form-item
            :label="$T('用户组')"
            prop="userGroupId"
            class="custom-form-item"
          >
            <PSelect
              class="fullwidth"
              filterable
              v-bind="ElSelect_UserGroup"
              v-model="formData.userGroupId"
            />
          </el-form-item>
          <el-form-item
            :label="$T('角色')"
            prop="roleId"
            class="custom-form-item"
          >
            <PSelect
              class="fullwidth"
              ref="role"
              filterable
              v-bind="ElOption_Roles"
              v-model="formData.roleId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="16">
          <el-form-item>
            <el-tabs class="user-permission-tabs" type="border-card">
              <el-tab-pane
                v-for="(option, index) in permissionNodesOptions"
                :key="index"
                :label="option.label"
              >
                <component
                  :is="option.component"
                  :handlerData="option.handlerData"
                  :props="option.prop"
                  :user="originUserData"
                  ref="mods"
                ></component>
              </el-tab-pane>
            </el-tabs>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </omega-dialog>
</template>

<script>
import { UserApi } from "../api/userCenter";
import { userPermissionNodesOptions } from "../api/config";
import rule from "../utils/rule";

import PSelect from "../components/p-select.vue";

import ModelNodes from "./modules/ModelNodes.vue";
import GraphNodes from "./modules/GraphNodes.vue";
import PecstarNodes from "./modules/PecstarNodes.vue";

export default {
  name: "EditUser",
  components: {
    PSelect,
    /* eslint-disable no-undef */
    // 注册权限节点的相关组件模块
    ModelNodes,
    GraphNodes,
    PecstarNodes
    /* eslint-disable no-undef */
  },
  props: {
    id: Number
  },
  computed: {
    isEdit() {
      return !!this.id;
    },
    title() {
      return this.isEdit ? $T("编辑用户") : $T("新建用户");
    }
  },
  data() {
    return {
      formData: {
        name: "",
        nicName: "",
        password: "",
        _checkPassword: "",
        email: "",
        mobilePhone: "",
        userGroupId: null,
        roleId: null
      },
      rules: {
        name: [
          { required: true, message: $T("用户名不能为空！"), trigger: "blur" }
        ],
        nicName: [
          { required: true, message: $T("昵称不能为空！"), trigger: "blur" }
        ],
        userGroupId: [
          { required: true, message: $T("请选择用户组！"), trigger: "blur" }
        ],
        roleId: [
          { required: true, message: $T("请选择用户角色！"), trigger: "blur" }
        ],
        password: [
          rule.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error($T("密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.formData.password) {
                callback(new Error($T("密码不一致")));
                return;
              }

              callback();
            }
          }
        ],
        mobilePhone: [
          rule.check_phone,
          { required: true, message: $T("移动电话不能为空"), trigger: "blur" }
        ],
        email: [
          {
            type: "email",
            required: true,
            message: $T("请输入正确的邮箱地址"),
            trigger: "blur"
          }
        ]
      },
      ElSelect_UserGroup: {
        autoload: true,
        handlerData: UserApi.getUserGroups
      },
      ElOption_Roles: {
        autoload: true,
        handlerData: UserApi.getRoles
      },
      originUserData: undefined,
      permissionNodesOptions: userPermissionNodesOptions
    };
  },
  created() {
    if (this.isEdit) {
      UserApi.get({ id: this.id }).then(data => {
        this.originUserData = data;
        this.formData = {
          name: data.name,
          nicName: data.nicName,
          email: data.email,
          mobilePhone: data.mobilePhone,
          userGroupId: data.relativeUserGroup && data.relativeUserGroup[0],
          roleId: data.roles && data.roles[0] && data.roles[0].id
        };
      });
    }
  },
  methods: {
    async onBeforeConfirm() {
      const { form } = this.$refs;
      await form.validate();
      const res = await UserApi.edit(this.getData());

      return res.data;
    },

    getRole(id) {
      const roles = this.$refs.role.getData();
      return this._.find(roles, { id });
    },

    getData() {
      const formData = this.formData;

      const _data = {
        avatar: formData.avatar,
        email: formData.email,
        mobilePhone: formData.mobilePhone,
        name: formData.name,
        nicName: formData.nicName,
        roles: [this.getRole(formData.roleId)],
        relativeUserGroup: [formData.userGroupId]
      };
      // 回填数据
      _.forEach(this.$refs.mods, mod => {
        _.assign(_data, mod.getData());
      });
      if (this.isEdit) {
        if (!this.originUserData.expiredTime) {
          this.originUserData.expiredTime = 0;
        }
        if (!this.originUserData.state) {
          this.originUserData.state = 0;
        }
        return _.assign({}, this.originUserData, _data);
      }
      return _.assign(
        {},
        {
          password: formData.password
        },
        _data
      );
    }
  }
};
</script>
<style lang="scss" scoped>
.user-permission-tabs::v-deep.el-tabs {
  margin-left: -80px;
  & > .el-tabs__content {
    & > .el-tab-pane {
      height: 380px;
    }
  }
}
.user-form::v-deep.el-form {
  .el-form-item--small.el-form-item {
    margin-bottom: 28px;
  }
}
</style>
