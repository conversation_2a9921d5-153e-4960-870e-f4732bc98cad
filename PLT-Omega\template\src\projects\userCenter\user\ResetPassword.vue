<template>
  <omega-dialog
    :title="$T('重置密码')"
    width="500px"
    :on-before-confirm="onBeforeConfirm"
  >
    <el-form ref="form" :model="form" label-width="110px" :rules="rules">
      <el-form-item :label="$T('用户名')" prop="userName">
        <el-input disabled :value="userName" />
      </el-form-item>
      <el-form-item :label="$T('新密码')" prop="newPassword">
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="$T('请输入新的密码')"
          v-model.trim="form.newPassword"
        />
      </el-form-item>
      <el-form-item :label="$T('新密码确认')" prop="_checkPassword">
        <el-input
          clearable
          type="password"
          maxlength="18"
          :placeholder="$T('请输入确认密码')"
          v-model.trim="form._checkPassword"
        />
      </el-form-item>
      <el-form-item :label="$T('管理员密码')" prop="rootPassword">
        <el-input
          clearable
          type="password"
          maxlength="32"
          :placeholder="$T('请输入管理员密码（当前账号密码）')"
          v-model.trim="form.rootPassword"
        />
      </el-form-item>
    </el-form>
  </omega-dialog>
</template>
<script>
import rule from "../utils/rule";
import { UserApi } from "../api/userCenter";
import omegaAuth from "@omega/auth";

export default {
  name: "DialogResetUserPassword",
  components: {},
  props: {
    id: Number,
    userName: String
  },
  data() {
    return {
      form: {
        rootPassword: "",
        newPassword: "",
        _checkPassword: ""
      },
      rules: {
        rootPassword: [
          {
            required: true,
            message: $T("管理员密码不能为空"),
            trigger: "blur"
          }
        ],
        newPassword: [
          rule.check_strongPassword,
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (this._.isNil(value) || value === "") {
                callback(new Error($T("新密码不能为空")));
                return;
              }

              callback();
            }
          }
        ],
        _checkPassword: [
          {
            required: true,
            type: "string",
            trigger: "blur",
            validator: (rule, value, callback) => {
              if (value !== this.form.newPassword) {
                callback(new Error($T("密码不一致")));
                return;
              }
              callback();
            }
          }
        ]
      }
    };
  },
  methods: {
    getData() {
      const form = this.form;
      return {
        userId: this.id,
        userName: this.userName,
        rootName: omegaAuth.user.getUserName(),
        rootId: omegaAuth.user.getUserId(),
        newPassword: form.newPassword,
        rootPassword: form.rootPassword
      };
    },

    async onBeforeConfirm() {
      const { form } = this.$refs;

      await form.validate();

      await UserApi.resetPassword(this.getData());

      return true;
    }
  }
};
</script>
