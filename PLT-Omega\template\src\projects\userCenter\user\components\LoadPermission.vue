<template>
  <TreePermission ref="treePermission" :props="props" :isView="isView">
    <template #header v-if="hasBlackList">
      <BlackList v-model="isBlackList" :isView="isView" />
    </template>
  </TreePermission>
</template>

<script>
import TreePermission from "./TreePermission.vue";
import BlackList from "./BlackList.vue";

export default {
  name: "LoadPermission",
  components: { TreePermission, BlackList },
  props: {
    handlerData: {
      type: Function,
      default() {
        return Promise.resolve([]);
      }
    },
    props: {
      type: Object,
      default() {
        return {
          name: "name",
          children: "children",
          uuKeys: ["id", "modelLabel"],
          dataKeys: ["id", "modelLabel"]
        };
      }
    },
    checkNodes: {
      type: Array,
      default() {
        return [];
      }
    },
    isBlackList: {
      type: Number,
      default: 0
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  },
  computed: {
    hasBlackList() {
      return this.isBlackList !== -1;
    }
  },
  watch: {
    checkNodes: {
      immediate: true,
      handler() {
        this.handlerData().then(data => {
          const checkNodes = this.checkNodes.map(checkNode => {
            return this.assignWithKeys(checkNode, this.props.uuKeys);
          });
          if (this.isView) {
            this.$refs.treePermission.setData(
              this.filterTreeData(data, this.checkNodes)
            );
          } else {
            this.$refs.treePermission.setData(data);
            this.$refs.treePermission.setValue({
              checkNodes
            });
          }
        });
      }
    }
  },
  methods: {
    getData() {
      const item = {
        authIds: [],
        childSelectState: -1,
        disabled: false
      };

      let { checkNodes } = this.$refs.treePermission.getValue();

      checkNodes = checkNodes.map(checkNode => {
        return _.assign(
          {},
          item,
          this.assignWithKeys(checkNode, this.props.dataKeys)
        );
      });
      return {
        isBlackListModel: this.isBlackList,
        checkNodes
      };
    },
    assignWithKeys(obj, keys) {
      const ret = {};
      _.forEach(keys, key => {
        ret[key] = obj[key];
      });
      return ret;
    },
    // 过滤出选中节点树
    filterTreeData(data, checkNodes) {
      const props = this.props;
      const _this = this;
      function loop(items) {
        for (let index = 0; index < items.length; ) {
          const item = items[index];
          if (!_.find(checkNodes, _this.assignWithKeys(item, props.uuKeys))) {
            // 删除该元素
            items.splice(index, 1);
            continue;
          }

          const children = item[props.children];
          if (children && children.length) {
            loop(children);
          }
          index++;
        }
      }
      loop(data);
      return data;
    }
  }
};
</script>
