<template>
  <div class="tree-permission">
    <header>
      <slot name="header" />
      <div class="tree-permission-operates" v-if="!isView">
        <el-button type="text" @click="onCheckAllClick">
          {{ $T("全选") }}
        </el-button>
        <el-button type="text" @click="onReverseClick">
          {{ $T("反选") }}
        </el-button>
        <el-button type="text" @click="onCancelClick">
          {{ $T("取消") }}
        </el-button>
      </div>
    </header>
    <main>
      <cet-ztree ref="ztree" :setting="setting" :props="props" />
    </main>
  </div>
</template>

<script>
import Vue from "vue";

export default {
  name: "",
  data() {
    if (this.isView) {
      return {
        setting: {}
      };
    }
    return {
      setting: {
        check: {
          enable: true
        },
        view: {
          addHoverDom: this.addHoverDom
        }
      }
    };
  },
  props: {
    props: {
      type: Object,
      default() {
        return {
          name: "name",
          children: "children"
        };
      }
    },
    isView: {
      type: <PERSON>olean,
      default: false
    }
  },
  methods: {
    addHoverDom(treeId, treeNode) {
      if (!treeNode.isParent) {
        return;
      }
      const checkbox = this.getNodeReverseCheckbox(treeNode);
      $("#" + treeNode.tId).append(checkbox.$el);
    },
    getNodeReverseCheckbox(treeNode) {
      let reverseBtn = this.___reverseBtn;
      if (reverseBtn) {
        reverseBtn.$off();
        reverseBtn.$on("click", () => {
          this.evNodeReverseCheckboxClick(treeNode);
        });
        return reverseBtn;
      }

      reverseBtn = this.___reverseBtn = this.createNodeReverseBtn();
      reverseBtn.$mount();

      this.$on("hook:beforeDestroy", () => {
        reverseBtn.$destroy();
      });
      return reverseBtn;
    },
    createNodeReverseBtn() {
      return new Vue({
        render() {
          return (
            <el-button
              class="tree-permission-btn"
              type="text"
              nativeOnClick={this.evClick}
            >
              {$T("反选")}
            </el-button>
          );
        },
        methods: {
          evClick() {
            this.$emit("click");
          }
        }
      });
    },
    evNodeReverseCheckboxClick(treeNode) {
      _.forEach(treeNode.children, node => {
        this.$refs.ztree.checkNode(node, !node.checked, true);
      });
    },
    onCheckAllClick() {
      this.$refs.ztree.checkAllNodes(true);
    },
    onReverseClick() {
      const nodes = this.$refs.ztree.getNodes();
      _.forEach(nodes, node => {
        this.$refs.ztree.checkNode(node, !node.checked, true);
      });
    },
    onCancelClick() {
      this.$refs.ztree.checkAllNodes(false);
    },
    setData(data) {
      this.$refs.ztree.setData(data);
    },
    setValue({ checkNodes }) {
      let nodeList = [];
      const ztree = this.$refs.ztree;
      const nodes = ztree.getNodes();
      nodeList = ztree.transformToArray(nodes);
      _.forEach(checkNodes, checkNode => {
        const node = _.find(nodeList, checkNode);
        if (node && !node.isParent) {
          ztree.checkNode(node, true, true);
        }
      });
    },
    getValue() {
      return {
        checkNodes: this.$refs.ztree.getCheckedNodes(true)
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.tree-permission {
  height: 100%;
  & > header {
    position: relative;
    height: 40px;
  }
  & > main {
    height: calc(100% - 40px);
  }

  &::v-deep {
    li[treenode] {
      position: relative;
    }
    .tree-permission-btn {
      position: absolute;
      top: 0;
      right: 16px;
    }
  }
}

.tree-permission-operates {
  position: absolute;
  top: 50%;
  right: 16px;
  transform: translateY(-50%);
}
</style>
