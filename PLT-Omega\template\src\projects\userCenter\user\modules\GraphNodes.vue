<template>
  <LoadPermission
    ref="loadPermission"
    :handlerData="handlerData"
    :props="props"
    :checkNodes="checkNodes"
    :isBlackList="isBlackList"
    :isView="isView"
  />
</template>

<script>
import LoadPermission from "../components/LoadPermission.vue";
export default {
  name: "ReportNodes",
  components: { LoadPermission },
  props: {
    handlerData: {
      require: true,
      type: Function
    },
    user: {
      require: true,
      default() {
        return {
          graphNodes: [],
          isGraphNodesBlacklist: 0
        };
      }
    },
    isView: {
      type: <PERSON><PERSON>an,
      default: false
    }
  },
  data() {
    return {
      props: {
        name: "nodeName",
        children: "children",
        uuKeys: ["nodeId", "nodeType"],
        dataKeys: ["nodeId", "nodeType", "nodeName"]
      },
      checkNodes: this.fixNodeId(_.cloneDeep(this.user.graphNodes), false),
      isBlackList: this.user.isGraphNodesBlacklist
    };
  },
  methods: {
    getData() {
      const { checkNodes, isBlackList } = this.$refs.loadPermission.getData();
      return {
        graphNodes: this.fixNodeId(checkNodes, true),
        isGraphNodesBlacklist: isBlackList
      };
    },
    // HACK 图形节点数据[nodeID & nodeId]的矫正
    fixNodeId(nodes, flag) {
      _.forEach(nodes, node => {
        if (flag) {
          node.nodeID = node.nodeId;
          delete node.nodeId;
        } else {
          node.nodeId = node.nodeID;
          delete node.nodeID;
        }
      });
      return nodes;
    }
  }
};
</script>
