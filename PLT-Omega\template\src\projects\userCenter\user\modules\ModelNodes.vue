<template>
  <LoadPermission
    ref="loadPermission"
    :handlerData="handlerData"
    :checkNodes="checkNodes"
    :isBlackList="isBlackList"
    :props="props"
    :isView="isView"
  />
</template>

<script>
import LoadPermission from "../components/LoadPermission.vue";
export default {
  name: "ReportNodes",
  components: { LoadPermission },
  props: {
    handlerData: {
      require: true,
      type: Function
    },
    user: {
      require: true,
      default() {
        return {
          modelNodes: [],
          isModelNodesBlacklist: 0
        };
      }
    },
    isView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      props: {
        name: "name",
        children: "children",
        uuKeys: ["id", "modelLabel"],
        dataKeys: ["id", "modelLabel"]
      },
      checkNodes: this.user.modelNodes,
      isBlackList: this.user.isModelNodesBlacklist
    };
  },
  watch: {
    user: {
      deep: true,
      handler(val) {
        this.checkNodes = val.modelNodes;
        this.isBlackList = val.isModelNodesBlacklist;
      }
    }
  },
  methods: {
    getData() {
      const { checkNodes, isBlackListModel } =
        this.$refs.loadPermission.getData();
      return {
        modelNodes: checkNodes,
        isModelNodesBlacklist: isBlackListModel
      };
    }
  }
};
</script>
