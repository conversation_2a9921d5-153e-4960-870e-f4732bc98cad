<template>
  <LoadPermission
    ref="loadPermission"
    :handlerData="handlerData"
    :props="props"
    :checkNodes="checkNodes"
    :isBlackList="-1"
    :isView="isView"
  />
</template>

<script>
import LoadPermission from "../components/LoadPermission.vue";
export default {
  name: "ReportNodes",
  components: { LoadPermission },
  props: {
    handlerData: {
      require: true,
      type: Function
    },
    user: {
      require: true,
      default() {
        return {
          pecstarNodes: []
        };
      }
    },
    isView: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  data() {
    return {
      props: {
        name: "nodeN<PERSON>",
        children: "children",
        uuKeys: ["nodeId", "nodeType"],
        dataKeys: ["nodeId", "nodeType"]
      },
      checkNodes: this.fixNodeId(_.cloneDeep(this.user.pecstarNodes), false)
    };
  },
  methods: {
    getData() {
      const { checkNodes } = this.$refs.loadPermission.getData();
      return {
        pecstarNodes: this.fixNodeId(checkNodes, true)
      };
    },
    // HACK [nodeID & nodeId]的矫正
    fixNodeId(nodes, flag) {
      _.forEach(nodes, node => {
        if (flag) {
          node.nodeID = node.nodeId;
          delete node.nodeId;
        } else {
          node.nodeId = node.nodeID;
          delete node.nodeID;
        }
      });
      return nodes;
    }
  }
};
</script>
