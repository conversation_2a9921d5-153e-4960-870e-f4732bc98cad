export default {
  check_stringLessThan140: {
    min: 1,
    max: 140,
    message: "长度在 1 到 140 个字符",
    trigger: ["blur", "change"],
  },
  check_name: {
    min: 1,
    max: 20,
    message: "长度在 1 到 20 个字符",
    trigger: ["blur", "change"],
  },
  check_strongPassword: {
    pattern:
      /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[`~!@$%^&*()+=[\]{}\\|;:'"<,>.?/]).{8,18}$/,
    trigger: ["blur", "change"],
    message: "密码需含有大写、小写字母、数字和特殊字符，且长度为8~18位",
  },
  check_pattern_name: {
    // pattern: /^[a-z]+$/,
    // pattern: /((?=[\x21-\x7e]+)[^A-Za-z0-9])/,
    // pattern: /^((?!`~!@#$%^&*()_+-=[]{}\|;:\'"<,>.?\/).)*$/,
    pattern: /^((?![`~!@$%^&*()+=[\]{}\\|;:'"<,>.?/]).)*$/, //如果把-加进去就表示数字也算特殊字符了，所以-不能加进去
    message: "请不要输入特殊字符",
    trigger: ["blur", "change"],
  },
  check_phone: {
    pattern: /^1[3|4|5|7|8][0-9]\d{8}$/,
    trigger: ["blur", "change"],
    message: "请输入正确的手机号",
  },

  check_telephone: {
    pattern: /^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$/,
    trigger: ["blur", "change"],
    message: "请输入正确的固定电话 例：010-88888888-123",
  },
  check_phone_or_telephone: {
    pattern:
      /(^((0\d{2,3})-)(\d{7,8})(-(\d{3,}))?$)|(^1[3|4|5|7|8][0-9]\d{8}$)/,
    trigger: ["blur", "change"],
    message: "请输入正确的手机号或固定电话",
  },
};
