/**
 * @param {Array} items
 * @param {Any} value 查找的value
 * @prop {Function} diffFn 对比判定函数
 * @prop {String} childKey
 * @prop {String} key
 */
export function find(
  items,
  value,
  { diffFn, childKey = "child", valueKey = "id" }
) {
  function isEqual(item) {
    return diffFn
      ? diffFn(item[valueKey], value, item)
      : hasProp(item, valueKey) && item[valueKey] === value;
  }

  function loop(items) {
    for (const item of items) {
      if (isEqual(item)) {
        return item;
      }
      if (item[childKey]) {
        const ret = loop(item[childKey]);
        if (ret) {
          return ret;
        }
      }
    }
  }
  return loop(items) || null;
}

export default {
  find,
};
