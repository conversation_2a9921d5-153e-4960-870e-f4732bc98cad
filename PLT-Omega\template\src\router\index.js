/*
 * @Author: your name
 * @Date: 2022-03-29 11:42:04
 * @LastEditTime: 2023-05-04 17:47:10
 * @LastEditors: 'zhuyunxia' <EMAIL>
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \omega-repo\template\src\router\index.js
 */
import { LayoutMain } from "@omega/layout";

import pages from "./pages.js";

/** @type {import("vue-router").RouteConfig[]} */

const appRoutes = [
  ...pages,
  {
    path: "/aa/testttt",
    component: () => import("@/projects/chartdemo/index.vue")
  },
  {
    path: "/",
    component: LayoutMain,
    children: [
      {
        path: "/demo",
        component: () => import("@/projects/chartdemo/index.vue")
      },
      {
        path: "/grid",
        meta: {
          keepAlive: true
        },
        component: () => import("@/projects/grid/index.vue")
      },
      {
        path: "/pecreport",
        component: () => import("@/projects/pecReport/index.vue")
      },
      {
        path: "/usercenter",
        component: () => import("@omega/usercenter")
      },
      {
        path: "/tailwind",
        component: () => import("@/projects/tailwind/index.vue")
      },
      {
        path: "/datatime",
        component: () => import("@/projects/datatime/index.vue")
      },
      {
        path: "/dashboard",
        component: () => import("@/projects/dashboard/index.vue")
      },
      {
        path: "/fullscreendashboard/:name/:mode",
        component: () => import("@/projects/fullscreenDashboard/index.vue")
      },
      {
        path: "/trenddemo",
        component: () => import("@/projects/trenddemo/index.vue")
      },
      {
        path: "/selectMapDemo",
        meta: {
          keepAlive: true
        },
        component: () => import("@/projects/select-map-point-demo/index.vue")
      },
      {
        path: "/homepage",
        component: () => import("@/projects/homepage/index.vue")
      },
      /* cetCommonPages */
      {
        path: "/CetTable",
        component: () => import("@/projects/cetCommonPages/CetTable/index.vue")
      }
    ]
  }
];

export default {
  routes: appRoutes
};
