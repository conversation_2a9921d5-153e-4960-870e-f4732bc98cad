import { httping, http } from "@omega/http";

export default httping;

export const util = {
  getContentDispositionFileNmae(res) {
    const contentDisposition = res.headers["content-disposition"];
    const regExp = /filename=([^;]*)/;
    if (contentDisposition) {
      const [, fileName] = regExp.exec(contentDisposition);
      return window.decodeURIComponent(fileName);
    }
  },
  /**
   * 下载文件
   * @param url 文件url
   * @param name 文件名
   */
  download(url, name) {
    const a = document.createElement("a");
    a.download = name;
    a.href = url;
    a.target = "_blank";
    document.head.appendChild(a);
    a.click();
    document.head.removeChild(a);
  },
};

/**
 * 以POST请求方式需要导出的文件
 * @param url 必须
 * @param param 非必须
 * @param filename 非必须
 */
export function download(url, param, filename) {
  return http({
    url: url,
    method: "POST",
    responseType: "blob",
    data: param,
  }).then((res) => {
    const url = window.URL.createObjectURL(res.data);
    util.download(url, filename || util.getContentDispositionFileNmae(res));
  });
}
