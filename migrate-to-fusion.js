#!/usr/bin/env node

/**
 * PLT-Omega to Fusion Migration Script
 * 自动化迁移脚本，帮助将PLT-Omega项目迁移到Fusion架构
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class FusionMigrator {
  constructor(sourceDir, targetDir) {
    this.sourceDir = path.resolve(sourceDir);
    this.targetDir = path.resolve(targetDir);
    this.backupDir = path.join(this.targetDir, 'backup');
  }

  async migrate() {
    console.log('🚀 开始PLT-Omega到Fusion迁移...');
    
    try {
      await this.createBackup();
      await this.createFusionStructure();
      await this.migrateSourceCode();
      await this.updatePackageJson();
      await this.createFusionConfigs();
      await this.updateVueConfig();
      await this.createOmegaExtensions();
      await this.updateRouterConfig();
      await this.installDependencies();
      
      console.log('✅ 迁移完成！');
      console.log('📝 请查看migration-guide.md了解后续手动调整步骤');
    } catch (error) {
      console.error('❌ 迁移失败:', error.message);
      console.log('🔄 正在恢复备份...');
      await this.restoreBackup();
    }
  }

  async createBackup() {
    console.log('📦 创建备份...');
    if (fs.existsSync(this.targetDir)) {
      if (!fs.existsSync(this.backupDir)) {
        fs.mkdirSync(this.backupDir, { recursive: true });
      }
      execSync(`cp -r "${this.targetDir}" "${this.backupDir}/original"`);
    }
  }

  async createFusionStructure() {
    console.log('🏗️ 创建Fusion项目结构...');
    
    // 创建目录结构
    const dirs = [
      this.targetDir,
      path.join(this.targetDir, 'main'),
      path.join(this.targetDir, 'sub'),
      path.join(this.targetDir, 'main/src/omega/extend'),
      path.join(this.targetDir, 'main/src/layout/toBlade')
    ];
    
    dirs.forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  async migrateSourceCode() {
    console.log('📁 迁移源代码...');
    
    if (fs.existsSync(this.sourceDir)) {
      // 复制源代码到main目录
      const mainDir = path.join(this.targetDir, 'main');
      execSync(`cp -r "${this.sourceDir}"/* "${mainDir}"/`);
    }
  }

  async updatePackageJson() {
    console.log('📦 更新package.json...');
    
    // 创建根目录package.json
    const rootPackageJson = {
      name: path.basename(this.targetDir),
      version: "1.0.0",
      workspaces: ["main", "sub"],
      scripts: {
        "dev": "npm run dev -w main",
        "build": "npm run build -w main",
        "dev:main": "npm run dev -w main",
        "dev:sub": "npm run dev -w sub",
        "build:main": "npm run build -w main",
        "build:sub": "npm run build -w sub"
      },
      author: "migration-script",
      license: "ISC"
    };
    
    fs.writeFileSync(
      path.join(this.targetDir, 'package.json'),
      JSON.stringify(rootPackageJson, null, 2)
    );

    // 更新main/package.json
    const mainPackageJsonPath = path.join(this.targetDir, 'main/package.json');
    if (fs.existsSync(mainPackageJsonPath)) {
      const mainPackageJson = JSON.parse(fs.readFileSync(mainPackageJsonPath, 'utf8'));
      
      // 更新依赖
      const newDependencies = {
        "@altair/blade": "^1.6.6",
        "@altair/lord": "^1.10.4",
        "@omega/video": "^2.0.19",
        "@omega/usercenter": "^1.0.4",
        "crypto-js": "^4.1.1"
      };
      
      mainPackageJson.dependencies = {
        ...mainPackageJson.dependencies,
        ...newDependencies
      };
      
      // 更新omega包版本
      const omegaUpdates = {
        "@omega/app": "^1.10.1",
        "@omega/auth": "^1.16.2",
        "@omega/layout": "^1.14.3",
        "@omega/http": "^1.7.1",
        "@omega/i18n": "^1.7.0",
        "@omega/theme": "^1.12.12"
      };
      
      Object.keys(omegaUpdates).forEach(pkg => {
        if (mainPackageJson.dependencies[pkg]) {
          mainPackageJson.dependencies[pkg] = omegaUpdates[pkg];
        }
      });
      
      fs.writeFileSync(mainPackageJsonPath, JSON.stringify(mainPackageJson, null, 2));
    }
  }

  async createFusionConfigs() {
    console.log('⚙️ 创建Fusion配置文件...');
    
    // 创建pnpm-workspace.yaml
    const workspaceConfig = `packages:
  - "main"
  - "sub"
`;
    fs.writeFileSync(path.join(this.targetDir, 'pnpm-workspace.yaml'), workspaceConfig);
    
    // 创建omega/fusion.js
    const fusionConfig = `import omegaApp from "@omega/app";
import { FusionManager } from "@altair/lord";

// 开发环境配置
const develop = {
  appList: [
    {
      name: "cloud-auth"
      // url: "//localhost:9529"
      // alive: false
      // preload: true,
      // exec: true
    }
  ],
  options: {
    isMultiProject: true
  }
};

// 生产环境配置
const config = {
  appList: [],
  options: {
    isMultiProject: true
  }
};

omegaApp.plugin.register(
  FusionManager,
  process.env.NODE_ENV === "development" ? develop : config
);
`;
    
    fs.writeFileSync(
      path.join(this.targetDir, 'main/src/omega/fusion.js'),
      fusionConfig
    );
  }

  async createOmegaExtensions() {
    console.log('🔌 创建Omega扩展插件...');
    
    // 创建blade.js
    const bladeConfig = `import { DashboradPagePlugin } from "@altair/blade/src/plugins/dashborad";
import { GraphPagePlugin } from "@altair/blade/src/plugins/graph";
import { SodaPagePlugin } from "@altair/blade/src/plugins/soda";
import { LinkPagePlugin } from "@altair/blade/src/plugins/link";
import { IFramePagePlugin } from "@altair/blade/src/plugins/iframe";
import { BladePlugins } from "@altair/blade";
import omegaApp from "@omega/app";

omegaApp.plugin.register(BladePlugins, {});

omegaApp.plugin.register(DashboradPagePlugin);
omegaApp.plugin.register(GraphPagePlugin);
omegaApp.plugin.register(SodaPagePlugin);
omegaApp.plugin.register(LinkPagePlugin);
omegaApp.plugin.register(IFramePagePlugin);
`;
    
    fs.writeFileSync(
      path.join(this.targetDir, 'main/src/omega/extend/blade.js'),
      bladeConfig
    );

    // 创建toBlade组件
    const toBladeComponent = `<template>
  <span
    class="to-blade"
    :title="$T('系统设置')"
    v-if="user.isRoot() && isFusionSystemConfig"
  >
    <omega-icon @click="goBlade" symbolId="workbench-sg" />
  </span>
</template>

<script>
import { user } from "@omega/auth";
import { isFusionSystemConfig } from "@altair/lord";

export default {
  name: "ToBlade",
  data() {
    return {
      isFusionSystemConfig,
      user
    };
  },
  methods: {
    goBlade() {
      this.$router.push("/bladeView");
    }
  }
};
</script>

<style scoped>
.to-blade {
  cursor: pointer;
  margin-left: 8px;
}
</style>
`;
    
    fs.writeFileSync(
      path.join(this.targetDir, 'main/src/layout/toBlade/index.vue'),
      toBladeComponent
    );
  }

  async updateVueConfig() {
    console.log('⚙️ 更新Vue配置...');
    
    const vueConfigPath = path.join(this.targetDir, 'main/vue.config.js');
    if (fs.existsSync(vueConfigPath)) {
      let vueConfig = fs.readFileSync(vueConfigPath, 'utf8');
      
      // 添加@altair编译支持
      if (vueConfig.includes('transpileDependencies')) {
        vueConfig = vueConfig.replace(
          /transpileDependencies:\s*\[([^\]]*)\]/,
          'transpileDependencies: [$1, "@altair/blade"]'
        );
      } else {
        vueConfig = vueConfig.replace(
          'module.exports = omegaCliDevserverHandler({',
          'module.exports = omegaCliDevserverHandler({\n  transpileDependencies: ["@omega", "@altair/blade"],'
        );
      }
      
      fs.writeFileSync(vueConfigPath, vueConfig);
    }
  }

  async updateRouterConfig() {
    console.log('🛣️ 更新路由配置...');
    
    const routerPath = path.join(this.targetDir, 'main/src/router/index.js');
    if (fs.existsSync(routerPath)) {
      let routerConfig = fs.readFileSync(routerPath, 'utf8');
      
      // 添加Fusion相关导入
      if (!routerConfig.includes('@altair/lord')) {
        routerConfig = routerConfig.replace(
          'import { LayoutMain } from "@omega/layout";',
          `import { LayoutMain } from "@omega/layout";
import { FusionContainer } from "@altair/lord";
import { BladeView } from "@altair/blade";`
        );
      }
      
      fs.writeFileSync(routerPath, routerConfig);
    }
  }

  async installDependencies() {
    console.log('📦 安装依赖...');
    
    try {
      process.chdir(this.targetDir);
      execSync('npm install', { stdio: 'inherit' });
    } catch (error) {
      console.warn('⚠️ 依赖安装失败，请手动运行 npm install');
    }
  }

  async restoreBackup() {
    if (fs.existsSync(this.backupDir)) {
      execSync(`cp -r "${this.backupDir}/original"/* "${this.targetDir}"/`);
      console.log('✅ 备份已恢复');
    }
  }
}

// 命令行使用
if (require.main === module) {
  const args = process.argv.slice(2);
  const sourceDir = args[0] || './';
  const targetDir = args[1] || './fusion-project';
  
  const migrator = new FusionMigrator(sourceDir, targetDir);
  migrator.migrate().catch(console.error);
}

module.exports = FusionMigrator;
